from typing import Optional
from pydantic import BaseModel, constr, ConfigDict, EmailStr
from datetime import datetime

class UserBase(BaseModel):
    """Base user schema with common fields."""
    username: str
    full_name: str | None = None
    email: EmailStr

    model_config = ConfigDict(from_attributes=True)

class UserCreate(UserBase):
    """Schema for creating a new user."""
    password: constr(min_length=8)
    full_name: str

    model_config = ConfigDict(from_attributes=True)

class UserUpdate(UserBase):
    """Schema for updating a user."""
    password: Optional[constr(min_length=8)] = None

    model_config = ConfigDict(from_attributes=True)

class UserInDB(UserBase):
    """Internal user schema with hashed password."""
    id: int
    is_active: bool = True
    hashed_password: str
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class UserResponse(BaseModel):
    """Response model for user info."""
    username: str
    full_name: str
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True, json_schema_extra={
        "example": {
            "username": "testuser",
            "full_name": "Test User",
            "id": 1,
            "is_active": True,
            "created_at": "2025-03-24T00:14:24.964Z",
            "updated_at": "2025-03-24T00:14:24.964Z"
        }
    })
