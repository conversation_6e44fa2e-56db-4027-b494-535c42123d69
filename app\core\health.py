"""
Health check system for ComplianceMax.
"""

import time
from typing import Dict, Any, List
import redis
from sqlalchemy import text
from app.core.config import settings
from app.core.logging import get_logger
from app.core.database import db, engine
from app.core.cache import RedisCache
from app.core.metrics import MetricsCollector

logger = get_logger(__name__)

class HealthCheck:
    """Health check system for monitoring system components."""

    @staticmethod
    async def check_database() -> Dict[str, Any]:
        """Check database connectivity and performance."""
        start_time = time.time()
        status = "healthy"
        details = {}

        try:
            # Test basic query
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            # Get connection pool stats
            pool_status = db.get_pool_status()
            details.update(pool_status)
            
            # Add response time
            details["response_time"] = round(time.time() - start_time, 3)
            
        except Exception as e:
            status = "unhealthy"
            details["error"] = str(e)
            logger.error(f"Database health check failed: {str(e)}")
            MetricsCollector.track_error("health_check", "database_error")

        return {
            "component": "database",
            "status": status,
            "details": details
        }

    @staticmethod
    async def check_redis() -> Dict[str, Any]:
        """Check Redis connectivity and performance."""
        start_time = time.time()
        status = "healthy"
        details = {}

        try:
            redis_client = RedisCache().redis
            
            # Test basic operations
            redis_client.set("health_check", "1")
            redis_client.get("health_check")
            redis_client.delete("health_check")
            
            # Get Redis info
            info = redis_client.info()
            details.update({
                "connected_clients": info["connected_clients"],
                "used_memory": info["used_memory_human"],
                "total_connections_received": info["total_connections_received"]
            })
            
            # Add response time
            details["response_time"] = round(time.time() - start_time, 3)
            
        except Exception as e:
            status = "unhealthy"
            details["error"] = str(e)
            logger.error(f"Redis health check failed: {str(e)}")
            MetricsCollector.track_error("health_check", "redis_error")

        return {
            "component": "redis",
            "status": status,
            "details": details
        }

    @staticmethod
    async def check_disk_space() -> Dict[str, Any]:
        """Check available disk space."""
        import shutil
        
        start_time = time.time()
        status = "healthy"
        details = {}

        try:
            total, used, free = shutil.disk_usage("/")
            
            # Convert to GB
            total_gb = total / (2**30)
            used_gb = used / (2**30)
            free_gb = free / (2**30)
            
            details.update({
                "total_gb": round(total_gb, 2),
                "used_gb": round(used_gb, 2),
                "free_gb": round(free_gb, 2),
                "usage_percent": round((used / total) * 100, 2)
            })
            
            # Check if disk space is low
            if free_gb < settings.MIN_FREE_DISK_GB:
                status = "warning"
                details["warning"] = f"Low disk space: {free_gb}GB free"
            
            # Add response time
            details["response_time"] = round(time.time() - start_time, 3)
            
        except Exception as e:
            status = "unhealthy"
            details["error"] = str(e)
            logger.error(f"Disk space check failed: {str(e)}")
            MetricsCollector.track_error("health_check", "disk_space_error")

        return {
            "component": "disk_space",
            "status": status,
            "details": details
        }

    @staticmethod
    async def check_memory() -> Dict[str, Any]:
        """Check system memory usage."""
        import psutil
        
        start_time = time.time()
        status = "healthy"
        details = {}

        try:
            memory = psutil.virtual_memory()
            
            details.update({
                "total_gb": round(memory.total / (2**30), 2),
                "available_gb": round(memory.available / (2**30), 2),
                "used_gb": round(memory.used / (2**30), 2),
                "usage_percent": memory.percent
            })
            
            # Check if memory usage is high
            if memory.percent > settings.MAX_MEMORY_PERCENT:
                status = "warning"
                details["warning"] = f"High memory usage: {memory.percent}%"
            
            # Add response time
            details["response_time"] = round(time.time() - start_time, 3)
            
        except Exception as e:
            status = "unhealthy"
            details["error"] = str(e)
            logger.error(f"Memory check failed: {str(e)}")
            MetricsCollector.track_error("health_check", "memory_error")

        return {
            "component": "memory",
            "status": status,
            "details": details
        }

    @classmethod
    async def check_all(cls) -> Dict[str, Any]:
        """Run all health checks."""
        start_time = time.time()
        results = []
        overall_status = "healthy"

        # Run all checks
        checks = [
            cls.check_database(),
            cls.check_redis(),
            cls.check_disk_space(),
            cls.check_memory()
        ]
        
        for check in checks:
            result = await check
            results.append(result)
            
            # Update overall status
            if result["status"] == "unhealthy":
                overall_status = "unhealthy"
            elif result["status"] == "warning" and overall_status == "healthy":
                overall_status = "warning"

        return {
            "status": overall_status,
            "timestamp": time.time(),
            "duration": round(time.time() - start_time, 3),
            "components": results
        } 