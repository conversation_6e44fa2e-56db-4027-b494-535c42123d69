"""
Monitoring configuration for ComplianceMax.
"""
from prometheus_client import Counter, Histogram, Gauge
import structlog
from typing import Dict, Any
import time

# Metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

REQUEST_LATENCY = Histogram(
    'http_request_duration_seconds',
    'HTTP request latency',
    ['method', 'endpoint']
)

ACTIVE_REQUESTS = Gauge(
    'http_requests_active',
    'Number of active HTTP requests'
)

DB_QUERY_DURATION = Histogram(
    'db_query_duration_seconds',
    'Database query duration',
    ['operation', 'table']
)

CACHE_HITS = Counter(
    'cache_hits_total',
    'Total cache hits',
    ['cache_type']
)

CACHE_MISSES = Counter(
    'cache_misses_total',
    'Total cache misses',
    ['cache_type']
)

ERROR_COUNT = Counter(
    'error_total',
    'Total errors',
    ['type', 'location']
)

# Structured logging setup
logger = structlog.get_logger()

class MetricsMiddleware:
    """Middleware for collecting request metrics."""
    
    async def __call__(self, request, call_next):
        ACTIVE_REQUESTS.inc()
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.url.path,
                status=response.status_code
            ).inc()
            
            REQUEST_LATENCY.labels(
                method=request.method,
                endpoint=request.url.path
            ).observe(time.time() - start_time)
            
            return response
            
        except Exception as e:
            ERROR_COUNT.labels(
                type=type(e).__name__,
                location=request.url.path
            ).inc()
            raise
            
        finally:
            ACTIVE_REQUESTS.dec()

class DatabaseMetrics:
    """Database query metrics collection."""
    
    @staticmethod
    async def record_query(operation: str, table: str, start_time: float):
        """Record database query metrics."""
        duration = time.time() - start_time
        DB_QUERY_DURATION.labels(
            operation=operation,
            table=table
        ).observe(duration)

class CacheMetrics:
    """Cache operation metrics collection."""
    
    @staticmethod
    def record_hit(cache_type: str):
        """Record cache hit."""
        CACHE_HITS.labels(cache_type=cache_type).inc()
    
    @staticmethod
    def record_miss(cache_type: str):
        """Record cache miss."""
        CACHE_MISSES.labels(cache_type=cache_type).inc()

def setup_monitoring(app: Any) -> None:
    """Configure monitoring for the application."""
    
    # Add metrics middleware
    app.add_middleware(MetricsMiddleware)
    
    # Configure structured logging
    structlog.configure(
        processors=[
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.PrintLoggerFactory(),
        wrapper_class=structlog.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Expose metrics endpoint
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    from fastapi import Response
    
    @app.get("/metrics")
    async def metrics():
        return Response(
            generate_latest(),
            media_type=CONTENT_TYPE_LATEST
        ) 