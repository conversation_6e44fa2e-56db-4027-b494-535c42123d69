from sqlalchemy import Column, Integer, String, ForeignKey, Enum, Text, DateTime
from sqlalchemy.orm import relationship
from app.core.database import Base
from app.models.base import TimestampMixin
from enum import Enum as PyEnum

class ComplianceStatus(str, PyEnum):
    UNDER_REVIEW = "under_review"
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    NEEDS_DOCUMENTATION = "needs_documentation"

class RiskLevel(str, PyEnum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class MitigationStatus(str, PyEnum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    BLOCKED = "blocked"

class DocumentType(str, PyEnum):
    POLICY = "policy"
    PROCEDURE = "procedure"
    EVIDENCE = "evidence"
    REPORT = "report"
    OTHER = "other"

class Document(Base, TimestampMixin):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(512), nullable=False)
    mime_type = Column(String(128))
    status = Column(String(50), default="pending")
    document_type = Column(Enum(DocumentType), nullable=False)
    compliance_status = Column(Enum(ComplianceStatus), nullable=False, default=ComplianceStatus.UNDER_REVIEW)
    risk_level = Column(Enum(RiskLevel), nullable=True)
    mitigation_status = Column(Enum(MitigationStatus), nullable=True)
    description = Column(Text, nullable=True)
    review_date = Column(DateTime, nullable=True)
    review_notes = Column(Text, nullable=True)

    # Define cascading relationships with proper passive_deletes
    user = relationship("User", back_populates="documents")
    cbcs_scans = relationship("CBCSScan", back_populates="document", cascade="all, delete-orphan", passive_deletes=True)
    reviews = relationship("ComplianceReview", back_populates="document", cascade="all, delete-orphan", passive_deletes=True)
    review_documents = relationship("ReviewDocument", back_populates="document", cascade="all, delete-orphan", passive_deletes=True)
    
    def __repr__(self):
        return f"<Document {self.id}: {self.filename}>"