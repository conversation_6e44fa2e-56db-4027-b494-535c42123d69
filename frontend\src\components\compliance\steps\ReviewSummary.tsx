import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  Chip,
} from '@mui/material';
import {
  Description,
  Assignment,
  Policy,
  AttachMoney,
  Event,
  Person,
} from '@mui/icons-material';

interface ReviewSummaryProps {
  data: {
    projectDetails: any;
    documents: any[];
    selectedPolicies: string[];
  };
}

const ReviewSummary: React.FC<ReviewSummaryProps> = ({ data }) => {
  const theme = useTheme();

  const formatDate = (date: Date | null) => {
    if (!date) return 'Not specified';
    return new Date(date).toLocaleDateString();
  };

  const formatCurrency = (amount: string | number) => {
    if (!amount) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(Number(amount));
  };

  return (
    <Box>
      <Typography
        variant="h6"
        sx={{ mb: 3, color: 'rgba(255, 255, 255, 0.9)' }}
      >
        Review Summary
      </Typography>

      <Grid container spacing={3}>
        {/* Project Details */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: 3,
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
            }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 2, color: 'rgba(255, 255, 255, 0.9)' }}
            >
              Project Details
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Assignment sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                      {data.projectDetails?.projectName || 'Untitled Project'}
                    </Typography>
                  }
                  secondary={
                    <Typography
                      variant="body2"
                      sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
                    >
                      Project Name
                    </Typography>
                  }
                />
              </ListItem>
              <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)' }} />
              <ListItem>
                <ListItemIcon>
                  <Person sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                      {data.projectDetails?.applicant || 'Not specified'}
                    </Typography>
                  }
                  secondary={
                    <Typography
                      variant="body2"
                      sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
                    >
                      Applicant
                    </Typography>
                  }
                />
              </ListItem>
              <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)' }} />
              <ListItem>
                <ListItemIcon>
                  <Event sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                        {formatDate(data.projectDetails?.startDate)} -{' '}
                        {formatDate(data.projectDetails?.endDate)}
                      </Typography>
                    </Box>
                  }
                  secondary={
                    <Typography
                      variant="body2"
                      sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
                    >
                      Project Period
                    </Typography>
                  }
                />
              </ListItem>
              <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)' }} />
              <ListItem>
                <ListItemIcon>
                  <AttachMoney sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                      {formatCurrency(data.projectDetails?.totalCost)}
                    </Typography>
                  }
                  secondary={
                    <Typography
                      variant="body2"
                      sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
                    >
                      Total Cost
                    </Typography>
                  }
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Documents */}
        <Grid item xs={12} md={6}>
          <Paper
            sx={{
              p: 3,
              height: '100%',
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
            }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 2, color: 'rgba(255, 255, 255, 0.9)' }}
            >
              Uploaded Documents
            </Typography>
            <List>
              {data.documents.map((doc: any, index: number) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Description sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                        {doc.name}
                      </Typography>
                    }
                  />
                  <Chip
                    label={doc.status}
                    size="small"
                    color={doc.status === 'complete' ? 'success' : 'default'}
                    sx={{
                      backgroundColor:
                        doc.status === 'complete'
                          ? `${theme.palette.success.main}20`
                          : 'rgba(255, 255, 255, 0.1)',
                      color:
                        doc.status === 'complete'
                          ? theme.palette.success.main
                          : 'rgba(255, 255, 255, 0.7)',
                    }}
                  />
                </ListItem>
              ))}
              {data.documents.length === 0 && (
                <ListItem>
                  <ListItemText
                    primary={
                      <Typography sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                        No documents uploaded
                      </Typography>
                    }
                  />
                </ListItem>
              )}
            </List>
          </Paper>
        </Grid>

        {/* Selected Policies */}
        <Grid item xs={12} md={6}>
          <Paper
            sx={{
              p: 3,
              height: '100%',
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
            }}
          >
            <Typography
              variant="h6"
              sx={{ mb: 2, color: 'rgba(255, 255, 255, 0.9)' }}
            >
              Selected Policies
            </Typography>
            <List>
              {data.selectedPolicies.map((policy: string, index: number) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Policy sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                        {policy}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
              {data.selectedPolicies.length === 0 && (
                <ListItem>
                  <ListItemText
                    primary={
                      <Typography sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                        No policies selected
                      </Typography>
                    }
                  />
                </ListItem>
              )}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReviewSummary;
