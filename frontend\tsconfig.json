{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@common/*": ["src/components/common/*"], "@auth/*": ["src/components/auth/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}