"""
Test Configuration Module

Test-specific configuration settings that override base settings.
Focuses on isolation and reproducibility of tests.
"""

from .base import *
import tempfile

# Use temporary directory for test storage
TEMP_DIR = Path(tempfile.mkdtemp(prefix='compliancemax_test_'))

# Override storage paths for testing
STORAGE_DIR = TEMP_DIR / 'storage'
LOGS_DIR = TEMP_DIR / 'logs'

# Create test directories
STORAGE_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Test document storage configuration
DOCUMENT_STORAGE = {
    'policies': STORAGE_DIR / 'policies',
    'requirements': STORAGE_DIR / 'requirements',
    'uploads': STORAGE_DIR / 'uploads',
    'results': STORAGE_DIR / 'results'
}

# Create test storage directories
for path in DOCUMENT_STORAGE.values():
    path.mkdir(exist_ok=True)

# Use in-memory SQLite for tests
DATABASE = {
    'engine': 'sqlite',
    'name': ':memory:',
    'path': None
}

# Disable caching in tests
CACHE = {
    'enabled': False,
    'backend': 'memory',
    'ttl': 0,
    'max_size': 0
}

# Disable rate limiting in tests
API['rate_limit']['enabled'] = False

# Test-specific logging configuration
LOGGING['handlers']['file']['filename'] = LOGS_DIR / 'test.log'
LOGGING['loggers']['']['level'] = 'DEBUG'

# Lower thresholds for testing
POLICY_MATCHER['confidence_threshold'] = 0.3
POLICY_MATCHER['min_similarity_score'] = 0.2
POLICY_MATCHER['cache_enabled'] = False

COMPLIANCE_CHECKER['confidence_threshold'] = 0.3
COMPLIANCE_CHECKER['required_match_percentage'] = 0.5
COMPLIANCE_CHECKER['store_results'] = True

# Disable strict mode for testing
REQUIREMENT_VALIDATOR['strict_mode'] = False
REQUIREMENT_VALIDATOR['validate_references'] = False

# Test security settings
SECURITY.update({
    'debug': True,
    'ssl_verify': False,
    'secret_key': 'test-key-not-for-production',
    'allowed_hosts': ['*']
})

# Enable all features for testing
FEATURES = {
    'enable_ai_matching': True,
    'enable_batch_processing': True,
    'enable_advanced_search': True,
    'enable_export': True,
    'enable_notifications': True
}

# Test document processor settings
DOCUMENT_PROCESSOR.update({
    'max_file_size': 5 * 1024 * 1024,  # 5MB for tests
    'extract_metadata': True,
    'store_original': True,
    'scan_uploads': False,  # Disable malware scanning in tests
    'backup_enabled': False
})

def cleanup():
    """Clean up temporary test directories."""
    import shutil
    shutil.rmtree(TEMP_DIR, ignore_errors=True) 