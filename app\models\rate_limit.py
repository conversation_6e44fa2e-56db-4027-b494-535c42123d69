from sqlalchemy import Column, String, Integer, DateTime
from sqlalchemy.sql import func
from app.core.database import Base

class RateLimit(Base):
    """Rate limit model for tracking API request limits"""
    __tablename__ = "rate_limits"

    id = Column(String(50), primary_key=True, index=True)
    requests = Column(Integer, default=0, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    def __repr__(self):
        return f"<RateLimit(id={self.id}, requests={self.requests}, timestamp={self.timestamp})>"
