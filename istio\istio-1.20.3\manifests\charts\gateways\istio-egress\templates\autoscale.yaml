{{ $gateway := index .Values "gateways" "istio-egressgateway" }}
{{- if and $gateway.autoscaleEnabled $gateway.autoscaleMin $gateway.autoscaleMax }}
{{- if not .Values.global.autoscalingv2API }}
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $gateway.name }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ $gateway.labels | toYaml | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "EgressGateways"
spec:
  maxReplicas: {{ $gateway.autoscaleMax }}
  minReplicas: {{ $gateway.autoscaleMin }}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $gateway.name }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      targetAverageUtilization: {{ $gateway.cpu.targetAverageUtilization }}
---
{{- else }}
{{- if (semverCompare ">=1.23-0" .Capabilities.KubeVersion.GitVersion)}}
apiVersion: autoscaling/v2
{{- else }}
apiVersion: autoscaling/v2beta2
{{- end }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $gateway.name }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ $gateway.labels | toYaml | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "EgressGateways"
spec:
  maxReplicas: {{ $gateway.autoscaleMax }}
  minReplicas: {{ $gateway.autoscaleMin }}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $gateway.name }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ $gateway.cpu.targetAverageUtilization }}
---
{{- end }}
{{- end }}
