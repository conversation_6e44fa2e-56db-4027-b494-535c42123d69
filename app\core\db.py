"""
Database configuration and optimization for ComplianceMax.
Implements connection pooling, query optimization, and monitoring.
"""

from typing import Generator
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.engine import Engine
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
import time
from app.core.config import settings
from app.core.logging import get_logger, log_performance_metric

logger = get_logger(__name__)

# Create database engine with optimized pooling
engine = create_engine(
    settings.SQLALCHEMY_DATABASE_URI,
    poolclass=QueuePool,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_timeout=30,  # seconds
    pool_recycle=1800,  # 30 minutes
    echo=settings.DEBUG
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@contextmanager
def get_db() -> Generator[Session, None, None]:
    """Get a database session with performance monitoring."""
    session = SessionLocal()
    start_time = time.time()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database error: {str(e)}", exc_info=True)
        raise
    finally:
        duration = time.time() - start_time
        log_performance_metric("database_session", duration)
        session.close()

# Query performance monitoring
@event.listens_for(Engine, "before_cursor_execute")
def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    conn.info.setdefault('query_start_time', []).append(time.time())

@event.listens_for(Engine, "after_cursor_execute")
def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total_time = time.time() - conn.info['query_start_time'].pop()
    if total_time > 0.5:  # Log slow queries (>500ms)
        logger.warning(
            f"Slow query detected: {total_time:.2f} seconds\n"
            f"Query: {statement}\n"
            f"Parameters: {parameters}"
        )
    log_performance_metric(
        "database_query",
        total_time,
        {
            "query": statement,
            "parameters": str(parameters)
        }
    )

def init_db() -> None:
    """Initialize database with optimized settings."""
    from app.models import Base  # Import all models
    
    try:
        # Create tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")
        
        # Analyze tables for query optimization
        with engine.connect() as conn:
            conn.execute("ANALYZE VERBOSE")
            logger.info("Database statistics updated")
            
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}", exc_info=True)
        raise

def check_db_connection() -> bool:
    """Check database connection and report status."""
    try:
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {str(e)}", exc_info=True)
        return False

def get_db_stats() -> dict:
    """Get database statistics and performance metrics."""
    try:
        with engine.connect() as conn:
            # Get connection pool stats
            pool_stats = {
                "pool_size": engine.pool.size(),
                "checkedin": engine.pool.checkedin(),
                "checkedout": engine.pool.checkedout(),
                "overflow": engine.pool.overflow()
            }
            
            # Get database size and other stats
            db_stats = conn.execute("""
                SELECT
                    pg_database_size(current_database()) as db_size,
                    (SELECT count(*) FROM pg_stat_activity) as active_connections,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_queries
            """).fetchone()
            
            return {
                "pool_stats": pool_stats,
                "db_size": db_stats[0],
                "active_connections": db_stats[1],
                "active_queries": db_stats[2]
            }
    except Exception as e:
        logger.error(f"Failed to get database stats: {str(e)}", exc_info=True)
        return {}

def optimize_db() -> None:
    """Perform database optimization tasks."""
    try:
        with engine.connect() as conn:
            # Analyze tables
            conn.execute("ANALYZE VERBOSE")
            
            # Update statistics
            conn.execute("VACUUM ANALYZE")
            
            # Reindex tables
            conn.execute("REINDEX DATABASE %s", (settings.POSTGRES_DB,))
            
            logger.info("Database optimization completed successfully")
    except Exception as e:
        logger.error(f"Database optimization failed: {str(e)}", exc_info=True)
        raise

# Database health check
def health_check() -> dict:
    """Perform a comprehensive database health check."""
    health_status = {
        "status": "healthy",
        "details": {}
    }
    
    try:
        # Check connection
        if not check_db_connection():
            health_status["status"] = "unhealthy"
            health_status["details"]["connection"] = "failed"
            return health_status
        
        # Get database stats
        stats = get_db_stats()
        if not stats:
            health_status["status"] = "degraded"
            health_status["details"]["stats"] = "unavailable"
        else:
            health_status["details"]["stats"] = stats
        
        # Check pool status
        pool_stats = stats.get("pool_stats", {})
        if pool_stats.get("checkedout", 0) > pool_stats.get("pool_size", 0) * 0.9:
            health_status["status"] = "warning"
            health_status["details"]["pool"] = "near capacity"
        
        return health_status
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "details": {"error": str(e)}
        } 