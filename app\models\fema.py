"""FEMA application and compliance models."""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import String, DateTime, ForeignKey, Text, Boolean, Integer, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base, TimestampMixin
import enum

class ApplicationStatus(str, enum.Enum):
    PENDING = "pending"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_INFO = "needs_info"

class ComplianceStatus(str, enum.Enum):
    UNDER_REVIEW = "under_review"
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    NEEDS_DOCUMENTATION = "needs_documentation"
    REQUIRES_REVIEW = "requires_review"

class FEMAApplication(Base, TimestampMixin):
    """Model for tracking FEMA Public Assistance applications."""
    __tablename__ = "fema_applications"

    id: Mapped[str] = mapped_column(String, primary_key=True)
    disaster_number: Mapped[str] = mapped_column(String, nullable=False)
    applicant_id: Mapped[str] = mapped_column(String, nullable=False)
    project_title: Mapped[str] = mapped_column(String, nullable=False)
    status: Mapped[ApplicationStatus] = mapped_column(
        SQLEnum(ApplicationStatus), 
        default=ApplicationStatus.PENDING
    )
    compliance_status: Mapped[ComplianceStatus] = mapped_column(
        SQLEnum(ComplianceStatus), 
        default=ComplianceStatus.UNDER_REVIEW
    )
    submission_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"))

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="applications")
    documents: Mapped[List["ComplianceDocument"]] = relationship("ComplianceDocument", back_populates="application")
    reviews: Mapped[List["ComplianceReviewV2"]] = relationship("ComplianceReviewV2", back_populates="application")

    def __repr__(self) -> str:
        return f"<FEMAApplication {self.id} ({self.project_title})>"

class ComplianceDocument(Base, TimestampMixin):
    """Model for tracking compliance-related documents."""
    __tablename__ = "compliance_documents"

    id: Mapped[str] = mapped_column(String, primary_key=True)
    application_id: Mapped[str] = mapped_column(String, ForeignKey("fema_applications.id"))
    document_type: Mapped[str] = mapped_column(String, nullable=False)
    file_path: Mapped[str] = mapped_column(String, nullable=False)
    original_filename: Mapped[str] = mapped_column(String, nullable=False)
    file_size: Mapped[Optional[int]] = mapped_column(Integer)  # Size in bytes
    mime_type: Mapped[Optional[str]] = mapped_column(String)
    upload_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    last_accessed: Mapped[Optional[datetime]] = mapped_column(DateTime)
    compliance_status: Mapped[ComplianceStatus] = mapped_column(
        SQLEnum(ComplianceStatus), 
        default=ComplianceStatus.UNDER_REVIEW
    )
    review_notes: Mapped[Optional[str]] = mapped_column(Text)
    version: Mapped[int] = mapped_column(Integer, default=1)
    is_archived: Mapped[bool] = mapped_column(Boolean, default=False)
    archive_path: Mapped[Optional[str]] = mapped_column(String)
    archive_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    checksum: Mapped[Optional[str]] = mapped_column(String)  # For file integrity verification

    # Relationships
    application: Mapped["FEMAApplication"] = relationship("FEMAApplication", back_populates="documents")
    reviews: Mapped[List["ComplianceReviewV2"]] = relationship("ComplianceReviewV2", back_populates="document")

    def __repr__(self) -> str:
        return f"<ComplianceDocument {self.id} ({self.document_type})>" 