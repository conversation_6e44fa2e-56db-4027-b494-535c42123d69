"""
Document processing and management package.

This package handles all document-related functionality including:
- Document storage and retrieval
- Document processing and analysis
- OCR and text extraction
- Version control
- Compliance checking
"""

from .services import DocumentService, DocumentProcessor
from .storage import StorageService

# Import models last to avoid circular imports
from .models import Document, DocumentVersion

__all__ = [
    'Document',
    'DocumentVersion',
    'DocumentService',
    'DocumentProcessor',
    'StorageService',
] 