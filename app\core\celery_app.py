from celery import Celery
from celery.signals import task_failure, after_setup_logger
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

celery_app = Celery(
    "compliancemax",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=[
        "app.services.ocr_service",
        "app.services.policy_validator",
        "app.services.compliance_checker"
    ]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=3600,  # 1 hour max task runtime
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=200,
    broker_connection_retry_on_startup=True,
    task_routes={
        'ocr.*': {'queue': 'ocr'},
        'policy.*': {'queue': 'policy'},
        'compliance.*': {'queue': 'compliance'}
    },
    task_queues={
        'ocr': {'routing_key': 'ocr.#'},
        'policy': {'routing_key': 'policy.#'},
        'compliance': {'routing_key': 'compliance.#'}
    }
)

# Error handling
@task_failure.connect
def handle_task_failure(task_id, exception, args, kwargs, traceback, einfo, **_):
    logger.error(
        f"Task {task_id} failed: {str(exception)}",
        exc_info=(type(exception), exception, traceback)
    )

# Setup logging
@after_setup_logger.connect
def setup_celery_logging(logger, *args, **kwargs):
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    # Add file handler
    fh = logging.FileHandler('celery.log')
    fh.setLevel(logging.INFO)
    fh.setFormatter(formatter)
    logger.setLevel(logging.INFO)
    logger.addHandler(fh)
    logger.info("Celery logging configured with INFO level to celery.log")

@celery_app.task
def check_celery_health():
    """Periodic health check task."""
    try:
        # Verify Redis connection
        celery_app.backend.client.ping()
        logger.info("Celery health check: OK")
        return True
    except Exception as e:
        logger.error(f"Celery health check failed: {str(e)}")
        return False

# Schedule periodic tasks
@celery_app.on_after_finalize.connect
def setup_periodic_tasks(sender, **kwargs):
    logger.info("Configuring periodic tasks...")
    sender.add_periodic_task(
        300.0,  # 5 minutes
        check_celery_health.s(),
        name='check-celery-health'
    )