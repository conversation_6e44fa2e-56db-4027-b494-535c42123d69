import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from beanie import init_beanie
from app.db.user import User
from app.core.config import settings

async def test_db_connection():
    """Test MongoDB connection and Beanie initialization."""
    try:
        print("Connecting to MongoDB...")
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        
        # Test basic connection
        await client.admin.command('ping')
        print("MongoDB connection successful!")
        
        # Test Beanie initialization
        print("Initializing Beanie...")
        await init_beanie(
            database=client[settings.MONGODB_DB_NAME],
            document_models=[User]
        )
        print("Beanie initialization successful!")
        
        # Test User model operations
        print("\nTesting User model operations...")
        user_count = await User.count()
        print(f"Found {user_count} users in the database")
        
        if user_count > 0:
            first_user = await User.find_one({})
            if first_user:
                print("\nSample user data (excluding sensitive fields):")
                user_dict = first_user.model_dump(exclude={"hashed_password"})
                for key, value in user_dict.items():
                    print(f"{key}: {value}")
        
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(test_db_connection())
