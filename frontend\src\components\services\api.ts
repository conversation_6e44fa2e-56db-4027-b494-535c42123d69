import axios from 'axios';
import { useState } from 'react';

interface Project {
  id: string;
  name: string;
  status: string;
  start_date: string;
  end_date?: string;
  description: string;
}

const API_URL = 'http://localhost:8000';

export const useApi = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const getProjects = async (): Promise<Project[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await axios.get(`${API_URL}/api/v1/projects`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching projects:', error);
      setError(error.response?.data?.detail || 'Failed to fetch projects');
      return [];
    } finally {
      setIsLoading(false);
    }
  };
  
  const getProject = async (id: string): Promise<Project | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await axios.get(`${API_URL}/api/v1/projects/${id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });
      return response.data;
    } catch (error: any) {
      console.error(`Error fetching project ${id}:`, error);
      setError(error.response?.data?.detail || 'Failed to fetch project');
      return null;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Basic login function
  const login = async (username: string, password: string) => {
    try {
      const response = await axios.post(`${API_URL}/api/v1/auth/login`, {
        username,
        password
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Login failed');
    }
  };
  
  return {
    isLoading,
    error,
    getProjects,
    getProject,
    login
  };
}; 