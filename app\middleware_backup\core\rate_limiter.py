import time
from typing import Optional
from fastapi import HTTPException, status
from redis.asyncio import Redis
from app.core.config import settings

class RateLimiter:
    def __init__(self, times: int, seconds: int):
        self.times = times
        self.seconds = seconds
        self.redis: Optional[Redis] = None

    async def __call__(self, request: Request, redis: Redis = Depends(get_redis)):
        self.redis = redis
        client_ip = request.client.host
        key = f"rate_limit:{client_ip}"
        try:
            current = await self.redis.get(key)
            if current is None:
                await self.redis.setex(key, self.seconds, 1)
                return
            count = int(current)
            if count >= self.times:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Rate limit exceeded",
                    headers={"Retry-After": str(self.seconds)}
                )
            await self.redis.incr(key)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Rate limiting error: {str(e)}"
            )