"""
Policy Matcher Module

Provides unified policy matching functionality with improved accuracy and performance.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple
from pathlib import Path
import json
from dataclasses import dataclass
from datetime import datetime
import re
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

@dataclass
class PolicyMatch:
    """Represents a match between a requirement and a policy."""
    policy_id: str
    requirement_id: str
    confidence: float
    matched_text: str
    context: str
    match_type: str
    timestamp: datetime

class PolicyMatcher:
    """Enhanced policy matching system with multiple matching strategies."""
    
    def __init__(self, storage_path: Path):
        """
        Initialize the policy matcher.
        
        Args:
            storage_path: Path to the document storage directory
        """
        self.storage_path = Path(storage_path)
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        self.policies = {}
        self.requirements = {}
        self.vectorizer = TfidfVectorizer(
            max_features=10000,
            stop_words='english',
            ngram_range=(1, 3)
        )
        
        # Cached vectors for performance
        self._policy_vectors = None
        self._requirement_vectors = None

    def _setup_logging(self):
        """Configure logging for the policy matcher."""
        handler = logging.FileHandler('policy_matcher.log')
        handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def load_policies(self, policies_dir: Path):
        """Load policies from the specified directory."""
        policies_dir = Path(policies_dir)
        if not policies_dir.exists():
            raise FileNotFoundError(f"Policies directory not found: {policies_dir}")
            
        self.policies = {}
        for policy_file in policies_dir.glob('*.json'):
            try:
                with open(policy_file, 'r', encoding='utf-8') as f:
                    policy_data = json.load(f)
                    self.policies[policy_file.stem] = policy_data
            except Exception as e:
                self.logger.error(f"Error loading policy file {policy_file}: {str(e)}")
                
        self._policy_vectors = None  # Reset cached vectors
        self.logger.info(f"Loaded {len(self.policies)} policies")

    def load_requirements(self, requirements_file: Path):
        """Load requirements from the specified file."""
        requirements_file = Path(requirements_file)
        if not requirements_file.exists():
            raise FileNotFoundError(f"Requirements file not found: {requirements_file}")
            
        try:
            with open(requirements_file, 'r', encoding='utf-8') as f:
                self.requirements = json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading requirements file: {str(e)}")
            raise
            
        self._requirement_vectors = None  # Reset cached vectors
        self.logger.info(f"Loaded {len(self.requirements)} requirements")

    def match_requirements(self) -> List[PolicyMatch]:
        """
        Match requirements to policies using multiple strategies.
        
        Returns:
            List of PolicyMatch objects representing matches found
        """
        matches = []
        
        # Perform semantic matching
        semantic_matches = self._semantic_matching()
        matches.extend(semantic_matches)
        
        # Perform regex pattern matching
        pattern_matches = self._pattern_matching()
        matches.extend(pattern_matches)
        
        # Perform reference matching
        reference_matches = self._reference_matching()
        matches.extend(reference_matches)
        
        # Sort matches by confidence
        matches.sort(key=lambda x: x.confidence, reverse=True)
        
        return matches

    def _semantic_matching(self) -> List[PolicyMatch]:
        """Perform semantic matching using TF-IDF and cosine similarity."""
        matches = []
        
        # Prepare document vectors if not cached
        if self._policy_vectors is None or self._requirement_vectors is None:
            policy_texts = [p['content'] for p in self.policies.values()]
            requirement_texts = [r['text'] for r in self.requirements.values()]
            
            # Fit vectorizer on all texts
            all_texts = policy_texts + requirement_texts
            self.vectorizer.fit(all_texts)
            
            # Transform documents
            self._policy_vectors = self.vectorizer.transform(policy_texts)
            self._requirement_vectors = self.vectorizer.transform(requirement_texts)
        
        # Calculate similarities
        similarities = cosine_similarity(self._requirement_vectors, self._policy_vectors)
        
        # Process matches
        for req_idx, req_id in enumerate(self.requirements.keys()):
            for pol_idx, pol_id in enumerate(self.policies.keys()):
                confidence = similarities[req_idx, pol_idx]
                
                if confidence >= 0.5:  # Minimum confidence threshold
                    matches.append(PolicyMatch(
                        policy_id=pol_id,
                        requirement_id=req_id,
                        confidence=float(confidence),
                        matched_text=self._extract_matching_context(
                            self.requirements[req_id]['text'],
                            self.policies[pol_id]['content']
                        ),
                        context=self._get_context(self.policies[pol_id]['content'], 
                                                self.requirements[req_id]['text']),
                        match_type='semantic',
                        timestamp=datetime.now()
                    ))
        
        return matches

    def _pattern_matching(self) -> List[PolicyMatch]:
        """Perform regex pattern matching for specific requirement patterns."""
        matches = []
        
        for req_id, req in self.requirements.items():
            # Extract potential patterns from requirement
            patterns = self._extract_patterns(req['text'])
            
            for pol_id, pol in self.policies.items():
                for pattern in patterns:
                    if re.search(pattern, pol['content'], re.IGNORECASE):
                        match = re.search(pattern, pol['content'], re.IGNORECASE)
                        matches.append(PolicyMatch(
                            policy_id=pol_id,
                            requirement_id=req_id,
                            confidence=0.8,  # High confidence for exact pattern matches
                            matched_text=match.group(0),
                            context=self._get_context(pol['content'], match.group(0)),
                            match_type='pattern',
                            timestamp=datetime.now()
                        ))
        
        return matches

    def _reference_matching(self) -> List[PolicyMatch]:
        """Match requirements based on explicit references in policies."""
        matches = []
        
        for pol_id, pol in self.policies.items():
            # Look for reference patterns (e.g., "per requirement 123", "see requirement ABC")
            reference_pattern = r'(?:per|see|ref(?:er(?:ence)?)?\.?|requirements?)\s+([A-Z0-9-]+)'
            
            for match in re.finditer(reference_pattern, pol['content'], re.IGNORECASE):
                ref_id = match.group(1)
                
                if ref_id in self.requirements:
                    matches.append(PolicyMatch(
                        policy_id=pol_id,
                        requirement_id=ref_id,
                        confidence=0.9,  # Very high confidence for explicit references
                        matched_text=match.group(0),
                        context=self._get_context(pol['content'], match.group(0)),
                        match_type='reference',
                        timestamp=datetime.now()
                    ))
        
        return matches

    def _extract_patterns(self, text: str) -> List[str]:
        """Extract regex patterns from requirement text."""
        patterns = []
        
        # Extract quoted phrases
        quoted = re.findall(r'"([^"]+)"', text)
        patterns.extend([re.escape(q) for q in quoted])
        
        # Extract key phrases (e.g., "must", "shall", "required to")
        key_phrases = re.findall(r'\b(must|shall|required to|needs to)\b\s+[^.;]+', text, 
                               re.IGNORECASE)
        patterns.extend([re.escape(p) for p in key_phrases])
        
        return patterns

    def _get_context(self, text: str, match_text: str, context_chars: int = 200) -> str:
        """Get surrounding context for a matched text."""
        try:
            start_pos = text.lower().index(match_text.lower())
            start = max(0, start_pos - context_chars)
            end = min(len(text), start_pos + len(match_text) + context_chars)
            
            context = text[start:end]
            if start > 0:
                context = f"...{context}"
            if end < len(text):
                context = f"{context}..."
                
            return context
        except ValueError:
            return match_text

    def _extract_matching_context(self, req_text: str, pol_text: str) -> str:
        """Extract the most relevant matching context between requirement and policy."""
        # Split into sentences
        req_sentences = re.split(r'[.!?]+', req_text)
        pol_sentences = re.split(r'[.!?]+', pol_text)
        
        # Vectorize sentences
        all_sentences = req_sentences + pol_sentences
        sentence_vectors = self.vectorizer.fit_transform(all_sentences)
        
        # Find best matching policy sentence for each requirement sentence
        best_matches = []
        req_vectors = sentence_vectors[:len(req_sentences)]
        pol_vectors = sentence_vectors[len(req_sentences):]
        
        similarities = cosine_similarity(req_vectors, pol_vectors)
        
        for i, row in enumerate(similarities):
            best_idx = np.argmax(row)
            if row[best_idx] >= 0.5:  # Minimum similarity threshold
                best_matches.append(pol_sentences[best_idx].strip())
        
        return ' '.join(best_matches) if best_matches else ""

    def export_matches(self, output_file: Path, matches: List[PolicyMatch]):
        """Export matches to a JSON file."""
        output_file = Path(output_file)
        
        # Convert matches to dictionary format
        match_data = [
            {
                'policy_id': m.policy_id,
                'requirement_id': m.requirement_id,
                'confidence': m.confidence,
                'matched_text': m.matched_text,
                'context': m.context,
                'match_type': m.match_type,
                'timestamp': m.timestamp.isoformat()
            }
            for m in matches
        ]
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(match_data, f, indent=2)
            self.logger.info(f"Exported {len(matches)} matches to {output_file}")
        except Exception as e:
            self.logger.error(f"Error exporting matches: {str(e)}")
            raise 