import os
from pathlib import Path
from typing import List, Dict
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('document_storage_check.log'),
        logging.StreamHandler()
    ]
)

class DocumentStorageChecker:
    def __init__(self, base_path: str = "storage"):
        self.base_path = Path(base_path)
        self.stats = {
            "total_files": 0,
            "total_size": 0,
            "missing_files": 0,
            "corrupt_files": 0,
            "by_extension": {},
            "by_date": {}
        }

    def check_storage_directory(self) -> bool:
        """Verify storage directory exists and is accessible."""
        try:
            if not self.base_path.exists():
                logging.warning(f"Storage directory {self.base_path} does not exist")
                self.base_path.mkdir(parents=True, exist_ok=True)
                logging.info(f"Created storage directory at {self.base_path}")
            
            # Test write permission
            test_file = self.base_path / ".test_write"
            test_file.touch()
            test_file.unlink()
            
            return True
        except Exception as e:
            logging.error(f"Storage directory check failed: {str(e)}")
            return False

    def scan_documents(self) -> Dict:
        """Scan all documents in storage and collect statistics."""
        try:
            for root, _, files in os.walk(self.base_path):
                for file in files:
                    if file.startswith('.'):  # Skip hidden files
                        continue
                        
                    file_path = Path(root) / file
                    self.stats["total_files"] += 1
                    
                    # Get file size
                    try:
                        size = file_path.stat().st_size
                        self.stats["total_size"] += size
                    except Exception as e:
                        logging.error(f"Error getting size for {file_path}: {str(e)}")
                        self.stats["corrupt_files"] += 1
                        continue
                    
                    # Track by extension
                    ext = file_path.suffix.lower()
                    self.stats["by_extension"][ext] = self.stats["by_extension"].get(ext, 0) + 1
                    
                    # Track by date
                    try:
                        date = datetime.fromtimestamp(file_path.stat().st_mtime)
                        date_str = date.strftime("%Y-%m-%d")
                        self.stats["by_date"][date_str] = self.stats["by_date"].get(date_str, 0) + 1
                    except Exception as e:
                        logging.error(f"Error getting date for {file_path}: {str(e)}")
            
            return self.stats
        except Exception as e:
            logging.error(f"Error scanning documents: {str(e)}")
            return self.stats

    def generate_report(self) -> str:
        """Generate a human-readable report of the storage check."""
        report = [
            "Document Storage Check Report",
            "===========================",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Storage Path: {self.base_path}",
            "",
            "Statistics:",
            f"Total Files: {self.stats['total_files']}",
            f"Total Size: {self.stats['total_size'] / (1024*1024):.2f} MB",
            f"Corrupt Files: {self.stats['corrupt_files']}",
            "",
            "Files by Extension:",
        ]
        
        for ext, count in sorted(self.stats["by_extension"].items()):
            report.append(f"  {ext}: {count}")
            
        report.extend([
            "",
            "Files by Date:",
        ])
        
        for date, count in sorted(self.stats["by_date"].items()):
            report.append(f"  {date}: {count}")
            
        return "\n".join(report)

def main():
    checker = DocumentStorageChecker()
    
    # Check storage directory
    if not checker.check_storage_directory():
        logging.error("Storage directory check failed. Exiting.")
        return
    
    # Scan documents
    stats = checker.scan_documents()
    
    # Generate and save report
    report = checker.generate_report()
    with open("document_storage_report.txt", "w") as f:
        f.write(report)
    
    logging.info("Document storage check completed successfully")
    logging.info(f"Report saved to document_storage_report.txt")

if __name__ == "__main__":
    main() 