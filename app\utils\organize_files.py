import os
import shutil
from datetime import datetime
from typing import Dict, List, Optional
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class FileOrganizer:
    """Handles file organization and management tasks."""
    
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)
        self.organized_dir = self.base_dir / "organized"
        self.archive_dir = self.base_dir / "archive"
        self.temp_dir = self.base_dir / "temp"
        
        # Create necessary directories
        for directory in [self.organized_dir, self.archive_dir, self.temp_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def organize_files(self, source_dir: str, file_patterns: Dict[str, List[str]]) -> Dict[str, int]:
        """
        Organize files from source directory based on patterns.
        
        Args:
            source_dir: Source directory containing files to organize
            file_patterns: Dictionary mapping categories to file patterns
            
        Returns:
            Dictionary with count of files moved per category
        """
        source_path = Path(source_dir)
        if not source_path.exists():
            raise FileNotFoundError(f"Source directory not found: {source_dir}")
            
        stats = {category: 0 for category in file_patterns}
        
        try:
            # Process all files in source directory
            for file_path in source_path.rglob("*"):
                if file_path.is_file():
                    # Determine category based on patterns
                    category = self._get_file_category(file_path, file_patterns)
                    if category:
                        # Create category directory if it doesn't exist
                        category_dir = self.organized_dir / category
                        category_dir.mkdir(exist_ok=True)
                        
                        # Move file to appropriate directory
                        new_path = category_dir / file_path.name
                        if new_path.exists():
                            # Handle duplicates by adding timestamp
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            new_path = category_dir / f"{file_path.stem}_{timestamp}{file_path.suffix}"
                        
                        shutil.move(str(file_path), str(new_path))
                        stats[category] += 1
                        logger.info(f"Moved {file_path.name} to {category}")
            
            return stats
            
        except Exception as e:
            logger.error(f"Error organizing files: {str(e)}")
            raise
    
    def _get_file_category(self, file_path: Path, patterns: Dict[str, List[str]]) -> Optional[str]:
        """Determine file category based on patterns."""
        filename = file_path.name.lower()
        
        for category, category_patterns in patterns.items():
            for pattern in category_patterns:
                if pattern.lower() in filename:
                    return category
        
        return None
    
    def archive_old_files(self, days_threshold: int = 30) -> int:
        """
        Move files older than threshold to archive directory.
        
        Args:
            days_threshold: Number of days before files are considered old
            
        Returns:
            Number of files archived
        """
        now = datetime.now()
        archived_count = 0
        
        try:
            for category_dir in self.organized_dir.iterdir():
                if category_dir.is_dir():
                    archive_category_dir = self.archive_dir / category_dir.name
                    archive_category_dir.mkdir(exist_ok=True)
                    
                    for file_path in category_dir.iterdir():
                        if file_path.is_file():
                            # Check file age
                            file_age = now - datetime.fromtimestamp(file_path.stat().st_mtime)
                            if file_age.days > days_threshold:
                                # Move to archive
                                archive_path = archive_category_dir / file_path.name
                                if archive_path.exists():
                                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                    archive_path = archive_category_dir / f"{file_path.stem}_{timestamp}{file_path.suffix}"
                                
                                shutil.move(str(file_path), str(archive_path))
                                archived_count += 1
                                logger.info(f"Archived {file_path.name}")
            
            return archived_count
            
        except Exception as e:
            logger.error(f"Error archiving files: {str(e)}")
            raise
    
    def cleanup_temp_files(self) -> int:
        """
        Remove temporary files.
        
        Returns:
            Number of files cleaned up
        """
        cleaned_count = 0
        
        try:
            for file_path in self.temp_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()
                    cleaned_count += 1
                    logger.info(f"Removed temporary file: {file_path.name}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning up temporary files: {str(e)}")
            raise

def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Organize files into categories")
    parser.add_argument("source_dir", help="Source directory containing files to organize")
    parser.add_argument("--base-dir", default=".", help="Base directory for organized files")
    parser.add_argument("--archive-days", type=int, default=30, help="Days threshold for archiving")
    
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Define file patterns
    file_patterns = {
        "policies": ["policy", "procedure", "guideline"],
        "reports": ["report", "analysis", "summary"],
        "forms": ["form", "template", "checklist"],
        "correspondence": ["letter", "memo", "email"],
        "other": []
    }
    
    try:
        organizer = FileOrganizer(args.base_dir)
        
        # Organize files
        stats = organizer.organize_files(args.source_dir, file_patterns)
        logger.info("File organization complete:")
        for category, count in stats.items():
            logger.info(f"  {category}: {count} files")
        
        # Archive old files
        archived = organizer.archive_old_files(args.archive_days)
        logger.info(f"Archived {archived} old files")
        
        # Cleanup temp files
        cleaned = organizer.cleanup_temp_files()
        logger.info(f"Cleaned up {cleaned} temporary files")
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 