"""
Rate limiting middleware for ComplianceMax.
Implements flexible rate limiting with Redis support.
"""

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON>NResponse
import time
import asyncio
from typing import Dict, Optional, Tuple
import redis.asyncio as redis
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

class RateLimiter:
    """Base rate limiter interface."""
    
    async def check_rate_limit(
        self,
        key: str,
        max_requests: int,
        time_window: int
    ) -> Tuple[bool, Optional[int]]:
        """
        Check if request is within rate limit.
        
        Args:
            key: Unique identifier for the rate limit
            max_requests: Maximum number of requests allowed
            time_window: Time window in seconds
            
        Returns:
            Tuple of (is_allowed, retry_after)
        """
        raise NotImplementedError

class RedisRateLimiter(RateLimiter):
    """Redis-based rate limiter implementation."""
    
    def __init__(self):
        """Initialize Redis connection."""
        self.redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD,
            decode_responses=True
        )
    
    async def check_rate_limit(
        self,
        key: str,
        max_requests: int,
        time_window: int
    ) -> Tuple[bool, Optional[int]]:
        """Check rate limit using Redis."""
        try:
            # Get current count
            current = await self.redis.get(f"ratelimit:{key}")
            
            if current is None:
                # First request
                pipeline = self.redis.pipeline()
                pipeline.set(f"ratelimit:{key}", 1)
                pipeline.expire(f"ratelimit:{key}", time_window)
                await pipeline.execute()
                return True, None
            
            current = int(current)
            if current >= max_requests:
                # Rate limit exceeded
                ttl = await self.redis.ttl(f"ratelimit:{key}")
                return False, ttl
            
            # Increment counter
            await self.redis.incr(f"ratelimit:{key}")
            return True, None
            
        except Exception as e:
            logger.error(f"Redis rate limiting error: {str(e)}", exc_info=True)
            # Fail open if Redis is unavailable
            return True, None

class InMemoryRateLimiter(RateLimiter):
    """In-memory rate limiter implementation."""
    
    def __init__(self):
        """Initialize in-memory storage."""
        self.requests: Dict[str, list] = {}
    
    async def check_rate_limit(
        self,
        key: str,
        max_requests: int,
        time_window: int
    ) -> Tuple[bool, Optional[int]]:
        """Check rate limit using in-memory storage."""
        now = time.time()
        
        # Clean up old requests
        if key in self.requests:
            self.requests[key] = [
                ts for ts in self.requests[key]
                if ts > now - time_window
            ]
        else:
            self.requests[key] = []
        
        # Check rate limit
        if len(self.requests[key]) >= max_requests:
            oldest_request = self.requests[key][0]
            retry_after = int(oldest_request + time_window - now)
            return False, retry_after
        
        # Add new request
        self.requests[key].append(now)
        return True, None

class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting requests."""
    
    def __init__(self, app):
        """Initialize rate limiter."""
        super().__init__(app)
        
        # Choose rate limiter implementation
        if settings.REDIS_HOST and settings.REDIS_PORT:
            logger.info("Using Redis-based rate limiter")
            self.rate_limiter = RedisRateLimiter()
        else:
            logger.info("Using in-memory rate limiter")
            self.rate_limiter = InMemoryRateLimiter()
        
        # Rate limit configurations
        self.rate_limits = {
            "default": (100, 60),  # 100 requests per minute
            "auth": (20, 60),      # 20 auth requests per minute
            "api": (1000, 60),     # 1000 API requests per minute
            "upload": (50, 60),    # 50 uploads per minute
        }
    
    def get_rate_limit_key(self, request: Request) -> str:
        """Generate rate limit key based on request."""
        client_ip = request.client.host if request.client else "unknown"
        
        # Determine rate limit type
        if "/api/v1/auth" in request.url.path:
            return f"auth:{client_ip}"
        elif "/api/v1/documents/upload" in request.url.path:
            return f"upload:{client_ip}"
        elif "/api/v1/" in request.url.path:
            return f"api:{client_ip}"
        return f"default:{client_ip}"
    
    async def dispatch(self, request: Request, call_next) -> JSONResponse:
        """Handle rate limiting for requests."""
        # Get rate limit key and configuration
        key = self.get_rate_limit_key(request)
        limit_type = key.split(":")[0]
        max_requests, time_window = self.rate_limits.get(limit_type, self.rate_limits["default"])
        
        # Check rate limit
        allowed, retry_after = await self.rate_limiter.check_rate_limit(
            key=key,
            max_requests=max_requests,
            time_window=time_window
        )
        
        if not allowed:
            logger.warning(
                f"Rate limit exceeded",
                extra={
                    "client_ip": request.client.host if request.client else "unknown",
                    "path": request.url.path,
                    "limit_type": limit_type,
                    "retry_after": retry_after
                }
            )
            
            return JSONResponse(
                status_code=429,
                content={
                    "detail": "Too many requests",
                    "retry_after": retry_after
                },
                headers={"Retry-After": str(retry_after)}
            )
        
        return await call_next(request) 