﻿from app.models.base import Base
from app.models.user import User
from app.models.document import Document
from app.models.policy import PolicyDocument
from app.models.compliance import ComplianceReviewV2
from app.models.project import Project
from app.models.disaster import DisasterDeclaration
from app.models.review import BaseReview, ComplianceReview, EHPReview, CostReview, MitigationReview
from app.models.history import ReviewHistory
from app.models.subscription import Subscription
from app.models.report import Report
from app.models.cost import Cost
from app.models.flood_map import FloodMap
from app.models.bca import BCAAnalysis
from app.models.ehp import EHPReviewV2
from app.models.cbcs import CBCSComplianceReview, Standard, Requirement
from app.models.geo_location import GeoLocation
from app.models.mapping import MappingLocation, FloodZone, RiskMap
from app.models.document_version import DocumentVersion
from app.models.cbcs_scan import CBCSScan, ScanStatus

# This ensures all models are registered with Base.metadata
__all__ = [
    "Base",
    "User",
    "Document",
    "PolicyDocument",
    "ComplianceReviewV2",
    "ComplianceReview",
    "Project",
    "DisasterDeclaration",
    "BaseReview",
    "EHPReview",
    "CostReview",
    "MitigationReview",
    "ReviewHistory",
    "Subscription",
    "Report",
    "Cost",
    "FloodMap",
    "BCAAnalysis",
    "EHPReviewV2",
    "CBCSComplianceReview",
    "Standard",
    "Requirement",
    "GeoLocation",
    "MappingLocation",
    "FloodZone",
    "RiskMap",
    "DocumentVersion",
    "CBCSScan",
    "ScanStatus"
]
