from datetime import datetime
from typing import List
from sqlalchemy import String, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base, TimestampMixin

class Standard(Base, TimestampMixin):
    __tablename__ = "standards"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    code: Mapped[str] = mapped_column(String, unique=True, index=True)
    title: Mapped[str] = mapped_column(String)
    description: Mapped[str] = mapped_column(String)
    version: Mapped[str] = mapped_column(String)
    effective_date: Mapped[datetime] = mapped_column(DateTime)
    
    requirements: Mapped[List["Requirement"]] = relationship("Requirement", back_populates="standard")

class Requirement(Base, TimestampMixin):
    __tablename__ = "requirements"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    standard_id: Mapped[int] = mapped_column(ForeignKey("standards.id"))
    code: Mapped[str] = mapped_column(String)
    description: Mapped[str] = mapped_column(String)
    criteria: Mapped[str] = mapped_column(String)
    
    standard: Mapped["Standard"] = relationship("Standard", back_populates="requirements")
    reviews: Mapped[List["CBCSComplianceReview"]] = relationship("CBCSComplianceReview", back_populates="requirement")

class CBCSComplianceReview(Base):
    __tablename__ = "cbcs_compliance_reviews"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    requirement_id: Mapped[int] = mapped_column(ForeignKey("requirements.id"))
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.id"))
    status: Mapped[str] = mapped_column(String)  # Compliant, Non-Compliant, Pending
    review_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    reviewer_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    notes: Mapped[str] = mapped_column(String)
    evidence: Mapped[str] = mapped_column(String)
    
    # Relationships
    requirement: Mapped["Requirement"] = relationship("Requirement", back_populates="reviews")
    project: Mapped["Project"] = relationship("Project", back_populates="cbcs_compliance_reviews")
    reviewer: Mapped["User"] = relationship("User", back_populates="cbcs_compliance_reviews")