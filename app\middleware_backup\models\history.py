from datetime import datetime
from typing import Dict, Optional
from sqlalchemy import String, DateTime, JSON, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base
from enum import Enum

class ChangeType(str, Enum):
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    STATUS_CHANGE = "status_change"
    FINDINGS_UPDATE = "findings_update"
    DOCUMENT_UPLOAD = "document_upload"

class ReviewHistory(Base):
    __tablename__ = "review_history"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    review_id: Mapped[int] = mapped_column(ForeignKey("reviews.id"))
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    change_type: Mapped[ChangeType] = mapped_column(SQLEnum(ChangeType))
    previous_state: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    new_state: Mapped[Dict] = mapped_column(JSON)
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    comment: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    # Relationships
    review: Mapped["BaseReview"] = relationship("BaseReview", back_populates="history")
    user: Mapped["User"] = relationship("User", back_populates="review_changes")
