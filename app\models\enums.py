"""Common enums used across the application."""

from enum import Enum as PyEnum

class DocumentType(str, PyEnum):
    """Document type enum."""
    APPLICATION = "application"
    POLICY = "policy"
    REPORT = "report"
    INVOICE = "invoice"
    CONTRACT = "contract"
    PERMIT = "permit"
    PHOTO = "photo"
    OTHER = "other"

class ComplianceStatus(str, PyEnum):
    """Document compliance status."""
    UNDER_REVIEW = "under_review"
    COMPLIANT = "compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    NON_COMPLIANT = "non_compliant"
    NEEDS_DOCUMENTATION = "needs_documentation"
    REQUIRES_REVIEW = "requires_review"
    UNKNOWN = "unknown"

class ValidationStatus(str, PyEnum):
    """Validation status for compliance checks."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class RiskLevel(str, PyEnum):
    """Risk level for compliance findings."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class MitigationStatus(str, PyEnum):
    """Status of mitigation actions."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    NOT_REQUIRED = "not_required"
    FAILED = "failed" 