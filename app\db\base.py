# Import all models here for Alembic
from app.core.database import Base
from app.models.user import User
from app.models.document import Document
from app.models.application import Application
from app.models.document_link import DocumentLink
from app.models.compliance import ComplianceResult, ComplianceRequirement
from app.models.checklist import ChecklistItem

# This allows Alembic to find all models when generating migrations 