{{- if and (.Values.autoscaling.enabled) (eq .Values.kind "Deployment") }}
{{- if (semverCompare ">=1.23-0" .Capabilities.KubeVersion.GitVersion)}}
apiVersion: autoscaling/v2
{{- else }}
apiVersion: autoscaling/v2beta2
{{- end }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "gateway.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway.labels" . | nindent 4 }}
  annotations:
    {{- .Values.annotations | toYaml | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: {{ .Values.kind | default "Deployment" }}
    name: {{ include "gateway.name" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
          type: Utilization
    {{- end }}
  {{- if .Values.autoscaling.autoscaleBehavior }}
  behavior: {{ toYaml .Values.autoscaling.autoscaleBehavior | nindent 4 }}
  {{- end }}
{{- end }}
