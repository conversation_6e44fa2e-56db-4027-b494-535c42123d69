from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from app.services.websocket import websocket_manager
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.websocket("/ws/{dr_number}")
async def websocket_endpoint(websocket: WebSocket, dr_number: str):
    """WebSocket endpoint for real-time updates"""
    await websocket_manager.connect(websocket, dr_number)
    try:
        while True:
            data = await websocket.receive_json()
            # Handle incoming WebSocket messages
            await websocket_manager.broadcast_to_disaster(dr_number, data)
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, dr_number)
    except Exception as e:
        logger.error(f"WebSocket error for DR-{dr_number}: {e}")
        await websocket.close()
