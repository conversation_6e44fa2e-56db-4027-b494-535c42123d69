from fastapi import HTTPException, Request
from redis import Redis
import time
from typing import Optional, Dict
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class RateLimiter:
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.default_rate_limit = settings.RATE_LIMIT_PER_MINUTE
        self.default_window = 60  # 1 minute window
        self.endpoint_limits: Dict[str, int] = {
            "/api/v1/documents/process": 10,  # Heavy processing endpoints
            "/api/v1/compliance/check": 20,
            "/api/v1/policies/validate": 30,
        }

    async def check_rate_limit(
        self,
        request: Request,
        api_key: Optional[str] = None
    ) -> bool:
        """Check if request is within rate limits."""
        try:
            # Get client identifier (API key or IP)
            client_id = api_key if api_key else request.client.host
            
            # Get endpoint-specific limit
            path = request.url.path
            rate_limit = self.endpoint_limits.get(
                path,
                self.default_rate_limit
            )

            # Create Redis key
            key = f"rate_limit:{client_id}:{path}"
            
            current = int(time.time())
            window_start = current - self.default_window
            
            pipe = self.redis.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)
            # Add current request
            pipe.zadd(key, {str(current): current})
            # Count requests in window
            pipe.zcard(key)
            # Set key expiration
            pipe.expire(key, self.default_window)
            
            results = pipe.execute()
            request_count = results[2]

            if request_count > rate_limit:
                logger.warning(
                    f"Rate limit exceeded for {client_id} on {path}: "
                    f"{request_count}/{rate_limit} requests"
                )
                raise HTTPException(
                    status_code=429,
                    detail={
                        "error": "Too many requests",
                        "limit": rate_limit,
                        "window": self.default_window,
                        "retry_after": self.default_window
                    }
                )

            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Rate limit check failed: {str(e)}")
            return True  # Allow request on error

class APIKeyAuth:
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.cache_ttl = 300  # 5 minutes

    async def validate_api_key(
        self,
        api_key: str,
        required_scope: Optional[str] = None
    ) -> Dict:
        """Validate API key and check scopes."""
        try:
            # Check cache first
            cache_key = f"api_key:{api_key}"
            cached_data = self.redis.get(cache_key)
            
            if cached_data:
                key_data = eval(cached_data)  # Safe as we control the cache
            else:
                # In production, this would check against a database
                key_data = await self._get_api_key_data(api_key)
                if key_data:
                    # Cache the result
                    self.redis.setex(
                        cache_key,
                        self.cache_ttl,
                        str(key_data)
                    )

            if not key_data:
                raise HTTPException(
                    status_code=401,
                    detail="Invalid API key"
                )

            if required_scope and required_scope not in key_data["scopes"]:
                raise HTTPException(
                    status_code=403,
                    detail=f"Missing required scope: {required_scope}"
                )

            return key_data

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"API key validation failed: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Internal server error during authentication"
            )

    async def _get_api_key_data(self, api_key: str) -> Optional[Dict]:
        """Get API key data from database."""
        # This is a placeholder implementation
        # In production, this would query your API key database
        if api_key == "test_key":
            return {
                "key": api_key,
                "owner": "test_user",
                "scopes": ["read", "write"],
                "rate_limit": 100
            }
        return None

class SecurityMiddleware:
    def __init__(
        self,
        redis_client: Redis,
        exclude_paths: Optional[List[str]] = None
    ):
        self.rate_limiter = RateLimiter(redis_client)
        self.api_auth = APIKeyAuth(redis_client)
        self.exclude_paths = exclude_paths or ["/docs", "/redoc", "/openapi.json"]

    async def __call__(
        self,
        request: Request,
        call_next
    ):
        """Process each request through security middleware."""
        path = request.url.path
        
        # Skip excluded paths
        if path in self.exclude_paths:
            return await call_next(request)

        try:
            # Get API key from header
            api_key = request.headers.get("X-API-Key")
            
            # Validate API key if present
            if api_key:
                await self.api_auth.validate_api_key(api_key)
            
            # Check rate limit
            await self.rate_limiter.check_rate_limit(request, api_key)
            
            # Process request
            response = await call_next(request)
            
            return response

        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail}
            )
