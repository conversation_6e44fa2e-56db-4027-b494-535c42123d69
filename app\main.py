"""
Main application module for ComplianceMax.
Implements FastAPI application with optimized configuration and middleware.
"""

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import uvicorn
from typing import Callable
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.logging import setup_logging, get_logger, log_performance_metric
from app.core.db import init_db, health_check
from app.core.metrics import MetricsMiddleware, MetricsCollector
from app.core.exceptions import (
    ComplianceMaxError,
    handle_compliance_max_error,
    ValidationError,
    DatabaseError
)
from app.api.v1.api import api_router
from app.middleware.logging import RequestLoggingMiddleware
from app.middleware.security import SecurityHeadersMiddleware
from app.middleware.rate_limiting import RateLimitingMiddleware

logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Handle application startup and shutdown events.
    """
    # Startup
    logger.info("Starting ComplianceMax application")
    setup_logging()
    init_db()
    
    yield
    
    # Shutdown
    logger.info("Shutting down ComplianceMax application")

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="ComplianceMax - Advanced Compliance Management System",
    lifespan=lifespan,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom middleware
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RateLimitingMiddleware)
app.add_middleware(MetricsMiddleware)

# Performance monitoring middleware
@app.middleware("http")
async def add_performance_monitoring(request: Request, call_next: Callable) -> Response:
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    
    # Log request performance
    log_performance_metric(
        "http_request",
        duration,
        {
            "path": request.url.path,
            "method": request.method,
            "status_code": response.status_code
        }
    )
    
    # Add timing header
    response.headers["X-Process-Time"] = str(duration)
    return response

# Error handlers
@app.exception_handler(ComplianceMaxError)
async def compliance_max_error_handler(request: Request, exc: ComplianceMaxError) -> JSONResponse:
    """Handle ComplianceMaxError exceptions."""
    logger.error(
        f"Application error: {exc.message}",
        extra={
            "error_code": exc.code,
            "details": exc.details,
            "path": request.url.path
        }
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.to_dict()
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unhandled exceptions."""
    logger.error(
        f"Unhandled exception: {str(exc)}",
        extra={
            "path": request.url.path,
            "error_type": type(exc).__name__
        },
        exc_info=True
    )
    
    error = DatabaseError(
        message="Internal server error",
        details={"type": type(exc).__name__}
    )
    return JSONResponse(
        status_code=error.status_code,
        content=error.to_dict()
    )

# Health check endpoint
@app.get("/health")
async def get_health():
    """
    Get application health status including database health.
    """
    db_health = health_check()
    
    return {
        "status": "healthy" if db_health["status"] == "healthy" else "degraded",
        "version": settings.VERSION,
        "database": db_health
    }

# Metrics endpoint
@app.get("/metrics")
async def get_metrics():
    """
    Get application metrics in Prometheus format.
    """
    return Response(
        content=MetricsCollector.get_metrics(),
        media_type="text/plain"
    )

# Include API routes
app.include_router(api_router, prefix=settings.API_V1_STR)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        workers=settings.MAX_WORKERS,
        log_level=settings.LOG_LEVEL.lower()
    )
