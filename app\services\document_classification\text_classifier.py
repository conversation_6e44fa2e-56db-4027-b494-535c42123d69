"""
Text Document Classifier
Implements document classification for text files.
"""

import os
import re
from typing import Dict, Any, List
from datetime import datetime
from .base import DocumentClassifier

class TextClassifier(DocumentClassifier):
    """Classifier implementation for text files."""
    
    def __init__(self):
        self.supported_formats = ['.txt', '.md', '.csv', '.log', '.json', '.xml', '.yaml', '.yml']
    
    def classify(self, document_path: str) -> Dict[str, Any]:
        """
        Classify a text file and extract metadata.
        
        Args:
            document_path: Path to the text file
            
        Returns:
            Dictionary containing:
            - line_count: Number of lines
            - word_count: Number of words
            - character_count: Number of characters
            - file_size: Size in bytes
            - encoding: File encoding
            - has_unicode: Whether file contains non-ASCII characters
            - is_binary: Whether file contains binary data
            - modification_date: File modification date
            - creation_date: File creation date
        """
        if not self.validate_document(document_path):
            raise ValueError(f"Invalid text document: {document_path}")
            
        # Get file metadata
        stat = os.stat(document_path)
        modification_date = datetime.fromtimestamp(stat.st_mtime)
        creation_date = datetime.fromtimestamp(stat.st_ctime)
        
        # Read file content
        with open(document_path, 'rb') as f:
            content = f.read()
        
        # Try to detect encoding
        try:
            text = content.decode('utf-8')
            encoding = 'utf-8'
        except UnicodeDecodeError:
            try:
                text = content.decode('latin-1')
                encoding = 'latin-1'
            except:
                text = content.decode('ascii', errors='ignore')
                encoding = 'ascii'
        
        # Check for binary content
        is_binary = bool(re.search(b'[\x00-\x08\x0E-\x1F]', content))
        
        # Calculate metrics
        lines = text.splitlines()
        words = re.findall(r'\w+', text)
        
        return {
            'line_count': len(lines),
            'word_count': len(words),
            'character_count': len(text),
            'file_size': stat.st_size,
            'encoding': encoding,
            'has_unicode': bool(re.search(r'[^\x00-\x7F]', text)),
            'is_binary': is_binary,
            'modification_date': modification_date,
            'creation_date': creation_date
        }
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats.
        
        Returns:
            List containing ['.txt', '.md', '.csv', '.log', '.json', '.xml', '.yaml', '.yml']
        """
        return self.supported_formats
    
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if a document is a valid text file.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if document is a valid text file, False otherwise
        """
        if not os.path.exists(document_path):
            return False
            
        if not any(document_path.lower().endswith(ext) for ext in self.supported_formats):
            return False
            
        try:
            # Try to read the file to validate it's a text file
            with open(document_path, 'rb') as f:
                content = f.read(1024)  # Read first 1KB
                
            # Check for binary content
            if b'\x00' in content:
                return False
                
            # Try to decode as text
            content.decode('utf-8', errors='ignore')
            return True
        except:
            return False 