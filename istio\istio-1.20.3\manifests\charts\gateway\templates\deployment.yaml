apiVersion: apps/v1
kind: {{ .Values.kind | default "Deployment" }}
metadata:
  name: {{ include "gateway.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway.labels" . | nindent 4}}
  annotations:
    {{- .Values.annotations | toYaml | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  {{- with .Values.replicaCount }}
  replicas: {{ . }}
  {{- end }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "gateway.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        sidecar.istio.io/inject: "true"
        {{- with .Values.revision }}
        istio.io/rev: {{ . }}
        {{- end }}
        {{- include "gateway.podLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "gateway.serviceAccountName" . }}
      securityContext:
      {{- if .Values.securityContext }}
        {{- toYaml .Values.securityContext | nindent 8 }}
      {{- else if (semverCompare ">=1.22-0" .Capabilities.KubeVersion.GitVersion) }}
        # Safe since 1.22: https://github.com/kubernetes/kubernetes/pull/103326
        sysctls:
        - name: net.ipv4.ip_unprivileged_port_start
          value: "0"
      {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: istio-proxy
          # "auto" will be populated at runtime by the mutating webhook. See https://istio.io/latest/docs/setup/additional-setup/sidecar-injection/#customizing-injection
          image: auto
          {{- with .Values.imagePullPolicy }}
          imagePullPolicy: {{ . }}
          {{- end }}
          securityContext:
          {{- if .Values.containerSecurityContext }}
            {{- toYaml .Values.containerSecurityContext | nindent 12 }}
          {{- else if (semverCompare ">=1.22-0" .Capabilities.KubeVersion.GitVersion) }}
            # Safe since 1.22: https://github.com/kubernetes/kubernetes/pull/103326
            capabilities:
              drop:
              - ALL
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: true
            runAsUser: 1337
            runAsGroup: 1337
            runAsNonRoot: true
          {{- else }}
            capabilities:
              drop:
              - ALL
              add:
              - NET_BIND_SERVICE
            runAsUser: 0
            runAsGroup: 1337
            runAsNonRoot: false
            allowPrivilegeEscalation: true
            readOnlyRootFilesystem: true
          {{- end }}
          env:
          {{- with .Values.networkGateway }}
          - name: ISTIO_META_REQUESTED_NETWORK_VIEW
            value: "{{.}}"
          {{- end }}
          {{- range $key, $val := .Values.env }}
          - name: {{ $key }}
            value: {{ $val | quote }}
          {{- end }}
          ports:
          - containerPort: 15090
            protocol: TCP
            name: http-envoy-prom
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{ toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ $.Values.terminationGracePeriodSeconds }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
