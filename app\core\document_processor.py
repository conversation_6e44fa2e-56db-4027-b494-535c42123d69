"""
Document processing system for ComplianceMax.
Handles document parsing, OCR, and text extraction.
"""

import os
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import pytesseract
from PIL import Image
import pdf2image
import docx
import pandas as pd
import easyocr
from paddleocr import PaddleOCR
import magic
import logging
from concurrent.futures import ThreadPoolExecutor
from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import MetricsCollector, track_performance
from app.core.exceptions import DocumentProcessingError

logger = get_logger(__name__)

class DocumentProcessor:
    """Main document processing class."""
    
    def __init__(self):
        """Initialize document processor with OCR engines."""
        self.mime = magic.Magic(mime=True)
        self.supported_formats = {
            'application/pdf': self._process_pdf,
            'image/jpeg': self._process_image,
            'image/png': self._process_image,
            'image/tiff': self._process_image,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._process_docx,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': self._process_excel,
            'text/plain': self._process_text
        }
        
        # Initialize OCR engines if enabled
        if settings.ENABLE_MULTI_ENGINE_OCR:
            self.ocr_engines = {
                'tesseract': self._tesseract_ocr,
                'easyocr': self._easyocr_ocr,
                'paddleocr': self._paddleocr_ocr
            }
            self.reader_easyocr = easyocr.Reader(['en'])
            self.reader_paddleocr = PaddleOCR(use_angle_cls=True, lang='en')
        else:
            self.ocr_engines = {'tesseract': self._tesseract_ocr}
    
    @track_performance("document_processing")
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """
        Process a document and extract its content.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            # Detect file type
            mime_type = self.mime.from_file(file_path)
            
            if mime_type not in self.supported_formats:
                raise DocumentProcessingError(
                    message=f"Unsupported file format: {mime_type}",
                    details={"supported_formats": list(self.supported_formats.keys())}
                )
            
            # Process document based on type
            processor = self.supported_formats[mime_type]
            start_time = time.time()
            
            result = await processor(file_path)
            
            # Track processing metrics
            duration = time.time() - start_time
            MetricsCollector.track_document_processing(
                doc_type=mime_type,
                duration=duration,
                status="success"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Document processing failed: {str(e)}", exc_info=True)
            MetricsCollector.track_document_processing(
                doc_type=mime_type if 'mime_type' in locals() else 'unknown',
                duration=time.time() - start_time if 'start_time' in locals() else 0,
                status="error"
            )
            raise DocumentProcessingError(
                message=f"Failed to process document: {str(e)}",
                details={"file_path": file_path}
            )
    
    async def _process_pdf(self, file_path: str) -> Dict[str, Any]:
        """Process PDF documents."""
        try:
            # Convert PDF to images
            images = pdf2image.convert_from_path(file_path)
            
            # Process each page in parallel
            with ThreadPoolExecutor() as executor:
                texts = list(executor.map(self._process_page, images))
            
            return {
                "text": "\n\n".join(texts),
                "page_count": len(images),
                "metadata": self._extract_pdf_metadata(file_path)
            }
        except Exception as e:
            raise DocumentProcessingError(
                message=f"PDF processing failed: {str(e)}",
                details={"file_path": file_path}
            )
    
    def _process_page(self, image: Image.Image) -> str:
        """Process a single page using multiple OCR engines."""
        texts = []
        
        for engine_name, engine_func in self.ocr_engines.items():
            try:
                text = engine_func(image)
                texts.append(text)
            except Exception as e:
                logger.warning(f"OCR engine {engine_name} failed: {str(e)}")
        
        # Combine and clean results
        return self._combine_ocr_results(texts)
    
    def _tesseract_ocr(self, image: Image.Image) -> str:
        """Perform OCR using Tesseract."""
        return pytesseract.image_to_string(image)
    
    def _easyocr_ocr(self, image: Image.Image) -> str:
        """Perform OCR using EasyOCR."""
        result = self.reader_easyocr.readtext(image)
        return "\n".join(text for _, text, _ in result)
    
    def _paddleocr_ocr(self, image: Image.Image) -> str:
        """Perform OCR using PaddleOCR."""
        result = self.reader_paddleocr.ocr(image)
        return "\n".join(text for _, (text, _) in result)
    
    def _combine_ocr_results(self, texts: List[str]) -> str:
        """Combine and clean OCR results from multiple engines."""
        if not texts:
            return ""
        
        # Use the longest result as it's likely the most complete
        return max(texts, key=len)
    
    async def _process_docx(self, file_path: str) -> Dict[str, Any]:
        """Process Word documents."""
        try:
            doc = docx.Document(file_path)
            
            text = "\n".join(paragraph.text for paragraph in doc.paragraphs)
            tables = []
            
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    table_data.append([cell.text for cell in row.cells])
                tables.append(table_data)
            
            return {
                "text": text,
                "tables": tables,
                "metadata": {
                    "core_properties": self._extract_docx_metadata(doc)
                }
            }
        except Exception as e:
            raise DocumentProcessingError(
                message=f"Word document processing failed: {str(e)}",
                details={"file_path": file_path}
            )
    
    async def _process_excel(self, file_path: str) -> Dict[str, Any]:
        """Process Excel documents."""
        try:
            excel_file = pd.ExcelFile(file_path)
            sheets = {}
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                sheets[sheet_name] = df.to_dict(orient='records')
            
            return {
                "sheets": sheets,
                "metadata": {
                    "sheet_names": excel_file.sheet_names
                }
            }
        except Exception as e:
            raise DocumentProcessingError(
                message=f"Excel document processing failed: {str(e)}",
                details={"file_path": file_path}
            )
    
    async def _process_image(self, file_path: str) -> Dict[str, Any]:
        """Process image documents."""
        try:
            image = Image.open(file_path)
            text = self._process_page(image)
            
            return {
                "text": text,
                "metadata": {
                    "size": image.size,
                    "format": image.format,
                    "mode": image.mode
                }
            }
        except Exception as e:
            raise DocumentProcessingError(
                message=f"Image processing failed: {str(e)}",
                details={"file_path": file_path}
            )
    
    async def _process_text(self, file_path: str) -> Dict[str, Any]:
        """Process text documents."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            return {
                "text": text,
                "metadata": {
                    "size": os.path.getsize(file_path),
                    "encoding": "utf-8"
                }
            }
        except Exception as e:
            raise DocumentProcessingError(
                message=f"Text file processing failed: {str(e)}",
                details={"file_path": file_path}
            )
    
    def _extract_pdf_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from PDF document."""
        try:
            import PyPDF2
            with open(file_path, 'rb') as f:
                pdf = PyPDF2.PdfReader(f)
                info = pdf.metadata
                return {
                    "title": info.get('/Title', ''),
                    "author": info.get('/Author', ''),
                    "subject": info.get('/Subject', ''),
                    "creator": info.get('/Creator', ''),
                    "producer": info.get('/Producer', ''),
                    "page_count": len(pdf.pages)
                }
        except Exception as e:
            logger.warning(f"Failed to extract PDF metadata: {str(e)}")
            return {}
    
    def _extract_docx_metadata(self, doc: docx.Document) -> Dict[str, Any]:
        """Extract metadata from Word document."""
        try:
            core_props = doc.core_properties
            return {
                "author": core_props.author,
                "title": core_props.title,
                "subject": core_props.subject,
                "created": core_props.created.isoformat() if core_props.created else None,
                "modified": core_props.modified.isoformat() if core_props.modified else None,
                "last_modified_by": core_props.last_modified_by
            }
        except Exception as e:
            logger.warning(f"Failed to extract Word document metadata: {str(e)}")
            return {} 