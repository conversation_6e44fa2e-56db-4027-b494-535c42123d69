from celery import shared_task
import smtplib
from email.mime.text import MIMEText
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

@shared_task
def send_notification(user_id: int, subject: str, message: str):
    try:
        msg = MIMEText(message)
        msg['Subject'] = subject
        msg['From'] = settings.EMAIL_FROM
        msg['To'] = settings.EMAIL_TO

        with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server:
            server.starttls()
            server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
            server.send_message(msg)
        logger.info(f"Notification sent to user {user_id}: {subject}")
    except Exception as e:
        logger.error(f"Failed to send notification to user {user_id}: {str(e)}")
        raise

def notify_user(user_id: int, event: str, details: dict):
    subject = f"ComplianceMax Notification: {event}"
    message = f"Event: {event}\nDetails: {details}"
    send_notification.delay(user_id, subject, message)