from datetime import datetime
from typing import List, Optional, Dict
from pydantic import BaseModel, Field
from app.models.compliance import ReviewStatus, ReviewPriority

class ReviewDocumentBase(BaseModel):
    document_id: int

class ReviewDocumentCreate(ReviewDocumentBase):
    pass

class ReviewDocumentResponse(ReviewDocumentBase):
    review_id: int
    added_at: datetime

    class Config:
        from_attributes = True

class ComplianceReviewBase(BaseModel):
    project_id: int
    reviewer_id: int
    priority: ReviewPriority
    due_date: datetime
    notes: Optional[str] = None

class ComplianceReviewCreate(ComplianceReviewBase):
    pass

class ComplianceReviewUpdate(BaseModel):
    status: ReviewStatus
    findings: Optional[List[Dict]] = None
    recommendations: Optional[List[Dict]] = None

class ComplianceReviewResponse(ComplianceReviewBase):
    id: int
    status: ReviewStatus
    findings: List[Dict] = Field(default_factory=list)
    recommendations: List[Dict] = Field(default_factory=list)
    review_date: datetime
    completed_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    documents: List[ReviewDocumentResponse] = Field(default_factory=list)

    class Config:
        from_attributes = True

class ComplianceReviewList(BaseModel):
    total: int
    items: List[ComplianceReviewResponse]

    class Config:
        from_attributes = True
