# Adapted from istio-discovery/templates/mutatingwebhook.yaml
# Removed paths for legacy and default selectors since a revision tag
# is inherently created from a specific revision
{{/* Copy just what we need to avoid expensive deepCopy */}}
{{- $whv := dict
 "revision" .Values.revision
  "injectionURL" .Values.istiodRemote.injectionURL
  "namespace" .Release.Namespace }}
{{- define "core" }}
- name: {{.Prefix}}sidecar-injector.istio.io
  clientConfig:
    {{- if .injectionURL }}
    url: {{ .injectionURL }}
    {{- else }}
    service:
      name: istiod{{- if not (eq .revision "") }}-{{ .revision }}{{- end }}
      namespace: {{ .namespace }}
      path: "/inject"
    {{- end }}
  sideEffects: None
  rules:
    - operations: [ "CREATE" ]
      apiGroups: [""]
      apiVersions: ["v1"]
      resources: ["pods"]
  failurePolicy: Fail
  admissionReviewVersions: ["v1beta1", "v1"]
{{- end }}

apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: istio-revision-tag-default
  labels:
    istio.io/tag: "default"
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Pilot"
    app: sidecar-injector
    release: {{ .Release.Name }}
webhooks:
{{- include "core" (mergeOverwrite (deepCopy $whv) (dict "Prefix" "rev.namespace.") ) }}
  namespaceSelector:
    matchExpressions:
    - key: istio.io/rev
      operator: In
      values:
      - "default"
    - key: istio-injection
      operator: DoesNotExist
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: NotIn
      values:
      - "false"
{{- include "core" (mergeOverwrite (deepCopy $whv) (dict "Prefix" "rev.object.") ) }}
  namespaceSelector:
    matchExpressions:
    - key: istio.io/rev
      operator: DoesNotExist
    - key: istio-injection
      operator: DoesNotExist
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: NotIn
      values:
      - "false"
    - key: istio.io/rev
      operator: In
      values:
      - "default"

{{- /* Case 1: Namespace selector enabled, and object selector is not injected */}}
{{- include "core" (mergeOverwrite (deepCopy $whv) (dict "Prefix" "namespace.") ) }}
  namespaceSelector:
    matchExpressions:
    - key: istio-injection
      operator: In
      values:
      - enabled
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: NotIn
      values:
      - "false"

{{- /* Case 2: no namespace label, but object selector is enabled (and revision label is not, which has priority) */}}
{{- include "core" (mergeOverwrite (deepCopy $whv) (dict "Prefix" "object.") ) }}
  namespaceSelector:
    matchExpressions:
    - key: istio-injection
      operator: DoesNotExist
    - key: istio.io/rev
      operator: DoesNotExist
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: In
      values:
      - "true"
    - key: istio.io/rev
      operator: DoesNotExist

{{- if .Values.sidecarInjectorWebhook.enableNamespacesByDefault }}
{{- /* Special case 3: no labels at all */}}
{{- include "core" (mergeOverwrite (deepCopy $whv) (dict "Prefix" "auto.") ) }}
  namespaceSelector:
    matchExpressions:
    - key: istio-injection
      operator: DoesNotExist
    - key: istio.io/rev
      operator: DoesNotExist
    - key: "kubernetes.io/metadata.name"
      operator: "NotIn"
      values: ["kube-system","kube-public","kube-node-lease","local-path-storage"]
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: DoesNotExist
    - key: istio.io/rev
      operator: DoesNotExist
{{- end }}
