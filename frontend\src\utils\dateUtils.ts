/**
 * Format a date string to a human-readable format
 * @param dateString - ISO date string
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short'
  }).format(date);
}

/**
 * Get relative time string (e.g. "2 hours ago")
 * @param dateString - ISO date string
 * @returns Relative time string
 */
export function getRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const rtf = new Intl.RelativeTimeFormat('en', { numeric: 'auto' });
  const now = new Date();
  const diffInSeconds = (date.getTime() - now.getTime()) / 1000;
  
  // Convert to appropriate unit
  if (Math.abs(diffInSeconds) < 60) {
    return rtf.format(Math.round(diffInSeconds), 'second');
  } else if (Math.abs(diffInSeconds) < 3600) {
    return rtf.format(Math.round(diffInSeconds / 60), 'minute');
  } else if (Math.abs(diffInSeconds) < 86400) {
    return rtf.format(Math.round(diffInSeconds / 3600), 'hour');
  } else if (Math.abs(diffInSeconds) < 2592000) {
    return rtf.format(Math.round(diffInSeconds / 86400), 'day');
  } else if (Math.abs(diffInSeconds) < 31536000) {
    return rtf.format(Math.round(diffInSeconds / 2592000), 'month');
  } else {
    return rtf.format(Math.round(diffInSeconds / 31536000), 'year');
  }
}

/**
 * Parse a date range string into start and end dates
 * @param dateRange - Date range string in format "YYYY-MM-DD/YYYY-MM-DD"
 * @returns Object with start and end dates
 */
export function parseDateRange(dateRange: string): { start: Date; end: Date } {
  const [start, end] = dateRange.split('/');
  return {
    start: new Date(start),
    end: new Date(end)
  };
}

/**
 * Format a date range to a human-readable string
 * @param start - Start date
 * @param end - End date
 * @returns Formatted date range string
 */
export function formatDateRange(start: Date, end: Date): string {
  const formatter = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
  return `${formatter.format(start)} - ${formatter.format(end)}`;
}

/**
 * Check if a date is between two other dates
 * @param date - Date to check
 * @param start - Start date
 * @param end - End date
 * @returns True if date is between start and end
 */
export function isDateBetween(date: Date, start: Date, end: Date): boolean {
  return date >= start && date <= end;
}

/**
 * Get the duration between two dates in a human-readable format
 * @param start - Start date
 * @param end - End date
 * @returns Duration string
 */
export function getDuration(start: Date, end: Date): string {
  const diffInSeconds = (end.getTime() - start.getTime()) / 1000;
  
  if (diffInSeconds < 60) {
    return `${Math.round(diffInSeconds)} seconds`;
  } else if (diffInSeconds < 3600) {
    return `${Math.round(diffInSeconds / 60)} minutes`;
  } else if (diffInSeconds < 86400) {
    return `${Math.round(diffInSeconds / 3600)} hours`;
  } else if (diffInSeconds < 2592000) {
    return `${Math.round(diffInSeconds / 86400)} days`;
  } else if (diffInSeconds < 31536000) {
    return `${Math.round(diffInSeconds / 2592000)} months`;
  } else {
    return `${Math.round(diffInSeconds / 31536000)} years`;
  }
}
