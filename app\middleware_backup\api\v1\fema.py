from typing import List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException
from app.core.deps import get_current_active_user
from app.db.user import User
from app.services.fema_api_service import FEMAAPIService

router = APIRouter()
fema_service = FEMAAPIService()

@router.get("/disasters")
async def get_disasters(
    state: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
) -> List[Dict]:
    """Get disaster declarations from FEMA API."""
    try:
        return await fema_service.get_disaster_declarations(state)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/public-assistance/{disaster_number}")
async def get_public_assistance(
    disaster_number: str,
    current_user: User = Depends(get_current_active_user)
) -> List[Dict]:
    """Get public assistance data for a specific disaster."""
    try:
        return await fema_service.get_public_assistance_data(disaster_number)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/validate-disaster/{disaster_number}")
async def validate_disaster(
    disaster_number: str,
    current_user: User = Depends(get_current_active_user)
) -> Dict:
    """Validate if a disaster declaration exists and is active."""
    is_valid = await fema_service.validate_disaster_declaration(disaster_number)
    return {
        "disaster_number": disaster_number,
        "is_valid": is_valid
    }
