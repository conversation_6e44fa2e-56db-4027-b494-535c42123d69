from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy import select
from app.core.database import get_db
from app.models.cbcs import CBCSScan, Document
from app.models.document_type import DocumentType
from app.schemas.cbcs import CBCSScanResponse
from app.core.security import get_current_active_user
from app.models.user import User
from app.services.ocr_service import process_document
from typing import List
import os
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/cbcs", tags=["cbcs"])

def upload_document(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    try:
        # Validate file type
        if not file.content_type in ['application/pdf', 'image/jpeg', 'image/png']:
            raise HTTPException(status_code=400, detail="Unsupported file type")

        # Save the file temporarily
        file_path = os.path.join(settings.UPLOAD_DIR, file.filename)
        with open(file_path, "wb") as f:
            content = file.file.read()
            f.write(content)

        # Create document record
        document = Document(
            user_id=current_user.id,
            filename=file.filename,
            file_path=file_path,
            mime_type=file.content_type,
            status="pending"
        )
        db.add(document)
        db.commit()
        db.refresh(document)

        # Create CBCS scan record
        scan = CBCSScan(
            user_id=current_user.id,
            document_id=document.id,
            status="pending"
        )
        db.add(scan)
        db.commit()
        db.refresh(scan)

        # Offload OCR processing to Celery
        process_document.delay(file_path, {"use_tesseract": True, "use_easyocr": True})

        return scan
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

def get_scans(db: Session = Depends(get_db), current_user: User = Depends(get_current_active_user)):
    query = select(CBCSScan).where(CBCSScan.user_id == current_user.id)
    result = db.execute(query)
    scans = result.scalars().all()
    return scans

def get_compliance_reviews(db: Session = Depends(get_db), current_user: User = Depends(get_current_active_user)):
    query = select(ComplianceReview).where(ComplianceReview.reviewer_id == current_user.id)
    result = db.execute(query)
    reviews = result.scalars().all()
    return reviews