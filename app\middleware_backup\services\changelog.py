from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.document import Document
from app.models.document_version import DocumentVersion

class ChangelogService:
    @staticmethod
    def _get_field_changes(old_data: Dict[str, Any], new_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Compare two versions and return the changed fields."""
        changes = {}
        for field in new_data:
            if field in old_data and old_data[field] != new_data[field]:
                changes[field] = {
                    "old": old_data[field],
                    "new": new_data[field]
                }
        return changes

    @staticmethod
    def _format_field_name(field_name: str) -> str:
        """Convert snake_case field names to human-readable format."""
        return field_name.replace("_", " ").title()

    @staticmethod
    def _format_value(value: Any) -> str:
        """Format values for human-readable output."""
        if value is None:
            return "None"
        if isinstance(value, datetime):
            return value.strftime("%Y-%m-%d %H:%M:%S")
        if isinstance(value, bool):
            return "Yes" if value else "No"
        if isinstance(value, (int, float)):
            return str(value)
        if isinstance(value, dict):
            return ", ".join(f"{k}: {v}" for k, v in value.items())
        return str(value)

    @staticmethod
    def _generate_change_description(field: str, old_value: Any, new_value: Any) -> str:
        """Generate a human-readable description of a field change."""
        field_name = ChangelogService._format_field_name(field)
        old_str = ChangelogService._format_value(old_value)
        new_str = ChangelogService._format_value(new_value)
        
        if field in ["compliance_percentage", "mitigation_funding_amount"]:
            return f"{field_name} changed from {old_str} to {new_str}"
        elif field in ["compliance_status", "risk_level", "mitigation_status"]:
            return f"Status updated from {old_str} to {new_str}"
        elif field.endswith("_date"):
            return f"{field_name} changed to {new_str}"
        elif field == "requirements":
            return f"Requirements list was updated"
        else:
            return f"{field_name} was updated from '{old_str}' to '{new_str}'"

    @staticmethod
    def get_version_changelog(
        db: Session,
        document_id: int,
        version_number: int,
        user_id: int
    ) -> Optional[Dict[str, Any]]:
        """Get changelog for a specific version."""
        # Get the version and its previous version
        current_version = db.query(DocumentVersion).join(Document).filter(
            and_(
                Document.id == document_id,
                Document.user_id == user_id,
                DocumentVersion.version_number == version_number
            )
        ).first()
        
        if not current_version:
            return None
            
        # For version 1, there's no previous version to compare
        if current_version.version_number == 1:
            return {
                "version": 1,
                "timestamp": current_version.created_at,
                "change_type": "CREATE",
                "summary": "Initial document creation",
                "changes": ["Document was created"]
            }
        
        # Get previous version
        previous_version = db.query(DocumentVersion).join(Document).filter(
            and_(
                Document.id == document_id,
                Document.user_id == user_id,
                DocumentVersion.version_number == version_number - 1
            )
        ).first()
        
        if not previous_version:
            return None
        
        # Compare versions
        old_data = {
            field: getattr(previous_version, field)
            for field in previous_version.__dict__
            if not field.startswith('_') and field not in ['id', 'document_id', 'version_number', 'created_at', 'created_by_id', 'change_type', 'change_summary']
        }
        
        new_data = {
            field: getattr(current_version, field)
            for field in current_version.__dict__
            if not field.startswith('_') and field not in ['id', 'document_id', 'version_number', 'created_at', 'created_by_id', 'change_type', 'change_summary']
        }
        
        changes = ChangelogService._get_field_changes(old_data, new_data)
        
        # Generate human-readable change descriptions
        change_descriptions = [
            ChangelogService._generate_change_description(field, change["old"], change["new"])
            for field, change in changes.items()
        ]
        
        return {
            "version": current_version.version_number,
            "timestamp": current_version.created_at,
            "change_type": current_version.change_type,
            "summary": current_version.change_summary,
            "changes": change_descriptions
        }

    @staticmethod
    def get_document_changelog(
        db: Session,
        document_id: int,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get changelog for all versions of a document."""
        versions = db.query(DocumentVersion).join(Document).filter(
            and_(
                Document.id == document_id,
                Document.user_id == user_id
            )
        ).order_by(DocumentVersion.version_number.desc())\
         .offset(skip).limit(limit).all()
        
        changelog = []
        for version in versions:
            version_log = ChangelogService.get_version_changelog(
                db, document_id, version.version_number, user_id
            )
            if version_log:
                changelog.append(version_log)
        
        return changelog
