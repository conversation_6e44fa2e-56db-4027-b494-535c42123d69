from sqlalchemy.orm import Session
from fastapi import HTTPException
from datetime import datetime

from app.models.project import Project
from app.schemas.project import ProjectCreate

class ProjectService:
    def __init__(self, db: Session):
        self.db = db

    def create_project(self, project: ProjectCreate, owner_id: int) -> Project:
        db_project = Project(
            **project.model_dump(),
            owner_id=owner_id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        self.db.add(db_project)
        self.db.commit()
        self.db.refresh(db_project)
        return db_project

    def get_project(self, project_id: int) -> Project:
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        return project

    def get_projects(self, owner_id: int, skip: int = 0, limit: int = 100) -> list[Project]:
        return (
            self.db.query(Project)
            .filter(Project.owner_id == owner_id)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_project(self, project_id: int, project: ProjectCreate) -> Project:
        db_project = self.get_project(project_id)
        for key, value in project.model_dump().items():
            setattr(db_project, key, value)
        db_project.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(db_project)
        return db_project

    def delete_project(self, project_id: int) -> None:
        db_project = self.get_project(project_id)
        self.db.delete(db_project)
        self.db.commit()
