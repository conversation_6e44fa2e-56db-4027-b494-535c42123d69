"""Service for handling Compliance Based Control System (CBCS) scans."""

from sqlalchemy.orm import Session
from app.models.cbcs import Standard, Requirement, ComplianceReview
from app.schemas.cbcs import StandardCreate, RequirementCreate, ReviewCreate
from datetime import datetime
from typing import Dict, List, Optional
from fastapi import HTTPException

from app.models.document import Document
from app.models.compliance import CBCSScan, ScanStatus
from app.core.config import get_settings

settings = get_settings()

class CBCSService:
    def __init__(self, db: Session):
        self.db = db

    def create_standard(self, standard: StandardCreate) -> Standard:
        db_standard = Standard(
            code=standard.code,
            title=standard.title,
            description=standard.description,
            version=standard.version,
            effective_date=standard.effective_date
        )
        self.db.add(db_standard)
        self.db.commit()
        self.db.refresh(db_standard)
        return db_standard

    def create_requirement(self, requirement: RequirementCreate) -> Requirement:
        db_requirement = Requirement(
            standard_id=requirement.standard_id,
            code=requirement.code,
            description=requirement.description,
            criteria=requirement.criteria
        )
        self.db.add(db_requirement)
        self.db.commit()
        self.db.refresh(db_requirement)
        return db_requirement

    def create_review(self, review: ReviewCreate, reviewer_id: int) -> ComplianceReview:
        db_review = ComplianceReview(
            requirement_id=review.requirement_id,
            project_id=review.project_id,
            status=review.status,
            reviewer_id=reviewer_id,
            notes=review.notes,
            evidence=review.evidence
        )
        self.db.add(db_review)
        self.db.commit()
        self.db.refresh(db_review)
        return db_review

    def get_standard(self, standard_id: int) -> Standard:
        return self.db.query(Standard).filter(Standard.id == standard_id).first()

    def get_requirements(self, standard_id: int) -> list[Requirement]:
        return self.db.query(Requirement).filter(Requirement.standard_id == standard_id).all()

    def get_reviews(self, project_id: int) -> list[ComplianceReview]:
        return self.db.query(ComplianceReview).filter(ComplianceReview.project_id == project_id).all()

    async def create_scan(self, document_id: int) -> CBCSScan:
        """Create a new CBCS scan for a document."""
        document = self.db.query(Document).filter(Document.id == document_id).first()
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        scan = CBCSScan(
            document_id=document_id,
            status=ScanStatus.PENDING
        )
        self.db.add(scan)
        self.db.commit()
        self.db.refresh(scan)
        return scan

    async def start_scan(self, scan_id: int) -> CBCSScan:
        """Start processing a CBCS scan."""
        scan = self.db.query(CBCSScan).filter(CBCSScan.id == scan_id).first()
        if not scan:
            raise HTTPException(status_code=404, detail="Scan not found")

        if scan.status != ScanStatus.PENDING:
            raise HTTPException(status_code=400, detail=f"Scan is already {scan.status}")

        scan.status = ScanStatus.IN_PROGRESS
        scan.scan_date = datetime.utcnow()
        self.db.commit()
        self.db.refresh(scan)
        return scan

    async def complete_scan(
        self,
        scan_id: int,
        results: Dict,
        error_message: Optional[str] = None
    ) -> CBCSScan:
        """Complete a CBCS scan with results."""
        scan = self.db.query(CBCSScan).filter(CBCSScan.id == scan_id).first()
        if not scan:
            raise HTTPException(status_code=404, detail="Scan not found")

        if scan.status == ScanStatus.COMPLETED:
            raise HTTPException(status_code=400, detail="Scan is already completed")

        scan.status = ScanStatus.COMPLETED if not error_message else ScanStatus.FAILED
        scan.results = results
        scan.error_message = error_message
        self.db.commit()
        self.db.refresh(scan)
        return scan

    async def get_scan(self, scan_id: int) -> CBCSScan:
        """Get a specific CBCS scan."""
        scan = self.db.query(CBCSScan).filter(CBCSScan.id == scan_id).first()
        if not scan:
            raise HTTPException(status_code=404, detail="Scan not found")
        return scan

    async def get_document_scans(self, document_id: int) -> List[CBCSScan]:
        """Get all CBCS scans for a document."""
        return self.db.query(CBCSScan).filter(CBCSScan.document_id == document_id).all()

    async def get_latest_scan(self, document_id: int) -> Optional[CBCSScan]:
        """Get the most recent CBCS scan for a document."""
        return (
            self.db.query(CBCSScan)
            .filter(CBCSScan.document_id == document_id)
            .order_by(CBCSScan.scan_date.desc())
            .first()
        )

    async def analyze_scan_results(self, scan_id: int) -> Dict:
        """Analyze the results of a completed CBCS scan."""
        scan = await self.get_scan(scan_id)
        if scan.status != ScanStatus.COMPLETED:
            raise HTTPException(status_code=400, detail="Scan is not completed")

        if not scan.results:
            return {"status": "error", "message": "No scan results available"}

        # Analyze the scan results and return findings
        return {
            "status": "success",
            "document_id": scan.document_id,
            "scan_date": scan.scan_date,
            "findings": scan.results.get("findings", []),
            "risk_level": scan.results.get("risk_level", "MEDIUM"),
            "recommendations": scan.results.get("recommendations", [])
        }
