import redis
import json
import secrets
from typing import Any, Dict, Optional
from app.core.config import settings

# Redis connection pool
redis_pool = redis.ConnectionPool.from_url(settings.REDIS_URL)

class RedisSessionStore:
    """Redis-based session store for CSRF tokens and user sessions."""
    
    def __init__(self, prefix: str = "session:", expire: int = settings.SESSION_EXPIRE):
        self.prefix = prefix
        self.expire = expire
        self.redis = redis.Redis(connection_pool=redis_pool)
    
    def _get_key(self, session_id: str) -> str:
        """Get the full Redis key for a session ID."""
        return f"{self.prefix}{session_id}"
    
    def create_session(self, data: Optional[Dict[str, Any]] = None) -> str:
        """Create a new session with optional data and return the session ID."""
        session_id = secrets.token_urlsafe(32)
        if data is None:
            data = {}
        
        # Always include a CSRF token
        if "csrf_token" not in data:
            data["csrf_token"] = secrets.token_urlsafe(32)
        
        self.save_session(session_id, data)
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data by session ID."""
        key = self._get_key(session_id)
        data = self.redis.get(key)
        if data:
            # Reset expiry on access
            self.redis.expire(key, self.expire)
            return json.loads(data)
        return None
    
    def save_session(self, session_id: str, data: Dict[str, Any]) -> None:
        """Save session data with expiry."""
        key = self._get_key(session_id)
        self.redis.setex(key, self.expire, json.dumps(data))
    
    def delete_session(self, session_id: str) -> None:
        """Delete a session."""
        key = self._get_key(session_id)
        self.redis.delete(key)

# Singleton instance
session_store = RedisSessionStore()
