from datetime import datetime
from typing import Optional
from sqlalchemy import String, Float, DateTime, ForeignKey, JSON, Integer, Column
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class GeoLocation(Base):
    __tablename__ = "geo_locations"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    latitude = Column(Float)
    longitude = Column(Float)
    address = Column(String)
    city = Column(String)
    state = Column(String(2))
    zip_code = Column(String(10))
    fema_flood_zone = Column(String, nullable=True)
    parcel_id = Column(String, nullable=True)
    elevation_data = Column(JSON, default=dict)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    
    # Relationships
    project = relationship("Project", back_populates="geo_locations")

    def __repr__(self):
        return f"<GeoLocation {self.address}, {self.city}, {self.state}>"
