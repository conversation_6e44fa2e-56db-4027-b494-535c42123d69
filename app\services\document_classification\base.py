"""
Base Document Classifier
Defines the interface for document classification services.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List

class DocumentClassifier(ABC):
    """Abstract base class for document classification services."""
    
    @abstractmethod
    def classify(self, document_path: str) -> Dict[str, Any]:
        """
        Classify a document and return its metadata.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            Dictionary containing classification results and metadata
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats.
        
        Returns:
            List of supported file extensions
        """
        pass
    
    @abstractmethod
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if a document can be processed.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if document is valid, False otherwise
        """
        pass 