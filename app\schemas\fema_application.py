"""Schemas for FEMA applications and compliance reviews."""

from typing import List, Optional, Dict
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID

from app.models.fema import ApplicationStatus, ComplianceStatus

class FEMAApplicationBase(BaseModel):
    """Base schema for FEMA application."""
    disaster_number: str = Field(..., description="FEMA disaster declaration number")
    applicant_id: str = Field(..., description="Applicant identifier")
    project_title: str = Field(..., description="Title of the project")

class FEMAApplicationCreate(FEMAApplicationBase):
    """Schema for creating a FEMA application."""
    pass

class FEMAApplicationUpdate(BaseModel):
    """Schema for updating a FEMA application."""
    disaster_number: Optional[str] = None
    applicant_id: Optional[str] = None
    project_title: Optional[str] = None
    status: Optional[ApplicationStatus] = None
    compliance_status: Optional[ComplianceStatus] = None

class FEMAApplication(FEMAApplicationBase):
    """Schema for a complete FEMA application."""
    id: str
    status: ApplicationStatus
    compliance_status: ComplianceStatus
    submission_date: datetime
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ComplianceDocumentBase(BaseModel):
    """Base schema for compliance document."""
    document_type: str = Field(..., description="Type of document")
    original_filename: str = Field(..., description="Original filename")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    mime_type: Optional[str] = Field(None, description="MIME type of the file")

class ComplianceDocumentCreate(ComplianceDocumentBase):
    """Schema for creating a compliance document."""
    application_id: str = Field(..., description="Associated FEMA application ID")

class ComplianceDocument(ComplianceDocumentBase):
    """Schema for a complete compliance document."""
    id: str
    application_id: str
    file_path: str
    upload_date: datetime
    last_accessed: Optional[datetime]
    compliance_status: ComplianceStatus
    review_notes: Optional[str]
    version: int
    is_archived: bool
    archive_path: Optional[str]
    archive_date: Optional[datetime]
    checksum: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ComplianceReviewBase(BaseModel):
    """Base schema for compliance review."""
    review_type: str = Field(..., description="Type of review (initial, appeal, amendment)")
    findings: List[Dict] = Field(default_factory=list, description="List of compliance findings")
    requirements_met: List[str] = Field(default_factory=list, description="List of met requirements")
    deficiencies: List[Dict] = Field(default_factory=list, description="List of deficiencies")
    recommendations: Optional[str] = Field(None, description="Review recommendations")

class ComplianceReviewCreate(ComplianceReviewBase):
    """Schema for creating a compliance review."""
    application_id: str = Field(..., description="Associated FEMA application ID")
    document_id: str = Field(..., description="Primary document being reviewed")
    policy_version_id: str = Field(..., description="Policy version used for review")

class ComplianceReviewUpdate(BaseModel):
    """Schema for updating a compliance review."""
    status: Optional[ApplicationStatus] = None
    findings: Optional[List[Dict]] = None
    requirements_met: Optional[List[str]] = None
    deficiencies: Optional[List[Dict]] = None
    recommendations: Optional[str] = None

class ComplianceReview(ComplianceReviewBase):
    """Schema for a complete compliance review."""
    id: str
    application_id: str
    document_id: str
    policy_version_id: str
    status: ApplicationStatus
    confidence_score: Optional[float]
    reviewer_id: int
    review_date: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ReviewDocumentLink(BaseModel):
    """Schema for linking supporting documents to a review."""
    review_id: str = Field(..., description="Review ID")
    document_id: str = Field(..., description="Supporting document ID")

    class Config:
        orm_mode = True 