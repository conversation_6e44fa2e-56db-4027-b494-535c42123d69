from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List, Dict, Any

class GeoLocationBase(BaseModel):
    project_id: int
    location_type: str  # building, area, infrastructure
    latitude: float
    longitude: float
    address: str

class GeoLocationCreate(GeoLocationBase):
    pass

class GeoLocationResponse(GeoLocationBase):
    id: int
    
    class Config:
        from_attributes = True

class FloodZoneBase(BaseModel):
    zone_code: str
    description: str
    risk_level: str
    boundary: Dict[str, Any]  # GeoJSON format

class FloodZoneCreate(FloodZoneBase):
    pass

class FloodZoneResponse(FloodZoneBase):
    id: int
    
    class Config:
        from_attributes = True

class RiskMapBase(BaseModel):
    name: str
    description: str
    map_data: Dict[str, Any]  # JSON format
    last_updated: datetime
    risk_level: str

class RiskMapCreate(RiskMapBase):
    pass

class RiskMapResponse(RiskMapBase):
    id: int
    
    class Config:
        from_attributes = True
