"""API endpoints for FEMA documentation checklist management."""

from typing import List, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_active_user
from app.documents.services import DocumentService
from documentation_checklist_service import DocumentationChecklistService
from documentation_checklist import DocumentationStatus

router = APIRouter(prefix="/api/v1/checklists", tags=["checklists"])

def get_checklist_service(
    db: Session = Depends(get_db),
    document_service: DocumentService = Depends()
) -> DocumentationChecklistService:
    """Dependency to get checklist service instance."""
    return DocumentationChecklistService(db, document_service)

@router.post("/create")
async def create_checklist(
    categories: List[str],
    service: DocumentationChecklistService = Depends(get_checklist_service),
    current_user = Depends(get_current_active_user)
):
    """Create a new documentation checklist."""
    checklist = await service.create_checklist(categories)
    return {
        "message": "Checklist created successfully",
        "categories": categories,
        "report": checklist.generate_report()
    }

@router.post("/link-document")
async def link_document(
    document_id: str,
    category: str,
    requirement: str,
    status: str = DocumentationStatus.COMPLETE,
    notes: str = "",
    service: DocumentationChecklistService = Depends(get_checklist_service),
    current_user = Depends(get_current_active_user)
):
    """Link a document to a checklist requirement."""
    try:
        checklist_doc = await service.link_document_to_requirement(
            document_id=document_id,
            category=category,
            requirement=requirement,
            status=status,
            notes=notes
        )
        return {
            "message": "Document linked successfully",
            "checklist_document": {
                "id": checklist_doc.id,
                "document_id": checklist_doc.document_id,
                "category": checklist_doc.category,
                "requirement": checklist_doc.requirement,
                "status": checklist_doc.status,
                "notes": checklist_doc.notes
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/status/{category}")
async def get_requirement_documents(
    category: str,
    requirement: str,
    service: DocumentationChecklistService = Depends(get_checklist_service),
    current_user = Depends(get_current_active_user)
):
    """Get all documents linked to a specific requirement."""
    documents = await service.get_documents_for_requirement(category, requirement)
    return {
        "category": category,
        "requirement": requirement,
        "documents": [
            {
                "id": doc.id,
                "title": doc.title,
                "filename": doc.filename,
                "updated_at": doc.updated_at
            }
            for doc in documents
        ]
    }

@router.get("/report")
async def generate_report(
    categories: List[str],
    service: DocumentationChecklistService = Depends(get_checklist_service),
    current_user = Depends(get_current_active_user)
):
    """Generate a comprehensive compliance report."""
    report = await service.generate_compliance_report(categories)
    return report

@router.get("/validate")
async def validate_coverage(
    categories: List[str],
    service: DocumentationChecklistService = Depends(get_checklist_service),
    current_user = Depends(get_current_active_user)
):
    """Validate documentation coverage for specified categories."""
    missing_docs = await service.validate_documentation_coverage(categories)
    return {
        "categories": categories,
        "missing_requirements": missing_docs
    } 