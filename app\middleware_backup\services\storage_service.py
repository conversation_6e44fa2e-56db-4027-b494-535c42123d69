import os
import shutil
import uuid
import logging
import aiofiles
from fastapi import UploadFile, HTTPException, status
from datetime import datetime
from pathlib import Path
import hashlib

from app.core.config import settings

logger = logging.getLogger(__name__)

class StorageService:
    """
    Service for handling document storage and retrieval.
    """
    
    def __init__(self):
        """Initialize the storage service with configured directories."""
        self.upload_dir = settings.UPLOAD_DIR
        self.storage_dir = settings.STORAGE_DIR
        
        # Ensure directories exist
        os.makedirs(self.upload_dir, exist_ok=True)
        os.makedirs(self.storage_dir, exist_ok=True)
    
    async def save_upload_file(self, file: UploadFile, user_id: int) -> dict:
        """
        Save an uploaded file to storage and return file metadata.
        
        Args:
            file (UploadFile): The uploaded file
            user_id (int): ID of the user uploading the file
            
        Returns:
            dict: File metadata including path, hash, etc.
        """
        try:
            # Create user directory if it doesn't exist
            user_dir = os.path.join(self.storage_dir, str(user_id))
            os.makedirs(user_dir, exist_ok=True)
            
            # Generate a unique filename
            original_filename = file.filename
            filename = self._generate_safe_filename(original_filename)
            
            # Full path to save the file
            file_path = os.path.join(user_dir, filename)
            
            # Save the file
            file_hash = await self._save_file_with_hash(file, file_path)
            
            # Generate file metadata
            file_size = os.path.getsize(file_path)
            
            return {
                "filename": original_filename,
                "file_path": file_path,
                "file_hash": file_hash,
                "file_size": file_size,
                "mime_type": file.content_type,
                "upload_date": datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"Error saving uploaded file: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error saving file: {str(e)}"
            )
    
    async def delete_file(self, file_path: str) -> bool:
        """
        Delete a file from storage.
        
        Args:
            file_path (str): Path to the file to delete
            
        Returns:
            bool: True if successfully deleted, False otherwise
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted file: {file_path}")
                return True
            else:
                logger.warning(f"File not found for deletion: {file_path}")
                return False
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {str(e)}", exc_info=True)
            return False
    
    def get_file_info(self, file_path: str) -> dict:
        """
        Get file information.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            dict: File metadata
        """
        try:
            if not os.path.exists(file_path):
                return {"error": "File not found"}
                
            stats = os.stat(file_path)
            file_size = stats.st_size
            modified_date = datetime.fromtimestamp(stats.st_mtime).isoformat()
            filename = os.path.basename(file_path)
            
            return {
                "filename": filename,
                "file_path": file_path,
                "file_size": file_size,
                "modified_date": modified_date
            }
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {str(e)}", exc_info=True)
            return {"error": str(e)}
    
    def _generate_safe_filename(self, original_filename: str) -> str:
        """
        Generate a safe filename that avoids collisions and path traversal.
        
        Args:
            original_filename (str): Original filename from the upload
            
        Returns:
            str: Safe filename
        """
        # Get file extension
        ext = ""
        if "." in original_filename:
            ext = "." + original_filename.rsplit(".", 1)[1].lower()
            
        # Generate a UUID-based filename
        safe_name = f"{uuid.uuid4().hex}{ext}"
        
        return safe_name
    
    async def _save_file_with_hash(self, upload_file: UploadFile, file_path: str) -> str:
        """
        Save a file to disk and calculate its hash.
        
        Args:
            upload_file (UploadFile): The uploaded file
            file_path (str): Path to save the file
            
        Returns:
            str: SHA-256 hash of the file
        """
        sha256 = hashlib.sha256()
        
        # Reset file position
        await upload_file.seek(0)
        
        async with aiofiles.open(file_path, "wb") as buffer:
            # Process in 1MB chunks
            chunk_size = 1024 * 1024
            while True:
                chunk = await upload_file.read(chunk_size)
                if not chunk:
                    break
                sha256.update(chunk)
                await buffer.write(chunk)
        
        # Reset file position again
        await upload_file.seek(0)
        
        return sha256.hexdigest()
    
    def verify_file_integrity(self, file_path: str, expected_hash: str) -> bool:
        """
        Verify file integrity by comparing its hash.
        
        Args:
            file_path (str): Path to the file
            expected_hash (str): Expected SHA-256 hash
            
        Returns:
            bool: True if hash matches, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                return False
                
            sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                # Process in 1MB chunks
                chunk_size = 1024 * 1024
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    sha256.update(chunk)
            
            actual_hash = sha256.hexdigest()
            return actual_hash == expected_hash
        except Exception as e:
            logger.error(f"Error verifying file integrity: {str(e)}", exc_info=True)
            return False
