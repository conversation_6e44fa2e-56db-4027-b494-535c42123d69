from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import joinedload
from app.models.bca import BCAAnalysis, BCABenefit, BCACost, BCAMethodology, BCRStatus
from app.models.project import Project
from app.models.user import User
from app.core.config import settings
from fastapi import HTTPException, UploadFile
import numpy as np
import io
import json
import csv
import logging
from decimal import Decimal

logger = logging.getLogger(__name__)

class BCAService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_analysis(
        self,
        project_id: int,
        dr_number: str,
        created_by_id: int,
        project_useful_life: int,
        discount_rate: float,
        price_level: datetime
    ) -> BCAAnalysis:
        """Create a new BCA analysis"""
        try:
            # Verify project exists
            project = await self.db.get(Project, project_id)
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Create analysis with default values for BCR and NPV (will be updated)
            analysis = BCAAnalysis(
                project_id=project_id,
                dr_number=dr_number,
                created_by_id=created_by_id,
                project_useful_life=project_useful_life,
                bcr=0.0,
                npv=0.0
            )
            self.db.add(analysis)

            # Create methodology
            methodology = BCAMethodology(
                analysis=analysis,
                discount_rate=discount_rate,
                price_level=price_level,
                calculation_method="fema_standard"
            )
            self.db.add(methodology)

            await self.db.commit()
            await self.db.refresh(analysis)
            return analysis

        except Exception as e:
            logger.error(f"Error creating BCA analysis: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def add_benefit(
        self,
        analysis_id: UUID,
        category: str,
        annual_value: float,
        documentation: List[Dict],
        assumptions: str
    ) -> BCABenefit:
        """Add a benefit to BCA analysis"""
        try:
            analysis = await self.get_analysis(analysis_id)
            if analysis.status != BCRStatus.DRAFT:
                raise HTTPException(
                    status_code=400,
                    detail="Can only modify analysis in draft status"
                )

            # Calculate present value
            methodology = await self.get_methodology(analysis_id)
            present_value = self._calculate_present_value(
                annual_value,
                analysis.project_useful_life,
                methodology.discount_rate
            )

            benefit = BCABenefit(
                analysis_id=analysis_id,
                category=category,
                annual_value=annual_value,
                present_value=present_value,
                documentation=documentation,
                assumptions=assumptions
            )
            self.db.add(benefit)
            await self.db.commit()
            await self.db.refresh(benefit)
            
            # Update BCR
            await self._update_bcr(analysis_id)
            return benefit

        except Exception as e:
            logger.error(f"Error adding benefit: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def add_cost(
        self,
        analysis_id: UUID,
        category: str,
        amount: float,
        recurring: bool,
        frequency: Optional[str],
        documentation: List[Dict]
    ) -> BCACost:
        """Add a cost to BCA analysis"""
        try:
            analysis = await self.get_analysis(analysis_id)
            if analysis.status != BCRStatus.DRAFT:
                raise HTTPException(
                    status_code=400,
                    detail="Can only modify analysis in draft status"
                )

            # Calculate present value
            methodology = await self.get_methodology(analysis_id)
            present_value = self._calculate_present_value(
                amount,
                analysis.project_useful_life if recurring else 1,
                methodology.discount_rate
            )

            cost = BCACost(
                analysis_id=analysis_id,
                category=category,
                amount=amount,
                recurring=recurring,
                frequency=frequency,
                present_value=present_value,
                documentation=documentation
            )
            self.db.add(cost)
            await self.db.commit()
            await self.db.refresh(cost)
            
            # Update BCR
            await self._update_bcr(analysis_id)
            return cost

        except Exception as e:
            logger.error(f"Error adding cost: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def get_analysis(self, analysis_id: UUID) -> BCAAnalysis:
        """Get BCA analysis with all relationships"""
        stmt = select(BCAAnalysis).where(
            BCAAnalysis.id == analysis_id
        ).options(
            joinedload(BCAAnalysis.benefits),
            joinedload(BCAAnalysis.costs),
            joinedload(BCAAnalysis.methodology)
        )
        result = await self.db.execute(stmt)
        analysis = result.unique().scalar_one_or_none()
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        return analysis

    async def get_methodology(self, analysis_id: UUID) -> BCAMethodology:
        """Get the methodology for a specific analysis"""
        stmt = select(BCAMethodology).where(BCAMethodology.analysis_id == analysis_id)
        result = await self.db.execute(stmt)
        methodology = result.scalar_one_or_none()
        if not methodology:
            raise HTTPException(status_code=404, detail="Methodology not found")
        return methodology

    async def submit_for_review(self, analysis_id: UUID) -> BCAAnalysis:
        """Submit BCA analysis for review"""
        analysis = await self.get_analysis(analysis_id)
        
        # Validate analysis before submission
        validation_result = await self.validate_analysis(analysis_id)
        if not validation_result.get("is_valid"):
            raise HTTPException(
                status_code=400,
                detail=f"Analysis failed validation: {validation_result.get('message')}"
            )
            
        analysis.status = BCRStatus.IN_REVIEW
        await self.db.commit()
        await self.db.refresh(analysis)
        return analysis

    async def approve_analysis(
        self,
        analysis_id: UUID,
        approved_by_id: int
    ) -> BCAAnalysis:
        """Approve BCA analysis"""
        analysis = await self.get_analysis(analysis_id)
        if analysis.status != BCRStatus.IN_REVIEW:
            raise HTTPException(
                status_code=400,
                detail="Can only approve analysis in review status"
            )
            
        # Validate one more time before approval
        validation_result = await self.validate_analysis(analysis_id)
        if not validation_result.get("is_valid"):
            raise HTTPException(
                status_code=400,
                detail=f"Analysis failed validation: {validation_result.get('message')}"
            )
            
        analysis.status = BCRStatus.APPROVED
        analysis.approved_by_id = approved_by_id
        await self.db.commit()
        await self.db.refresh(analysis)
        return analysis
        
    async def reject_analysis(
        self,
        analysis_id: UUID,
        reason: str
    ) -> BCAAnalysis:
        """Reject BCA analysis"""
        analysis = await self.get_analysis(analysis_id)
        if analysis.status != BCRStatus.IN_REVIEW:
            raise HTTPException(
                status_code=400,
                detail="Can only reject analysis in review status"
            )
            
        analysis.status = BCRStatus.REJECTED
        # Store rejection reason as a special consideration in the methodology
        methodology = await self.get_methodology(analysis_id)
        rejection_note = f"REJECTED: {reason}"
        
        if methodology.special_considerations:
            methodology.special_considerations += f"\n\n{rejection_note}"
        else:
            methodology.special_considerations = rejection_note
            
        await self.db.commit()
        await self.db.refresh(analysis)
        return analysis

    async def get_analyses_by_project(
        self,
        project_id: int,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[BCAAnalysis], int]:
        """Get all BCA analyses for a project with pagination"""
        stmt = select(BCAAnalysis).where(
            BCAAnalysis.project_id == project_id
        ).options(
            joinedload(BCAAnalysis.methodology)
        ).order_by(
            BCAAnalysis.updated_at.desc()
        ).limit(limit).offset(offset)
        
        count_stmt = select(func.count()).select_from(BCAAnalysis).where(
            BCAAnalysis.project_id == project_id
        )
        
        result = await self.db.execute(stmt)
        count_result = await self.db.execute(count_stmt)
        
        analyses = result.unique().scalars().all()
        total = count_result.scalar_one()
        
        return analyses, total

    async def export_to_bca_toolkit(self, analysis_id: UUID) -> bytes:
        """Export analysis to FEMA BCA Toolkit format"""
        analysis = await self.get_analysis(analysis_id)
        methodology = await self.get_methodology(analysis_id)
        
        # Create CSV-like structure for FEMA format
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header information
        writer.writerow(["FEMA BCA TOOLKIT EXPORT"])
        writer.writerow(["Project ID", str(analysis.project_id)])
        writer.writerow(["DR Number", analysis.dr_number])
        writer.writerow(["Analysis Date", analysis.analysis_date.strftime("%Y-%m-%d")])
        writer.writerow(["BCR", str(analysis.bcr)])
        writer.writerow(["NPV", str(analysis.npv)])
        writer.writerow(["Project Useful Life", str(analysis.project_useful_life)])
        writer.writerow(["Discount Rate", str(methodology.discount_rate)])
        writer.writerow(["Price Level", methodology.price_level.strftime("%Y-%m-%d")])
        writer.writerow(["Calculation Method", methodology.calculation_method])
        writer.writerow([])
        
        # Write benefits
        writer.writerow(["BENEFITS"])
        writer.writerow(["Category", "Annual Value", "Present Value", "Assumptions"])
        for benefit in analysis.benefits:
            writer.writerow([
                benefit.category, 
                str(benefit.annual_value), 
                str(benefit.present_value),
                benefit.assumptions
            ])
        writer.writerow([])
        
        # Write costs
        writer.writerow(["COSTS"])
        writer.writerow(["Category", "Amount", "Recurring", "Frequency", "Present Value"])
        for cost in analysis.costs:
            writer.writerow([
                cost.category, 
                str(cost.amount), 
                "Yes" if cost.recurring else "No",
                cost.frequency or "N/A",
                str(cost.present_value)
            ])
        
        # Return as bytes
        content = output.getvalue().encode("utf-8")
        output.close()
        return content
        
    async def import_from_bca_toolkit(
        self,
        project_id: int,
        created_by_id: int,
        file: UploadFile
    ) -> BCAAnalysis:
        """Import analysis from FEMA BCA Toolkit format"""
        try:
            # Read the file content
            content = await file.read()
            csv_content = content.decode("utf-8").splitlines()
            reader = csv.reader(csv_content)
            
            # Parse header information
            rows = list(reader)
            
            # Extract basic information
            dr_number = None
            project_useful_life = settings.BCA_DEFAULT_PROJECT_LIFE
            discount_rate = settings.BCA_DEFAULT_DISCOUNT_RATE
            price_level = datetime.now()
            
            # Parse the header section
            for i, row in enumerate(rows):
                if len(row) >= 2:
                    if row[0] == "DR Number":
                        dr_number = row[1]
                    elif row[0] == "Project Useful Life":
                        project_useful_life = int(row[1])
                    elif row[0] == "Discount Rate":
                        discount_rate = float(row[1])
                    elif row[0] == "Price Level":
                        try:
                            price_level = datetime.strptime(row[1], "%Y-%m-%d")
                        except ValueError:
                            pass
            
            if not dr_number:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid BCA Toolkit file: missing DR Number"
                )
            
            # Create the analysis
            analysis = await self.create_analysis(
                project_id=project_id,
                dr_number=dr_number,
                created_by_id=created_by_id,
                project_useful_life=project_useful_life,
                discount_rate=discount_rate,
                price_level=price_level
            )
            
            # Parse benefits
            in_benefits_section = False
            for row in rows:
                if not row:
                    in_benefits_section = False
                    continue
                    
                if row[0] == "BENEFITS":
                    in_benefits_section = True
                    continue
                    
                if in_benefits_section and row[0] != "Category" and len(row) >= 4:
                    try:
                        category = row[0]
                        annual_value = float(row[1])
                        assumptions = row[3] if len(row) > 3 else ""
                        
                        await self.add_benefit(
                            analysis_id=analysis.id,
                            category=category,
                            annual_value=annual_value,
                            documentation=[],
                            assumptions=assumptions
                        )
                    except (ValueError, IndexError) as e:
                        logger.warning(f"Error parsing benefit row: {e}")
            
            # Parse costs
            in_costs_section = False
            for row in rows:
                if not row:
                    in_costs_section = False
                    continue
                    
                if row[0] == "COSTS":
                    in_costs_section = True
                    continue
                    
                if in_costs_section and row[0] != "Category" and len(row) >= 5:
                    try:
                        category = row[0]
                        amount = float(row[1])
                        recurring = row[2].lower() == "yes"
                        frequency = row[3] if row[3] != "N/A" else None
                        
                        await self.add_cost(
                            analysis_id=analysis.id,
                            category=category,
                            amount=amount,
                            recurring=recurring,
                            frequency=frequency,
                            documentation=[]
                        )
                    except (ValueError, IndexError) as e:
                        logger.warning(f"Error parsing cost row: {e}")
            
            # Refresh analysis with calculated BCR
            await self._update_bcr(analysis.id)
            return await self.get_analysis(analysis.id)
            
        except Exception as e:
            logger.error(f"Error importing BCA Toolkit file: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def validate_analysis(self, analysis_id: UUID) -> Dict[str, Any]:
        """Validate BCA analysis before submission/approval"""
        try:
            analysis = await self.get_analysis(analysis_id)
            
            # Check for minimum requirements
            if not analysis.benefits:
                return {"is_valid": False, "message": "Analysis must have at least one benefit"}
                
            if not analysis.costs:
                return {"is_valid": False, "message": "Analysis must have at least one cost"}
                
            # Check BCR
            if analysis.bcr < 1.0:
                return {"is_valid": False, "message": "BCR must be at least 1.0 for FEMA approval"}
                
            # Check documentation
            for benefit in analysis.benefits:
                if not benefit.documentation:
                    return {
                        "is_valid": False, 
                        "message": f"Benefit '{benefit.category}' requires documentation"
                    }
                    
            for cost in analysis.costs:
                if not cost.documentation:
                    return {
                        "is_valid": False, 
                        "message": f"Cost '{cost.category}' requires documentation"
                    }
            
            # Check methodology
            methodology = await self.get_methodology(analysis_id)
            if methodology.discount_rate <= 0:
                return {"is_valid": False, "message": "Discount rate must be positive"}
                
            # Validate project useful life based on FEMA guidelines
            if analysis.project_useful_life < 5 or analysis.project_useful_life > 100:
                return {
                    "is_valid": False, 
                    "message": "Project useful life must be between 5 and 100 years per FEMA guidelines"
                }
                
            # All checks passed
            return {"is_valid": True, "message": "Analysis is valid"}
            
        except Exception as e:
            logger.error(f"Error validating analysis: {e}")
            return {"is_valid": False, "message": f"Error during validation: {str(e)}"}
            
    async def validate_methodology(self, methodology_id: UUID) -> Dict[str, Any]:
        """Validate BCA methodology for compliance with FEMA guidelines"""
        stmt = select(BCAMethodology).where(BCAMethodology.id == methodology_id)
        result = await self.db.execute(stmt)
        methodology = result.scalar_one_or_none()
        
        if not methodology:
            return {"is_valid": False, "message": "Methodology not found"}
            
        # Check if using a FEMA-approved discount rate
        if methodology.calculation_method == "fema_standard":
            if abs(methodology.discount_rate - 7.0) > 0.01:  # FEMA standard is 7%
                return {
                    "is_valid": False, 
                    "message": "FEMA standard methodology requires a 7% discount rate"
                }
                
        # All checks passed
        return {"is_valid": True, "message": "Methodology is valid"}

    async def _update_bcr(self, analysis_id: UUID) -> None:
        """Update BCR after adding/modifying benefits or costs"""
        try:
            analysis = await self.get_analysis(analysis_id)
            
            # Sum up benefits
            total_benefits = sum(b.present_value for b in analysis.benefits)
            
            # Sum up costs
            total_costs = sum(c.present_value for c in analysis.costs)
            
            # Calculate BCR and NPV
            if total_costs > 0:
                bcr = total_benefits / total_costs
                npv = total_benefits - total_costs
            else:
                bcr = 0
                npv = 0
                
            # Update analysis
            analysis.bcr = bcr
            analysis.npv = npv
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"Error updating BCR: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    def _calculate_present_value(
        self,
        annual_value: float,
        years: int,
        discount_rate: float
    ) -> float:
        """Calculate present value using FEMA standard formula"""
        if discount_rate == 0:
            return annual_value * years
            
        r = discount_rate / 100  # Convert from percentage to decimal
        
        # Calculate discount factor for each year and sum
        present_value = 0
        for year in range(1, years + 1):
            discount_factor = 1 / ((1 + r) ** year)
            present_value += annual_value * discount_factor
            
        return present_value
        
    def _calculate_npv(
        self,
        benefits: List[float],
        costs: List[float],
        years: int,
        discount_rate: float
    ) -> float:
        """Calculate Net Present Value"""
        r = discount_rate / 100  # Convert from percentage to decimal
        
        # Generate cash flow for each year (benefits - costs)
        cash_flows = []
        for year in range(years + 1):
            if year == 0:  # Initial investment
                cash_flow = -sum(costs)
            else:
                # Annual benefits
                cash_flow = sum(benefits)
            cash_flows.append(cash_flow)
            
        # Calculate NPV
        npv = 0
        for i, cf in enumerate(cash_flows):
            npv += cf / ((1 + r) ** i)
            
        return npv
