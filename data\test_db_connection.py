from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.models.user import User

def test_db_connection():
    """Test database connection and query."""
    try:
        # Test connection
        with engine.connect() as conn:
            print("Successfully connected to database!")

        # Test session and query
        db = SessionLocal()
        try:
            users = db.query(User).all()
            print("\nUsers in database:")
            for user in users:
                print(f"- Email: {user.email}, Disabled: {user.disabled}")
        finally:
            db.close()

    except Exception as e:
        print(f"Error connecting to database: {str(e)}")

if __name__ == "__main__":
    test_db_connection()
