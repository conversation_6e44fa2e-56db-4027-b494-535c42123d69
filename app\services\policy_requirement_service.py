"""Service for managing policy requirements and compliance criteria."""

from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from uuid import uuid4

from app.models.compliance_review import PolicyRequirement

class PolicyRequirementService:
    def __init__(self, db: Session):
        self.db = db

    async def create_requirement(
        self,
        title: str,
        description: str,
        category: str,
        required_documents: List[str],
        compliance_criteria: Dict
    ) -> PolicyRequirement:
        """Create a new policy requirement."""
        requirement = PolicyRequirement(
            id=str(uuid4()),
            title=title,
            description=description,
            category=category,
            required_documents=required_documents,
            compliance_criteria=compliance_criteria
        )
        self.db.add(requirement)
        self.db.commit()
        self.db.refresh(requirement)
        return requirement

    async def get_requirements_by_category(
        self,
        category: str
    ) -> List[PolicyRequirement]:
        """Get all policy requirements for a specific category."""
        return self.db.query(PolicyRequirement).filter(
            PolicyRequirement.category == category
        ).all()

    async def update_requirement(
        self,
        requirement_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None,
        required_documents: Optional[List[str]] = None,
        compliance_criteria: Optional[Dict] = None
    ) -> PolicyRequirement:
        """Update an existing policy requirement."""
        requirement = self.db.query(PolicyRequirement).filter(
            PolicyRequirement.id == requirement_id
        ).first()
        
        if not requirement:
            raise ValueError(f"Requirement {requirement_id} not found")

        if title is not None:
            requirement.title = title
        if description is not None:
            requirement.description = description
        if required_documents is not None:
            requirement.required_documents = required_documents
        if compliance_criteria is not None:
            requirement.compliance_criteria = compliance_criteria

        self.db.commit()
        self.db.refresh(requirement)
        return requirement

    async def delete_requirement(
        self,
        requirement_id: str
    ) -> None:
        """Delete a policy requirement."""
        requirement = self.db.query(PolicyRequirement).filter(
            PolicyRequirement.id == requirement_id
        ).first()
        
        if requirement:
            self.db.delete(requirement)
            self.db.commit()

    async def validate_compliance(
        self,
        requirement_id: str,
        document_content: str,
        supporting_documents: List[Dict]
    ) -> Dict:
        """
        Validate document compliance against a specific requirement.
        Returns validation results including matched criteria and confidence score.
        """
        requirement = self.db.query(PolicyRequirement).filter(
            PolicyRequirement.id == requirement_id
        ).first()
        
        if not requirement:
            raise ValueError(f"Requirement {requirement_id} not found")

        # Initialize validation results
        results = {
            "requirement_id": requirement.id,
            "title": requirement.title,
            "is_compliant": True,
            "matched_criteria": [],
            "missing_criteria": [],
            "confidence_score": 0.0,
            "required_documents_present": True,
            "missing_documents": []
        }

        # Check required documents
        for doc_type in requirement.required_documents:
            if not any(doc["type"] == doc_type for doc in supporting_documents):
                results["required_documents_present"] = False
                results["missing_documents"].append(doc_type)

        # Check compliance criteria
        total_criteria = len(requirement.compliance_criteria)
        matched_count = 0

        for criterion, rules in requirement.compliance_criteria.items():
            if self._check_criterion_compliance(document_content, rules):
                results["matched_criteria"].append(criterion)
                matched_count += 1
            else:
                results["missing_criteria"].append(criterion)

        # Calculate confidence score
        if total_criteria > 0:
            results["confidence_score"] = matched_count / total_criteria

        # Determine overall compliance
        results["is_compliant"] = (
            results["required_documents_present"] and
            results["confidence_score"] >= 0.7  # Minimum threshold
        )

        return results

    def _check_criterion_compliance(
        self,
        content: str,
        rules: Dict
    ) -> bool:
        """
        Check if document content matches a specific compliance criterion.
        Supports various rule types (keywords, patterns, values, etc.).
        """
        if not rules:
            return True

        # Check required keywords
        if "keywords" in rules:
            if not any(keyword.lower() in content.lower() for keyword in rules["keywords"]):
                return False

        # Check required patterns
        if "patterns" in rules:
            import re
            if not any(re.search(pattern, content) for pattern in rules["patterns"]):
                return False

        # Check value ranges
        if "value_range" in rules:
            import re
            values = re.findall(r'\d+(?:\.\d+)?', content)
            if not values:
                return False
            
            min_val = rules["value_range"].get("min")
            max_val = rules["value_range"].get("max")
            
            values = [float(v) for v in values]
            if min_val and not any(v >= min_val for v in values):
                return False
            if max_val and not any(v <= max_val for v in values):
                return False

        return True 