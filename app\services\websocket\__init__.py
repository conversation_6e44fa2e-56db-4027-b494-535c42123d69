from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, List
from datetime import datetime
import json
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, dr_number: str):
        """Connect a new WebSocket client"""
        await websocket.accept()
        if dr_number not in self.active_connections:
            self.active_connections[dr_number] = []
        self.active_connections[dr_number].append(websocket)
        logger.info(f"New WebSocket connection for DR-{dr_number}")

    def disconnect(self, websocket: WebSocket, dr_number: str):
        """Disconnect a WebSocket client"""
        if dr_number in self.active_connections:
            self.active_connections[dr_number].remove(websocket)
            logger.info(f"WebSocket disconnected for DR-{dr_number}")

    async def broadcast_to_disaster(self, dr_number: str, message: dict):
        """Broadcast a message to all clients connected to a specific disaster"""
        if dr_number in self.active_connections:
            dead_connections = []
            for connection in self.active_connections[dr_number]:
                try:
                    await connection.send_json(message)
                except WebSocketDisconnect:
                    dead_connections.append(connection)
                except Exception as e:
                    logger.error(f"Error broadcasting to DR-{dr_number}: {e}")
                    dead_connections.append(connection)
            
            # Clean up dead connections
            for dead in dead_connections:
                self.disconnect(dead, dr_number)

    async def send_update(self, dr_number: str, update_type: str, data: dict):
        """Send an update to all clients connected to a specific disaster"""
        message = {
            "type": update_type,
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.broadcast_to_disaster(dr_number, message)

# Global WebSocket manager instance
websocket_manager = WebSocketManager()
