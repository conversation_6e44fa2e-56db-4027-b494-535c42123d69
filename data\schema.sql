-- ComplianceMax Database Schema

-- TABLES
CREATE TABLE alembic_version (
	version_num VARCHAR(32) NOT NULL, 
	CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);

CREATE TABLE bca_analyses (
	id CHAR(32) NOT NULL, 
	project_id INTEGER NOT NULL, 
	dr_number VARCHAR(50) NOT NULL, 
	version INTEGER NOT NULL, 
	status VARCHAR(9) NOT NULL, 
	bcr FLOAT NOT NULL, 
	npv FLOAT NOT NULL, 
	project_useful_life INTEGER NOT NULL, 
	analysis_date DATETIME NOT NULL, 
	created_by_id INTEGER NOT NULL, 
	approved_by_id INTEGER, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id), 
	FOREIGN KEY(created_by_id) REFERENCES users (id), 
	FOREIG<PERSON> KEY(approved_by_id) REFERENCES users (id)
);

CREATE TABLE bca_benefits (
	id CHAR(32) NOT NULL, 
	analysis_id CHAR(32) NOT NULL, 
	category VARCHAR(100) NOT NULL, 
	annual_value FLOAT NOT NULL, 
	present_value FLOAT NOT NULL, 
	documentation JSON NOT NULL, 
	assumptions TEXT NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(analysis_id) REFERENCES bca_analyses (id)
);

CREATE TABLE bca_costs (
	id CHAR(32) NOT NULL, 
	analysis_id CHAR(32) NOT NULL, 
	category VARCHAR(100) NOT NULL, 
	amount FLOAT NOT NULL, 
	recurring BOOLEAN NOT NULL, 
	frequency VARCHAR(50), 
	present_value FLOAT NOT NULL, 
	documentation JSON NOT NULL, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(analysis_id) REFERENCES bca_analyses (id)
);

CREATE TABLE bca_methodologies (
	id CHAR(32) NOT NULL, 
	analysis_id CHAR(32) NOT NULL, 
	discount_rate FLOAT NOT NULL, 
	price_level DATETIME NOT NULL, 
	calculation_method VARCHAR(50) NOT NULL, 
	special_considerations TEXT, 
	created_at DATETIME NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(analysis_id) REFERENCES bca_analyses (id)
);

CREATE TABLE billing (
	id INTEGER NOT NULL, 
	subscription_id INTEGER NOT NULL, 
	amount FLOAT NOT NULL, 
	billing_date DATETIME NOT NULL, 
	payment_status VARCHAR NOT NULL, 
	payment_method VARCHAR NOT NULL, 
	invoice_number VARCHAR NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(subscription_id) REFERENCES subscriptions (id)
);

CREATE TABLE budgets (
	id INTEGER NOT NULL, 
	project_id INTEGER, 
	fiscal_year INTEGER, 
	total_amount FLOAT, 
	allocated_amount FLOAT, 
	remaining_amount FLOAT, 
	last_updated DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id)
);

CREATE TABLE cbcs_compliance_reviews (
	id INTEGER NOT NULL, 
	requirement_id INTEGER NOT NULL, 
	project_id INTEGER NOT NULL, 
	status VARCHAR NOT NULL, 
	review_date DATETIME NOT NULL, 
	reviewer_id INTEGER NOT NULL, 
	notes VARCHAR NOT NULL, 
	evidence VARCHAR NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(requirement_id) REFERENCES requirements (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id), 
	FOREIGN KEY(reviewer_id) REFERENCES users (id)
);

CREATE TABLE cbcs_scans (
	id INTEGER NOT NULL, 
	document_id INTEGER NOT NULL, 
	status VARCHAR(11) NOT NULL, 
	scan_date DATETIME, 
	results JSON, 
	error_message VARCHAR, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(document_id) REFERENCES documents (id)
);

CREATE TABLE compliance_documents (
	id VARCHAR NOT NULL, 
	application_id VARCHAR, 
	document_type VARCHAR NOT NULL, 
	file_path VARCHAR NOT NULL, 
	original_filename VARCHAR NOT NULL, 
	file_size INTEGER, 
	mime_type VARCHAR, 
	upload_date DATETIME, 
	last_accessed DATETIME, 
	compliance_status VARCHAR(19), 
	review_notes TEXT, 
	version INTEGER, 
	is_archived BOOLEAN, 
	archive_path VARCHAR, 
	archive_date DATETIME, 
	checksum VARCHAR, 
	PRIMARY KEY (id), 
	FOREIGN KEY(application_id) REFERENCES fema_applications (id)
);

CREATE TABLE compliance_reviews (
	id INTEGER NOT NULL, 
	policy_version_id VARCHAR NOT NULL, 
	compliance_score FLOAT NOT NULL, 
	violations JSON NOT NULL, 
	remediation_plan JSON NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(id) REFERENCES reviews (id), 
	FOREIGN KEY(policy_version_id) REFERENCES policy_versions (id)
);

CREATE TABLE compliance_reviews_v2 (
	id VARCHAR NOT NULL, 
	status VARCHAR(10), 
	review_type VARCHAR NOT NULL, 
	confidence_score FLOAT, 
	applicant_document_id VARCHAR, 
	policy_version_id VARCHAR, 
	findings JSON, 
	requirements_met JSON, 
	deficiencies JSON, 
	recommendations TEXT, 
	reviewer_id INTEGER, 
	review_date DATETIME, 
	last_updated DATETIME, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(applicant_document_id) REFERENCES documents (id), 
	FOREIGN KEY(policy_version_id) REFERENCES policy_versions (id), 
	FOREIGN KEY(reviewer_id) REFERENCES users (id)
);

CREATE TABLE cost_benefits (
	id INTEGER NOT NULL, 
	project_id INTEGER, 
	benefit_description VARCHAR, 
	estimated_savings FLOAT, 
	implementation_cost FLOAT, 
	roi_percentage FLOAT, 
	payback_period FLOAT, 
	PRIMARY KEY (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id)
);

CREATE TABLE cost_reviews (
	id INTEGER NOT NULL, 
	total_cost FLOAT NOT NULL, 
	cost_breakdown JSON NOT NULL, 
	alternatives JSON NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(id) REFERENCES reviews (id)
);

CREATE TABLE costs (
	id INTEGER NOT NULL, 
	project_id INTEGER, 
	type VARCHAR(14), 
	amount FLOAT, 
	description VARCHAR, 
	date_incurred DATETIME, 
	category VARCHAR, 
	vendor VARCHAR, 
	invoice_number VARCHAR, 
	PRIMARY KEY (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id)
);

CREATE TABLE disasters (
	id VARCHAR NOT NULL, 
	dr_number VARCHAR NOT NULL, 
	incident_type VARCHAR NOT NULL, 
	incident_period_start DATETIME NOT NULL, 
	incident_period_end DATETIME, 
	declaration_date DATETIME NOT NULL, 
	state VARCHAR NOT NULL, 
	affected_areas JSON, 
	fema_metadata JSON, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id)
);

CREATE TABLE document_versions (
	id INTEGER NOT NULL, 
	document_id INTEGER NOT NULL, 
	version_number INTEGER NOT NULL, 
	created_at DATETIME NOT NULL, 
	created_by_id INTEGER NOT NULL, 
	title VARCHAR NOT NULL, 
	description VARCHAR, 
	fema_id VARCHAR, 
	compliance_status VARCHAR NOT NULL, 
	compliance_percentage FLOAT, 
	compliance_notes VARCHAR, 
	last_compliance_check DATETIME, 
	due_date DATETIME, 
	risk_level VARCHAR NOT NULL, 
	disaster_number VARCHAR, 
	disaster_type VARCHAR, 
	incident_period_start DATETIME, 
	incident_period_end DATETIME, 
	declaration_date DATETIME, 
	mitigation_status VARCHAR, 
	mitigation_plan_expires DATETIME, 
	mitigation_funding_amount FLOAT, 
	requirements JSON, 
	last_review_date DATETIME, 
	next_review_date DATETIME, 
	review_frequency_days INTEGER, 
	jurisdiction VARCHAR, 
	state_code VARCHAR, 
	county_code VARCHAR, 
	change_summary VARCHAR NOT NULL, 
	change_type VARCHAR NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(document_id) REFERENCES documents (id) ON DELETE CASCADE, 
	FOREIGN KEY(created_by_id) REFERENCES users (id)
);

CREATE TABLE documents (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	filename VARCHAR(255) NOT NULL, 
	file_path VARCHAR(512) NOT NULL, 
	mime_type VARCHAR(128), 
	status VARCHAR(50), 
	document_type VARCHAR(9) NOT NULL, 
	compliance_status VARCHAR(19) NOT NULL, 
	risk_level VARCHAR(8), 
	mitigation_status VARCHAR(11), 
	description TEXT, 
	review_date DATETIME, 
	review_notes TEXT, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(user_id) REFERENCES users (id)
);

CREATE TABLE ehp_review_documents_v2 (
	review_id INTEGER NOT NULL, 
	document_id INTEGER NOT NULL, 
	document_type VARCHAR NOT NULL, 
	added_at DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL, 
	PRIMARY KEY (review_id, document_id), 
	FOREIGN KEY(review_id) REFERENCES ehp_reviews_v2 (id), 
	FOREIGN KEY(document_id) REFERENCES documents (id)
);

CREATE TABLE ehp_reviews (
	id INTEGER NOT NULL, 
	environmental_impacts JSON NOT NULL, 
	historical_considerations JSON NOT NULL, 
	required_permits JSON NOT NULL, 
	mitigation_measures JSON NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(id) REFERENCES reviews (id)
);

CREATE TABLE ehp_reviews_v2 (
	id INTEGER NOT NULL, 
	project_id INTEGER NOT NULL, 
	reviewer_id INTEGER NOT NULL, 
	review_type VARCHAR(12) NOT NULL, 
	status VARCHAR(11) NOT NULL, 
	environmental_impacts JSON NOT NULL, 
	historical_considerations JSON NOT NULL, 
	required_permits JSON NOT NULL, 
	mitigation_measures JSON NOT NULL, 
	review_date DATETIME NOT NULL, 
	completed_date DATETIME, 
	notes VARCHAR, 
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id), 
	FOREIGN KEY(reviewer_id) REFERENCES users (id)
);

CREATE TABLE fema_applications (
	id VARCHAR NOT NULL, 
	disaster_number VARCHAR NOT NULL, 
	applicant_id VARCHAR NOT NULL, 
	project_title VARCHAR NOT NULL, 
	status VARCHAR(10), 
	compliance_status VARCHAR(19), 
	submission_date DATETIME, 
	last_updated DATETIME, 
	user_id INTEGER, 
	PRIMARY KEY (id), 
	FOREIGN KEY(user_id) REFERENCES users (id)
);

CREATE TABLE firmette_requests (
	id INTEGER NOT NULL, 
	latitude FLOAT NOT NULL, 
	longitude FLOAT NOT NULL, 
	address VARCHAR, 
	status VARCHAR NOT NULL, 
	created_at DATETIME NOT NULL, 
	completed_at DATETIME, 
	flood_map_id INTEGER, 
	user_id INTEGER NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(flood_map_id) REFERENCES flood_maps (id), 
	FOREIGN KEY(user_id) REFERENCES users (id)
);

CREATE TABLE flood_maps (
	id INTEGER NOT NULL, 
	firm_panel_id VARCHAR NOT NULL, 
	effective_date DATETIME NOT NULL, 
	map_scale INTEGER NOT NULL, 
	flood_zone VARCHAR NOT NULL, 
	latitude FLOAT NOT NULL, 
	longitude FLOAT NOT NULL, 
	firmette_url VARCHAR NOT NULL, 
	last_updated DATETIME NOT NULL, 
	user_id INTEGER NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(user_id) REFERENCES users (id)
);

CREATE TABLE flood_zones (
	id INTEGER NOT NULL, 
	zone_code VARCHAR NOT NULL, 
	description VARCHAR NOT NULL, 
	risk_level VARCHAR NOT NULL, 
	boundary JSON NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE geo_locations (
	id INTEGER NOT NULL, 
	project_id INTEGER, 
	latitude FLOAT, 
	longitude FLOAT, 
	address VARCHAR, 
	city VARCHAR, 
	state VARCHAR(2), 
	zip_code VARCHAR(10), 
	fema_flood_zone VARCHAR, 
	parcel_id VARCHAR, 
	elevation_data JSON, 
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP), 
	updated_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id)
);

CREATE TABLE mapping_locations (
	id INTEGER NOT NULL, 
	project_id INTEGER NOT NULL, 
	location_type VARCHAR NOT NULL, 
	latitude FLOAT NOT NULL, 
	longitude FLOAT NOT NULL, 
	address VARCHAR NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id)
);

CREATE TABLE mitigation_reviews (
	id INTEGER NOT NULL, 
	cost_estimate FLOAT NOT NULL, 
	recommendations JSON NOT NULL, 
	implementation_timeline INTEGER NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(id) REFERENCES reviews (id)
);

CREATE TABLE policy_disaster_association (
	policy_version_id INTEGER NOT NULL, 
	disaster_id VARCHAR NOT NULL, 
	PRIMARY KEY (policy_version_id, disaster_id), 
	FOREIGN KEY(policy_version_id) REFERENCES policy_versions (id), 
	FOREIGN KEY(disaster_id) REFERENCES disasters (dr_number)
);

CREATE TABLE policy_documents (
	id VARCHAR NOT NULL, 
	policy_number VARCHAR NOT NULL, 
	title VARCHAR NOT NULL, 
	description TEXT, 
	policy_type VARCHAR(10), 
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP), 
	updated_at DATETIME, 
	PRIMARY KEY (id)
);

CREATE TABLE policy_requirements (
	id VARCHAR NOT NULL, 
	title VARCHAR NOT NULL, 
	description VARCHAR NOT NULL, 
	category VARCHAR NOT NULL, 
	required_documents VARCHAR, 
	compliance_criteria VARCHAR, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id)
);

CREATE TABLE policy_versions (
	id VARCHAR NOT NULL, 
	policy_id VARCHAR, 
	version_number VARCHAR NOT NULL, 
	content TEXT NOT NULL, 
	effective_date DATETIME NOT NULL, 
	expiration_date DATETIME, 
	document_url VARCHAR, 
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP), 
	changes_summary TEXT, 
	approval_status VARCHAR, 
	approved_by INTEGER, 
	updated_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(policy_id) REFERENCES policy_documents (id), 
	FOREIGN KEY(approved_by) REFERENCES users (id)
);

CREATE TABLE projects (
	id INTEGER NOT NULL, 
	name VARCHAR, 
	description VARCHAR, 
	owner_id INTEGER, 
	is_active BOOLEAN, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(owner_id) REFERENCES users (id)
);

CREATE TABLE report_templates (
	id INTEGER NOT NULL, 
	name VARCHAR NOT NULL, 
	description VARCHAR NOT NULL, 
	type VARCHAR(10) NOT NULL, 
	template_data JSON NOT NULL, 
	is_system BOOLEAN NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE reports (
	id INTEGER NOT NULL, 
	title VARCHAR NOT NULL, 
	type VARCHAR(10) NOT NULL, 
	parameters JSON NOT NULL, 
	created_at DATETIME NOT NULL, 
	created_by INTEGER NOT NULL, 
	data JSON NOT NULL, 
	format VARCHAR NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(created_by) REFERENCES users (id)
);

CREATE TABLE requirements (
	id INTEGER NOT NULL, 
	standard_id INTEGER NOT NULL, 
	code VARCHAR NOT NULL, 
	description VARCHAR NOT NULL, 
	criteria VARCHAR NOT NULL, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id), 
	FOREIGN KEY(standard_id) REFERENCES standards (id)
);

CREATE TABLE review_documents (
	review_id INTEGER NOT NULL, 
	document_id INTEGER NOT NULL, 
	added_at DATETIME DEFAULT (CURRENT_TIMESTAMP), 
	PRIMARY KEY (review_id, document_id), 
	FOREIGN KEY(review_id) REFERENCES compliance_reviews_v2 (id), 
	FOREIGN KEY(document_id) REFERENCES documents (id)
);

CREATE TABLE review_history (
	id INTEGER NOT NULL, 
	review_id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	change_type VARCHAR(15) NOT NULL, 
	previous_state JSON, 
	new_state JSON NOT NULL, 
	timestamp DATETIME NOT NULL, 
	comment VARCHAR, 
	PRIMARY KEY (id), 
	FOREIGN KEY(review_id) REFERENCES reviews (id), 
	FOREIGN KEY(user_id) REFERENCES users (id)
);

CREATE TABLE reviews (
	id INTEGER NOT NULL, 
	type VARCHAR(10) NOT NULL, 
	status VARCHAR(11) NOT NULL, 
	project_id INTEGER NOT NULL, 
	reviewer_id INTEGER NOT NULL, 
	findings JSON NOT NULL, 
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL, 
	updated_at DATETIME NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(project_id) REFERENCES projects (id), 
	FOREIGN KEY(reviewer_id) REFERENCES users (id)
);

CREATE TABLE risk_maps (
	id INTEGER NOT NULL, 
	name VARCHAR NOT NULL, 
	description VARCHAR NOT NULL, 
	map_data JSON NOT NULL, 
	last_updated DATETIME NOT NULL, 
	risk_level VARCHAR NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE standards (
	id INTEGER NOT NULL, 
	code VARCHAR NOT NULL, 
	title VARCHAR NOT NULL, 
	description VARCHAR NOT NULL, 
	version VARCHAR NOT NULL, 
	effective_date DATETIME NOT NULL, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id)
);

CREATE TABLE subscription_features (
	id INTEGER NOT NULL, 
	tier VARCHAR(12) NOT NULL, 
	feature_name VARCHAR NOT NULL, 
	feature_description VARCHAR NOT NULL, 
	is_enabled BOOLEAN NOT NULL, 
	PRIMARY KEY (id)
);

CREATE TABLE subscriptions (
	id INTEGER NOT NULL, 
	user_id INTEGER NOT NULL, 
	tier VARCHAR(12) NOT NULL, 
	start_date DATETIME NOT NULL, 
	end_date DATETIME NOT NULL, 
	is_active BOOLEAN NOT NULL, 
	auto_renew BOOLEAN NOT NULL, 
	PRIMARY KEY (id), 
	FOREIGN KEY(user_id) REFERENCES users (id)
);

CREATE TABLE users (
	id INTEGER NOT NULL, 
	username VARCHAR NOT NULL, 
	email VARCHAR NOT NULL, 
	full_name VARCHAR, 
	hashed_password VARCHAR NOT NULL, 
	is_active BOOLEAN, 
	totp_secret VARCHAR, 
	is_two_factor_enabled BOOLEAN, 
	created_at DATETIME, 
	updated_at DATETIME, 
	PRIMARY KEY (id)
);

-- INDICES
CREATE INDEX ix_bca_analyses_dr_number ON bca_analyses (dr_number);

CREATE INDEX ix_billing_id ON billing (id);

CREATE INDEX ix_budgets_id ON budgets (id);

CREATE INDEX ix_cbcs_compliance_reviews_id ON cbcs_compliance_reviews (id);

CREATE INDEX ix_cbcs_scans_id ON cbcs_scans (id);

CREATE INDEX ix_cost_benefits_id ON cost_benefits (id);

CREATE INDEX ix_costs_id ON costs (id);

CREATE UNIQUE INDEX ix_disasters_dr_number ON disasters (dr_number);

CREATE INDEX ix_document_versions_id ON document_versions (id);

CREATE INDEX ix_documents_id ON documents (id);

CREATE INDEX ix_ehp_reviews_v2_id ON ehp_reviews_v2 (id);

CREATE INDEX ix_flood_maps_firm_panel_id ON flood_maps (firm_panel_id);

CREATE INDEX ix_flood_zones_id ON flood_zones (id);

CREATE INDEX ix_geo_locations_id ON geo_locations (id);

CREATE INDEX ix_mapping_locations_id ON mapping_locations (id);

CREATE INDEX ix_policy_documents_id ON policy_documents (id);

CREATE INDEX ix_policy_documents_policy_number ON policy_documents (policy_number);

CREATE INDEX ix_policy_versions_id ON policy_versions (id);

CREATE INDEX ix_projects_id ON projects (id);

CREATE INDEX ix_projects_name ON projects (name);

CREATE INDEX ix_report_templates_id ON report_templates (id);

CREATE INDEX ix_reports_id ON reports (id);

CREATE INDEX ix_requirements_id ON requirements (id);

CREATE INDEX ix_review_history_id ON review_history (id);

CREATE INDEX ix_reviews_id ON reviews (id);

CREATE INDEX ix_risk_maps_id ON risk_maps (id);

CREATE UNIQUE INDEX ix_standards_code ON standards (code);

CREATE INDEX ix_standards_id ON standards (id);

CREATE INDEX ix_subscription_features_id ON subscription_features (id);

CREATE INDEX ix_subscriptions_id ON subscriptions (id);

CREATE UNIQUE INDEX ix_users_email ON users (email);

CREATE INDEX ix_users_id ON users (id);

CREATE UNIQUE INDEX ix_users_username ON users (username);

-- TRIGGERS
-- VIEWS
