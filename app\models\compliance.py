from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, JSON, Text, Float, Enum as SQLE<PERSON>, func, Boolean
from sqlalchemy.orm import relationship
from app.models.base import Base, TimestampMixin
from uuid import uuid4
import enum
from enum import Enum as PyEnum
from typing import Optional, List, Dict
from app.models.enums import ComplianceStatus, RiskLevel, ValidationStatus

class ApplicationStatus(str, enum.Enum):
    PENDING = "pending"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_INFO = "needs_info"

class ComplianceStatus(str, PyEnum):
    UNDER_REVIEW = "under_review"
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    NEEDS_DOCUMENTATION = "needs_documentation"
    REQUIRES_REVIEW = "requires_review"

class RiskLevel(str, PyEnum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ScanStatus(str, PyEnum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class ReviewStatus(str, PyEnum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"

class ComplianceReviewV2(Base, TimestampMixin):
    __tablename__ = "compliance_reviews_v2"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    
    # Review metadata
    status = Column(SQLEnum(ApplicationStatus), default=ApplicationStatus.PENDING)
    review_type = Column(String, nullable=False)  # e.g., "initial", "appeal", "amendment"
    confidence_score = Column(Float)
    
    # Document references
    applicant_document_id = Column(String, ForeignKey("documents.id"))
    policy_version_id = Column(String, ForeignKey("policy_versions.id"))
    
    # Review content
    findings = Column(JSON, default=list)  # List of compliance findings
    requirements_met = Column(JSON, default=list)  # List of met requirements
    deficiencies = Column(JSON, default=list)  # List of deficiencies
    recommendations = Column(Text)
    
    # Review process
    reviewer_id = Column(Integer, ForeignKey("users.id"))
    review_date = Column(DateTime, default=datetime.utcnow)
    last_updated = Column(DateTime, onupdate=datetime.utcnow)
    
    # Relationships
    applicant_document = relationship("Document", foreign_keys=[applicant_document_id])
    policy_version = relationship("PolicyVersion", foreign_keys=[policy_version_id])
    reviewer = relationship("User")
    
    def __repr__(self):
        return f"<ComplianceReviewV2 {self.id} ({self.status})>"

class ReviewDocument(Base):
    __tablename__ = "review_documents"
    
    review_id = Column(Integer, ForeignKey("compliance_reviews_v2.id"), primary_key=True)
    document_id = Column(Integer, ForeignKey("documents.id"), primary_key=True)
    added_at = Column(DateTime, server_default=func.now())
    
    # Relationships
    review = relationship("ComplianceReviewV2")
    document = relationship("Document")

class FEMAApplication(Base):
    __tablename__ = "fema_applications"

    id = Column(String, primary_key=True)
    disaster_number = Column(String, nullable=False)
    applicant_id = Column(String, nullable=False)
    project_title = Column(String, nullable=False)
    status = Column(SQLEnum(ApplicationStatus), default=ApplicationStatus.PENDING)
    compliance_status = Column(SQLEnum(ComplianceStatus), default=ComplianceStatus.UNDER_REVIEW)
    submission_date = Column(DateTime, default=datetime.utcnow)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    user = relationship("User", back_populates="applications")
    documents = relationship("ComplianceDocument", back_populates="application")

class ComplianceDocument(Base):
    __tablename__ = "compliance_documents"

    id = Column(String, primary_key=True)
    application_id = Column(String, ForeignKey("fema_applications.id"))
    document_type = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    file_size = Column(Integer)  # Size in bytes
    mime_type = Column(String)
    upload_date = Column(DateTime, default=datetime.utcnow)
    last_accessed = Column(DateTime)
    compliance_status = Column(SQLEnum(ComplianceStatus), default=ComplianceStatus.UNDER_REVIEW)
    review_notes = Column(Text)
    version = Column(Integer, default=1)
    is_archived = Column(Boolean, default=False)
    archive_path = Column(String)
    archive_date = Column(DateTime)
    checksum = Column(String)  # For file integrity verification
    
    # Relationships
    application = relationship("FEMAApplication", back_populates="documents")
    reviews = relationship("ComplianceReviewV2", back_populates="document")
    
    def __repr__(self):
        return f"<ComplianceDocument {self.id} ({self.document_type})>"

class PolicyRequirement(Base, TimestampMixin):
    """Model for storing policy requirements and compliance criteria.
    
    This model represents a policy requirement that documents must comply with.
    It includes validation criteria, required evidence, and scoring mechanisms.
    """
    __tablename__ = "policy_requirements"

    # Primary key and identifiers
    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    policy_version_id = Column(String, ForeignKey("policy_versions.id"), nullable=True, index=True)
    
    # Basic information
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String, nullable=False, index=True)
    subcategory = Column(String, nullable=True, index=True)
    
    # Requirement details
    requirement_text = Column(Text, nullable=False)
    required_documents = Column(JSON, nullable=False, default=list)  # List of required document types
    required_fields = Column(JSON, nullable=False, default=list)  # List of required fields
    required_keywords = Column(JSON, nullable=True)  # Keywords that should be present
    
    # Validation and compliance criteria
    validation_criteria = Column(JSON, nullable=False)  # Criteria for validating compliance
    validation_rules = Column(JSON, nullable=False)  # Rules for automated validation
    scoring_criteria = Column(JSON, nullable=False)  # Criteria for scoring compliance
    scoring_weight = Column(Float, nullable=False, default=1.0)  # Weight in overall compliance score
    confidence_threshold = Column(Float, nullable=False, default=0.8)  # Minimum confidence for validation
    
    # Status and tracking
    status = Column(String, nullable=False, default="active")
    effective_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    expiration_date = Column(DateTime, nullable=True)
    source = Column(String, nullable=True)  # Source of the requirement (e.g., "FEMA", "State")
    is_active = Column(Boolean, nullable=False, default=True)
    
    # Relationships
    policy_version = relationship("PolicyVersion", backref="requirements")
    
    def __repr__(self):
        return f"<PolicyRequirement(id={self.id}, category={self.category}, title={self.title})>"

    def to_dict(self) -> dict:
        """Convert instance to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "category": self.category,
            "subcategory": self.subcategory,
            "requirement_text": self.requirement_text,
            "required_documents": self.required_documents,
            "required_fields": self.required_fields,
            "validation_criteria": self.validation_criteria,
            "scoring_criteria": self.scoring_criteria,
            "scoring_weight": self.scoring_weight,
            "status": self.status,
            "effective_date": self.effective_date.isoformat() if self.effective_date else None,
            "expiration_date": self.expiration_date.isoformat() if self.expiration_date else None,
            "source": self.source,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

class CBCSScan(Base):
    """Model for tracking Compliance Based Control System scans."""
    __tablename__ = "cbcs_scans"

    id = Column(Integer, primary_key=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    status = Column(SQLEnum(ScanStatus), nullable=False, default=ScanStatus.PENDING)
    scan_date = Column(DateTime, nullable=True)
    results = Column(JSON, nullable=True)
    error_message = Column(String, nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    document = relationship("Document", back_populates="cbcs_scans")

    def __repr__(self):
        return f"<CBCSScan {self.id} ({self.status})>"

class CBCSComplianceReview(Base):
    """Model for tracking CBCS compliance reviews."""
    __tablename__ = "cbcs_compliance_reviews"

    id = Column(Integer, primary_key=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    review_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    status = Column(SQLEnum(ReviewStatus), nullable=False, default=ReviewStatus.PENDING)
    findings = Column(JSON, nullable=True)
    score = Column(Float, nullable=True)
    risk_level = Column(String, nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    document = relationship("Document", back_populates="cbcs_reviews")
    reviewer = relationship("User", back_populates="cbcs_reviews")

    def __repr__(self):
        return f"<CBCSComplianceReview {self.id} ({self.status})>"

class ValidationStatus(str, PyEnum):
    """Status of validation checks."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class ComplianceResult(Base):
    """Model for storing compliance validation results."""
    __tablename__ = "compliance_results"

    id = Column(String, primary_key=True, index=True)
    document_id = Column(String, ForeignKey("documents.id"), nullable=False, index=True)
    application_id = Column(String, ForeignKey("applications.id"), nullable=False, index=True)
    validation_status = Column(String, nullable=False, default=ValidationStatus.PENDING)
    compliance_status = Column(String, nullable=False, default=ComplianceStatus.NEEDS_DOCUMENTATION)
    risk_level = Column(String, nullable=False, default=RiskLevel.MEDIUM)
    confidence_score = Column(Float, nullable=False, default=0.0)
    findings = Column(JSON, nullable=False, default=dict)
    deficiencies = Column(JSON, nullable=False, default=dict)
    recommendations = Column(JSON, nullable=False, default=list)
    result_metadata = Column(JSON, nullable=True)
    validator_version = Column(String, nullable=False)
    validated_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    document = relationship("Document", back_populates="compliance_results")
    application = relationship("Application", back_populates="compliance_results")

    def __repr__(self):
        return f"<ComplianceResult(id={self.id}, status={self.compliance_status}, risk_level={self.risk_level})>"

class ComplianceRequirement(Base):
    """Model for storing compliance requirements."""
    __tablename__ = "compliance_requirements"

    id = Column(String, primary_key=True, index=True)
    category = Column(String, nullable=False, index=True)
    subcategory = Column(String, nullable=True, index=True)
    description = Column(String, nullable=False)
    validation_rules = Column(JSON, nullable=False)
    required_fields = Column(JSON, nullable=False)
    scoring_weight = Column(Float, nullable=False, default=1.0)
    is_active = Column(Boolean, nullable=False, default=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<ComplianceRequirement(id={self.id}, category={self.category})>"
