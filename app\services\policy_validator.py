from datetime import datetime
from typing import List, Dict, Optional
from beanie import Document, Link
from pydantic import BaseModel, Field
import logging
from app.core.celery_app import celery_app
from app.core.cache import RedisCache

logger = logging.getLogger(__name__)

class PolicyRequirement(BaseModel):
    """Pydantic model for policy requirements validation."""
    id: str
    title: str
    description: str
    category: str
    subcategory: Optional[str] = None
    requirement_text: str
    required_documents: List[str] = Field(default_factory=list)
    required_fields: List[str] = Field(default_factory=list)
    required_keywords: Optional[List[str]] = None
    validation_criteria: Dict
    validation_rules: Dict
    scoring_criteria: Dict
    scoring_weight: float = 1.0
    confidence_threshold: float = 0.8
    status: str = "active"
    effective_date: datetime
    expiration_date: Optional[datetime] = None
    source: Optional[str] = None
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        """Pydantic config."""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class PolicyVersion(Document):
    policy_id: str
    version_number: str
    content: str
    effective_date: datetime
    expiration_date: Optional[datetime]
    requirements: List[PolicyRequirement]
    metadata: Dict

    class Settings:
        name = "policy_versions"
        indexes = [
            "policy_id",
            "effective_date",
            "expiration_date"
        ]

class PolicyValidationService:
    def __init__(self):
        self.cache = RedisCache()
        self.cache_ttl = 3600  # 1 hour

    async def get_applicable_policies(
        self, 
        dr_number: str, 
        incident_date: datetime
    ) -> List[PolicyVersion]:
        """Get all policies applicable to a specific disaster declaration."""
        cache_key = f"applicable_policies:{dr_number}:{incident_date.isoformat()}"
        
        # Try to get from cache first
        cached_result = await self.cache.get(cache_key)
        if cached_result:
            return cached_result

        try:
            policies = await PolicyVersion.find(
                {
                    "effective_date": {"$lte": incident_date},
                    "$or": [
                        {"expiration_date": None},
                        {"expiration_date": {"$gte": incident_date}}
                    ]
                }
            ).to_list()

            # Cache the result
            await self.cache.set(cache_key, policies, self.cache_ttl)
            return policies

        except Exception as e:
            logger.error(f"Error fetching applicable policies: {str(e)}")
            raise

    @celery_app.task(name="policy.validate_document")
    async def validate_document(
        self, 
        document_id: str, 
        dr_number: str,
        incident_date: datetime
    ) -> Dict:
        """Validate a document against applicable policies."""
        try:
            # Get applicable policies
            policies = await self.get_applicable_policies(dr_number, incident_date)
            
            validation_results = {
                "document_id": document_id,
                "dr_number": dr_number,
                "validation_date": datetime.utcnow(),
                "results": []
            }

            for policy in policies:
                result = await self._validate_against_policy(document_id, policy)
                validation_results["results"].append(result)

            return validation_results

        except Exception as e:
            logger.error(f"Document validation failed: {str(e)}")
            raise

    async def _validate_against_policy(
        self, 
        document_id: str, 
        policy: PolicyVersion
    ) -> Dict:
        """Validate a document against a specific policy version."""
        try:
            validation_result = {
                "policy_id": policy.policy_id,
                "version": policy.version_number,
                "requirements_met": [],
                "requirements_missing": [],
                "requirements_partial": []
            }

            for requirement in policy.requirements:
                requirement_result = await self._validate_requirement(
                    document_id, 
                    requirement
                )
                
                if requirement_result["status"] == "met":
                    validation_result["requirements_met"].append(requirement_result)
                elif requirement_result["status"] == "missing":
                    validation_result["requirements_missing"].append(requirement_result)
                else:
                    validation_result["requirements_partial"].append(requirement_result)

            validation_result["overall_status"] = (
                "compliant" if not validation_result["requirements_missing"] 
                else "non_compliant"
            )

            return validation_result

        except Exception as e:
            logger.error(f"Policy validation failed: {str(e)}")
            raise

    async def _validate_requirement(
        self, 
        document_id: str, 
        requirement: PolicyRequirement
    ) -> Dict:
        """Validate a specific requirement against a document."""
        try:
            validation_result = {
                "requirement_id": requirement.id,
                "requirement_text": requirement.requirement_text,
                "validation_type": requirement.validation_type,
            }

            # Different validation types
            if requirement.validation_type == "text_presence":
                validation_result.update(
                    await self._validate_text_presence(document_id, requirement)
                )
            elif requirement.validation_type == "date_range":
                validation_result.update(
                    await self._validate_date_range(document_id, requirement)
                )
            elif requirement.validation_type == "numeric_value":
                validation_result.update(
                    await self._validate_numeric_value(document_id, requirement)
                )

            return validation_result

        except Exception as e:
            logger.error(f"Requirement validation failed: {str(e)}")
            raise

    async def _validate_text_presence(
        self, 
        document_id: str, 
        requirement: PolicyRequirement
    ) -> Dict:
        """Validate text presence in document."""
        # Implementation would use NLP to check for semantic presence
        # This is a placeholder
        return {
            "status": "met",
            "confidence": 0.85,
            "details": "Required text found in document"
        }

    async def _validate_date_range(
        self, 
        document_id: str, 
        requirement: PolicyRequirement
    ) -> Dict:
        """Validate date ranges in document."""
        # Implementation would extract and validate dates
        # This is a placeholder
        return {
            "status": "met",
            "confidence": 0.90,
            "details": "Dates within required range"
        }

    async def _validate_numeric_value(
        self, 
        document_id: str, 
        requirement: PolicyRequirement
    ) -> Dict:
        """Validate numeric values in document."""
        # Implementation would extract and validate numeric values
        # This is a placeholder
        return {
            "status": "met",
            "confidence": 0.95,
            "details": "Numeric values meet requirements"
        }
