from datetime import datetime
from typing import Optional
from sqlalchemy import String, Integer, DateTime, JSON, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base
from app.models.document import Document

class DocumentVersion(Base):
    __tablename__ = "document_versions"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id", ondelete="CASCADE"))
    version_number: Mapped[int]
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    created_by_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    
    # Store the complete state at this version
    title: Mapped[str] = mapped_column(String)
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    fema_id: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    compliance_status: Mapped[str] = mapped_column(String)
    compliance_percentage: Mapped[Optional[float]] = mapped_column(nullable=True)
    compliance_notes: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    last_compliance_check: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    due_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    risk_level: Mapped[str] = mapped_column(String)
    
    # Disaster declaration fields
    disaster_number: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    disaster_type: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    incident_period_start: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    incident_period_end: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    declaration_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Mitigation fields
    mitigation_status: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    mitigation_plan_expires: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    mitigation_funding_amount: Mapped[Optional[float]] = mapped_column(nullable=True)
    
    # Compliance requirements
    requirements: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    last_review_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    next_review_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    review_frequency_days: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Geographic information
    jurisdiction: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    state_code: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    county_code: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    
    # Change metadata
    change_summary: Mapped[str] = mapped_column(String)
    change_type: Mapped[str] = mapped_column(String)  # e.g., "UPDATE", "COMPLIANCE_CHECK", "MITIGATION_UPDATE"
    
    # Relationships
    document: Mapped[Document] = relationship("Document", back_populates="versions")
    created_by: Mapped["User"] = relationship("User")  # type: ignore
    
    class Config:
        orm_mode = True
