// API configuration
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1';
export const STANDALONE_API_URL = import.meta.env.VITE_STANDALONE_API_URL || 'http://localhost:8000/api/v1';
export const WS_BASE_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8000';

// Document analysis configuration
export const SUPPORTED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain'
] as const;

export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

// UI configuration
export const COMPLIANCE_SCORE_THRESHOLDS = {
  HIGH: 0.8,
  MEDIUM: 0.6,
  LOW: 0.4
} as const;

export const SEVERITY_COLORS = {
  high: 'error',
  medium: 'warning',
  low: 'success'
} as const;

// Analysis settings
export const ANALYSIS_SETTINGS = {
  SIMILARITY_THRESHOLD: 0.7,
  TEMPORAL_MATCH_TOLERANCE_DAYS: 7,
  MIN_REQUIREMENT_MATCH_SCORE: 0.6
} as const;

// WebSocket configuration
export const WS_RECONNECT_MAX_ATTEMPTS = 5;
export const WS_RECONNECT_DELAY = 1000; // Base delay in ms
export const WS_CONNECTION_TIMEOUT = 5000; // 5 seconds

// Cache configuration
export const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
export const MAX_CACHE_SIZE = 100; // Maximum number of items to cache
