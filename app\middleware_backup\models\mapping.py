from datetime import datetime
from typing import Dict
from sqlalchemy import String, Float, JSO<PERSON>, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base

class MappingLocation(Base):
    __tablename__ = "mapping_locations"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.id"))
    location_type: Mapped[str] = mapped_column(String)  # building, area, infrastructure
    latitude: Mapped[float] = mapped_column(Float)
    longitude: Mapped[float] = mapped_column(Float)
    address: Mapped[str] = mapped_column(String)
    
    # Relationships
    project = relationship("Project", back_populates="mapping_locations")
    
class FloodZone(Base):
    __tablename__ = "flood_zones"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    zone_code: Mapped[str] = mapped_column(String)
    description: Mapped[str] = mapped_column(String)
    risk_level: Mapped[str] = mapped_column(String)
    boundary: Mapped[Dict] = mapped_column(JSON)  # Store polygon coordinates as GeoJSON
    
class RiskMap(Base):
    __tablename__ = "risk_maps"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String)
    description: Mapped[str] = mapped_column(String)
    map_data: Mapped[Dict] = mapped_column(JSON)
    last_updated: Mapped[datetime] = mapped_column(DateTime)
    risk_level: Mapped[str] = mapped_column(String)





