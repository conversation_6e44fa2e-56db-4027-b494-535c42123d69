<!DOCTYPE html>
<html lang="en">
<head>
  <title>
    eCFR :: 44 CFR 206.202 -- Application procedures.
  </title>

  <link rel="canonical" href="https://www.ecfr.gov/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202" />

  <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon/apple-touch-icon-18ac1159c2217e79fb45c21adc630384b65af33c4e6de8eca0ff8527a4fe080e.png">
<link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon/favicon-32x32-601e211398bdd3b5d26f99ae1ddae16e940b6a6223cb83f429419e6709f596f2.png">
<link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon/favicon-16x16-8e7cf46953c8eadd70c27e99d2977556a7ad517c1d809782451b24be80323d14.png">
<link rel="manifest" href="/assets/favicon/site-17bc871d6b501401cd30d1f57e54a581f21f9c964717dd77d28e6f026da4b560.webmanifest">
<link rel="mask-icon" href="/assets/favicon/safari-pinned-tab-42724ccbb4192754e3500f02bee26d07b1c8432d7179433dd34d8d59c4e0882b.svg" color="#fab827">
<link rel="shortcut icon" href="/assets/favicon/favicon-014ef9fe5cc7969da15185d0790dba54c9c41bbcedda5477f9a7214d98263525.ico">
<meta name="msapplication-TileColor" content="#ffc40d">
<meta name="msapplication-config" content="/assets/favicon/browserconfig-d9fe2961ea6e1d05b91dec740f2c7b3b916c646691e7c909c258dea3ad28cd94.xml">
<meta name="theme-color" content="#ffffff">


    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script src="/assets/google_analytics-a2da3bb5c78c39ec6f425a2de69612f49200191a66f6aaac3b373bc53618cf0e.js" async="async" nonce="uZ1bXhAV0dwTwY7foMO/Fg=="></script>

    <!-- We participate in the US government's analytics program. See the data at analytics.usa.gov. -->
    <script async type="text/javascript" nonce="uZ1bXhAV0dwTwY7foMO/Fg==" src="https://dap.digitalgov.gov/Universal-Federated-Analytics-Min.js?agency=NARA&subagency=fedreg" id="_fed_an_ua_tag"></script>

  <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link
href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,700&family=Bitter:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap"
rel="preload"
as="style">
<link
href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,700&family=Bitter:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap"
rel="stylesheet">


  <link rel="stylesheet" media="all" href="/assets/vendor-cc34441bbea8b613b4f6687fdbe0b20be2784482fce498378af44244a20c31e1.css" data-turbo-track="true" />
  <link rel="stylesheet" media="all" href="/assets/application-cab3fe704abacf30de204627077d84af75f499a7a95a6ebf38ab9f441d80db4b.css" data-turbo-track="true" />
  <link rel="stylesheet" media="print" href="/assets/print-e85ef07360cd8210c510308470ec9745d5d7b31065d9007a9d9b6935c561c232.css" data-turbo-track="true" />

  <script src="/assets/application_pipeline-6e9bac29d04e6167a6b9f568762498b74aec03673eb610a7cf33d96a421ededb.js" data-turbo-eval="false" async="async" defer="defer" nonce="uZ1bXhAV0dwTwY7foMO/Fg=="></script>

  <script type="importmap" data-turbo-track="reload" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">{
  "imports": {
    "application": "/assets/application-335c9d0168c1b2a3960dd56fdf4f6c209d0eec1942adb72b6441483bed14e77f.js",
    "@hotwired/turbo-rails": "/assets/turbo.min-13afb55d5ff2e3b7de0fd4b9108f94b28404a803c84d13970d490b2d1d9223bc.js",
    "@hotwired/stimulus": "https://ga.jspm.io/npm:@hotwired/stimulus@3.2.2/dist/stimulus.js",
    "@hotwired/stimulus-loading": "/assets/stimulus-loading-6024ee603e0509bba59098881b54a52936debca30ff797835b5ec6a4ef77ba37.js",
    "@lodash/debounce": "https://ga.jspm.io/npm:lodash.debounce@4.0.8/index.js",
    "stimulus-autocomplete": "/assets/stimulus-autocomplete-0e2d350d1a0100abce1840193618fa0e606c0425d5631eb27dee0fb068cc32df.js",
    "stimulus-use": "https://ga.jspm.io/npm:stimulus-use@0.52.0/dist/index.js",
    "controllers/agencies_controller": "/assets/controllers/agencies_controller-b39755bd0bdc29806a7e88bb2015c8cb873f41228c3332df2a9a0b91e94af87c.js",
    "controllers/application": "/assets/controllers/application-e361dd5f2e228c2fb25a1549ad19f7ab2ee254e8123572f3fb9694ad08711254.js",
    "controllers/changes_controller": "/assets/controllers/changes_controller-7d46a5241e38838956e42fa95fe3b8a38901c1c5c739c623498328449cb8f6d1.js",
    "controllers/compare_controller": "/assets/controllers/compare_controller-d4c7c2f7183e9a0487fb4156d3be50ca77ced0e1881289fbe3b0920b4a88ec95.js",
    "controllers/element_controller": "/assets/controllers/element_controller-124c5e519399db700016351f28875d1aebd96b8966ef7655beb3e8ec42529c3e.js",
    "controllers/form_controller": "/assets/controllers/form_controller-521a8ae5e19bf25bcf358a35c386589c4ae49399968da815af3e5fedb4e66cad.js",
    "controllers": "/assets/controllers/index-075d84f1ecd1595c9ab3e04d66cb668dc9f6fb81d4809c437cbb0a5c0ec6fce2.js",
    "controllers/modal_controller": "/assets/controllers/modal_controller-b9cff36cd77bfb60e03304caa4eecacff19fc3713510141975d9e6f2748ac706.js",
    "controllers/placeholder_controller": "/assets/controllers/placeholder_controller-db5bbdf7df068e036eb77a730c74a68b59d680045962165deb575098d64ea172.js",
    "controllers/recent_changes_controller": "/assets/controllers/recent_changes_controller-b55783709d43d67c169ce404aad187bb8a49f140d816a203405cc382ae31d605.js",
    "controllers/suggestions_controller": "/assets/controllers/suggestions_controller-c86bd514065aba875525a390293adf8e27176ccdd869a92a035c06b027e67771.js",
    "controllers/timeline_controller": "/assets/controllers/timeline_controller-d797067edc737523ee3485893dd7f9f0e658d0e421b93ec3657bd7dcf6d9d1d2.js"
  }
}</script>
<link rel="modulepreload" href="/assets/application-335c9d0168c1b2a3960dd56fdf4f6c209d0eec1942adb72b6441483bed14e77f.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/turbo.min-13afb55d5ff2e3b7de0fd4b9108f94b28404a803c84d13970d490b2d1d9223bc.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="https://ga.jspm.io/npm:@hotwired/stimulus@3.2.2/dist/stimulus.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/stimulus-loading-6024ee603e0509bba59098881b54a52936debca30ff797835b5ec6a4ef77ba37.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="https://ga.jspm.io/npm:lodash.debounce@4.0.8/index.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/stimulus-autocomplete-0e2d350d1a0100abce1840193618fa0e606c0425d5631eb27dee0fb068cc32df.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="https://ga.jspm.io/npm:stimulus-use@0.52.0/dist/index.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/agencies_controller-b39755bd0bdc29806a7e88bb2015c8cb873f41228c3332df2a9a0b91e94af87c.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/application-e361dd5f2e228c2fb25a1549ad19f7ab2ee254e8123572f3fb9694ad08711254.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/changes_controller-7d46a5241e38838956e42fa95fe3b8a38901c1c5c739c623498328449cb8f6d1.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/compare_controller-d4c7c2f7183e9a0487fb4156d3be50ca77ced0e1881289fbe3b0920b4a88ec95.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/element_controller-124c5e519399db700016351f28875d1aebd96b8966ef7655beb3e8ec42529c3e.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/form_controller-521a8ae5e19bf25bcf358a35c386589c4ae49399968da815af3e5fedb4e66cad.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/index-075d84f1ecd1595c9ab3e04d66cb668dc9f6fb81d4809c437cbb0a5c0ec6fce2.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/modal_controller-b9cff36cd77bfb60e03304caa4eecacff19fc3713510141975d9e6f2748ac706.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/placeholder_controller-db5bbdf7df068e036eb77a730c74a68b59d680045962165deb575098d64ea172.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/recent_changes_controller-b55783709d43d67c169ce404aad187bb8a49f140d816a203405cc382ae31d605.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/suggestions_controller-c86bd514065aba875525a390293adf8e27176ccdd869a92a035c06b027e67771.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<link rel="modulepreload" href="/assets/controllers/timeline_controller-d797067edc737523ee3485893dd7f9f0e658d0e421b93ec3657bd7dcf6d9d1d2.js" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">
<script type="module" nonce="uZ1bXhAV0dwTwY7foMO/Fg==">import "application"</script>

  

  <meta name="viewport" content="width=970">
  <meta name="format-detection" content="telephone=no">
    <meta name="dcterms.title"
    content="44 CFR 206.202 -- Application procedures." />
	<meta name="dcterms.bibliographicCitation"
    content="44 CFR 206.202" />

  <meta property="og:title"
    content="44 CFR 206.202 -- Application procedures." />
  <meta property="og:url"
    content="/current/title-44/part-206/section-206.202" />

  <meta property="ecfr:citationDate"
    content="" />


</head>
  <body data-environment="production" data-honeybadger-js-api-key="f722cff2" data-revision="130f95f24e2ae143b26776074c400bb37206c688" data-google-ga4-tag="G-K7SMYBYX81" data-service="ecfr-web" data-domain=".ecfr.gov">

    <!-- Zendesk Form -->
      <div class="btn" id="feedbackbutton" type="button"><span class="icon-ecfr-chat"></span>Site Feedback</div>
      <div id="legacy_browser_modal" class="modal fade" role="dialog" aria-labelledby="genericModal" aria-hidden="true" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">You are using an unsupported browser
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
        </h5>
      </div>
      <div class="modal-body">
        <p>
          You are using an unsupported browser. This web site is designed for the current versions of
          Microsoft Edge, Google Chrome, Mozilla Firefox, or Safari.
        </p>
      </div>
    </div>
  </div>
</div>

      <div id="interstitial-feedback-modal" class="modal fade in" aria-hidden="true" tabindex="-1" role="dialog" aria-labelledby="Feedback: Contact Us">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
        <h3 class="text-center">Site Feedback</h3>
      </div>
      <div class="modal-body">
        <p>
          The Office of the Federal Register publishes documents on behalf of Federal agencies but does not have any authority over their programs. We recommend you directly contact the agency associated with the content in question.
        </p>
        <hr>
        <div class="row">
          <div id="website-feedback" class="col-xs-6 col-md-6 feedback-section">
            <p>
              If you have comments or suggestions on how to improve the www.ecfr.gov website or have questions about using www.ecfr.gov, please choose the 'Website Feedback' button below.
            </p>
            <div class="text-center">
              <a class="btn btn-primary btn-medium text-center" href="">
                <div><span class="icon-ecfr icon-ecfr-help "></span></div>
                Website Feedback
</a>            </div>
          </div>
          <div id="content-feedback" class="col-xs-6 col-md-6 feedback-section">
            <p>
              If you would like to comment on the current content, please use the 'Content Feedback' button below for instructions on contacting the issuing agency
            </p>
            <div class="text-center">
              <a class="btn btn-primary btn-medium text-center" href="">
                <div><span class="icon-ecfr icon-ecfr-legal "></span></div>
                Content Feedback
</a>            </div>
          </div>
        </div>
        <div class="text-center small-text">If you have questions for the Agency that issued the current document please contact the agency directly.</div>
      </div>
    </div>
  </div>
</div>

      <div id="zendesk-feedback-modal">
  <div class="modal-content zendesk-modal-content">
    <div class="modal-header">
      <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">×</span>
      </button>
      <h3>Website Feedback</h3>
    </div>

    <div class="modal-body">
      <form novalidate="novalidate" class="simple_form zendesk_ticket" enctype="multipart/form-data" action="/zendesk_tickets" accept-charset="UTF-8" method="post">
        <div class="form-group string required zendesk_ticket_name"><label class="control-label string required" for="zendesk_ticket_name"><abbr title="required">*</abbr> Your Name</label><input class="form-control string required" required="required" aria-required="true" type="text" name="zendesk_ticket[name]" id="zendesk_ticket_name" /></div>
        <div class="form-group email required zendesk_ticket_email"><label class="control-label email required" for="zendesk_ticket_email"><abbr title="required">*</abbr> Email</label><input class="form-control string email required" required="required" aria-required="true" type="email" name="zendesk_ticket[email]" id="zendesk_ticket_email" /></div>
        <div class="form-group file optional zendesk_ticket_attachment"><label class="control-label file optional" for="zendesk_ticket_attachment">Attachment</label><input class="file optional" type="file" name="zendesk_ticket[attachment]" id="zendesk_ticket_attachment" /></div>
        <div class="form-group text required zendesk_ticket_comment"><label class="control-label text required" for="zendesk_ticket_comment"><abbr title="required">*</abbr> How can we help you?</label><textarea class="form-control text required" name="zendesk_ticket[comment]" id="zendesk_ticket_comment">
</textarea></div>
        <div class="form-group boolean required zendesk_ticket_technical_help"><div class="checkbox"><input value="0" autocomplete="off" type="hidden" name="zendesk_ticket[technical_help]" /><label class="boolean required" for="zendesk_ticket_technical_help"><input class="boolean required" required="required" aria-required="true" type="checkbox" value="1" name="zendesk_ticket[technical_help]" id="zendesk_ticket_technical_help" /><abbr title="required">*</abbr> I am requesting technical help or providing website feedback</label></div></div>
          <div class="modal-text">
            This contact form is only for website help or website suggestions. If you have questions or comments regarding a published document please
            contact the publishing agency. Comments or questions about document content can not be answered by OFR staff. Please do not provide confidential
            information or personal data.
          </div>
        <div class="form-group text-left">
          <input type="submit" name="commit" value="Create Ticket" class="btn btn-primary btn-medium" data-disable-with="Create Ticket" />
        </div>
</form>    </div>
  </div>
</div>


    

    <div class="container">
      <div class='row'>
        <div class="col-xs-12 col-md-12 layout-header">
          <div class="main-navigation" id="main-navigation">
  <ul class="container">
    <li id='nav-home' class="nav-home" tabindex>
      <a class="home" aria-label="Home" href="/">
        <span class="icon-ecfr icon-ecfr-home "></span>
</a>    </li>

    <li id='nav-browse' class="dropdown nav-browse" tabindex=0>
      <a class="top-nav">
        <span class="icon-ecfr icon-ecfr-eye "></span>
        Browse
        <span class="icon-ecfr icon-ecfr-menu-arrow "></span>
      </a>

      <ul class="subnav dropdown-list">
        <li>
          <a href="/titles"><span class="icon-ecfr icon-ecfr-book "></span> Titles</a>
        </li>
        <li>
          <a href="/agencies"><span class="icon-ecfr icon-ecfr-mindmap "></span> Agencies</a>
        </li>
        <li>
          <a href="/incorporation-by-reference"><span class="icon-ecfr icon-ecfr-directions-alt "></span> Incorporation by Reference</a>
        </li>
        <li>
          <a href="/issues"><span class="icon-ecfr icon-ecfr-hourglass "></span> Recent Updates</a>
        </li>
      </ul>
    </li>

    <li id='nav-search' class="nav-search" tabindex>
      <a class="search" aria-label="eCFR Search" href="/search">
        <span class="icon-ecfr icon-ecfr-search "></span> Search
</a>    </li>

    <li id='nav-recent-changes' class="nav-recent-changes" tabindex>
      <a aria-label="eCFR Recent Changes" href="/recent-changes">
        <span class="icon-ecfr icon-ecfr-hourglass "></span> Recent Changes
</a>    </li>

    <li id='nav-ecfr-corrections' class="nav-recent-changes" tabindex>
      <a aria-label="eCFR Corrections" href="/corrections/2025">
        <span class="icon-ecfr icon-ecfr-merge "></span> Corrections
</a>    </li>

    <li id='nav-reader-aids' class="dropdown nav-reader-aids" tabindex=0>
      <a class="top-nav">
        <span class="icon-ecfr icon-ecfr-directions "></span>
        Reader Aids
        <span class="icon-ecfr icon-ecfr-menu-arrow "></span>
      </a>

      <ul class="subnav dropdown-list hierarchical" tabindex=0>
        <li>
          <a tabindex="0" href="/reader-aids">Reader Aids Home</a>
        </li>
          <li>
            <a tabindex="0" href="/reader-aids/using-ecfr">Using the eCFR Point-in-Time System</a>
          </li>
          <li>
            <a tabindex="0" href="/reader-aids/understanding-the-ecfr">Understanding the eCFR</a>
          </li>
          <li>
            <a tabindex="0" href="/reader-aids/government-policy-and-ofr-procedures">Government Policy and OFR Procedures</a>
          </li>
          <li>
            <a tabindex="0" href="/reader-aids/ecfr-developer-resources">Developer Resources</a>
          </li>
      </ul>
    </li>

    <li id='nav-my-ecfr' class="dropdown nav-my-ecfr" tabindex=0>
      <a class="top-nav">
        <span class="icon-ecfr icon-ecfr-male-female-user "></span>
        My eCFR
        <span class="icon-ecfr icon-ecfr-menu-arrow "></span>
      </a>

      <ul class="subnav dropdown-list signed-in hidden" tabindex=0>
        <li>
          <a href="/my/subscriptions">My Subscriptions</a>
        </li>
        <li>
          <a data-turbo="false" href="/sign_out">Sign Out</a>
        </li>
      </ul>

      <ul class="subnav dropdown-list signed-out" tabindex=0>
        <li>
          <a data-turbo="false" href="https://www.ecfr.gov/auth/sign_in?jwt=eyJhbGciOiJIUzI1NiJ9.eyJub3RpZmljYXRpb25zIjp7ImluZm8iOiJZb3Ugd2lsbCBiZSByZWRpcmVjdGVkIGJhY2sgdG8gdGhlIGVDRlIgYWZ0ZXIgeW91IGhhdmUgc2lnbmVkIGluIG9yIGNyZWF0ZWQgYW4gYWNjb3VudC4ifX0.-sYkHWfC02yb0CpuUqmPkxxnF0yzlNHaS5520hYIUy8&amp;redirect_to=https://www.ecfr.gov/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202">Sign In / Sign Up</a>
        </li>
      </ul>
    </li>

    <li id='nav-inline-search' class="nav-inline-search" data-controller="placeholder">
      <form class="simple_form nav-search-form" novalidate="novalidate" action="/search" accept-charset="UTF-8" method="get">
        <input value="current" class="hidden" autocomplete="off" type="hidden" name="search[date]" id="search_date" />

          <input value="{&quot;title&quot;:&quot;44&quot;,&quot;chapter&quot;:&quot;I&quot;,&quot;subchapter&quot;:&quot;D&quot;,&quot;part&quot;:&quot;206&quot;,&quot;subpart&quot;:&quot;G&quot;,&quot;section&quot;:&quot;206.202&quot;}" autocomplete="off" type="hidden" name="search[prior_hierarchy]" id="search_prior_hierarchy" />

        <input autocomplete="off" class="string required nav-search-query" data-action="click-&gt;placeholder#suggestions" placeholder="Search the eCFR" type="text" name="search[query]" id="search_query" />

        <button name="button" type="submit" class="nav-search-btn" title="Search" data-action="click-&gt;placeholder#suggestions">
          <span class="icon-ecfr icon-ecfr-search "></span>
</button>
        <div class='current-hierarchy-dropdown hidden'>
        </div>
</form>    </li>
  </ul>

  <div class='user-utils'>
    <div class='signed-out'>
      <a class="utils-signed-out" tabindex="-1" data-turbo="false" href="https://www.ecfr.gov/auth/sign_in?jwt=eyJhbGciOiJIUzI1NiJ9.eyJub3RpZmljYXRpb25zIjp7ImluZm8iOiJZb3Ugd2lsbCBiZSByZWRpcmVjdGVkIGJhY2sgdG8gdGhlIGVDRlIgYWZ0ZXIgeW91IGhhdmUgc2lnbmVkIGluIG9yIGNyZWF0ZWQgYW4gYWNjb3VudC4ifX0.-sYkHWfC02yb0CpuUqmPkxxnF0yzlNHaS5520hYIUy8&amp;redirect_to=https://www.ecfr.gov/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202">Sign In / Sign Up</a>
    </div>

    <div class='signed-in hidden'>
      <p>
        Hi, <span class='user-email'></span>
          <a data-turbo="false" tabindex="-1" href="/sign_out">Sign Out</a>
      </p>
    </div>
  </div>
</div>


<div class="container">
  <div class="row">
    <div class="col-xs-12">
      <div class="logo">
        <div class="hgroup standard">
          <hgroup>
            <h1><a href="/" title="eCFR Home" tabindex=-1>eCFR</a></h1>
            <h2>The Electronic Code of Federal Regulations</h2>
          </hgroup>
        </div>
      </div>
    </div>
  </div>
</div>


        </div>
      </div>
    </div>

    <div class="container">
      <div class='external-url-modal'></div>
      <div class="modal fr-citation-modal" id="frCitationModal" tabindex="-1" role="dialog" aria-labelledby="Federal Register Citation Modal">
        <div class="modal-dialog" role="document">
          <div class="modal-content">
            <span class="icon-ecfr icon-ecfr-badge-x "></span>
            <div class="box box-enhanced "><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content :: FR Reference</h6><div class="row seal-meta"><div class="seal-desc col-md-12 col-xs-12"><p>Enhanced content is provided to the user to provide additional context.</p></div></div></div></div><div class="content-block ">
              <div class="modal-body"></div>
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content :: FR Reference</h6></div></div>          </div>
        </div>
      </div>

      <div class='row'>
        <div class="col-xs-12 col-md-12 title-bar-col">
          <div id="page-top" class="main-title-bar "><div class="bar left"></div><h1 class="">  <span class="icon-ecfr icon-ecfr-books "></span>
  Title 44
</h1><div class="bar right"></div></div>
        </div>
      </div>


          <div class="row " >
  <div class="col-xs-12 col-md-12 content-notification basic print-only print-only-legal-notice"
    id="content-notification-f4223aa6"
  >
    <div class="">

      

      <div class="message">
        <p>This content is from the eCFR and is authoritative but unofficial.<p>
      </div>

    </div>
  </div>
</div>

  <div class="row " >
  <div class="col-xs-12 col-md-12 content-notification info"
    id="content-notification-29d1e005"
  >
    <div class="">

      <span class="icon-ecfr icon-ecfr-calendar-alt "></span>

      <div class="message">
        Displaying title 44, up to date as of 3/28/2025. Title 44 was last amended 12/31/2024.<span class="reader-aid"><span data-toggle="tooltip" data-title="Click to learn more about dates in eCFR." class="svg-tooltip clickable" data-url="/reader-aids/understanding-the-ecfr/what-is-the-ecfr#special-rules-and-proceedures-for-future-amendments" data-container="span.reader-aid"><svg class="ecfr-svg-icon ecfr-svg-icon-info-circle "><use xlink:href="/assets/ecfr-icons-****************************************df40f52ca7d30476904de0ea.svg#ecfr-svg-icon-info-circle"></use></svg></span></span>
      </div>

        <div class="message-link">
          <a class="msg-link message-go-to-version" href="#">view historical versions</a>
        </div>
    </div>
  </div>
</div>

  <div class="row hidden" >
  <div class="col-xs-12 col-md-12 content-notification danger"
    id="drafting-site-notification"
  >
    <div class="">
        <div class="dismiss-link">
          <span class="icon-ecfr icon-ecfr-badge-x "></span>
        </div>

      <span class="icon-ecfr icon-ecfr-alert-alt "></span>

      <div class="message">
        A <a data-drafting="true" href="https://drafting.ecfr.gov/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202">drafting site is available</a> for use when drafting amendatory language
      </div>

        <div class="message-link">
          <a data-drafting="true" class="msg-link" href="https://drafting.ecfr.gov/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202">switch to drafting site</a>
        </div>
    </div>
  </div>
</div>






      <div class='row'>
        <div class="col-xs-12 col-md-12 content-col">
          <div>
  <div id="suggestions" data-controller="suggestions autocomplete"
    data-autocomplete-url-value="/suggestions"
    data-autocomplete-delay-value="300">

    <div class="modal hidden" data-controller="modal"
      data-action="keyup@document->modal#escClose"
      data-suggestions-target="modal">

      <button class="backdrop" type="button" tabindex="-1"
        data-action="modal#close"></button>

      <div class="content">

        <div class="panel panel-default panel-info" data-suggestions-target="help">
          <div class="panel-heading">
            <p>
              <strong>Navigate by entering citations or phrases</strong>
              (eg:
                <span data-action="click->suggestions#fillExample"
                  class="example badge badge-info">1 CFR 1.1</span>
                <span data-action="click->suggestions#fillExample"
                  class="example badge badge-info">49 CFR 172.101</span>
                <span data-action="click->suggestions#fillExample"
                  class="example badge badge-info">Organization and Purpose</span>
                <span data-action="click->suggestions#fillExample"
                  class="example badge badge-info">1/1.1</span>
                <span data-action="click->suggestions#fillExample"
                  class="example badge badge-info">Regulation Y</span>
                <span data-action="click->suggestions#fillExample"
                  class="example badge badge-info">FAR</span>).
            </p>
            <p>
              Choosing an item from
              <span class="suggestion-header-example">citations and headings</span>
              will bring you directly to the content. Choosing an item from
              <span class="suggestion-header-example">full text search results</span>
              will bring you to those results. Pressing enter in the search box
              will also bring you to search results.
            </p>
            <p>
              Background and more details are available in the
              <em>
                <a href="/reader-aids/using-ecfr/navigating-to-content-of-interest#search-navigation">Search &amp; Navigation</a>
              </em>
              guide.
            </p>
          </div>
        </div>

        <div class="cfr-ref-wrapper go-to-cfr-reference">
          <form class="simple_form go-to-cfr-reference" data-suggestions-target="form" data-turbo="false" novalidate="novalidate" action="/cfr-reference" accept-charset="UTF-8" method="get">
            <input value="current" data-param="date" autocomplete="off" type="hidden" name="search[date]" id="search_date" />
            <input value="{&quot;title&quot;:&quot;44&quot;,&quot;chapter&quot;:&quot;I&quot;,&quot;subchapter&quot;:&quot;D&quot;,&quot;part&quot;:&quot;206&quot;,&quot;subpart&quot;:&quot;G&quot;,&quot;section&quot;:&quot;206.202&quot;}" data-param="hierarchy" autocomplete="off" type="hidden" name="search[hierarchy]" id="search_hierarchy" />
            <input value="{&quot;title&quot;:&quot;44&quot;,&quot;chapter&quot;:&quot;I&quot;,&quot;subchapter&quot;:&quot;D&quot;,&quot;part&quot;:&quot;206&quot;,&quot;subpart&quot;:&quot;G&quot;,&quot;section&quot;:&quot;206.202&quot;}" data-param="prior_hierarchy" autocomplete="off" type="hidden" name="search[prior_hierarchy]" id="search_prior_hierarchy" />

            <div class="input-group input-group-lg">
              <input type="text" class="form-control"
                placeholder="Enter a search term or CFR reference (eg. fishing or 1 CFR 1.1)"
                aria-describedby="reference-search-icon"
                data-autocomplete-target="input"
                data-suggestions-target="input"
                name="search[query]"
                id="suggestion_query">

              <span class="input-group-btn"
                id="reference-search-icon"
                aria-label="search">
                <button class="btn btn-optional" type="button"
                  data-suggestions-target="searchIcon"
                  data-action="click->suggestions#goDefault">
                    <span class="icon-ecfr icon-ecfr-search "></span>
                </button>
              </span>
            </div>

            <div data-autocomplete-target="results"
              data-suggestions-target="results"></div>
</form>        </div>

      </div>
    </div>

    <form class="simple_form go-to-cfr-reference hidden" data-suggestions-target="nonModalForm" novalidate="novalidate" action="/cfr-reference" accept-charset="UTF-8" method="get">
      <input label="false" value="current" autocomplete="off" type="hidden" name="cfr[date]" id="cfr_date" />
      <input value="{&quot;title&quot;:&quot;44&quot;,&quot;chapter&quot;:&quot;I&quot;,&quot;subchapter&quot;:&quot;D&quot;,&quot;part&quot;:&quot;206&quot;,&quot;subpart&quot;:&quot;G&quot;,&quot;section&quot;:&quot;206.202&quot;}" autocomplete="off" type="hidden" name="cfr[prior_hierarchy]" id="cfr_prior_hierarchy" />
      <input data-autocomplete-target="hidden" autocomplete="off" type="hidden" name="cfr[suggestion_id]" id="cfr_suggestion_id" />

      <div class="input-group input-group-lg">
        <input type="text" class="form-control string optional"
          data-action="focus->suggestions#modal"
          placeholder="Enter a search term or CFR reference (eg. fishing or 1 CFR 1.1)"
          type="text"
          data-suggestions-target="nonModalInput"
          name="cfr[reference]"
          id="suggestion">
        <span class="input-group-btn"
          data-action="click->suggestions#go">

          <button class="btn btn-optional" type="button"
            data-suggestions-target="nonModalSearchIcon"
            data-action="click->suggestions#go">
              <span class="icon-ecfr icon-ecfr-search "></span>
          </button>
        </span>
      </div>
</form>  </div>
</div>

          



<div class='row'>
  <div class="col-xs-12 col-md-12 no-padding">
    <div class='cfr-ref-wrapper'>
  <div data-controller="placeholder">
    <form class="simple_form go-to-cfr-reference" novalidate="novalidate" action="/cfr-reference" accept-charset="UTF-8" method="get">

      <div class="input-group input-group-lg">
        <input type="text" class="form-control string optional"
          data-action="focus->placeholder#suggestions"
          placeholder="Enter a search term or CFR reference (eg. fishing or 1 CFR 1.1)"
          type="text"
          data-suggestions-target="nonModalInput"
          name="cfr[reference]"
          id="suggestion">
        <span class="input-group-btn"
          data-action="click->placeholder#suggestions">

          <button class="btn btn-optional" type="button"
            data-suggestions-target="nonModalSearchIcon"
            data-action="click->placeholder#suggestions">
              <span class="icon-ecfr icon-ecfr-search "></span>
          </button>
        </span>
      </div>
</form>  </div>
</div>

  </div>
</div>

<div class="row breadcrumbs-wrapper ">
  <div class="col-xs-12 col-md-12 breadcrumb-nav" data-hierarchy="{&quot;title&quot;:&quot;44&quot;,&quot;chapter&quot;:&quot;I&quot;,&quot;subchapter&quot;:&quot;D&quot;,&quot;part&quot;:&quot;206&quot;,&quot;subpart&quot;:&quot;G&quot;,&quot;section&quot;:&quot;206.202&quot;}" data-filtered-search-enabled="false">
    <div class='row'>
      <div class="col-xs-12 col-md-9">
        <ol>
    <li class="">
        <a class="breadcrumb-link" data-toggle="tooltip" data-placement="top" data-title="View Table of Contents for Title 44—Emergency Management and Assistance" href="/current/title-44">Title 44 <span class="print-only breadcrumbs-title" data-hierarchy="{'title':'44'}">—Emergency Management and Assistance</span></a>
    </li>
    <li class="">
        <a class="breadcrumb-link" data-toggle="tooltip" data-placement="top" data-title="View Table of Contents for  Chapter I—Federal Emergency Management Agency, Department of Homeland Security" href="/current/title-44/chapter-I"> Chapter I <span class="print-only breadcrumbs-title" data-hierarchy="{'title':'44','chapter':'I'}">—Federal Emergency Management Agency, Department of Homeland Security</span></a>
    </li>
    <li class="">
        <a class="breadcrumb-link" data-toggle="tooltip" data-placement="top" data-title="View Table of Contents for Subchapter D—Disaster Assistance" href="/current/title-44/chapter-I/subchapter-D">Subchapter D <span class="print-only breadcrumbs-title" data-hierarchy="{'title':'44','chapter':'I','subchapter':'D'}">—Disaster Assistance</span></a>
    </li>
    <li class="">
        <a class="breadcrumb-link" data-toggle="tooltip" data-placement="top" data-title="View Table of Contents for Part 206—Federal Disaster Assistance" href="/current/title-44/chapter-I/subchapter-D/part-206?toc=1">Part 206 <span class="print-only breadcrumbs-title" data-hierarchy="{'title':'44','chapter':'I','subchapter':'D','part':'206'}">—Federal Disaster Assistance</span></a>
    </li>
    <li class="">
        <a class="breadcrumb-link" data-toggle="tooltip" data-placement="top" data-title="View Table of Contents for Subpart G—Public Assistance Project Administration" href="/current/title-44/chapter-I/subchapter-D/part-206/subpart-G?toc=1">Subpart G <span class="print-only breadcrumbs-title" data-hierarchy="{'title':'44','chapter':'I','subchapter':'D','part':'206','subpart':'G'}">—Public Assistance Project Administration</span></a>
    </li>
    <li class="breadcrumb-current">
        § 206.202

    </li>
</ol>

      </div>

      <div class="col-xs-12 col-md-3 text-right">
        <ul class="prev-next-breadcrumb-nav">
  <li>
      <a id="previous-content-link" data-title="§ 206.201 Definitions used in this subpart." data-html="true" href="/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.201">Previous</a>
  </li>

  <li>
      <a id="next-content-link" data-title="§ 206.203 Federal grant assistance." data-html="true" href="/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.203">Next</a>
  </li>

  <li id="breadcrumb-back-to-top">
    <a data-title="Back to Top" data-turbo="false" href="#page-top">Top</a>
  </li>
</ul>

      </div>
    </div>
</div></div>


<div class="row ecfr-content-wrapper">
  <div class="col-xs-12 col-md-12 ecfr-content with-utility-bar">
    <div class="row">
      <div class="col-xs-3 col-sm-2 content-nav-wrapper content-nav-wrapper-wide">
        
<ul class="content-nav text-center dropdown drop-enhanced ui sticky">
  <li class="button">
    <span role="button" arial-label="compress content nav" class="icon-ecfr icon-ecfr-previous-media content-nav-compress-toggle"></span>
  </li>

  

<li class="inactive " id="utility-nav-toc" tabindex="0"><span role="button" aria-label="Table of Contents" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Table of Contents" class="icon-ecfr icon-ecfr-book "></span><span class="content-nav-label " role="button">Table of Contents</span><div class="box box-enhanced box-enhanced-toc box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Table of Contents</h6></div></div><div class="content-block ">      <p>
        The in-page Table of Contents is available only when multiple sections are being viewed.
      </p>
      <p>
        Use the navigation links in the gray bar above to view the table of contents that this content belongs to.
      </p>
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Table of Contents</h6></div></div></li>

  
<li class="enhanced " id="utility-nav-details" tabindex="0"><span role="button" aria-label="Details" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Details" class="icon-ecfr icon-ecfr-book-alt-2 "></span><span class="content-nav-label " role="button">Details</span><div class="box box-enhanced box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Details</h6></div></div><div class="content-block ">    <dl class="">
      <dt>URL</dt>
      <dd>
        https://www.ecfr.gov/current/title-44/part-206/section-206.202
        <span data-toggle="tooltip" data-title="Copy to Clipboard" class="svg-tooltip copy-to-clipboard" data-title-copied="Copied to Clipboard" data-copy-text="https://www.ecfr.gov/current/title-44/part-206/section-206.202"><svg class="ecfr-svg-icon ecfr-svg-icon-content-copy "><use xlink:href="/assets/ecfr-icons-****************************************df40f52ca7d30476904de0ea.svg#ecfr-svg-icon-content-copy"></use></svg></span>
      </dd>

        <dt>Citation</dt>
        <dd>
          44 CFR 206.202
          <span data-toggle="tooltip" data-title="Copy to Clipboard" class="svg-tooltip copy-to-clipboard" data-title-copied="Copied to Clipboard" data-copy-text="44 CFR 206.202"><svg class="ecfr-svg-icon ecfr-svg-icon-content-copy "><use xlink:href="/assets/ecfr-icons-****************************************df40f52ca7d30476904de0ea.svg#ecfr-svg-icon-content-copy"></use></svg></span>
        </dd>



        <dt>
          Agency
          <span data-toggle="tooltip" data-title="The agency currently associated with this eCFR content. Click for more details." class="svg-tooltip clickable" data-url="/reader-aids/using-ecfr/agency-references#details_sidebar" data-container="dl"><svg class="ecfr-svg-icon ecfr-svg-icon-info-circle "><use xlink:href="/assets/ecfr-icons-****************************************df40f52ca7d30476904de0ea.svg#ecfr-svg-icon-info-circle"></use></svg></span>
        </dt>
          <dd data-issuing-agency=federal-emergency-management-agency>Federal Emergency Management Agency, Department of Homeland Security</dd>
    </dl>

    <hr>

    <div id="source-and-authority">
        <div class="additional-source-and-authority">
      <div class="level">
        <a href="/current/title-44/part-206" class="cfr reference">Part 206</a>
        <div class="details" data-hierarchy="{:title=&gt;&quot;44&quot;, :chapter=&gt;&quot;I&quot;, :subchapter=&gt;&quot;D&quot;, :part=&gt;&quot;206&quot;}" data-current="false">
            <div class="authority">
              <h4 class="inline-header">Authority:</h4>
              <p class="inline-paragraph">
                Robert T. Stafford Disaster Relief and Emergency Assistance Act, <a href="https://www.govinfo.gov/link/uscode/42/5121" class="usc external" target="_blank" rel="noopener noreferrer">42 U.S.C. 5121</a> through <a href="https://www.govinfo.gov/link/uscode/42/5207" class="usc external" target="_blank" rel="noopener noreferrer">5207</a>; Homeland Security Act of 2002, <a href="https://www.govinfo.gov/link/uscode/6/101" class="usc external" target="_blank" rel="noopener noreferrer">6 U.S.C. 101</a> <em>et seq.;</em> Department of Homeland Security Delegation 9001.1; sec. 1105, <a href="https://www.govinfo.gov/link/plaw/113/public/2" class="publ external" target="_blank" rel="noopener noreferrer">Pub. L. 113-2</a>, 127 Stat. 43 (<a href="https://www.govinfo.gov/link/uscode/42/5189a" class="usc external" target="_blank" rel="noopener noreferrer">42 U.S.C. 5189a note</a>).


              </p>
            </div>
            <div class="source">
              <h4 class="inline-header">Source:</h4>
              <p class="inline-paragraph">
                <a href="https://www.federalregister.gov/citation/54-FR-11615" class="fr-reference" data-reference="54 FR 11615">54 FR 11615</a>, Mar. 21, 1989, unless otherwise noted.


              </p>
            </div>
        </div>
      </div>
      <div class="level">
        <a href="/current/title-44/part-206/subpart-G" class="cfr reference">Subpart G of Part 206</a>
        <div class="details" data-hierarchy="{:title=&gt;&quot;44&quot;, :chapter=&gt;&quot;I&quot;, :subchapter=&gt;&quot;D&quot;, :part=&gt;&quot;206&quot;, :subpart=&gt;&quot;G&quot;}" data-current="false">
            <div class="source">
              <h4 class="inline-header">Source:</h4>
              <p class="inline-paragraph">
                <a href="https://www.federalregister.gov/citation/55-FR-2304" class="fr-reference" data-reference="55 FR 2304">55 FR 2304</a>, Jan. 23, 1990, unless otherwise noted.


              </p>
            </div>
        </div>
      </div>
  </div>

    </div>
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Details</h6></div></div></li>

  
<li class="enhanced " id="" tabindex="0"><span role="button" aria-label="Print/PDF" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Print/PDF" class="icon-ecfr icon-ecfr-print "></span><span class="content-nav-label " role="button">Print/PDF</span><div class="box box-enhanced box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Print</h6></div></div><div class="content-block ">      <p>
        <a class="generate-pdf" data-original-text="Generate PDF" href="#">Generate PDF</a>

        <span class="pdf-spinner" style="display:none;">
          <span class="spinner"></span>
        </span>
      </p>
      <p>
        This content is from the eCFR and may include recent changes applied to the CFR.
        The official, published CFR, is updated annually and available below under
        "Published Edition". You can learn more about the process
        <a href="https://www.govinfo.gov/help/cfr#about">here</a>.
      </p>
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Print</h6></div></div></li>

  
<li class="enhanced " id="" tabindex="0"><span role="button" aria-label="Display Options" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Display Options" class="icon-ecfr icon-ecfr-doc-generic "></span><span class="content-nav-label " role="button">Display Options</span><div class="box box-enhanced box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Display Options</h6></div></div><div class="content-block ">    <turbo-frame id="side_display" src="/display/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202" loading="lazy">
      <span class="timeline-spinner">
        <span class="spinner"></span>
      </span>
    </turbo-frame>  
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Display Options</h6></div></div></li>

  
<li class="enhanced utility-nav-subscribe" id="utility-nav-subscribe" tabindex="0"><span role="button" aria-label="Subscribe" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Subscribe" class="icon-ecfr icon-ecfr-message "></span><span class="content-nav-label " role="button">Subscribe</span><div class="box box-enhanced box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Subscribe</h6></div></div><div class="content-block ">    <p><strong>Subscribe to:</strong> 44 CFR 206.202</p>

    <div class="subscription-option">
      <form class="simple_form form-horizontal subscribe" id="new_subscription" data-turbo="false" novalidate="novalidate" action="/my/subscriptions" accept-charset="UTF-8" method="post">
            <div class="form-group hidden subscription_title"><input class="form-control hidden hierarchy-input" value="44" name="search[hierarchy][title]" autocomplete="off" type="hidden" id="subscription_title" /></div>
            <div class="form-group hidden subscription_chapter"><input class="form-control hidden hierarchy-input" value="I" name="search[hierarchy][chapter]" autocomplete="off" type="hidden" id="subscription_chapter" /></div>
            <div class="form-group hidden subscription_subchapter"><input class="form-control hidden hierarchy-input" value="D" name="search[hierarchy][subchapter]" autocomplete="off" type="hidden" id="subscription_subchapter" /></div>
            <div class="form-group hidden subscription_part"><input class="form-control hidden hierarchy-input" value="206" name="search[hierarchy][part]" autocomplete="off" type="hidden" id="subscription_part" /></div>
            <div class="form-group hidden subscription_subpart"><input class="form-control hidden hierarchy-input" value="G" name="search[hierarchy][subpart]" autocomplete="off" type="hidden" id="subscription_subpart" /></div>
            <div class="form-group hidden subscription_section"><input class="form-control hidden hierarchy-input" value="206.202" name="search[hierarchy][section]" autocomplete="off" type="hidden" id="subscription_section" /></div>

        <div class="subscription-option">
          <span class="icon-ecfr icon-ecfr-message "></span><label class='email-label'>Via Email:</label>
          <input type="submit" name="commit" value="Subscribe" class="btn-as-link" data-disable-with="Subscribe" />
        </div>

        <!-- <div class="subscription-option"> -->
          <!-- <label class='rss-label'>Via RSS:</label> -->

        <!-- </div> -->
</form>    </div>
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Subscribe</h6></div></div></li>

  <li class="divider"></li>

    
<li class="enhanced " id="utility-nav-timeline" tabindex="0"><span role="button" aria-label="Timeline" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Timeline" class="icon-ecfr icon-ecfr-hourglass "></span><span class="content-nav-label " role="button">Timeline</span><div class="box box-enhanced box-enhanced-timeline box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Timeline</h6></div></div><div class="content-block ">
        <turbo-frame loading="lazy" data-controller="timeline" id="side_timeline" src="/timeline/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202" target="_top">
          <span class="timeline-spinner">
            <span class="spinner"></span>
          </span>
</turbo-frame></div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Timeline</h6></div></div></li>

  
<li class="enhanced " id="utility-nav-go-to-date" tabindex="0"><span role="button" aria-label="Go to Date" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Go to Date" class="icon-ecfr icon-ecfr-calendar-alt "></span><span class="content-nav-label " role="button">Go to Date</span><div class="box box-enhanced box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Go to Date</h6></div></div><div class="content-block ">    <form class="simple_form form-horizontal go-to-date" id="go-to-date-form" autocomplete="off" novalidate="novalidate" action="#" accept-charset="UTF-8" method="get">

      <div class="form-group bootstrap_datepicker optional go_to_date_date"><label class="control-label bootstrap_datepicker optional" for="go_to_date_date">Date</label><div class='input-group'>
<input class="form-control bootstrap_datepicker optional bootstrap-datepicker" data-date-start-date="1/03/2017" data-date-force-parse="false" data-date-assume-nearby-year="true" data-date-format="mm/dd/yyyy" data-date-autoclose="true" data-provide="datepicker" type="text" name="go_to_date[date_box]" id="go_to_date_date_box" />
<span class='input-group-addon calendar-btn'><span class='glyphicon glyphicon-calendar'></span></span>
</div>
<input class="form-control bootstrap_datepicker optional" data-date-start-date="1/03/2017" data-date-force-parse="false" data-date-assume-nearby-year="true" data-date-format="mm/dd/yyyy" data-date-autoclose="true" data-provide="datepicker" id="date_hidden" value="" autocomplete="off" type="hidden" name="go_to_date[date]" />
</div>

      <input type="submit" name="commit" value="Go to date" class="btn btn-default btn btn-primary-alt btn-medium" data-disable-with="Go to date" />
</form>
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Go to Date</h6></div></div></li>

  

<li class="enhanced " id="utility-nav-compare" tabindex="0"><span role="button" aria-label="Compare Dates" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Compare Dates" class="icon-ecfr icon-ecfr-forking "></span><span class="content-nav-label " role="button">Compare Dates</span><div class="box box-enhanced box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Compare Dates</h6></div></div><div class="content-block ">      <form class="simple_form form-horizontal compare-content" id="new_compare" autocomplete="off" novalidate="novalidate" action="/compare" accept-charset="UTF-8" method="post">

        <div class="form-group bootstrap_datepicker optional compare_from"><label class="control-label bootstrap_datepicker optional" for="compare_from">Compare</label><div class='input-group'>
<input class="form-control bootstrap_datepicker optional bootstrap-datepicker" data-date-start-date="1/03/2017" data-date-force-parse="false" data-date-assume-nearby-year="true" data-date-format="mm/dd/yyyy" data-date-autoclose="true" data-provide="datepicker" type="text" name="compare[from_box]" id="compare_from_box" />
<span class='input-group-addon calendar-btn'><span class='glyphicon glyphicon-calendar'></span></span>
</div>
<input class="form-control bootstrap_datepicker optional" data-date-start-date="1/03/2017" data-date-force-parse="false" data-date-assume-nearby-year="true" data-date-format="mm/dd/yyyy" data-date-autoclose="true" data-provide="datepicker" id="from_hidden" value="" autocomplete="off" type="hidden" name="compare[from]" />
</div>

        <div class="form-group bootstrap_datepicker optional compare_to"><label class="control-label bootstrap_datepicker optional" for="compare_to">to</label><div class='input-group'>
<input class="form-control bootstrap_datepicker optional bootstrap-datepicker" data-date-start-date="1/03/2017" data-date-force-parse="false" data-date-assume-nearby-year="true" data-date-format="mm/dd/yyyy" data-date-autoclose="true" data-provide="datepicker" type="text" value="03/27/2025" name="compare[to_box]" id="compare_to_box" />
<span class='input-group-addon calendar-btn'><span class='glyphicon glyphicon-calendar'></span></span>
</div>
<input class="form-control bootstrap_datepicker optional" data-date-start-date="1/03/2017" data-date-force-parse="false" data-date-assume-nearby-year="true" data-date-format="mm/dd/yyyy" data-date-autoclose="true" data-provide="datepicker" id="to_hidden" value="2025-03-27" autocomplete="off" type="hidden" name="compare[to]" />
</div>

        <div class="form-group hidden compare_hierarchy"><input class="form-control hidden" autocomplete="off" type="hidden" value="{&quot;title&quot;:&quot;44&quot;,&quot;chapter&quot;:&quot;I&quot;,&quot;subchapter&quot;:&quot;D&quot;,&quot;part&quot;:&quot;206&quot;,&quot;subpart&quot;:&quot;G&quot;,&quot;section&quot;:&quot;206.202&quot;,&quot;complete&quot;:true}" name="compare[hierarchy]" id="compare_hierarchy" /></div>
        <div class="form-group hidden compare_view_mode"><input class="form-control hidden" autocomplete="off" type="hidden" name="compare[view_mode]" id="compare_view_mode" /></div>

        <input type="submit" name="commit" value="Compare Dates" class="btn btn-default btn btn-primary-alt btn-medium" data-disable-with="Compare Dates" />
</form>
      <form class="simple_form compare-style-choices" id="new_compare_style_choices" novalidate="novalidate" action="#" accept-charset="UTF-8" method="get">
        <div class="form-group radio_buttons optional compare_style_choices_diff_color"><label class="control-label radio_buttons optional">Show changes as:</label><span class="radio"><label for="compare_style_choices_diff_color_rg-diff"><input class="radio_buttons optional" type="radio" value="rg-diff" checked="checked" name="compare_style_choices[diff_color]" id="compare_style_choices_diff_color_rg-diff" />red/green</label></span><span class="radio"><label for="compare_style_choices_diff_color_po-diff"><input class="radio_buttons optional" type="radio" value="po-diff" name="compare_style_choices[diff_color]" id="compare_style_choices_diff_color_po-diff" />purple/orange</label></span></div>

        <div class="form-group radio_buttons optional compare_style_choices_strikethrough"><label class="control-label radio_buttons optional">Show strike-through for removed content</label><span class="radio"><label for="compare_style_choices_strikethrough_true"><input class="radio_buttons optional" type="radio" value="true" checked="checked" name="compare_style_choices[strikethrough]" id="compare_style_choices_strikethrough_true" />yes</label></span><span class="radio"><label for="compare_style_choices_strikethrough_false"><input class="radio_buttons optional" readonly="readonly" type="radio" value="false" name="compare_style_choices[strikethrough]" id="compare_style_choices_strikethrough_false" />no</label></span></div>
</form></div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Compare Dates</h6></div></div></li>

  <li class="divider"></li>

  
<li class="enhanced published-edition" id="" tabindex="0"><span role="button" aria-label="Published Edition" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Published Edition" class="icon-ecfr icon-ecfr-doc-pdf "></span><span class="content-nav-label " role="button">Published Edition</span><div class="box box-enhanced box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Published Edition</h6></div></div><div class="content-block ">    <p>
      View the most recent official publication:
    </p>

    <ul class="list with-bullets enhanced">
        <li>
          <a data-turbo="false" href="https://www.govinfo.gov/app/collection/cfr/2025">View Title 44 on govinfo.gov</a>
        </li>
        <li>
          <a data-turbo="false" href="https://www.govinfo.gov/link/cfr/44/206?link-type=pdf&amp;sectionnum=202&amp;year=mostrecent">View the PDF for 44 CFR 206.202</a>
        </li>
    </ul>

    <p>
      These links go to the official, published CFR, which is updated annually. As a
      result, it may not include the most recent changes applied to the CFR.
      <a href="https://www.govinfo.gov/help/cfr#about">Learn more</a>.
    </p>
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Published Edition</h6></div></div></li>

  <li class="divider"></li>

  
<li class="enhanced developer-tools" id="" tabindex="0"><span role="button" aria-label="Developer Tools" data-delay="{&quot;show&quot;:300,&quot;hide&quot;:0}" data-toggle="tooltip" data-placement="top" data-title="Developer Tools" class="icon-ecfr icon-ecfr-console "></span><span class="content-nav-label " role="button">Developer Tools</span><div class="box box-enhanced box-full dropdown-menu dropdown-menu-right" role="menu"><div class="seal-block seal-block-header"><div class="seal-content"><h6>Enhanced Content - Developer Tools</h6></div></div><div class="content-block ">
      <turbo-frame id="side_developer" src="/developer/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202" loading="lazy">
        <span class="timeline-spinner">
          <span class="spinner"></span>
        </span>
        <br/><br/>
      </turbo-frame>

    <p>
      Information and documentation can be found in our
      <a href="/reader-aids/ecfr-developer-resources">developer resources</a>.
    </p>
</div><div class="seal-block seal-block-footer"><h6>Enhanced Content - Developer Tools</h6></div></div></li>
</ul>

      </div>

      <div class="col-xs-9 col-sm-10 content-col">
        <div class="box box-published no-print-header compare-settings"><div class="seal-block seal-block-header"><div class="seal-content"><h6>eCFR Content</h6><div class="row seal-meta"><div class="seal-desc col-md-12 col-xs-12"><p><p>The <em>Code of Federal Regulations</em> (CFR) is the official legal print publication containing the codification of the general and permanent rules published in the <em>Federal Register</em> by the departments and agencies of the Federal Government. The Electronic Code of Federal Regulations (eCFR) is a continuously updated online version of the CFR. It is not an official legal edition of the CFR.</p> <p><a href='/reader-aids/understanding-the-ecfr/what-is-the-ecfr'>Learn more</a> about the eCFR, its status, and the editorial process.<p></p></div></div></div></div><div class="content-block leaf">

          <div class="section" id="206.202">
<h4 data-hierarchy-metadata='{"path":"/current/title-44/section-206.202","citation":"44 CFR 206.202"}'>§ 206.202 Application procedures.</h4>
<div id="p-206.202(a)">
<p class="indent-1" data-title="206.202(a)"><span class="paragraph-hierarchy"><span class="paren">(</span>a<span class="paren">)</span></span> <em class="paragraph-heading">General.</em>  This section describes the policies and procedures that we use to process public assistance grants to States. Under this section the State is the recipient. As recipient you are responsible for processing subgrants to applicants under <a href="/current/title-2/part-200" class="cfr external">2 CFR parts 200</a> and <a href="/current/title-2/part-3002" class="cfr external">3002</a>, and <a href="/current/title-44/part-206" class="cfr external">44 CFR part 206</a>, and your own policies and procedures.</p>
</div>
<div id="p-206.202(b)">
<p class="indent-1" data-title="206.202(b)"><span class="paragraph-hierarchy"><span class="paren">(</span>b<span class="paren">)</span></span> <em class="paragraph-heading">Recipient.</em>  You are the grant administrator for all funds provided under the Public Assistance grant program. Your responsibilities under this section include:</p>
<div id="p-206.202(b)(1)">
<p class="indent-2" data-title="206.202(b)(1)"><span class="paragraph-hierarchy"><span class="paren">(</span>1<span class="paren">)</span></span> Providing technical advice and assistance to eligible subrecipients;</p>
</div>
<div id="p-206.202(b)(2)">
<p class="indent-2" data-title="206.202(b)(2)"><span class="paragraph-hierarchy"><span class="paren">(</span>2<span class="paren">)</span></span> Providing State support for project identification activities to include small and large project formulation and the validation of small projects;</p>
</div>
<div id="p-206.202(b)(3)">
<p class="indent-2" data-title="206.202(b)(3)"><span class="paragraph-hierarchy"><span class="paren">(</span>3<span class="paren">)</span></span> Ensuring that all potential applicants are aware of available public assistance; and</p>
</div>
<div id="p-206.202(b)(4)">
<p class="indent-2" data-title="206.202(b)(4)"><span class="paragraph-hierarchy"><span class="paren">(</span>4<span class="paren">)</span></span> Submitting documents necessary for the award of grants.</p>
</div>
</div>
<div id="p-206.202(c)">
<p class="indent-1" data-title="206.202(c)"><span class="paragraph-hierarchy"><span class="paren">(</span>c<span class="paren">)</span></span> <em class="paragraph-heading">Request for Public Assistance (Request).</em>  The recipient must send a completed <em>Request</em> (FEMA Form 90-49) to the Regional Administrator for each applicant who requests public assistance. You must send <em>Requests</em> to the Regional Administrator within 30 days after designation of the area where the damage occurred.</p>
</div>
<div id="p-206.202(d)">
<p class="indent-1" data-title="206.202(d)"><span class="paragraph-hierarchy"><span class="paren">(</span>d<span class="paren">)</span></span> <em class="paragraph-heading">Project Worksheets.</em> </p>
<div id="p-206.202(d)(1)">
<p class="indent-2" data-title="206.202(d)(1)"><span class="paragraph-hierarchy"><span class="paren">(</span>1<span class="paren">)</span></span> An applicant's authorized local representative is responsible for representing the applicant and for ensuring that the applicant has identified all eligible work and submitted all costs for disaster-related damages for funding.</p>
<div id="p-206.202(d)(1)(i)">
<p class="indent-3" data-title="206.202(d)(1)(i)"><span class="paragraph-hierarchy"><span class="paren">(</span>i<span class="paren">)</span></span> We or the applicant, assisted by the State as appropriate, will prepare a Project Worksheet (FEMA Form 90-91) for each project. The Project Worksheet must identify the eligible scope of work and must include a quantitative estimate for the eligible work.</p>
</div>
<div id="p-206.202(d)(1)(ii)">
<p class="indent-3" data-title="206.202(d)(1)(ii)"><span class="paragraph-hierarchy"><span class="paren">(</span>ii<span class="paren">)</span></span> The applicant will have 60 days following its first substantive meeting with us to identify and to report damage to us.</p>
</div>
</div>
<div id="p-206.202(d)(2)">
<p class="indent-2" data-title="206.202(d)(2)"><span class="paragraph-hierarchy"><span class="paren">(</span>2<span class="paren">)</span></span> When the estimated cost of work on a project is less than $3,000, that work is not eligible and we will not approve a Project Worksheet for the project. Such $3,000 amount shall be adjusted annually to reflect changes in the Consumer Price Index for All Urban Consumers published by the Department of Labor.</p>
</div>
</div>
<div id="p-206.202(e)">
<p class="indent-1" data-title="206.202(e)"><span class="paragraph-hierarchy"><span class="paren">(</span>e<span class="paren">)</span></span> <em class="paragraph-heading">Grant approval.</em> </p>
<div id="p-206.202(e)(1)">
<p class="indent-2" data-title="206.202(e)(1)"><span class="paragraph-hierarchy"><span class="paren">(</span>1<span class="paren">)</span></span> Before we obligate any funds to the State, the recipient must complete and send to the Regional Administrator a Standard Form (SF) 424, Application for Federal Assistance, and a SF 424D, Assurances for Construction Programs. After we receive the SF 424 and SF 424D, the Regional Administrator will obligate funds to the recipient based on the approved Project Worksheets. The recipient will then approve subgrants based on the Project Worksheets approved for each applicant.</p>
</div>
<div id="p-206.202(e)(2)">
<p class="indent-2" data-title="206.202(e)(2)"><span class="paragraph-hierarchy"><span class="paren">(</span>2<span class="paren">)</span></span> When the applicant submits the Project Worksheets, we will have 45 days to obligate Federal funds. If we have a delay beyond 45 days we will explain the delay to the recipient.</p>
</div>
</div>
<div id="p-206.202(f)">
<p class="indent-1" data-title="206.202(f)"><span class="paragraph-hierarchy"><span class="paren">(</span>f<span class="paren">)</span></span> <em class="paragraph-heading">Exceptions.</em>  The following are exceptions to the procedures and time limitations outlined in this section.</p>
<div id="p-206.202(f)(1)">
<p class="indent-2" data-title="206.202(f)(1)"><span class="paragraph-hierarchy"><span class="paren">(</span>1<span class="paren">)</span></span> <em class="paragraph-heading">Host-State Evacuation and/or Sheltering</em> —</p>
<div id="p-206.202(f)(1)(i)">
<p class="indent-3" data-title="206.202(f)(1)(i)"><span class="paragraph-hierarchy"><span class="paren">(</span>i<span class="paren">)</span></span> <em class="paragraph-heading">General.</em>  A grant to a host-State for sheltering and/or evacuation support is available under this section when an impact-State requests direct Federal assistance for sheltering and/or evacuation support pursuant to <a href="/current/title-44/section-206.208" class="cfr external">§ 206.208</a>. To receive this grant, a host-State must enter into a FEMA-Host-State Agreement, amend its State Administrative Plan pursuant to <a href="/current/title-44/section-206.207" class="cfr external">§ 206.207</a>, and submit a Standard Form SF424 <em>Application for Federal Assistance</em> directly to FEMA to apply for reimbursement of eligible costs for evacuating and/or sheltering individuals from an impact-State. Upon award, the host-State assumes the responsibilities of the “recipient” or “State” under this part with respect to its grant award.</p>
</div>
<div id="p-206.202(f)(1)(ii)">
<p class="indent-3" data-title="206.202(f)(1)(ii)"><span class="paragraph-hierarchy"><span class="paren">(</span>ii<span class="paren">)</span></span> <em class="paragraph-heading">Force Account Labor Costs.</em>  For the performance of eligible evacuation and sheltering support under sections 403 or 502 of the Stafford Act, the straight-time salaries and benefits of a host-State's permanently employed personnel are eligible for reimbursement. This is an exception to <a href="/current/title-44/section-206.228#p-206.228(a)(2)" class="cfr external">§ 206.228(a)(2)</a>.</p>
</div>
</div>
<div id="p-206.202(f)(2)"><p class="indent-2" data-title="206.202(f)(2)"><span class="paragraph-hierarchy"><span class="paren">(</span>2<span class="paren">)</span></span> <em class="paragraph-heading">Time limitations.</em>  The Regional Administrator may extend the time limitations shown in <a href="/current/title-44/section-206.202#p-206.202(c)" class="cfr external">paragraphs (c)</a> and <a href="/current/title-44/section-206.202#p-206.202(d)" class="cfr external">(d)</a> of this section when the recipient justifies and makes a request in writing. The justification must be based on extenuating circumstances beyond the recipient's or subrecipient's control.</p></div>
</div>
<p class="citation">[<a href="https://www.federalregister.gov/citation/64-FR-55160" class="fr-reference" data-reference="64 FR 55160">64 FR 55160</a>, Oct. 12, 1999, as amended at <a href="https://www.federalregister.gov/citation/74-FR-15350" class="fr-reference" data-reference="74 FR 15350">74 FR 15350</a>, Apr. 3, 2009; <a href="https://www.federalregister.gov/citation/74-FR-60213" class="fr-reference" data-reference="74 FR 60213">74 FR 60213</a>, Nov. 20, 2009; <a href="https://www.federalregister.gov/citation/79-FR-10686" class="fr-reference" data-reference="79 FR 10686">79 FR 10686</a>, Feb. 26, 2014; <a href="https://www.federalregister.gov/citation/79-FR-76086" class="fr-reference" data-reference="79 FR 76086">79 FR 76086</a>, Dec. 19, 2014; <a href="https://www.federalregister.gov/citation/82-FR-43" class="fr-reference" data-reference="82 FR 43">82 FR 43</a>, Jan. 3, 2017]
</p>
</div>
<script type="application/json" id="source-and-authority-data">{"origins":[{"level":"part","identifier":"206","label_level":"Part 206","hierarchy":{"title":"44","chapter":"I","subchapter":"D","part":"206"},"current":false,"authority":["Robert T. Stafford Disaster Relief and Emergency Assistance Act, \u003ca href=\"https://www.govinfo.gov/link/uscode/42/5121\" class=\"usc external\" target=\"_blank\" rel=\"noopener noreferrer\"\u003e42 U.S.C. 5121\u003c/a\u003e through \u003ca href=\"https://www.govinfo.gov/link/uscode/42/5207\" class=\"usc external\" target=\"_blank\" rel=\"noopener noreferrer\"\u003e5207\u003c/a\u003e; Homeland Security Act of 2002, \u003ca href=\"https://www.govinfo.gov/link/uscode/6/101\" class=\"usc external\" target=\"_blank\" rel=\"noopener noreferrer\"\u003e6 U.S.C. 101\u003c/a\u003e \u003cem\u003eet seq.;\u003c/em\u003e Department of Homeland Security Delegation 9001.1; sec. 1105, \u003ca href=\"https://www.govinfo.gov/link/plaw/113/public/2\" class=\"publ external\" target=\"_blank\" rel=\"noopener noreferrer\"\u003ePub. L. 113-2\u003c/a\u003e, 127 Stat. 43 (\u003ca href=\"https://www.govinfo.gov/link/uscode/42/5189a\" class=\"usc external\" target=\"_blank\" rel=\"noopener noreferrer\"\u003e42 U.S.C. 5189a note\u003c/a\u003e).\n"],"source":["\u003ca href=\"https://www.federalregister.gov/citation/54-FR-11615\" class=\"fr-reference\" data-reference=\"54 FR 11615\"\u003e54 FR 11615\u003c/a\u003e, Mar. 21, 1989, unless otherwise noted.\n"],"link":"/current/title-44/part-206","title":"Part 206"},{"level":"subpart","identifier":"G","label_level":"Subpart G","hierarchy":{"title":"44","chapter":"I","subchapter":"D","part":"206","subpart":"G"},"current":false,"authority":[],"source":["\u003ca href=\"https://www.federalregister.gov/citation/55-FR-2304\" class=\"fr-reference\" data-reference=\"55 FR 2304\"\u003e55 FR 2304\u003c/a\u003e, Jan. 23, 1990, unless otherwise noted.\n"],"link":"/current/title-44/part-206/subpart-G","title":"Subpart G of Part 206"}]}</script>

</div><div class="seal-block seal-block-footer"><h6>eCFR Content</h6></div></div>      </div>
    </div>
  </div>
</div>

        </div>
      </div>
    </div>

    <div class="site-footer container-fluid" id="footer">
  <div class="container">
    <div class="col-xs-12 col-md-12">
      <div class="row">
        <div class="col-xs-3 col-md-3">
          <h2>Pages</h2>
          <ul class="list with-bullets">
            <li><a href="/">Home</a></li>
            <li><a href="/">Titles</a></li>
            <li><a href="/search">Search</a></li>
            <li><a href="/recent-changes">Recent Changes</a></li>
            <li><a href="/corrections">Corrections</a></li>
          </ul>
        </div>

        <div class="col-xs-3 col-md-3">
          <h2>Reader Aids</h2>
          <ul class="list with-bullets">
              <li>
                <a href="https://www.ecfr.gov/reader-aids/using-ecfr">Using the eCFR Point-in-Time System</a>
              </li>
              <li>
                <a href="https://www.ecfr.gov/reader-aids/understanding-the-ecfr">Understanding the eCFR</a>
              </li>
              <li>
                <a href="https://www.ecfr.gov/reader-aids/government-policy-and-ofr-procedures">Government Policy and OFR Procedures</a>
              </li>
              <li>
                <a href="https://www.ecfr.gov/reader-aids/ecfr-developer-resources">Developer Resources</a>
              </li>
          </ul>
        </div>
        <div class="col-xs-3 col-md-3">
          <h2>Information</h2>
          <ul class="list with-bullets">
            <li><a href="/reader-aids/government-policy-and-ofr-procedures/about-this-site">About This Site</a></li>
            <li><a href="/reader-aids/government-policy-and-ofr-procedures/about-this-site#legal-status">Legal Status</a></li>
            <li><a href="/reader-aids/government-policy-and-ofr-procedures/privacy">Privacy</a></li>
            <li><a href="/reader-aids/government-policy-and-ofr-procedures/accessibility">Accessibility</a></li>
            <li><a href="/reader-aids/government-policy-and-ofr-procedures/foia">FOIA</a></li>
            <li><a href="https://www.archives.gov/eeo/policy/no-fear-notice.html">No Fear Act</a></li>
            <li><a href="https://www.archives.gov/federal-register/contact.html#coop">Continuity Information</a></li>
          </ul>
        </div>

        <div class="col-xs-3 col-md-3">
          <h2>My eCFR</h2>
          <ul class="list with-bullets">
            <li>
              <a href="/my/subscriptions">My Subscriptions</a>
            </li>
            <li>
              <a data-turbo="false" href="https://www.ecfr.gov/auth/sign_in?jwt=eyJhbGciOiJIUzI1NiJ9.eyJub3RpZmljYXRpb25zIjp7ImluZm8iOiJZb3Ugd2lsbCBiZSByZWRpcmVjdGVkIGJhY2sgdG8gdGhlIGVDRlIgYWZ0ZXIgeW91IGhhdmUgc2lnbmVkIGluIG9yIGNyZWF0ZWQgYW4gYWNjb3VudC4ifX0.-sYkHWfC02yb0CpuUqmPkxxnF0yzlNHaS5520hYIUy8&amp;redirect_to=https://www.ecfr.gov/current/title-44/chapter-I/subchapter-D/part-206/subpart-G/section-206.202">Sign In / Sign Up</a>
            </li>
          </ul>
        </div>


      </div>
    </div>
  </div>
</div>

</body></html>
