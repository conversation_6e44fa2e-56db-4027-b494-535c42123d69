"""
Logging middleware for ComplianceMax.
Implements request logging, performance monitoring, and error tracking.
"""

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import time
import uuid
from typing import Optional
from app.core.logging import get_logger, log_performance_metric

logger = get_logger(__name__)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for comprehensive request logging and monitoring."""
    
    async def dispatch(self, request: Request, call_next) -> Response:
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Start timing
        start_time = time.time()
        
        # Extract request details
        client_host = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        referer = request.headers.get("referer", "none")
        
        # Log request start
        logger.info(
            f"Request started",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "client_ip": client_host,
                "user_agent": user_agent,
                "referer": referer
            }
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Add response headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{duration:.3f}"
            
            # Log request completion
            logger.info(
                f"Request completed",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "status_code": response.status_code,
                    "duration": duration,
                    "client_ip": client_host
                }
            )
            
            # Record performance metric
            self._record_performance_metric(request, response, duration)
            
            return response
            
        except Exception as e:
            # Calculate duration even for failed requests
            duration = time.time() - start_time
            
            # Log error details
            logger.error(
                f"Request failed",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "error": str(e),
                    "duration": duration,
                    "client_ip": client_host
                },
                exc_info=True
            )
            
            # Re-raise the exception after logging
            raise
    
    def _record_performance_metric(
        self,
        request: Request,
        response: Response,
        duration: float,
        error: Optional[Exception] = None
    ) -> None:
        """Record request performance metrics."""
        
        # Extract path template if available
        route_path = request.url.path
        if request.scope.get("route"):
            route_path = request.scope["route"].path
        
        # Prepare metric metadata
        metadata = {
            "method": request.method,
            "path": route_path,
            "status_code": response.status_code,
            "client_ip": request.client.host if request.client else "unknown"
        }
        
        # Add error information if present
        if error:
            metadata["error"] = str(error)
            metadata["error_type"] = error.__class__.__name__
        
        # Record the metric
        log_performance_metric(
            operation="http_request",
            duration=duration,
            metadata=metadata
        )
        
        # Log slow requests
        if duration > 1.0:  # Log requests taking more than 1 second
            logger.warning(
                f"Slow request detected",
                extra={
                    "request_id": request.state.request_id,
                    "method": request.method,
                    "path": route_path,
                    "duration": duration,
                    "client_ip": request.client.host if request.client else "unknown"
                }
            )

class ErrorLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for detailed error logging and tracking."""
    
    async def dispatch(self, request: Request, call_next) -> Response:
        try:
            return await call_next(request)
        except Exception as e:
            # Get request ID if available
            request_id = getattr(request.state, "request_id", str(uuid.uuid4()))
            
            # Log detailed error information
            logger.error(
                f"Unhandled exception in request",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "client_ip": request.client.host if request.client else "unknown",
                    "error_type": e.__class__.__name__,
                    "error_message": str(e)
                },
                exc_info=True
            )
            
            # Re-raise the exception
            raise

class ContextLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for adding context information to logs."""
    
    async def dispatch(self, request: Request, call_next) -> Response:
        # Add context to request
        request.state.context = {
            "request_id": str(uuid.uuid4()),
            "client_ip": request.client.host if request.client else "unknown",
            "user_agent": request.headers.get("user-agent", "unknown"),
            "referer": request.headers.get("referer", "none")
        }
        
        # Process request with context
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Ensure context is available in error logs
            logger.error(
                f"Error processing request",
                extra={
                    **request.state.context,
                    "error_type": e.__class__.__name__,
                    "error_message": str(e)
                },
                exc_info=True
            )
            raise 