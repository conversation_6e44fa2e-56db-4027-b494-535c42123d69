{{- if regexMatch "^([0-9]*\\.){3}[0-9]*$" .Values.global.remotePilotAddress }}
# if the remotePilotAddress is an IP addr
apiVersion: v1
kind: Endpoints
metadata:
  {{- if .Values.pilot.enabled }}
  name: istiod-remote
  {{- else }}
  name: istiod
  {{- end }}
  namespace: {{ .Release.Namespace }}
subsets:
- addresses:
  - ip: {{ .Values.global.remotePilotAddress }}
  ports:
  - port: 15012
    name: tcp-istiod
    protocol: TCP
  - port: 15017
    name: tcp-webhook
    protocol: TCP
---
{{- end }}
