import axios from 'axios';
import { retry } from 'ts-retry-promise';

const api = axios.create({
    baseURL: 'http://localhost:8000/api/v1',
    timeout: 10000,
    withCredentials: true
});

api.interceptors.response.use(
    (response) => response.data,
    (error) => {
        if (error.response?.status === 401) {
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

export const getFloodMaps = async () => {
    return retry(
        () => api.get('/flood-maps'),
        { retries: 3, delay: 1000 }
    );
}; 