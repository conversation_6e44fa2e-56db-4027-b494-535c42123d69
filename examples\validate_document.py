from app.validation.checklist_validator import validate_checklist_item

def main():
    # Sample document content
    document_content = """
    Project Implementation Plan
    
    1. Project Scope
    This document outlines the scope and objectives for the flood mitigation project.
    
    2. Budget
    Total project cost: $250,000.00
    - Engineering design: $75,000
    - Construction: $150,000.00
    - Project management: $25,000
    
    3. Timeline
    Project schedule:
    - Design phase start: 2024-04-01
    - Construction start: 2024-07-15
    - Project completion: 2024-12-31
    
    4. Deliverables
    - Engineering drawings
    - Construction specifications
    - As-built documentation
    """
    
    # Example checklist items
    checklist_items = [
        {
            "category": "Documentation",
            "requirement": "Must include project scope",
            "required_keywords": ["scope", "objectives", "project"],
            "confidence_threshold": 0.7
        },
        {
            "category": "Budget",
            "requirement": "Must include detailed cost breakdown",
            "required_keywords": ["budget", "cost"],
            "requires_amounts": True,
            "confidence_threshold": 0.8
        },
        {
            "category": "Timeline",
            "requirement": "Must include project schedule",
            "required_keywords": ["schedule", "timeline"],
            "requires_dates": True,
            "date_format": "YYYY-MM-DD",
            "confidence_threshold": 0.7
        }
    ]
    
    print("Validating document against checklist items...")
    print("-" * 50)
    
    for item in checklist_items:
        print(f"\nChecking {item['category']}: {item['requirement']}")
        result = validate_checklist_item(document_content, item)
        
        print(f"Valid: {result.is_valid}")
        print(f"Confidence: {result.confidence:.2f}")
        print("Details:")
        print(result.message)
        print("-" * 30)

if __name__ == "__main__":
    main() 