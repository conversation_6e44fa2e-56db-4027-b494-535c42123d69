from datetime import datetime
from typing import List, Optional
from sqlalchemy import String, DateTime, ForeignKey, Float, JSON, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base, TimestampMixin
import enum

class ScanStatus(str, enum.Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class ReviewStatus(str, enum.Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"

class Standard(Base, TimestampMixin):
    __tablename__ = "standards"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    code: Mapped[str] = mapped_column(String, unique=True, index=True)
    title: Mapped[str] = mapped_column(String)
    description: Mapped[str] = mapped_column(String)
    version: Mapped[str] = mapped_column(String)
    effective_date: Mapped[datetime] = mapped_column(DateTime)
    
    requirements: Mapped[List["Requirement"]] = relationship("Requirement", back_populates="standard")

class Requirement(Base, TimestampMixin):
    __tablename__ = "requirements"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    standard_id: Mapped[int] = mapped_column(ForeignKey("standards.id"))
    code: Mapped[str] = mapped_column(String)
    description: Mapped[str] = mapped_column(String)
    criteria: Mapped[str] = mapped_column(String)
    
    standard: Mapped["Standard"] = relationship("Standard", back_populates="requirements")
    reviews: Mapped[List["CBCSComplianceReview"]] = relationship("CBCSComplianceReview", back_populates="requirement")

class CBCSComplianceReview(Base, TimestampMixin):
    __tablename__ = "cbcs_compliance_reviews"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    requirement_id: Mapped[int] = mapped_column(ForeignKey("requirements.id"))
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.id"))
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"), index=True)
    status: Mapped[ReviewStatus] = mapped_column(SQLEnum(ReviewStatus), default=ReviewStatus.PENDING)
    review_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    reviewer_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    notes: Mapped[str] = mapped_column(String)
    evidence: Mapped[str] = mapped_column(String)
    risk_level: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    compliance_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    findings: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    
    # Relationships
    requirement: Mapped["Requirement"] = relationship("Requirement", back_populates="reviews")
    project: Mapped["Project"] = relationship("Project", back_populates="cbcs_compliance_reviews")
    document: Mapped["Document"] = relationship("Document", back_populates="cbcs_reviews")
    reviewer: Mapped["User"] = relationship("User", back_populates="cbcs_compliance_reviews")
    scans: Mapped[List["CBCSScan"]] = relationship("CBCSScan", back_populates="compliance_review")

class CBCSScan(Base, TimestampMixin):
    __tablename__ = "cbcs_scans"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"), index=True)
    compliance_review_id: Mapped[int] = mapped_column(ForeignKey("cbcs_compliance_reviews.id"), index=True)
    scan_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    scan_type: Mapped[str] = mapped_column(String)  # OCR, NLP, Manual, etc.
    status: Mapped[ScanStatus] = mapped_column(SQLEnum(ScanStatus), default=ScanStatus.PENDING)
    scan_results: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    error_message: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    
    # Relationships
    document: Mapped["Document"] = relationship("Document", back_populates="cbcs_scans")
    compliance_review: Mapped["CBCSComplianceReview"] = relationship("CBCSComplianceReview", back_populates="scans")