{{ $gateway := index .Values "gateways" "istio-ingressgateway" }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $gateway.name }}-sds
  namespace: {{ .Release.Namespace }}
  labels:
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "IngressGateways"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ $gateway.name }}-sds
subjects:
- kind: ServiceAccount
  name: {{ $gateway.name }}-service-account
---
