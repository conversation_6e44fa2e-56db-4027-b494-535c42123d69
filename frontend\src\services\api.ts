import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../contexts/AuthContext';
import { useContext, useState } from 'react';
import { retry } from 'ts-retry-promise';
import { API_BASE_URL } from '../config';

const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
});

api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

interface Project {
  id: string;
  name: string;
  status: string;
  start_date: string;
  end_date?: string;
  description: string;
}

export const useApi = () => {
    const { setAuthData, logout } = useContext(AuthContext);
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const responseInterceptor = api.interceptors.response.use(
        (response) => response,
        (error) => {
            if (error.response?.status === 401) {
                logout();
                navigate('/login', { state: { message: 'Your session has expired. Please log in again.' } });
            }
            return Promise.reject(error);
        }
    );

    const login = async (username: string, password: string) => {
        setIsLoading(true);
        setError(null);
        
        try {
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            
            const response = await api.post('/auth/login', formData);
            return response.data;
        } catch (error: any) {
            console.error('Login error:', error);
            setError(error.response?.data?.detail || 'Failed to login');
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    const register = async (userData: { username: string; email: string; password: string; full_name: string }) => {
        setIsLoading(true);
        setError(null);
        
        try {
            const response = await api.post('/auth/register', userData);
            return response.data;
        } catch (error: any) {
            console.error('Registration error:', error);
            setError(error.response?.data?.detail || 'Failed to register');
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    const getBCAAnalyses = async () => {
        setIsLoading(true);
        setError(null);
        
        try {
            const response = await retry(
                () => api.get('/bca/'),
                { retries: 3, delay: 1000 }
            );
            return response.data;
        } catch (error: any) {
            console.error('Error fetching BCA analyses:', error);
            setError(error.response?.data?.detail || 'Failed to fetch BCA analyses');
            return [];
        } finally {
            setIsLoading(false);
        }
    };

    const getProjects = async (): Promise<Project[]> => {
        setIsLoading(true);
        setError(null);
        
        try {
            const response = await api.get('/projects');
            return response.data;
        } catch (error: any) {
            console.error('Error fetching projects:', error);
            setError(error.response?.data?.detail || 'Failed to fetch projects');
            return [];
        } finally {
            setIsLoading(false);
        }
    };
    
    const getProject = async (id: string): Promise<Project | null> => {
        setIsLoading(true);
        setError(null);
        
        try {
            const response = await api.get(`/projects/${id}`);
            return response.data;
        } catch (error: any) {
            console.error(`Error fetching project ${id}:`, error);
            setError(error.response?.data?.detail || 'Failed to fetch project');
            return null;
        } finally {
            setIsLoading(false);
        }
    };

    return {
        login,
        register,
        getBCAAnalyses,
        isLoading,
        error,
        getProjects,
        getProject,
    };
};

export { api };