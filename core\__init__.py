"""
ComplianceMax Core Module

This module provides the core functionality for the ComplianceMax system,
including document processing, policy matching, and compliance checking.
"""

from .document_processor import DocumentProcessor
from .policy_matcher import PolicyMatcher
from .requirement_validator import RequirementValidator
from .compliance_checker import ComplianceChecker

__version__ = '1.0.0'

__all__ = [
    'DocumentProcessor',
    'PolicyMatcher',
    'RequirementValidator',
    'ComplianceChecker',
] 