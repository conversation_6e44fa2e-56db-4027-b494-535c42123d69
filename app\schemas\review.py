from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from datetime import datetime
from app.models.review import ReviewStatus, ReviewType

class ReviewBase(BaseModel):
    project_id: int
    type: ReviewType
    status: ReviewStatus = ReviewStatus.PENDING
    findings: Dict = Field(default_factory=dict)

class MitigationReviewCreate(ReviewBase):
    cost_estimate: float
    recommendations: List[str]
    implementation_timeline: int

class EHPReviewCreate(ReviewBase):
    environmental_impact: Dict
    historical_considerations: Dict
    required_permits: List[str]

class CostReviewCreate(ReviewBase):
    total_cost: float
    cost_breakdown: Dict
    alternatives: List[Dict]

class ComplianceReviewCreate(ReviewBase):
    policy_version_id: int
    compliance_score: float
    violations: List[Dict]
    remediation_plan: Dict

class ReviewStatusUpdate(BaseModel):
    status: ReviewStatus

class ReviewFindings(BaseModel):
    findings: Dict

class ReviewResponse(ReviewBase):
    id: int
    reviewer_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    # Additional fields for specific review types
    cost_estimate: Optional[float] = None
    recommendations: Optional[List[str]] = None
    implementation_timeline: Optional[int] = None
    environmental_impact: Optional[Dict] = None
    historical_considerations: Optional[Dict] = None
    required_permits: Optional[List[str]] = None
    total_cost: Optional[float] = None
    cost_breakdown: Optional[Dict] = None
    alternatives: Optional[List[Dict]] = None
    policy_version_id: Optional[int] = None
    compliance_score: Optional[float] = None
    violations: Optional[List[Dict]] = None
    remediation_plan: Optional[Dict] = None

    class Config:
        from_attributes = True
