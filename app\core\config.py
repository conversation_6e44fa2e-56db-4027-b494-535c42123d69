"""
Configuration settings for ComplianceMax.

This module provides a centralized configuration system for the PA-CHECK application
using Pydantic for validation and environment variable loading. All application
settings should be defined here to maintain consistency and facilitate deployment
across different environments.
"""
import os
import secrets
from typing import Dict, List, Optional, Any, Union
from functools import lru_cache

from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, validator, SecretStr

class Settings(BaseSettings):
    """Application settings."""
    API_V1_STR: str = "/api/v1"

    # Application settings
    APP_NAME: str = Field(
        "ComplianceMax",
        description="Name of the application"
    )
    APP_VERSION: str = Field(
        "1.0.0",
        description="Application version number"
    )
    DEBUG: bool = Field(
        False,
        description="Debug mode flag - enables detailed error pages and logging"
    )

    # Database settings
    DATABASE_URL: str = Field(
        "sqlite:///./compliancemax.db",
        description="Database connection string (supports SQLite, PostgreSQL, MySQL)"
    )
    DB_POOL_SIZE: int = Field(
        5,
        description="Size of the database connection pool"
    )
    DB_MAX_OVERFLOW: int = Field(
        10,
        description="Maximum overflow connections for the database pool"
    )
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        # alias so tests that do settings.SQLALCHEMY_DATABASE_URI still work
        return self.DATABASE_URL

    @property
    def DATABASE_POOL_SIZE(self) -> int:
        return self.DB_POOL_SIZE

    @property
    def DATABASE_MAX_OVERFLOW(self) -> int:
        return self.DB_MAX_OVERFLOW

    # Redis settings
    REDIS_URL: str = Field(
        "redis://localhost:6379/0",
        description="Redis connection string for caching and message queuing"
    )
    REDIS_POOL_SIZE: int = Field(
        10,
        description="Size of the Redis connection pool"
    )

    # Security settings
    SECRET_KEY: str = Field(
        secrets.token_hex(32),
        description="Secret key for signing JWT tokens and cookies - MUST be changed in production"
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        30,
        description="Expiration time for access tokens in minutes"
    )
    ALGORITHM: str = Field(
        "HS256",
        description="Algorithm used for JWT token signing"
    )
    SECURE_HEADERS: bool = Field(
        True,
        description="Enable security headers (X-XSS-Protection, X-Content-Type-Options, etc.)"
    )
    CSRF_PROTECTION: bool = Field(
        True,
        description="Enable CSRF protection for API endpoints"
    )

    # CORS settings
    CORS_ORIGINS: List[str] = Field(
        ["*"],
        description="Allowed origins for CORS - should be restricted in production"
    )
    CORS_METHODS: List[str] = Field(
        ["*"],
        description="Allowed HTTP methods for CORS"
    )
    CORS_HEADERS: List[str] = Field(
        ["*"],
        description="Allowed HTTP headers for CORS"
    )

    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = Field(
        60,
        description="Maximum number of requests per minute per IP"
    )
    RATE_LIMIT_BURST: int = Field(
        100,
        description="Maximum burst size for rate limiting"
    )

    # Document processing settings
    REQUIREMENT_SIMILARITY_THRESHOLD: float = Field(
        0.8,
        description="Threshold for requirement similarity matching (0.0-1.0)"
    )
    MAX_DOCUMENT_SIZE_MB: int = Field(
        10,
        description="Maximum document size in megabytes"
    )
    ALLOWED_DOCUMENT_TYPES: List[str] = Field(
        ["pdf", "docx", "xlsx", "txt", "html"],
        description="List of allowed document file extensions"
    )

    # OCR settings
    OCR_ENGINE: str = Field(
        "tesseract",
        description="Default OCR engine to use (tesseract, easyocr, paddleocr)"
    )
    ENABLE_GPU: bool = Field(
        False,
        description="Enable GPU acceleration for OCR and ML models"
    )
    OCR_LANGUAGE: str = Field(
        "eng",
        description="Default language for OCR processing"
    )
    OCR_DPI: int = Field(
        300,
        description="DPI for image processing in OCR"
    )
    OCR_TIMEOUT: int = Field(
        300,
        description="Timeout for OCR processing in seconds"
    )

    # Document cache settings
    DOCUMENT_CACHE_ENABLED: bool = Field(
        True,
        description="Enable caching of processed documents"
    )
    DOCUMENT_CACHE_TTL: int = Field(
        86400,  # 24 hours
        description="Time-to-live for document cache entries in seconds"
    )
    METADATA_DATE_PATTERN: str = Field(
        r'\b(?:\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}|\d{4}[/.-]\d{1,2}[/.-]\d{1,2}|\w+ \d{1,2},? \d{4})\b',
        description="Regex pattern for date extraction in metadata service"
    )

    # Monitoring settings
    PROMETHEUS_ENABLED: bool = Field(
        True,
        description="Enable Prometheus metrics collection"
    )
    METRICS_PREFIX: str = Field(
        "compliancemax",
        description="Prefix for all metrics names"
    )

    # Real-time monitoring settings
    MONITORING_ENABLED: bool = Field(
        True,
        description="Enable real-time monitoring system"
    )
    ALERT_THRESHOLD_MODEL_LATENCY: float = Field(
        1.0,  # seconds
        description="Alert threshold for model latency in seconds"
    )
    ALERT_THRESHOLD_PREDICTION_ACCURACY: float = Field(
        0.9,  # 90%
        description="Alert threshold for prediction accuracy (0.0-1.0)"
    )
    ALERT_THRESHOLD_FEATURE_DRIFT: float = Field(
        0.2,  # 20%
        description="Alert threshold for feature drift detection (0.0-1.0)"
    )
    ALERT_THRESHOLD_ERROR_RATE: float = Field(
        0.05,  # 5%
        description="Alert threshold for error rate (0.0-1.0)"
    )
    ALERT_THRESHOLD_SYSTEM_LOAD: float = Field(
        0.8,  # 80%
        description="Alert threshold for system load (0.0-1.0)"
    )
    MONITORING_CHECK_INTERVAL: int = Field(
        60,  # seconds
        description="Interval for periodic monitoring checks in seconds"
    )

    # Backup settings
    BACKUP_ENABLED: bool = Field(
        True,
        description="Enable automated backups"
    )
    BACKUP_INTERVAL_HOURS: int = Field(
        24,
        description="Interval between backups in hours"
    )
    BACKUP_RETENTION_DAYS: int = Field(
        30,
        description="Number of days to retain backups"
    )
    S3_BUCKET: Optional[str] = Field(
        None,
        description="S3 bucket name for backup storage (None for local storage)"
    )
    S3_REGION: Optional[str] = Field(
        None,
        description="AWS region for S3 backup storage"
    )

    # ML settings
    ML_MODEL_PATH: str = Field(
        "./models",
        description="Path to ML model files"
    )
    ML_TRAINING_INTERVAL_DAYS: int = Field(
        7,
        description="Interval between model retraining in days"
    )
    ML_PREDICTION_CACHE_TTL: int = Field(
        3600,
        description="Time-to-live for prediction cache in seconds"
    )

    # Feature engineering settings
    FEATURE_ENGINEERING_ENABLED: bool = Field(
        True,
        description="Enable enhanced feature engineering"
    )
    FEATURE_HISTORY_RETENTION_DAYS: int = Field(
        90,
        description="Number of days to retain feature history"
    )
    FEATURE_SELECTION_METHOD: str = Field(
        "mutual_info",
        description="Default feature selection method (mutual_info, pca)"
    )
    FEATURE_DRIFT_THRESHOLD: float = Field(
        0.2,
        description="Threshold for detecting feature drift (0.0-1.0)"
    )

    # Integration settings
    INTEGRATION_SYNC_INTERVAL: int = Field(
        300,  # 5 minutes
        description="Interval for integration syncs in seconds"
    )
    INTEGRATION_HEALTH_CHECK_INTERVAL: int = Field(
        60,  # 1 minute
        description="Interval for integration health checks in seconds"
    )
    INTEGRATION_RETRY_ATTEMPTS: int = Field(
        3,
        description="Number of retry attempts for integration operations"
    )
    INTEGRATION_RETRY_DELAY: int = Field(
        5,
        description="Delay between retry attempts in seconds"
    )

    # Batch processing settings
    BATCH_PROCESSING_ENABLED: bool = Field(
        True,
        description="Enable batch processing system"
    )
    BATCH_WORKER_COUNT: int = Field(
        4,
        description="Number of worker processes for batch processing"
    )
    BATCH_QUEUE_MAX_SIZE: int = Field(
        1000,
        description="Maximum size of the batch processing queue"
    )
    BATCH_TASK_TIMEOUT: int = Field(
        3600,  # 1 hour
        description="Timeout for batch tasks in seconds"
    )
    BATCH_STUCK_TASK_THRESHOLD: int = Field(
        7200,  # 2 hours
        description="Time threshold to consider a task stuck in seconds"
    )

    # Logging settings
    LOG_LEVEL: str = Field(
        "INFO",
        description="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"
    )
    LOG_FORMAT: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Format string for log messages"
    )
    LOG_DIR: str = Field(
        "logs",
        description="Directory for log files"
    )
    LOG_FILE_MAX_BYTES: int = Field(
        10 * 1024 * 1024,  # 10MB
        description="Maximum size of log files before rotation in bytes"
    )
    LOG_FILE_BACKUP_COUNT: int = Field(
        5,
        description="Number of backup log files to keep"
    )
    LOG_AUDIT_RETENTION_DAYS: int = Field(
        30,
        description="Number of days to retain audit logs"
    )
    STRUCTURED_LOGGING: bool = Field(
        True,
        description="Enable structured logging with JSON format"
    )

    @validator("SECRET_KEY")
    def validate_secret_key(cls, v: str) -> str:
        """Validate secret key."""
        if len(v) < 32:
            raise ValueError("Secret key must be at least 32 characters long")
        return v

    @validator("DATABASE_URL")
    def validate_database_url(cls, v: str) -> str:
        """Validate database URL."""
        if not v:
            raise ValueError("Database URL cannot be empty")
        return v

    @validator("REDIS_URL")
    def validate_redis_url(cls, v: str) -> str:
        """Validate Redis URL."""
        if not v:
            raise ValueError("Redis URL cannot be empty")
        return v

    @validator("LOG_LEVEL")
    def validate_log_level(cls, v: str) -> str:
        """Validate log level."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Invalid log level. Must be one of {allowed_levels}")
        return v.upper()

    @validator("OCR_ENGINE")
    def validate_ocr_engine(cls, v: str) -> str:
        """Validate OCR engine."""
        allowed_engines = ["tesseract", "easyocr", "paddleocr"]
        if v.lower() not in allowed_engines:
            raise ValueError(f"Invalid OCR engine. Must be one of {allowed_engines}")
        return v.lower()

    @validator("FEATURE_SELECTION_METHOD")
    def validate_feature_selection_method(cls, v: str) -> str:
        """Validate feature selection method."""
        allowed_methods = ["mutual_info", "pca"]
        if v.lower() not in allowed_methods:
            raise ValueError(f"Invalid feature selection method. Must be one of {allowed_methods}")
        return v.lower()

    model_config = SettingsConfigDict(
        env_file=[".env", ".env.local"],  # Look for .env files
        env_file_encoding="utf-8",
        env_nested_delimiter="__",
        case_sensitive=True,
        extra="ignore"
    )

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()

settings = get_settings()
