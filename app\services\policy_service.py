from datetime import datetime
from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from fastapi import HTTPException
from app.models.policy import PolicyDocument, PolicyVersion, PolicyType
from app.models.disaster import DisasterDeclaration
import logging

logger = logging.getLogger(__name__)

class PolicyService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_policy(
        self,
        policy_number: str,
        title: str,
        description: str,
        policy_type: PolicyType,
        version_number: str,
        content: str,
        effective_date: datetime,
        expiration_date: Optional[datetime] = None,
        document_url: Optional[str] = None
    ) -> PolicyDocument:
        """Create a new policy document with initial version"""
        try:
            # Check if policy number already exists
            stmt = select(PolicyDocument).where(
                PolicyDocument.policy_number == policy_number
            )
            result = await self.db.execute(stmt)
            if result.scalar_one_or_none():
                raise HTTPException(
                    status_code=400,
                    detail="Policy number already exists"
                )

            # Create policy document
            policy = PolicyDocument(
                policy_number=policy_number,
                title=title,
                description=description,
                policy_type=policy_type
            )
            self.db.add(policy)
            
            # Create initial version
            version = PolicyVersion(
                policy=policy,
                version_number=version_number,
                content=content,
                effective_date=effective_date,
                expiration_date=expiration_date,
                document_url=document_url
            )
            self.db.add(version)
            
            await self.db.commit()
            await self.db.refresh(policy)
            return policy

        except Exception as e:
            logger.error(f"Error creating policy: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def get_policy(self, policy_id: int) -> PolicyDocument:
        """Get a policy document by ID"""
        stmt = select(PolicyDocument).where(
            PolicyDocument.id == policy_id
        ).options(
            joinedload(PolicyDocument.versions)
        )
        result = await self.db.execute(stmt)
        policy = result.unique().scalar_one_or_none()
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")
        return policy

    async def add_version(
        self,
        policy_id: int,
        version_number: str,
        content: str,
        effective_date: datetime,
        expiration_date: Optional[datetime] = None,
        document_url: Optional[str] = None
    ) -> PolicyVersion:
        """Add a new version to an existing policy"""
        try:
            policy = await self.get_policy(policy_id)
            
            # Check if version number already exists
            for version in policy.versions:
                if version.version_number == version_number:
                    raise HTTPException(
                        status_code=400,
                        detail="Version number already exists"
                    )

            version = PolicyVersion(
                policy=policy,
                version_number=version_number,
                content=content,
                effective_date=effective_date,
                expiration_date=expiration_date,
                document_url=document_url
            )
            self.db.add(version)
            await self.db.commit()
            await self.db.refresh(version)
            return version

        except Exception as e:
            logger.error(f"Error adding policy version: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def get_applicable_policies(
        self,
        dr_number: str,
        effective_date: datetime
    ) -> List[PolicyVersion]:
        """Get all policy versions applicable to a disaster"""
        try:
            # Get disaster declaration
            disaster = await self.db.get(DisasterDeclaration, dr_number)
            if not disaster:
                raise HTTPException(
                    status_code=404,
                    detail="Disaster declaration not found"
                )

            # Find applicable policy versions
            stmt = select(PolicyVersion).where(
                PolicyVersion.effective_date <= effective_date,
                (
                    PolicyVersion.expiration_date.is_(None) |
                    (PolicyVersion.expiration_date >= effective_date)
                )
            ).join(
                PolicyVersion.applicable_disasters
            ).where(
                DisasterDeclaration.dr_number == dr_number
            ).options(
                joinedload(PolicyVersion.policy)
            )
            
            result = await self.db.execute(stmt)
            versions = result.unique().scalars().all()
            return list(versions)

        except Exception as e:
            logger.error(f"Error getting applicable policies: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def search_policies(
        self,
        query: str,
        policy_type: Optional[PolicyType] = None,
        effective_date: Optional[datetime] = None
    ) -> List[PolicyDocument]:
        """Search for policy documents"""
        try:
            stmt = select(PolicyDocument).where(
                (PolicyDocument.title.ilike(f"%{query}%")) |
                (PolicyDocument.description.ilike(f"%{query}%"))
            )
            
            if policy_type:
                stmt = stmt.where(PolicyDocument.policy_type == policy_type)
                
            if effective_date:
                stmt = stmt.join(
                    PolicyDocument.versions
                ).where(
                    PolicyVersion.effective_date <= effective_date,
                    (
                        PolicyVersion.expiration_date.is_(None) |
                        (PolicyVersion.expiration_date >= effective_date)
                    )
                )
                
            stmt = stmt.options(joinedload(PolicyDocument.versions))
            result = await self.db.execute(stmt)
            policies = result.unique().scalars().all()
            return list(policies)

        except Exception as e:
            logger.error(f"Error searching policies: {e}")
            raise HTTPException(status_code=500, detail=str(e))
