"""Document storage service."""

import os
from datetime import datetime
from pathlib import Path
from typing import Optional
from uuid import uuid4

from fastapi import UploadFile

from app.core.config import settings


class StorageService:
    """Service for managing document file storage."""

    def __init__(self, base_path: Optional[str] = None):
        self.base_path = Path(base_path or settings.STORAGE_PATH)
        self.base_path.mkdir(parents=True, exist_ok=True)

    def _generate_path(self, filename: str) -> Path:
        """Generate a unique storage path for a file."""
        date_path = datetime.utcnow().strftime("%Y/%m/%d")
        unique_id = str(uuid4())
        return self.base_path / date_path / unique_id / filename

    async def store_file(self, file: UploadFile) -> str:
        """Store an uploaded file and return its storage path."""
        storage_path = self._generate_path(file.filename)
        storage_path.parent.mkdir(parents=True, exist_ok=True)

        # Write file content
        content = await file.read()
        storage_path.write_bytes(content)
        
        # Reset file pointer for potential reuse
        await file.seek(0)
        
        return str(storage_path.relative_to(self.base_path))

    async def get_file(self, relative_path: str) -> Optional[bytes]:
        """Retrieve file content by its storage path."""
        full_path = self.base_path / relative_path
        if not full_path.exists():
            return None
            
        return full_path.read_bytes()

    async def delete_file(self, relative_path: str) -> bool:
        """Delete a stored file."""
        full_path = self.base_path / relative_path
        if not full_path.exists():
            return False
            
        full_path.unlink()
        
        # Try to remove empty parent directories
        try:
            parent = full_path.parent
            while parent != self.base_path:
                if not any(parent.iterdir()):
                    parent.rmdir()
                parent = parent.parent
        except OSError:
            # Ignore errors in directory cleanup
            pass
            
        return True

    def get_full_path(self, relative_path: str) -> Path:
        """Get the full filesystem path for a storage path."""
        return self.base_path / relative_path 