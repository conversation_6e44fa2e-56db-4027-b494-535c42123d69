apiVersion: v1
kind: ConfigMap
metadata:
  name: multi-region-config
  namespace: compliancemax
data:
  regions:
    primary: us-east-1
    secondary: us-west-2
    failover_threshold: "300" # 5 minutes in seconds
    health_check_interval: "30" # 30 seconds
  
  database:
    replication:
      mode: synchronous
      strategy: multi-master
      regions:
        - name: us-east-1
          role: primary
          priority: 100
          connection_pool_size: 20
        - name: us-west-2
          role: secondary
          priority: 90
          connection_pool_size: 20
      monitoring:
        lag_threshold: 10 # seconds
        sync_check_interval: 5 # seconds
  
  cache:
    replication:
      mode: active-active
      regions:
        - us-east-1
        - us-west-2
      sync_strategy: eventual
      conflict_resolution: last_write_wins
  
  storage:
    type: s3
    backup:
      schedule: "0 */4 * * *"  # Every 4 hours
      retention: 30d
      cross_region: true
      encryption: AES256
      versioning: enabled
    
  failover:
    auto_failover: true
    health_check_interval: 30
    recovery_time_objective: 300  # 5 minutes
    recovery_point_objective: 0   # Zero data loss target
    rollback_threshold: 600  # 10 minutes
    
  monitoring:
    metrics:
      - name: replication_lag
        threshold: 10s
      - name: failover_time
        threshold: 300s
      - name: data_consistency
        threshold: 99.99
    alerts:
      - type: replication_lag
        severity: critical
        threshold: 30s
      - type: region_health
        severity: critical
        threshold: 80 