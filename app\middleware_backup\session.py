from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from app.core.session import session_store
from typing import Optional

class RedisSessionMiddleware(BaseHTTPMiddleware):
    """Middleware to handle Redis-based session management."""
    
    def __init__(self, app, cookie_name: str = "session_id", secure: bool = False):
        super().__init__(app)
        self.cookie_name = cookie_name
        self.secure = secure
    
    async def dispatch(self, request: Request, call_next):
        # Get or create session ID from cookie
        session_id = request.cookies.get(self.cookie_name)
        session_data = None
        
        if session_id:
            session_data = session_store.get_session(session_id)
        
        if not session_id or not session_data:
            # No valid session, create a new one
            session_id = session_store.create_session()
            session_data = session_store.get_session(session_id)
        
        # Attach session data to request state
        request.state.session = session_data
        request.state.session_id = session_id
        
        # Create custom session accessor methods
        def get_session_value(key: str, default=None):
            return session_data.get(key, default)
        
        def set_session_value(key: str, value):
            session_data[key] = value
            session_store.save_session(session_id, session_data)
        
        # Attach methods to request
        request.state.get_session = get_session_value
        request.state.set_session = set_session_value
        
        # Process the request
        response = await call_next(request)
        
        # Set the session cookie in the response
        response.set_cookie(
            key=self.cookie_name,
            value=session_id,
            httponly=True,
            secure=self.secure,
            samesite="lax",
            max_age=session_store.expire
        )
        
        return response
