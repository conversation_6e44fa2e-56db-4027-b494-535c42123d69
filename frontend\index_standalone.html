<!DOCTYPE html>
<html>
<head>
    <title>Port Connectivity Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .box {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            background-color: #f9f9f9;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Port Connectivity Test</h1>
    
    <div class="box">
        <h2>If you can see this page, your browser can access files from this directory.</h2>
        <p>This is a standalone HTML file that doesn't require any server to run.</p>
        <p id="file-path"></p>
    </div>
    
    <div class="box">
        <h2>Test API Connection</h2>
        <p>Click the button below to test if your browser can connect to the backend API at port 8000:</p>
        <button onclick="testApiConnection()">Test Backend Connection</button>
        <div id="api-result"></div>
    </div>
    
    <div class="box">
        <h2>Network Information</h2>
        <p>Your browser's location: <span id="browser-url"></span></p>
        <p>Current ports that should be available:</p>
        <ul>
            <li>Frontend: port 3000</li>
            <li>Backend API: port 8000</li>
        </ul>
    </div>
    
    <div class="box">
        <h2>Troubleshooting Steps</h2>
        <ol>
            <li>Try accessing the API directly at <a href="http://localhost:8000/docs" target="_blank">http://localhost:8000/docs</a></li>
            <li>Try using IP address instead of localhost: <a href="http://127.0.0.1:8000/docs" target="_blank">http://127.0.0.1:8000/docs</a></li>
            <li>Check if your firewall is blocking these ports</li>
            <li>Ensure no other applications are using these ports</li>
            <li>Try restarting the application with administrative privileges</li>
        </ol>
    </div>
    
    <script>
        // Display the current URL
        document.getElementById('browser-url').textContent = window.location.href;
        
        // Try to display the file path
        document.getElementById('file-path').textContent = "File path: " + window.location.pathname;
        
        // Function to test API connection
        function testApiConnection() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = "Testing connection to API...";
            
            fetch('http://localhost:8000/docs')
                .then(response => {
                    if (response.ok) {
                        resultDiv.innerHTML = '<p class="success">✅ Successfully connected to the backend API!</p>';
                    } else {
                        resultDiv.innerHTML = '<p class="error">❌ Connected to API but received error: ' + response.status + '</p>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<p class="error">❌ Failed to connect to API: ' + error.message + '</p>';
                    
                    // Try with 127.0.0.1 instead
                    resultDiv.innerHTML += '<p>Trying with 127.0.0.1 instead of localhost...</p>';
                    
                    fetch('http://127.0.0.1:8000/docs')
                        .then(response => {
                            if (response.ok) {
                                resultDiv.innerHTML += '<p class="success">✅ Successfully connected to the backend API using 127.0.0.1!</p>';
                            } else {
                                resultDiv.innerHTML += '<p class="error">❌ Connected to API using 127.0.0.1 but received error: ' + response.status + '</p>';
                            }
                        })
                        .catch(error => {
                            resultDiv.innerHTML += '<p class="error">❌ Failed to connect to API using 127.0.0.1: ' + error.message + '</p>';
                            resultDiv.innerHTML += '<p>This suggests a network or firewall issue blocking connections to port 8000.</p>';
                        });
                });
        }
    </script>
</body>
</html> 