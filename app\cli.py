"""
Command Line Interface for Document Processing
Provides commands to start, stop, and manage the document watcher service.
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from typing import List, Optional
from services.document_processing.watcher import DocumentWatcher

def setup_logging(log_level: str = "INFO") -> None:
    """Configure logging for the CLI."""
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def validate_directories(directories: List[str]) -> List[str]:
    """
    Validate and normalize directory paths.
    
    Args:
        directories: List of directory paths to validate
        
    Returns:
        List of normalized directory paths
    """
    valid_dirs = []
    for directory in directories:
        path = Path(directory).resolve()
        if not path.exists():
            logging.warning(f"Directory does not exist: {directory}")
            continue
        if not path.is_dir():
            logging.warning(f"Path is not a directory: {directory}")
            continue
        valid_dirs.append(str(path))
    return valid_dirs

def create_directories(directories: List[str]) -> None:
    """
    Create directories if they don't exist.
    
    Args:
        directories: List of directory paths to create
    """
    for directory in directories:
        path = Path(directory)
        path.mkdir(parents=True, exist_ok=True)
        logging.info(f"Created directory: {directory}")

def main() -> None:
    """Main entry point for the CLI."""
    parser = argparse.ArgumentParser(
        description="Document Processing Service CLI",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Required arguments
    parser.add_argument(
        "--watch",
        nargs="+",
        required=True,
        help="Directories to watch for documents"
    )
    
    # Optional arguments
    parser.add_argument(
        "--output",
        default="output",
        help="Directory for processed documents"
    )
    parser.add_argument(
        "--logs",
        default="logs",
        help="Directory for log files"
    )
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Logging level"
    )
    parser.add_argument(
        "--process-existing",
        action="store_true",
        help="Process existing documents in watch directories"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger("cli")
    
    try:
        # Validate and normalize directories
        watch_dirs = validate_directories(args.watch)
        if not watch_dirs:
            logger.error("No valid watch directories provided")
            sys.exit(1)
        
        # Create output and log directories
        create_directories([args.output, args.logs])
        
        # Initialize watcher
        watcher = DocumentWatcher(
            watch_directories=watch_dirs,
            output_directory=args.output,
            log_directory=args.logs
        )
        
        # Process existing documents if requested
        if args.process_existing:
            logger.info("Processing existing documents...")
            watcher.process_existing_documents()
        
        # Start watching
        logger.info("Starting document watcher...")
        logger.info(f"Watching directories: {', '.join(watch_dirs)}")
        logger.info(f"Output directory: {args.output}")
        logger.info(f"Log directory: {args.logs}")
        
        watcher.start()
        
    except KeyboardInterrupt:
        logger.info("Shutting down...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 