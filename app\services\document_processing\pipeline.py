"""
Document Processing Pipeline
Manages the end-to-end processing of documents.
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path
import json
import shutil

from ..document_classification.classifier import DefaultDocumentClassifier
from ..compliance.engine import RuleEngine
from .watcher import DocumentWatcher

logger = logging.getLogger(__name__)

class DocumentPipeline:
    """Manages document processing workflow."""
    
    def __init__(
        self,
        watch_directories: List[str],
        output_directory: str,
        log_directory: str,
        rules_directory: str
    ):
        """
        Initialize the pipeline.
        
        Args:
            watch_directories: Directories to watch for new documents
            output_directory: Directory for processed documents
            log_directory: Directory for log files
            rules_directory: Directory containing compliance rules
        """
        self.watch_directories = [Path(d) for d in watch_directories]
        self.output_directory = Path(output_directory)
        self.log_directory = Path(log_directory)
        self.rules_directory = Path(rules_directory)
        
        # Create necessary directories
        self.output_directory.mkdir(parents=True, exist_ok=True)
        self.log_directory.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.classifier = DefaultDocumentClassifier()
        self.rule_engine = RuleEngine(str(self.rules_directory))
        
        # Setup logging
        self._setup_logging()
        
        # Initialize watcher with callback to process_document
        self.watcher = DocumentWatcher(
            watch_directories=watch_directories,
            on_document_found=self.process_document,
            supported_extensions=self.classifier.get_supported_formats()
        )
        
        logger.info("Document pipeline initialized")
    
    def _setup_logging(self):
        """Configure logging for the pipeline."""
        log_file = self.log_directory / f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def start(self):
        """Start the document processing pipeline."""
        logger.info("Starting document processing pipeline")
        self.watcher.start()
    
    def stop(self):
        """Stop the document processing pipeline."""
        logger.info("Stopping document processing pipeline")
        self.watcher.stop()
    
    def process_document(self, document_path: str) -> Dict:
        """
        Process a single document through the pipeline.
        
        Args:
            document_path: Path to the document
            
        Returns:
            Dictionary containing processing results
        """
        try:
            logger.info(f"Processing document: {document_path}")
            
            # Classify document
            classification = self.classifier.classify(document_path)
            
            # Evaluate compliance
            compliance_results = self.rule_engine.evaluate_document(
                document_path,
                classification
            )
            
            # Save results
            results = {
                'document_path': document_path,
                'classification': classification,
                'compliance': compliance_results,
                'processing_time': datetime.now().isoformat()
            }
            
            self._save_results(results)
            
            logger.info(f"Document processed successfully: {document_path}")
            return results
            
        except Exception as e:
            logger.error(f"Error processing document {document_path}: {e}")
            raise
    
    def _save_results(self, results: Dict):
        """
        Save processing results to output directory.
        
        Args:
            results: Processing results to save
        """
        try:
            # Create results directory structure
            doc_path = Path(results['document_path'])
            doc_name = doc_path.stem
            doc_type = results['classification']['document_type']
            
            # Create type-specific directory
            type_dir = self.output_directory / doc_type
            type_dir.mkdir(exist_ok=True)
            
            # Save results JSON
            results_file = type_dir / f"{doc_name}_results.json"
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            
            # Copy original document
            doc_copy = type_dir / doc_path.name
            shutil.copy2(doc_path, doc_copy)
            
            logger.info(f"Results saved to: {results_file}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
            raise
    
    def get_processing_status(self, document_path: str) -> Optional[Dict]:
        """
        Get processing status of a document.
        
        Args:
            document_path: Path to the document
            
        Returns:
            Processing status or None if not found
        """
        try:
            doc_path = Path(document_path)
            doc_name = doc_path.stem
            
            # Search for results in all type directories
            for type_dir in self.output_directory.iterdir():
                if not type_dir.is_dir():
                    continue
                    
                results_file = type_dir / f"{doc_name}_results.json"
                if results_file.exists():
                    with open(results_file) as f:
                        return json.load(f)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting processing status: {e}")
            return None
    
    def get_all_processed_documents(self) -> List[Dict]:
        """
        Get all processed documents.
        
        Returns:
            List of processed documents with their status
        """
        try:
            processed_docs = []
            
            for type_dir in self.output_directory.iterdir():
                if not type_dir.is_dir():
                    continue
                    
                for results_file in type_dir.glob("*_results.json"):
                    with open(results_file) as f:
                        processed_docs.append(json.load(f))
            
            return processed_docs
            
        except Exception as e:
            logger.error(f"Error getting processed documents: {e}")
            return [] 