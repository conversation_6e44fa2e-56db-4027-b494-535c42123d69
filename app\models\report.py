from datetime import datetime
from typing import Dict
from sqlalchemy import String, JSON, DateTime, ForeignKey, Enum, <PERSON>ole<PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base
import enum

class ReportType(str, enum.Enum):
    COMPLIANCE = "compliance"
    COST = "cost"
    RISK = "risk"
    PROGRESS = "progress"
    CUSTOM = "custom"

class Report(Base):
    __tablename__ = "reports"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    title: Mapped[str] = mapped_column(String)
    type: Mapped[ReportType] = mapped_column(Enum(ReportType))
    parameters: Mapped[Dict] = mapped_column(JSON)
    created_at: Mapped[datetime] = mapped_column(DateTime)
    created_by: Mapped[int] = mapped_column(ForeignKey("users.id"))
    data: Mapped[Dict] = mapped_column(JSON)
    format: Mapped[str] = mapped_column(String)  # PDF, EXCEL, JSON
    
    # Relationships
    creator = relationship("User", back_populates="reports")
    
class ReportTemplate(Base):
    __tablename__ = "report_templates"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String)
    description: Mapped[str] = mapped_column(String)
    type: Mapped[ReportType] = mapped_column(Enum(ReportType))
    template_data: Mapped[Dict] = mapped_column(JSON)
    is_system: Mapped[bool] = mapped_column(Boolean, default=False)
