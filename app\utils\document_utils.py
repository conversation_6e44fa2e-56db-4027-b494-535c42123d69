"""
Document processing utilities.

Provides helper functions for efficient document processing including
text chunking, validation, and other common operations.
"""

from typing import List, Dict, Any
from app.core.config import settings

def chunk_text_blocks(text_blocks: List[Dict], max_size: int = None) -> List[List[Dict]]:
    """Split text blocks into manageable chunks for processing.
    
    Args:
        text_blocks: List of text block dictionaries
        max_size: Maximum characters per chunk
        
    Returns:
        List of chunked text block lists
    """
    if not text_blocks:
        return []
        
    max_chunk_size = max_size or settings.MAX_TEXT_CHUNK_SIZE
    
    chunks = []
    current_chunk = []
    current_size = 0
    
    for block in text_blocks:
        block_text = block.get("text", "")
        block_size = len(block_text)
        
        # If this single block is larger than max_size, we may need
        # to split it further, but for now we'll just put it in its own chunk
        if block_size > max_chunk_size:
            # Process existing chunk if any
            if current_chunk:
                chunks.append(current_chunk)
                current_chunk = []
                current_size = 0
            
            # Add large block as its own chunk
            chunks.append([block])
            continue
            
        # Check if adding this block would exceed the max size
        if current_size + block_size > max_chunk_size:
            # Start a new chunk
            chunks.append(current_chunk)
            current_chunk = [block]
            current_size = block_size
        else:
            # Add to current chunk
            current_chunk.append(block)
            current_size += block_size
    
    # Add the last chunk if it's not empty
    if current_chunk:
        chunks.append(current_chunk)
        
    return chunks

def merge_chunked_results(results: List[Dict]) -> Dict:
    """Merge results from processing multiple chunks.
    
    Intelligently combines results from multiple chunk processing operations,
    handling arrays, dictionaries, and other data types appropriately.
    
    Args:
        results: List of result dictionaries to merge
        
    Returns:
        Merged result dictionary
    """
    if not results:
        return {}
    
    # If only one result, return it directly
    if len(results) == 1:
        return results[0]
        
    merged = {}
    
    # Process each result dictionary
    for result in results:
        for key, value in result.items():
            # Handle list values (append)
            if isinstance(value, list):
                if key not in merged:
                    merged[key] = []
                merged[key].extend(value)
                
            # Handle dict values (merge)
            elif isinstance(value, dict):
                if key not in merged:
                    merged[key] = {}
                merged[key].update(value)
                
            # Handle numeric values (sum)
            elif isinstance(value, (int, float)):
                if key in merged:
                    merged[key] += value
                else:
                    merged[key] = value
                    
            # For others, just use the latest value
            else:
                merged[key] = value
    
    # Deduplicate lists if needed
    for key, value in merged.items():
        if isinstance(value, list):
            # If list items are dictionaries, deduplicate by a specific field
            if value and isinstance(value[0], dict) and "id" in value[0]:
                seen_ids = set()
                unique_items = []
                for item in value:
                    if item["id"] not in seen_ids:
                        seen_ids.add(item["id"])
                        unique_items.append(item)
                merged[key] = unique_items
    
    return merged
