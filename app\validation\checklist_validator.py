from datetime import datetime
import re
from typing import List, Dict, Optional, Tuple
import json

class ValidationResult:
    def __init__(self, is_valid: bool, message: str, confidence: float = 0.0):
        self.is_valid = is_valid
        self.message = message
        self.confidence = confidence

class ChecklistValidator:
    def __init__(self, document_content: str):
        self.content = document_content.lower()  # Convert to lowercase for case-insensitive matching
        
    def validate_keywords(self, required_keywords: List[str], threshold: float = 0.7) -> ValidationResult:
        """
        Validate that required keywords are present in the document.
        Returns confidence score based on keyword matches.
        """
        if not required_keywords:
            return ValidationResult(True, "No keywords required", 1.0)
            
        found_keywords = []
        for keyword in required_keywords:
            if keyword.lower() in self.content:
                found_keywords.append(keyword)
                
        confidence = len(found_keywords) / len(required_keywords)
        is_valid = confidence >= threshold
        
        message = (
            f"Found {len(found_keywords)} of {len(required_keywords)} required keywords. "
            f"Keywords found: {', '.join(found_keywords)}"
        )
        
        return ValidationResult(is_valid, message, confidence)
    
    def validate_dates(self, date_format: str = "YYYY-MM-DD") -> ValidationResult:
        """
        Validate that dates in the document match the required format.
        Supports common date formats and returns found dates.
        """
        # Convert format string to regex pattern
        format_patterns = {
            "YYYY-MM-DD": r"\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\d|3[01])",
            "MM/DD/YYYY": r"(?:0[1-9]|1[0-2])/(?:0[1-9]|[12]\d|3[01])/\d{4}",
            "DD/MM/YYYY": r"(?:0[1-9]|[12]\d|3[01])/(?:0[1-9]|1[0-2])/\d{4}"
        }
        
        pattern = format_patterns.get(date_format)
        if not pattern:
            return ValidationResult(False, f"Unsupported date format: {date_format}", 0.0)
            
        dates = re.findall(pattern, self.content)
        
        if not dates:
            return ValidationResult(False, "No dates found in the required format", 0.0)
            
        # Validate that found dates are actually valid
        valid_dates = []
        for date_str in dates:
            try:
                if date_format == "YYYY-MM-DD":
                    datetime.strptime(date_str, "%Y-%m-%d")
                elif date_format == "MM/DD/YYYY":
                    datetime.strptime(date_str, "%m/%d/%Y")
                elif date_format == "DD/MM/YYYY":
                    datetime.strptime(date_str, "%d/%m/%Y")
                valid_dates.append(date_str)
            except ValueError:
                continue
                
        confidence = len(valid_dates) / len(dates) if dates else 0.0
        message = f"Found {len(valid_dates)} valid dates in the required format: {', '.join(valid_dates)}"
        
        return ValidationResult(len(valid_dates) > 0, message, confidence)
    
    def validate_amounts(self, currency_symbol: str = "$") -> ValidationResult:
        """
        Validate that monetary amounts are present in the document.
        Supports different currency symbols and formats.
        """
        # Simple pattern to match currency amounts
        pattern = fr'{re.escape(currency_symbol)}\s*\d+(?:,\d+)*(?:\.\d+)?'
        matches = re.finditer(pattern, self.content)
        
        valid_amounts = []
        for match in matches:
            amount_str = match.group(0)
            # Remove currency symbol and whitespace
            cleaned = amount_str.replace(currency_symbol, "").strip()
            
            try:
                # First, check if it's already in the correct format
                if ',' in cleaned:
                    parts = cleaned.split('.')
                    integer_part = parts[0].replace(',', '')
                    if len(integer_part) > 3 and all(len(group) == 3 for group in parts[0].split(',')[1:]):
                        # It's already properly formatted with thousands separators
                        value = float(integer_part + ('.' + parts[1] if len(parts) > 1 else ''))
                        valid_amounts.append(amount_str)
                        continue
                
                # If not in correct format, convert and reformat
                value = float(cleaned.replace(',', ''))
                formatted = f"{currency_symbol}{value:,.2f}"
                valid_amounts.append(formatted)
            except (ValueError, IndexError):
                continue
        
        if not valid_amounts:
            return ValidationResult(False, "No valid monetary amounts found", 0.0)
        
        # Sort amounts numerically
        sorted_amounts = sorted(valid_amounts, key=lambda x: float(x.replace(currency_symbol, "").replace(",", "")))
        message = f"Found {len(valid_amounts)} valid monetary amounts: {', '.join(sorted_amounts)}"
        return ValidationResult(True, message, 1.0)

def validate_checklist_item(document_content: str, checklist_item: Dict) -> ValidationResult:
    """
    Main function to validate a document against a checklist item.
    Returns overall validation result combining all required checks.
    """
    validator = ChecklistValidator(document_content)
    results = []
    
    # Validate keywords if required
    if checklist_item.get("required_keywords"):
        keyword_result = validator.validate_keywords(
            checklist_item["required_keywords"],
            threshold=checklist_item.get("confidence_threshold", 0.7)
        )
        results.append(("Keywords", keyword_result))
    
    # Validate dates if required
    if checklist_item.get("requires_dates"):
        date_result = validator.validate_dates(
            date_format=checklist_item.get("date_format", "YYYY-MM-DD")
        )
        results.append(("Dates", date_result))
    
    # Validate amounts if required
    if checklist_item.get("requires_amounts"):
        amount_result = validator.validate_amounts()
        results.append(("Amounts", amount_result))
    
    # Calculate overall validation result
    valid_count = sum(1 for _, result in results if result.is_valid)
    overall_confidence = sum(result.confidence for _, result in results) / len(results) if results else 0.0
    
    is_valid = valid_count == len(results)
    messages = [f"{check}: {result.message}" for check, result in results]
    overall_message = "\n".join(messages)
    
    return ValidationResult(is_valid, overall_message, overall_confidence) 