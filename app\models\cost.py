from datetime import datetime
from typing import Optional
from sqlalchemy import String, Float, DateTime, ForeignKey, Enum, Integer, Column
from sqlalchemy.orm import relationship
from app.models.base import Base
import enum

class CostType(str, enum.Enum):
    MITIGATION = "mitigation"
    IMPLEMENTATION = "implementation"
    MAINTENANCE = "maintenance"
    CONSULTATION = "consultation"

class Cost(Base):
    __tablename__ = "costs"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    type = Column(Enum(CostType))
    amount = Column(Float)
    description = Column(String)
    date_incurred = Column(DateTime)
    category = Column(String)
    vendor = Column(String, nullable=True)
    invoice_number = Column(String, nullable=True)
    
    # Relationships
    project = relationship("Project", back_populates="costs")
    
class Budget(Base):
    __tablename__ = "budgets"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    fiscal_year = Column(Integer)
    total_amount = Column(Float)
    allocated_amount = Column(Float)
    remaining_amount = Column(Float)
    last_updated = Column(DateTime)
    
    # Relationships
    project = relationship("Project", back_populates="budgets")

class CostBenefit(Base):
    __tablename__ = "cost_benefits"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    benefit_description = Column(String)
    estimated_savings = Column(Float)
    implementation_cost = Column(Float)
    roi_percentage = Column(Float)
    payback_period = Column(Float)  # in months
    
    # Relationships
    project = relationship("Project", back_populates="cost_benefits")
