"""
PDF Document Classifier
Implements document classification for PDF files.
"""

import os
from typing import Dict, Any, List
from PyPDF2 import <PERSON>d<PERSON><PERSON><PERSON><PERSON>
from .base import DocumentClassifier

class PDFClassifier(DocumentClassifier):
    """Classifier implementation for PDF documents."""
    
    def __init__(self):
        self.supported_formats = ['.pdf']
    
    def classify(self, document_path: str) -> Dict[str, Any]:
        """
        Classify a PDF document and extract metadata.
        
        Args:
            document_path: Path to the PDF file
            
        Returns:
            Dictionary containing:
            - page_count: Number of pages
            - title: Document title
            - author: Document author
            - subject: Document subject
            - creation_date: Document creation date
            - modification_date: Document modification date
        """
        if not self.validate_document(document_path):
            raise ValueError(f"Invalid PDF document: {document_path}")
            
        reader = PdfReader(document_path)
        info = reader.metadata
        
        return {
            'page_count': len(reader.pages),
            'title': info.title if info else None,
            'author': info.author if info else None,
            'subject': info.subject if info else None,
            'creation_date': info.creation_date if info else None,
            'modification_date': info.modification_date if info else None
        }
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats.
        
        Returns:
            List containing '.pdf'
        """
        return self.supported_formats
    
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if a document is a valid PDF.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if document is a valid PDF, False otherwise
        """
        if not os.path.exists(document_path):
            return False
            
        if not document_path.lower().endswith('.pdf'):
            return False
            
        try:
            reader = PdfReader(document_path)
            return len(reader.pages) > 0
        except:
            return False 