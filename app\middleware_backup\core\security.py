from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import O<PERSON><PERSON>2<PERSON><PERSON>wordBearer
from jose import JW<PERSON>rror, jwt, ExpiredSignatureError
from passlib.context import CryptContext
from sqlalchemy.orm import Session
from app.core.config import settings
from app.core.database import get_db
from app.models.user import User
import logging
import re
import secrets
import base64
import pyotp
import qrcode
from io import BytesIO

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")

def generate_totp_secret() -> str:
    """
    Generate a random secret for TOTP (Time-based One-Time Password).
    
    Returns:
        str: A base32-encoded secret key for TOTP.
    """
    # Generate 20 random bytes (160 bits)
    random_bytes = secrets.token_bytes(20)
    # Convert to base32
    return base64.b32encode(random_bytes).decode('utf-8')

def generate_totp_uri(secret: str, username: str) -> str:
    """
    Generate a TOTP URI for QR code generation.
    
    Args:
        secret (str): The TOTP secret key.
        username (str): The username for the TOTP URI.
    
    Returns:
        str: The TOTP URI.
    """
    totp = pyotp.TOTP(secret)
    return totp.provisioning_uri(
        username,
        issuer_name="ComplianceMax"
    )

def verify_totp(secret: str, token: str) -> bool:
    """
    Verify a TOTP token.
    
    Args:
        secret (str): The TOTP secret key.
        token (str): The token to verify.
    
    Returns:
        bool: True if the token is valid, False otherwise.
    """
    totp = pyotp.TOTP(secret)
    return totp.verify(token)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def validate_password_complexity(password: str) -> bool:
    """
    Validate that a password meets complexity requirements.
    
    Requirements:
    - At least 8 characters long
    - At least one uppercase letter
    - At least one lowercase letter
    - At least one digit
    - At least one special character
    
    Args:
        password (str): The password to validate.
    
    Returns:
        bool: True if the password meets the requirements, False otherwise.
    
    Raises:
        HTTPException: If the password does not meet the requirements.
    """
    if len(password) < 8:
        logger.warning("Password validation failed: Password must be at least 8 characters long")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be at least 8 characters long"
        )
    
    if not re.search(r"[A-Z]", password):
        logger.warning("Password validation failed: Password must contain at least one uppercase letter")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one uppercase letter"
        )
    
    if not re.search(r"[a-z]", password):
        logger.warning("Password validation failed: Password must contain at least one lowercase letter")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one lowercase letter"
        )
    
    if not re.search(r"\d", password):
        logger.warning("Password validation failed: Password must contain at least one digit")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one digit"
        )
    
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
        logger.warning("Password validation failed: Password must contain at least one special character")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one special character"
        )
    
    return True

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """
    Authenticate a user by verifying their username and password.
    
    Args:
        db (Session): The database session.
        username (str): The username to authenticate.
        password (str): The password to verify.
    
    Returns:
        Optional[User]: The authenticated user if successful, None otherwise.
    """
    user = db.query(User).filter(User.username == username).first()
    if not user:
        logger.warning(f"Authentication failed: User {username} not found")
        return None
    if not verify_password(password, user.hashed_password):
        logger.warning(f"Authentication failed: Incorrect password for user {username}")
        return None
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            logger.warning("Token payload does not contain 'sub' field")
            raise credentials_exception
    except ExpiredSignatureError:
        logger.warning("Token has expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except JWTError as e:
        logger.warning(f"Invalid token: {str(e)}")
        raise credentials_exception

    query = db.query(User).filter(User.username == username)
    user = query.first()
    if user is None:
        logger.warning(f"User not found: {username}")
        raise credentials_exception
    return user

def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    if not current_user.is_active:
        logger.warning(f"Inactive user attempted access: {current_user.username}")
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user