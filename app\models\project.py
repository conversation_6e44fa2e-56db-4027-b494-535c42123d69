from sqlalchemy import Column, Integer, String, Foreign<PERSON>ey, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship
from app.models.base import Base, TimestampMixin

class Project(Base, TimestampMixin):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String, nullable=True)
    owner_id = Column(Integer, ForeignKey("users.id"))
    is_active = Column(Boolean, default=True)
    
    # Relationships
    owner = relationship("User", back_populates="projects")
    compliance_reviews = relationship("ComplianceReview", back_populates="project")
    cbcs_compliance_reviews = relationship("CBCSComplianceReview", back_populates="project")
    costs = relationship("Cost", back_populates="project")
    budgets = relationship("Budget", back_populates="project")
    cost_benefits = relationship("CostBenefit", back_populates="project")
    geo_locations = relationship("GeoLocation", back_populates="project")
    mapping_locations = relationship("MappingLocation", back_populates="project")