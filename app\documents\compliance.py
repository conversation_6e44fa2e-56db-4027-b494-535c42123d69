"""Document compliance checking functionality."""

from datetime import datetime
from typing import Dict, List, Optional

from .models import Document, ComplianceStatus, RiskLevel, MitigationStatus
from .analysis import AnalysisResult


class ComplianceChecker:
    """Service for checking document compliance."""

    def __init__(self):
        self.risk_thresholds = {
            "high": 0.8,
            "medium": 0.6,
            "low": 0.4
        }

    async def check_compliance(
        self,
        document: Document,
        analysis_result: AnalysisResult
    ) -> Dict:
        """Check document compliance based on analysis results."""
        compliance_checks = []
        
        # Check requirements compliance
        requirement_compliance = await self._check_requirements_compliance(
            analysis_result.requirements
        )
        compliance_checks.append(requirement_compliance)
        
        # Check temporal compliance
        temporal_compliance = await self._check_temporal_compliance(
            analysis_result.temporal_info
        )
        compliance_checks.append(temporal_compliance)
        
        # Check section compliance
        section_compliance = await self._check_section_compliance(
            analysis_result.sections
        )
        compliance_checks.append(section_compliance)
        
        # Check cross-reference compliance
        reference_compliance = await self._check_reference_compliance(
            analysis_result.cross_references
        )
        compliance_checks.append(reference_compliance)
        
        # Calculate overall compliance
        overall_status = self._calculate_overall_compliance(compliance_checks)
        risk_level = self._assess_risk_level(compliance_checks)
        
        return {
            "status": overall_status,
            "risk_level": risk_level,
            "checks": compliance_checks,
            "timestamp": datetime.utcnow().isoformat(),
            "confidence": analysis_result.confidence_score
        }

    async def _check_requirements_compliance(
        self,
        requirements: List[Dict]
    ) -> Dict:
        """Check compliance with mandatory requirements."""
        mandatory_reqs = [r for r in requirements if r["type"] == "mandatory"]
        recommended_reqs = [r for r in requirements if r["type"] == "recommended"]
        
        mandatory_compliance = len([r for r in mandatory_reqs if r["confidence"] > 0.7])
        recommended_compliance = len([r for r in recommended_reqs if r["confidence"] > 0.6])
        
        status = ComplianceStatus.COMPLIANT
        if not mandatory_reqs:
            status = ComplianceStatus.UNKNOWN
        elif mandatory_compliance < len(mandatory_reqs):
            status = ComplianceStatus.NON_COMPLIANT
        elif recommended_compliance < len(recommended_reqs):
            status = ComplianceStatus.PARTIALLY_COMPLIANT
            
        return {
            "type": "requirements",
            "status": status.value,
            "details": {
                "mandatory_total": len(mandatory_reqs),
                "mandatory_compliant": mandatory_compliance,
                "recommended_total": len(recommended_reqs),
                "recommended_compliant": recommended_compliance
            }
        }

    async def _check_temporal_compliance(self, temporal_info: Dict) -> Dict:
        """Check compliance with temporal requirements."""
        now = datetime.utcnow()
        
        # Check deadlines
        missed_deadlines = []
        upcoming_deadlines = []
        for deadline in temporal_info.get("deadlines", []):
            try:
                deadline_date = datetime.strptime(deadline["date"], "%Y-%m-%d")
                if deadline_date < now:
                    missed_deadlines.append(deadline)
                else:
                    upcoming_deadlines.append(deadline)
            except ValueError:
                continue
                
        status = ComplianceStatus.COMPLIANT
        if missed_deadlines:
            status = ComplianceStatus.NON_COMPLIANT
        elif not temporal_info.get("deadlines"):
            status = ComplianceStatus.UNKNOWN
            
        return {
            "type": "temporal",
            "status": status.value,
            "details": {
                "missed_deadlines": len(missed_deadlines),
                "upcoming_deadlines": len(upcoming_deadlines),
                "total_dates": len(temporal_info.get("dates", [])),
                "total_periods": len(temporal_info.get("periods", []))
            }
        }

    async def _check_section_compliance(self, sections: Dict) -> Dict:
        """Check compliance of document sections."""
        required_sections = {
            "requirements": False,
            "procedure": False,
            "definitions": False
        }
        
        high_importance_sections = []
        for section, details in sections.items():
            section_type = details.get("type")
            if section_type in required_sections:
                required_sections[section_type] = True
                
            if details.get("importance", 0) > 0.8:
                high_importance_sections.append(section)
                
        missing_sections = [
            section for section, present in required_sections.items()
            if not present
        ]
        
        status = ComplianceStatus.COMPLIANT
        if missing_sections:
            status = ComplianceStatus.PARTIALLY_COMPLIANT
        if not sections:
            status = ComplianceStatus.UNKNOWN
            
        return {
            "type": "sections",
            "status": status.value,
            "details": {
                "total_sections": len(sections),
                "missing_required": missing_sections,
                "high_importance": len(high_importance_sections)
            }
        }

    async def _check_reference_compliance(
        self,
        cross_references: List[Dict]
    ) -> Dict:
        """Check compliance of document cross-references."""
        reference_types = {
            "regulation": [],
            "policy": [],
            "form": [],
            "internal": [],
            "other": []
        }
        
        for ref in cross_references:
            ref_type = ref.get("type", "other")
            if ref_type in reference_types:
                reference_types[ref_type].append(ref)
                
        # Check if regulatory references exist
        status = ComplianceStatus.COMPLIANT
        if not reference_types["regulation"]:
            status = ComplianceStatus.PARTIALLY_COMPLIANT
        if not cross_references:
            status = ComplianceStatus.UNKNOWN
            
        return {
            "type": "references",
            "status": status.value,
            "details": {
                "total_references": len(cross_references),
                "by_type": {
                    k: len(v) for k, v in reference_types.items()
                }
            }
        }

    def _calculate_overall_compliance(
        self,
        compliance_checks: List[Dict]
    ) -> str:
        """Calculate overall compliance status."""
        status_weights = {
            ComplianceStatus.COMPLIANT.value: 1.0,
            ComplianceStatus.PARTIALLY_COMPLIANT.value: 0.5,
            ComplianceStatus.NON_COMPLIANT.value: 0.0,
            ComplianceStatus.UNKNOWN.value: 0.0
        }
        
        total_weight = 0
        weighted_sum = 0
        
        for check in compliance_checks:
            status = check["status"]
            weight = 1.0  # Default weight
            
            # Adjust weights based on check type
            if check["type"] == "requirements":
                weight = 2.0  # Requirements are most important
            elif check["type"] == "temporal":
                weight = 1.5  # Temporal compliance is next
                
            weighted_sum += status_weights[status] * weight
            total_weight += weight
            
        if total_weight == 0:
            return ComplianceStatus.UNKNOWN.value
            
        compliance_score = weighted_sum / total_weight
        
        if compliance_score >= 0.8:
            return ComplianceStatus.COMPLIANT.value
        elif compliance_score >= 0.5:
            return ComplianceStatus.PARTIALLY_COMPLIANT.value
        else:
            return ComplianceStatus.NON_COMPLIANT.value

    def _assess_risk_level(self, compliance_checks: List[Dict]) -> str:
        """Assess the risk level based on compliance checks."""
        risk_factors = []
        
        for check in compliance_checks:
            if check["type"] == "requirements":
                # High risk if missing mandatory requirements
                mandatory_missing = (
                    check["details"]["mandatory_total"] -
                    check["details"]["mandatory_compliant"]
                )
                if mandatory_missing > 0:
                    risk_factors.append(1.0)
                    
            elif check["type"] == "temporal":
                # High risk for missed deadlines
                if check["details"]["missed_deadlines"] > 0:
                    risk_factors.append(0.9)
                    
            elif check["type"] == "sections":
                # Medium risk for missing important sections
                if check["details"]["missing_required"]:
                    risk_factors.append(0.7)
                    
            elif check["type"] == "references":
                # Medium risk for missing regulatory references
                if check["details"]["by_type"]["regulation"] == 0:
                    risk_factors.append(0.6)
                    
        if not risk_factors:
            return RiskLevel.LOW.value
            
        max_risk = max(risk_factors)
        
        if max_risk >= self.risk_thresholds["high"]:
            return RiskLevel.HIGH.value
        elif max_risk >= self.risk_thresholds["medium"]:
            return RiskLevel.MEDIUM.value
        else:
            return RiskLevel.LOW.value 