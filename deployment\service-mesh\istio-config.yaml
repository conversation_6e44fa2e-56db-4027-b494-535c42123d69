apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: compliancemax-istio-config
  namespace: compliancemax
spec:
  profile: demo # Using demo profile for development
  components:
    base:
      enabled: true
    pilot:
      enabled: true
      k8s:
        resources:
          requests:
            memory: 2048Mi
            cpu: 500m
          limits:
            memory: 4096Mi
            cpu: 1000m
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
        service:
          type: LoadBalancer
          ports:
          - name: http2
            port: 80
            targetPort: 8080
          - name: https
            port: 443
            targetPort: 8443
    egressGateways:
    - name: istio-egressgateway
      enabled: true
  values:
    global:
      proxy:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
      mtls:
        enabled: true
      tracer:
        zipkin:
          address: zipkin.monitoring:9411
    pilot:
      traceSampling: 100.0
    meshConfig:
      enableTracing: true
      defaultConfig:
        tracing:
          sampling: 100.0
          zipkin:
            address: zipkin.monitoring:9411
      accessLogFile: /dev/stdout
      enableAutoMtls: true 