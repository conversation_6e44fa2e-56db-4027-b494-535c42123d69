﻿"""Models package."""

from app.models.base import Base
from app.models.enums import (
    ComplianceStatus,
    ValidationStatus,
    RiskLevel,
)
from app.models.user import User
from app.models.document import Document
from app.models.application import Application
from app.models.document_link import DocumentLink
from app.models.compliance import ComplianceResult, ComplianceRequirement

# This ensures all models are registered with Base.metadata
__all__ = [
    "Base",
    "ComplianceStatus",
    "ValidationStatus",
    "RiskLevel",
    "User",
    "Document",
    "Application",
    "DocumentLink",
    "ComplianceResult",
    "ComplianceRequirement",
]
