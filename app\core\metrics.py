"""
Metrics collection system for ComplianceMax.
"""

import time
from functools import wraps
from typing import Callable, Dict, Any
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, push_to_gateway
from app.core.config import settings
from app.core.logging import get_logger
"""
Metrics collection system for ComplianceMax.
"""

import time
from functools import wraps
from typing import Callable, Dict, Any
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, push_to_gateway
from app.core.config import settings
from app.core.logging import get_logger

# ─── STUBS FOR TEST IMPORTS ───
class MetricsMiddleware:
    """
    Stub middleware for collecting metrics.
    This no-op version satisfies the import in app.main and tests.
    """
    def __init__(self, app):
        self.app = app

    async def __call__(self, request, call_next):
        return await call_next(request)


class MetricsCollector:
    """
    Stub collector for metrics.
    """
    def record(self, name: str, value: float) -> None:
        pass
# ───────────────────────────────

# …the rest of your real metrics code follows…

logger = get_logger(__name__)

class MetricsCollector:
    """Prometheus metrics collector."""

    # Initialize registry
    registry = CollectorRegistry()

    # Document processing metrics
    document_processing_duration = Histogram(
        'document_processing_duration_seconds',
        'Time spent processing documents',
        ['doc_type'],
        registry=registry
    )

    document_processing_total = Counter(
        'document_processing_total',
        'Total number of documents processed',
        ['doc_type', 'status'],
        registry=registry
    )

    # Compliance analysis metrics
    compliance_analysis_duration = Histogram(
        'compliance_analysis_duration_seconds',
        'Time spent on compliance analysis',
        ['requirement_type'],
        registry=registry
    )

    compliance_status_total = Counter(
        'compliance_status_total',
        'Total number of compliance checks by status',
        ['status'],
        registry=registry
    )

    # Storage metrics
    storage_operation_duration = Histogram(
        'storage_operation_duration_seconds',
        'Time spent on storage operations',
        ['operation', 'backend'],
        registry=registry
    )

    storage_errors_total = Counter(
        'storage_errors_total',
        'Total number of storage operation errors',
        ['operation', 'backend'],
        registry=registry
    )

    # System metrics
    active_requests = Gauge(
        'active_requests',
        'Number of active requests',
        registry=registry
    )

    @classmethod
    def track_document_processing(cls, doc_type: str, duration: float, status: str) -> None:
        """Track document processing metrics."""
        try:
            cls.document_processing_duration.labels(doc_type=doc_type).observe(duration)
            cls.document_processing_total.labels(doc_type=doc_type, status=status).inc()
        except Exception as e:
            logger.error(f"Failed to track document processing metrics: {str(e)}")

    @classmethod
    def track_compliance_analysis(cls, requirement_type: str, duration: float, status: str) -> None:
        """Track compliance analysis metrics."""
        try:
            cls.compliance_analysis_duration.labels(requirement_type=requirement_type).observe(duration)
            cls.compliance_status_total.labels(status=status).inc()
        except Exception as e:
            logger.error(f"Failed to track compliance analysis metrics: {str(e)}")

    @classmethod
    def track_storage_operation(cls, operation: str, backend: str, duration: float, success: bool) -> None:
        """Track storage operation metrics."""
        try:
            cls.storage_operation_duration.labels(operation=operation, backend=backend).observe(duration)
            if not success:
                cls.storage_errors_total.labels(operation=operation, backend=backend).inc()
        except Exception as e:
            logger.error(f"Failed to track storage operation metrics: {str(e)}")

    @classmethod
    def push_metrics(cls) -> None:
        """Push metrics to Prometheus gateway."""
        try:
            push_to_gateway(
                settings.PROMETHEUS_GATEWAY,
                job='compliancemax',
                registry=cls.registry
            )
        except Exception as e:
            logger.error(f"Failed to push metrics: {str(e)}")

def track_performance(operation: str) -> Callable:
    """Decorator to track operation performance."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            success = True

            try:
                result = await func(*args, **kwargs)
                return result
            except Exception:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                try:
                    if operation == "document_processing":
                        doc_type = kwargs.get("doc_type", "unknown")
                        MetricsCollector.track_document_processing(
                            doc_type=doc_type,
                            duration=duration,
                            status="success" if success else "error"
                        )
                    elif operation == "compliance_analysis":
                        requirement_type = kwargs.get("requirement_type", "unknown")
                        MetricsCollector.track_compliance_analysis(
                            requirement_type=requirement_type,
                            duration=duration,
                            status="success" if success else "error"
                        )
                    elif operation == "storage_operation":
                        backend = kwargs.get("backend", "unknown")
                        MetricsCollector.track_storage_operation(
                            operation=kwargs.get("operation", "unknown"),
                            backend=backend,
                            duration=duration,
                            success=success
                        )
                except Exception as e:
                    logger.error(f"Failed to track performance metrics: {str(e)}")

        return wrapper
    return decorator