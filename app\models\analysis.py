"""Models for document analysis and content extraction."""

from sqlalchemy import Column, String, Float, DateTime, ForeignKey, JSON, Boolean, Integer
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base_class import Base

class DocumentAnalysis(Base):
    """Model for storing document analysis results."""
    __tablename__ = "document_analyses"

    id = Column(String, primary_key=True, index=True)
    document_id = Column(String, ForeignKey("documents.id"), nullable=False, index=True)
    analyzer_version = Column(String, nullable=False)
    content_type = Column(String, nullable=False)
    extracted_text = Column(String, nullable=True)
    sections = Column(JSON, nullable=False, default=dict)
    metadata = Column(JSON, nullable=True)
    confidence_scores = Column(JSON, nullable=False, default=dict)
    processing_status = Column(String, nullable=False, default="pending")
    error_details = Column(JSON, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    document = relationship("Document", back_populates="analyses")
    extracted_requirements = relationship("RequirementExtraction", back_populates="analysis", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DocumentAnalysis(id={self.id}, document_id={self.document_id})>"

class RequirementExtraction(Base):
    """Model for storing extracted requirements from documents."""
    __tablename__ = "requirement_extractions"

    id = Column(String, primary_key=True, index=True)
    analysis_id = Column(String, ForeignKey("document_analyses.id"), nullable=False, index=True)
    requirement_text = Column(String, nullable=False)
    section = Column(String, nullable=False)
    subsection = Column(String, nullable=True)
    page_number = Column(Integer, nullable=True)
    confidence_score = Column(Float, nullable=False, default=0.0)
    metadata = Column(JSON, nullable=True)
    validation_status = Column(String, nullable=False, default="pending")
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    analysis = relationship("DocumentAnalysis", back_populates="extracted_requirements")

    def __repr__(self):
        return f"<RequirementExtraction(id={self.id}, section={self.section})>"

class SectionMapping(Base):
    """Model for mapping document sections to policy requirements."""
    __tablename__ = "section_mappings"

    id = Column(String, primary_key=True, index=True)
    document_id = Column(String, ForeignKey("documents.id"), nullable=False, index=True)
    policy_requirement_id = Column(String, ForeignKey("policy_requirements.id"), nullable=False, index=True)
    section_text = Column(String, nullable=False)
    match_score = Column(Float, nullable=False, default=0.0)
    is_validated = Column(Boolean, nullable=False, default=False)
    validator_notes = Column(String, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    document = relationship("Document", backref="section_mappings")
    policy_requirement = relationship("PolicyRequirement", backref="section_mappings")

    def __repr__(self):
        return f"<SectionMapping(id={self.id}, document_id={self.document_id}, requirement_id={self.policy_requirement_id})>" 