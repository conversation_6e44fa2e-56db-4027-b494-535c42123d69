"""Document processing and management services."""

import json
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Tuple
from uuid import UUID

from fastapi import UploadFile
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.password import get_password_hash
from .models import Document, DocumentVersion, ComplianceCheck, ComplianceReport, ComplianceStatus, MitigationStatus, RiskLevel
from .storage import StorageService
from .utils import assess_risk_level, calculate_temporal_compliance, extract_references, generate_compliance_summary, validate_document_sections


class DocumentService:
    """Service for managing document operations."""

    def __init__(self, db: Session, storage: StorageService):
        self.db = db
        self.storage = storage

    async def create_document(self, file: UploadFile) -> Document:
        """Create a new document from an uploaded file."""
        # Generate storage path
        storage_path = await self.storage.store_file(file)
        
        # Create document record
        document = Document(
            filename=file.filename,
            content_type=file.content_type,
            storage_path=storage_path
        )
        
        self.db.add(document)
        self.db.commit()
        self.db.refresh(document)
        
        return document

    async def get_document(self, document_id: UUID) -> Optional[Document]:
        """Retrieve a document by ID."""
        return self.db.query(Document).filter(Document.id == str(document_id)).first()

    async def update_document(self, document_id: UUID, file: UploadFile) -> Document:
        """Update an existing document with a new version."""
        document = await self.get_document(document_id)
        if not document:
            raise ValueError(f"Document {document_id} not found")

        # Store new version
        new_path = await self.storage.store_file(file)
        
        # Create version record
        version = DocumentVersion(
            document_id=str(document_id),
            version_number=datetime.utcnow().isoformat(),
            storage_path=document.storage_path  # Store old path in version
        )
        
        # Update document
        document.filename = file.filename
        document.content_type = file.content_type
        document.storage_path = new_path
        document.updated_at = datetime.utcnow()
        
        self.db.add(version)
        self.db.commit()
        self.db.refresh(document)
        
        return document

    async def delete_document(self, document_id: UUID) -> bool:
        """Delete a document and all its versions."""
        document = await self.get_document(document_id)
        if not document:
            return False

        # Delete all versions
        for version in document.versions:
            await self.storage.delete_file(version.storage_path)
            self.db.delete(version)

        # Delete main document
        await self.storage.delete_file(document.storage_path)
        self.db.delete(document)
        self.db.commit()
        
        return True


class DocumentProcessor:
    """Service for processing document content."""

    def __init__(self, storage: StorageService):
        self.storage = storage

    async def extract_text(self, document: Document) -> str:
        """Extract text content from a document."""
        # Implementation depends on document type
        pass

    async def extract_metadata(self, document: Document) -> dict:
        """Extract metadata from a document."""
        # Implementation depends on document type
        pass

    async def analyze_content(self, document: Document) -> dict:
        """Perform content analysis on a document."""
        # Implementation depends on requirements
        pass 


class ComplianceService:
    """Service for checking document compliance."""

    def __init__(self):
        self.required_sections = [
            "Purpose",
            "Scope",
            "Policy Statement",
            "Responsibilities",
            "Compliance",
            "References",
            "Review",
        ]
        
    async def check_document_compliance(
        self,
        document: Document,
        check_types: Optional[List[str]] = None,
    ) -> ComplianceReport:
        """Run compliance checks on a document.
        
        Args:
            document: Document to check
            check_types: Optional list of specific checks to run
            
        Returns:
            ComplianceReport with check results
        """
        if check_types is None:
            check_types = ["temporal", "sections", "references"]
            
        checks: List[ComplianceCheck] = []
        compliance_scores: Dict[str, float] = {}
        
        # Run temporal compliance check
        if "temporal" in check_types:
            status, confidence, details = calculate_temporal_compliance(document)
            checks.append(
                ComplianceCheck(
                    document_id=document.id,
                    check_type="temporal",
                    status=status,
                    confidence=confidence,
                    details=details,
                    timestamp=datetime.now(),
                )
            )
            compliance_scores["temporal"] = confidence
            
        # Check required sections
        if "sections" in check_types:
            is_valid, missing = validate_document_sections(
                document, self.required_sections
            )
            status = (
                ComplianceStatus.COMPLIANT if is_valid
                else ComplianceStatus.NON_COMPLIANT
            )
            confidence = 1.0 if is_valid else 0.0
            details = (
                "All required sections present"
                if is_valid
                else f"Missing sections: {', '.join(missing)}"
            )
            checks.append(
                ComplianceCheck(
                    document_id=document.id,
                    check_type="sections",
                    status=status,
                    confidence=confidence,
                    details=details,
                    timestamp=datetime.now(),
                )
            )
            compliance_scores["sections"] = confidence
            
        # Check references
        if "references" in check_types:
            references = extract_references(document)
            has_refs = bool(references)
            status = (
                ComplianceStatus.COMPLIANT if has_refs
                else ComplianceStatus.NON_COMPLIANT
            )
            confidence = 1.0 if has_refs else 0.0
            details = (
                f"Found references: {', '.join(references)}"
                if has_refs
                else "No references found"
            )
            checks.append(
                ComplianceCheck(
                    document_id=document.id,
                    check_type="references",
                    status=status,
                    confidence=confidence,
                    details=details,
                    timestamp=datetime.now(),
                )
            )
            compliance_scores["references"] = confidence
            
        # Calculate overall compliance
        risk_level, risk_details = assess_risk_level(compliance_scores)
        overall_status = self._determine_overall_status(checks)
        
        # Generate summary
        summary = generate_compliance_summary(
            document,
            {check.check_type: check.status for check in checks},
            risk_level,
            {"Risk Assessment": risk_details},
        )
        
        # Create recommendations
        recommendations = self._generate_recommendations(checks)
        
        return ComplianceReport(
            document_id=document.id,
            status=overall_status,
            risk_level=risk_level,
            checks=checks,
            timestamp=datetime.now(),
            summary=summary,
            recommendations=recommendations,
        )
        
    def _determine_overall_status(
        self,
        checks: List[ComplianceCheck]
    ) -> ComplianceStatus:
        """Determine overall compliance status from individual checks."""
        if not checks:
            return ComplianceStatus.UNKNOWN
            
        statuses = [check.status for check in checks]
        
        if all(s == ComplianceStatus.COMPLIANT for s in statuses):
            return ComplianceStatus.COMPLIANT
            
        if any(s == ComplianceStatus.NON_COMPLIANT for s in statuses):
            return ComplianceStatus.NON_COMPLIANT
            
        return ComplianceStatus.PARTIALLY_COMPLIANT
        
    def _generate_recommendations(
        self,
        checks: List[ComplianceCheck]
    ) -> List[str]:
        """Generate recommendations based on compliance check results."""
        recommendations = []
        
        for check in checks:
            if check.status != ComplianceStatus.COMPLIANT:
                if check.check_type == "temporal":
                    recommendations.append(
                        "Schedule document review to maintain compliance"
                    )
                elif check.check_type == "sections":
                    recommendations.append(
                        "Add missing required sections to meet documentation standards"
                    )
                elif check.check_type == "references":
                    recommendations.append(
                        "Include relevant references to support document content"
                    )
                    
        return recommendations
        
    async def create_mitigation_plan(
        self,
        report: ComplianceReport,
        assignee: str,
        due_date: datetime,
    ) -> Dict:
        """Create a mitigation plan for non-compliant items.
        
        Args:
            report: Compliance report to create plan for
            assignee: Person responsible for mitigation
            due_date: When mitigation should be completed
            
        Returns:
            Dict containing mitigation plan details
        """
        if report.status == ComplianceStatus.COMPLIANT:
            return {
                "status": MitigationStatus.COMPLETED,
                "message": "No mitigation required - document is compliant",
            }
            
        tasks = []
        for check in report.checks:
            if check.status != ComplianceStatus.COMPLIANT:
                tasks.append({
                    "type": check.check_type,
                    "issue": check.details,
                    "assignee": assignee,
                    "due_date": due_date,
                    "status": MitigationStatus.NOT_STARTED,
                })
                
        return {
            "document_id": report.document_id,
            "status": MitigationStatus.NOT_STARTED,
            "tasks": tasks,
            "assignee": assignee,
            "due_date": due_date,
            "created_at": datetime.now(),
        } 