from datetime import datetime
from typing import Optional, List
from sqlalchemy import String, Integer, DateTime, ForeignKey, Enum, Text, Table, Column, func, JSON, Boolean
from sqlalchemy.orm import relationship
from app.models.base import Base, TimestampMixin
import enum
from uuid import uuid4
from app.models.compliance import PolicyRequirement  # Import the unified model

# Association table for policy versions and disasters
policy_disaster_association = Table(
    "policy_disaster_association",
    Base.metadata,
    Column("policy_version_id", Integer, ForeignKey("policy_versions.id"), primary_key=True),
    Column("disaster_id", String, ForeignKey("disasters.dr_number"), primary_key=True)
)

class PolicyType(str, enum.Enum):
    STANDARD = "standard"
    GUIDANCE = "guidance"
    PROCEDURE = "procedure"
    REGULATION = "regulation"

class PolicyDocument(Base, TimestampMixin):
    """Model for storing policy documents."""
    __tablename__ = "policy_documents"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid4()), index=True)
    policy_number = Column(String, nullable=False, index=True)
    title = Column(String, nullable=False)
    description = Column(Text)
    policy_type = Column(Enum(PolicyType))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    
    versions = relationship("PolicyVersion", back_populates="policy", cascade="all, delete-orphan")
    applicable_disasters = relationship("DisasterDeclaration", secondary=policy_disaster_association, back_populates="active_policies")

    def __repr__(self):
        return f"<PolicyDocument {self.policy_number}: {self.title}>"

class PolicyVersion(Base, TimestampMixin):
    """Model for storing policy document versions."""
    __tablename__ = "policy_versions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid4()), index=True)
    policy_id = Column(ForeignKey("policy_documents.id"))
    version_number = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    effective_date = Column(DateTime, nullable=False)
    expiration_date = Column(DateTime, nullable=True)
    document_url = Column(String, nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    
    # Metadata
    changes_summary = Column(Text)
    approval_status = Column(String, default="pending")
    approved_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    policy = relationship("PolicyDocument", back_populates="versions")
    approver = relationship("User")
    disasters = relationship("DisasterDeclaration", secondary=policy_disaster_association)
    compliance_reviews = relationship("ComplianceReview", back_populates="policy_version")
    requirements = relationship("PolicyRequirement", back_populates="policy_version")

    def __repr__(self):
        return f"<PolicyVersion {self.policy.policy_number} v{self.version_number}>"

class PolicyRequirement(Base, TimestampMixin):
    """Model for storing specific policy requirements."""
    __tablename__ = "policy_requirements"

    id = Column(String, primary_key=True, index=True)
    policy_version_id = Column(String, ForeignKey("policy_versions.id"), nullable=False, index=True)
    section = Column(String, nullable=False, index=True)
    subsection = Column(String, nullable=True)
    requirement_text = Column(String, nullable=False)
    validation_criteria = Column(JSON, nullable=False)
    required_evidence = Column(JSON, nullable=False)
    scoring_criteria = Column(JSON, nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    policy_version = relationship("PolicyVersion", backref="requirements")

    def __repr__(self):
        return f"<PolicyRequirement(id={self.id}, section={self.section})>"





