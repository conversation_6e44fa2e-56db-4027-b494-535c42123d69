"""
Metadata Compliance Rule
Implements document metadata validation rules.
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from .base import Rule

class MetadataRule(Rule):
    """Rule for validating document metadata."""
    
    def __init__(self,
                 rule_id: str,
                 name: str,
                 description: str,
                 field: str,
                 operator: str,
                 value: Union[str, int, float, bool, List[Any]],
                 required: bool = True,
                 **kwargs):
        """
        Initialize a metadata validation rule.
        
        Args:
            rule_id: Unique identifier for the rule
            name: Human-readable name of the rule
            description: Detailed description of the rule
            field: Metadata field to validate
            operator: Comparison operator (eq, ne, gt, lt, gte, lte, in, contains)
            value: Expected value or list of values
            required: Whether the field is required
            **kwargs: Additional rule parameters
        """
        super().__init__(rule_id, name, description, **kwargs)
        self.field = field
        self.operator = operator.lower()
        self.value = value
        self.required = required
        
        # Validate operator
        valid_operators = ['eq', 'ne', 'gt', 'lt', 'gte', 'lte', 'in', 'contains']
        if self.operator not in valid_operators:
            raise ValueError(f"Invalid operator: {self.operator}. Must be one of {valid_operators}")
    
    def evaluate(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate document metadata against the rule.
        
        Args:
            document: Document metadata to evaluate
            
        Returns:
            Dictionary containing evaluation results
        """
        # Check if field exists
        if self.field not in document:
            if self.required:
                return {
                    'passed': False,
                    'message': f"Required field '{self.field}' is missing",
                    'details': {
                        'field': self.field,
                        'operator': self.operator,
                        'expected': self.value,
                        'actual': None
                    },
                    'timestamp': datetime.utcnow().isoformat()
                }
            return {
                'passed': True,
                'message': f"Optional field '{self.field}' is missing",
                'details': {
                    'field': self.field,
                    'operator': self.operator,
                    'expected': self.value,
                    'actual': None
                },
                'timestamp': datetime.utcnow().isoformat()
            }
        
        # Get actual value
        actual = document[self.field]
        
        # Perform comparison
        passed = False
        message = ""
        
        if self.operator == 'eq':
            passed = actual == self.value
            message = f"Field '{self.field}' {'equals' if passed else 'does not equal'} expected value"
        elif self.operator == 'ne':
            passed = actual != self.value
            message = f"Field '{self.field}' {'does not equal' if passed else 'equals'} expected value"
        elif self.operator == 'gt':
            passed = actual > self.value
            message = f"Field '{self.field}' {'is greater than' if passed else 'is not greater than'} expected value"
        elif self.operator == 'lt':
            passed = actual < self.value
            message = f"Field '{self.field}' {'is less than' if passed else 'is not less than'} expected value"
        elif self.operator == 'gte':
            passed = actual >= self.value
            message = f"Field '{self.field}' {'is greater than or equal to' if passed else 'is not greater than or equal to'} expected value"
        elif self.operator == 'lte':
            passed = actual <= self.value
            message = f"Field '{self.field}' {'is less than or equal to' if passed else 'is not less than or equal to'} expected value"
        elif self.operator == 'in':
            passed = actual in self.value
            message = f"Field '{self.field}' {'is in' if passed else 'is not in'} expected values"
        elif self.operator == 'contains':
            passed = self.value in actual
            message = f"Field '{self.field}' {'contains' if passed else 'does not contain'} expected value"
        
        return {
            'passed': passed,
            'message': message,
            'details': {
                'field': self.field,
                'operator': self.operator,
                'expected': self.value,
                'actual': actual
            },
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def validate(self) -> bool:
        """
        Validate the rule configuration.
        
        Returns:
            True if the rule is valid, False otherwise
        """
        if not self.rule_id or not self.name or not self.description:
            return False
            
        if not self.field:
            return False
            
        if not self.operator:
            return False
            
        if self.value is None:
            return False
            
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the rule to a dictionary representation.
        
        Returns:
            Dictionary containing rule configuration
        """
        data = super().to_dict()
        data.update({
            'field': self.field,
            'operator': self.operator,
            'value': self.value,
            'required': self.required
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MetadataRule':
        """
        Create a rule instance from a dictionary.
        
        Args:
            data: Dictionary containing rule configuration
            
        Returns:
            MetadataRule instance
        """
        rule = cls(
            rule_id=data['rule_id'],
            name=data['name'],
            description=data['description'],
            field=data['field'],
            operator=data['operator'],
            value=data['value'],
            required=data.get('required', True),
            severity=data.get('severity', 'medium'),
            category=data.get('category', 'general'),
            enabled=data.get('enabled', True)
        )
        rule.created_at = datetime.fromisoformat(data['created_at'])
        rule.updated_at = datetime.fromisoformat(data['updated_at'])
        return rule 