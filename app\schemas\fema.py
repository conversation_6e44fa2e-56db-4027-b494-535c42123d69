from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, constr
from app.models.compliance import ApplicationStatus, ComplianceStatus

class FEMAApplicationBase(BaseModel):
    """Base schema for FEMA application data."""
    disaster_number: constr(min_length=4, max_length=10) = Field(..., description="FEMA disaster number")
    applicant_id: constr(min_length=4) = Field(..., description="FEMA applicant ID")
    project_title: constr(min_length=3, max_length=255) = Field(..., description="Project title")

class FEMAApplicationCreate(FEMAApplicationBase):
    """Schema for creating a new FEMA application."""
    pass

class FEMAApplicationUpdate(BaseModel):
    """Schema for updating an existing FEMA application."""
    disaster_number: Optional[str] = None
    applicant_id: Optional[str] = None
    project_title: Optional[str] = None
    status: Optional[ApplicationStatus] = None
    compliance_status: Optional[ComplianceStatus] = None

class FEMAApplicationResponse(FEMAApplicationBase):
    """Schema for FEMA application response."""
    id: str
    status: ApplicationStatus
    compliance_status: ComplianceStatus
    submission_date: datetime
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class FEMAApplicationWithDetails(FEMAApplicationResponse):
    """Schema for FEMA application with related details."""
    documents: List["ComplianceDocumentResponse"] = []
    reviews: List["ComplianceReviewResponse"] = []

    class Config:
        from_attributes = True 