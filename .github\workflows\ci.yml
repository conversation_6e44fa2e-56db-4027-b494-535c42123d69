name: CI

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install Dependencies
        run: npm install
        working-directory: frontend
      - name: Lint
        run: npm run lint
        working-directory: frontend
      - name: Format Check
        run: npm run format -- --check
        working-directory: frontend
      - name: Run Tests
        run: npm test -- --coverage
        working-directory: frontend

  backend:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: pa_check_db
        ports:
          - "5432:5432"
        options: >
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r pa-check/requirements.txt
      - name: Lint
        run: |
          black --check pa-check
          flake8 pa-check
          mypy pa-check
      - name: Run Tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/pa_check_db
        run: |
          pytest --cov=pa-check pa-check

  docker:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker Image
        run: |
          docker build -t pa-check:latest pa-check
          docker build -t compliancemax-frontend:latest frontend
