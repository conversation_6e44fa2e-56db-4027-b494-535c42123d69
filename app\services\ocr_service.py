from celery import shared_task
import pytesseract
import easyocr
import paddleocr
from PIL import Image
import logging
from datetime import datetime
import requests
from typing import Dict, Any
import os
from tenacity import retry, stop_after_attempt, wait_exponential

from app.core.config import settings

logger = logging.getLogger(__name__)

@shared_task
def process_document(document_path: str, config: dict) -> dict:
    try:
        logger.info(f"Processing document: {document_path}")
        
        # Validate that the file exists
        if not os.path.exists(document_path):
            logger.error(f"Document not found: {document_path}")
            return {'status': 'failed', 'error': 'Document file not found'}
            
        image = preprocess_image(document_path, config)
        results = []
        
        # Process with selected OCR engines
        if config.get('use_tesseract'):
            results.append(extract_text(image, 'tesseract'))
        if config.get('use_easyocr'):
            results.append(extract_text(image, 'easyocr'))
        if config.get('use_paddleocr'):
            results.append(extract_text(image, 'paddleocr'))
            
        combined_result = combine_results(results, config.get('weights', {}))
        confidence = calculate_confidence(combined_result)
        metadata = extract_metadata(document_path)

        # Chain xAI analysis task if configured
        if config.get('use_xai_analysis', False):
            xai_result = analyze_document_with_xai.delay(combined_result['text'], config).get()
            combined_result['xai_analysis'] = xai_result.get('result', {})

        return {
            'text': combined_result['text'],
            'confidence': confidence,
            'metadata': metadata,
            'status': 'completed',
            'xai_analysis': combined_result.get('xai_analysis', {})
        }
    except Exception as e:
        logger.error(f"Error processing document {document_path}: {str(e)}", exc_info=True)
        return {'status': 'failed', 'error': str(e)}

@shared_task
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def analyze_document_with_xai(document_text: str, config: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # Configure xAI API request
        xai_api_url = "https://api.x.ai/v1/analyze"  # Replace with actual xAI API endpoint
        headers = {
            "Authorization": f"Bearer {settings.XAI_API_KEY}",  # Add your xAI API key to settings
            "Content-Type": "application/json"
        }
        payload = {
            "text": document_text,
            "task": config.get("xai_task", "summarize"),  # e.g., "summarize", "extract_data", "analyze_compliance"
            "parameters": config.get("xai_parameters", {})
        }

        # Call xAI API
        response = requests.post(xai_api_url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        result = response.json()

        logger.info(f"xAI analysis completed for document")
        return {
            "status": "completed",
            "result": result,
            "error": None
        }
    except requests.exceptions.Timeout:
        logger.error("xAI analysis timed out")
        return {
            "status": "failed",
            "result": None,
            "error": "API request timed out"
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"xAI API request failed: {str(e)}")
        return {
            "status": "failed",
            "result": None,
            "error": f"API request failed: {str(e)}"
        }
    except Exception as e:
        logger.error(f"xAI analysis failed: {str(e)}", exc_info=True)
        return {
            "status": "failed",
            "result": None,
            "error": str(e)
        }

def preprocess_image(image_path: str, config: dict) -> Image.Image:
    image = Image.open(image_path)
    if config.get('grayscale'):
        image = image.convert('L')
    if config.get('resize'):
        image = image.resize(config['resize'])
    return image

def extract_text(image: Image.Image, engine: str) -> dict:
    result = {'engine': engine, 'text': '', 'error': None}
    
    try:
        if engine == 'tesseract':
            # Handle case where Tesseract might not be installed
            if not hasattr(pytesseract, 'image_to_string'):
                result['error'] = "Tesseract not properly installed"
                return result
                
            result['text'] = pytesseract.image_to_string(image)
            return result
        elif engine == 'easyocr':
            reader = easyocr.Reader(['en'])
            ocr_result = reader.readtext(image)
            result['text'] = ' '.join([res[1] for res in ocr_result])
            return result
        elif engine == 'paddleocr':
            ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='en')
            ocr_result = ocr.ocr(image, cls=True)
            result['text'] = ' '.join([line[1][0] for line in ocr_result])
            return result
        else:
            result['error'] = f"Unsupported OCR engine: {engine}"
            return result
    except Exception as e:
        logger.error(f"{engine} OCR failed: {str(e)}", exc_info=True)
        result['error'] = str(e)
        return result

def extract_metadata(document_path: str) -> dict:
    try:
        file_stats = os.stat(document_path)
        return {
            'path': document_path,
            'filename': os.path.basename(document_path),
            'size_bytes': file_stats.st_size,
            'modified_at': datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
            'processed_at': datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Error extracting metadata: {str(e)}")
        return {'path': document_path, 'processed_at': datetime.utcnow().isoformat()}

def combine_results(results: list, weights: dict) -> dict:
    if not results:
        return {'text': '', 'confidence': 0.0}
    
    # Filter out failed results
    valid_results = [r for r in results if r.get('error') is None and r.get('text')]
    if not valid_results:
        return {'text': '', 'confidence': 0.0}

    # If weights are provided, use weighted combination
    if weights:
        weighted_text = ""
        total_weight = 0
        for result in valid_results:
            engine = result['engine']
            weight = weights.get(engine, 1.0)
            weighted_text += result['text'] + " "
            total_weight += weight
        
        return {
            'text': weighted_text.strip(),
            'confidence': min(0.95, total_weight / len(weights) if weights else 0.8)
        }
    
    # Simple combination: use the first valid result
    return {'text': valid_results[0]['text'], 'confidence': 0.95}

def calculate_confidence(result: dict) -> float:
    # More sophisticated confidence calculation could be implemented here
    # based on OCR engine-specific confidence metrics
    return result.get('confidence', 0.95)
