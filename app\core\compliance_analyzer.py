"""
Compliance analysis system for ComplianceMax.
Implements document analysis and compliance validation.
"""

from typing import Dict, Any, List, Optional, Set
import re
from datetime import datetime
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import spacy
from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import track_performance
from app.core.exceptions import DocumentProcessingError
import asyncio
import json
from dataclasses import dataclass
from enum import Enum
from app.core.monitoring.metrics import track_compliance_check
from app.core.security.analytics import SecurityEvent, ThreatLevel
from app.core.document_processor import DocumentProcessor

logger = get_logger(__name__)

class ComplianceStatus(Enum):
    """Compliance status levels."""
    COMPLIANT = "compliant"
    PARTIAL = "partial"
    NON_COMPLIANT = "non_compliant"
    UNKNOWN = "unknown"

@dataclass
class ComplianceCheck:
    """Compliance check result."""
    document_id: str
    policy_id: str
    timestamp: datetime
    status: ComplianceStatus
    score: float
    findings: List[Dict]
    remediation: List[str]

class ComplianceAnalyzer:
    """Main compliance analysis class."""
    
    def __init__(self):
        """Initialize compliance analyzer with NLP models."""
        # Load NLP models
        self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.nlp = spacy.load('en_core_web_sm')
        
        # Compile regex patterns
        self.date_pattern = re.compile(settings.METADATA_DATE_PATTERN)
        
        # Initialize similarity threshold
        self.similarity_threshold = settings.REQUIREMENT_SIMILARITY_THRESHOLD
        
        self.doc_processor = DocumentProcessor()
        self.check_history = []
        self.policy_cache = {}
        
        # Start background tasks
        self.cleanup_task = asyncio.create_task(self._periodic_cleanup())
        self.analysis_task = asyncio.create_task(self._periodic_analysis())
    
    @track_performance("compliance_analysis")
    async def analyze_document(
        self,
        document_content: Dict[str, Any],
        requirements: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Analyze document content against compliance requirements.
        
        Args:
            document_content: Extracted document content and metadata
            requirements: List of compliance requirements to check
            
        Returns:
            Analysis results including compliance status and findings
        """
        try:
            # Extract text content
            text = document_content.get("text", "")
            if not text:
                raise DocumentProcessingError(
                    message="No text content found in document",
                    details={"content_keys": list(document_content.keys())}
                )
            
            # Analyze document structure
            structure = self._analyze_structure(text)
            
            # Extract key elements
            entities = self._extract_entities(text)
            dates = self._extract_dates(text)
            metadata = self._extract_metadata(document_content)
            
            # Check compliance requirements
            compliance_results = await self._check_compliance(
                text=text,
                structure=structure,
                entities=entities,
                requirements=requirements
            )
            
            return {
                "compliance_status": self._determine_compliance_status(compliance_results),
                "findings": compliance_results,
                "metadata": metadata,
                "entities": entities,
                "dates": dates,
                "structure": structure
            }
            
        except Exception as e:
            logger.error(f"Compliance analysis failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message=f"Failed to analyze document: {str(e)}",
                details={"error_type": type(e).__name__}
            )
    
    def _analyze_structure(self, text: str) -> Dict[str, Any]:
        """Analyze document structure and organization."""
        doc = self.nlp(text)
        
        # Identify sections
        sections = []
        current_section = None
        
        for i, para in enumerate(doc.sents):
            # Check for section headers
            if self._is_section_header(para.text):
                if current_section:
                    sections.append(current_section)
                current_section = {
                    "title": para.text.strip(),
                    "content": [],
                    "start_index": i
                }
            elif current_section:
                current_section["content"].append(para.text)
        
        if current_section:
            sections.append(current_section)
        
        # Analyze paragraph structure
        paragraphs = [
            {
                "text": para.text,
                "length": len(para),
                "entities": [(ent.text, ent.label_) for ent in para.ents]
            }
            for para in doc.sents
        ]
        
        return {
            "sections": sections,
            "paragraphs": paragraphs,
            "sentence_count": len(list(doc.sents)),
            "paragraph_count": len(paragraphs)
        }
    
    def _is_section_header(self, text: str) -> bool:
        """Determine if text is likely a section header."""
        # Check common patterns for section headers
        if len(text.strip()) < 3:
            return False
            
        patterns = [
            r'^\d+\.\s+[A-Z]',  # Numbered sections
            r'^[IVXLC]+\.\s+[A-Z]',  # Roman numeral sections
            r'^[A-Z][a-z]+\s*\d*\.',  # Capitalized word followed by number
            r'^[A-Z\s]{4,}'  # All caps text
        ]
        
        return any(re.match(pattern, text.strip()) for pattern in patterns)
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract named entities from text."""
        doc = self.nlp(text)
        
        entities = {}
        for ent in doc.ents:
            if ent.label_ not in entities:
                entities[ent.label_] = []
            if ent.text not in entities[ent.label_]:
                entities[ent.label_].append(ent.text)
        
        return entities
    
    def _extract_dates(self, text: str) -> List[str]:
        """Extract dates from text."""
        return [
            match.group()
            for match in self.date_pattern.finditer(text)
        ]
    
    def _extract_metadata(self, document_content: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and normalize document metadata."""
        metadata = document_content.get("metadata", {})
        
        # Normalize common metadata fields
        normalized = {
            "author": metadata.get("author", ""),
            "title": metadata.get("title", ""),
            "created_date": metadata.get("created", ""),
            "modified_date": metadata.get("modified", ""),
            "document_type": self._determine_document_type(document_content),
            "size": metadata.get("size", 0)
        }
        
        return normalized
    
    def _determine_document_type(self, document_content: Dict[str, Any]) -> str:
        """Determine document type from content and metadata."""
        metadata = document_content.get("metadata", {})
        
        if "page_count" in metadata:
            return "PDF"
        elif "core_properties" in metadata:
            return "Word Document"
        elif "sheets" in document_content:
            return "Excel Document"
        elif "encoding" in metadata:
            return "Text Document"
        elif "format" in metadata:
            return f"Image ({metadata['format']})"
        return "Unknown"
    
    async def _check_compliance(
        self,
        text: str,
        structure: Dict[str, Any],
        entities: Dict[str, List[str]],
        requirements: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Check document against compliance requirements."""
        results = []
        
        # Get document embeddings
        doc_embedding = self.sentence_model.encode([text])[0]
        
        for req in requirements:
            requirement_text = req["description"]
            req_embedding = self.sentence_model.encode([requirement_text])[0]
            
            # Calculate similarity
            similarity = cosine_similarity(
                doc_embedding.reshape(1, -1),
                req_embedding.reshape(1, -1)
            )[0][0]
            
            # Check requirement-specific criteria
            criteria_met = self._check_requirement_criteria(
                req,
                text,
                structure,
                entities
            )
            
            results.append({
                "requirement_id": req["id"],
                "requirement": requirement_text,
                "similarity_score": float(similarity),
                "criteria_met": criteria_met,
                "compliant": similarity >= self.similarity_threshold and all(criteria_met.values()),
                "findings": self._generate_findings(
                    req,
                    similarity,
                    criteria_met
                )
            })
        
        return results
    
    def _check_requirement_criteria(
        self,
        requirement: Dict[str, Any],
        text: str,
        structure: Dict[str, Any],
        entities: Dict[str, List[str]]
    ) -> Dict[str, bool]:
        """Check specific criteria for a requirement."""
        criteria = {}
        
        # Check required entities
        if "required_entities" in requirement:
            for entity_type in requirement["required_entities"]:
                found = any(
                    entity_type in entities and len(entities[entity_type]) > 0
                )
                criteria[f"has_{entity_type}"] = found
        
        # Check required sections
        if "required_sections" in requirement:
            for section in requirement["required_sections"]:
                found = any(
                    s["title"].lower().find(section.lower()) != -1
                    for s in structure["sections"]
                )
                criteria[f"has_section_{section}"] = found
        
        # Check date requirements
        if "requires_date" in requirement and requirement["requires_date"]:
            criteria["has_date"] = len(self._extract_dates(text)) > 0
        
        return criteria
    
    def _generate_findings(
        self,
        requirement: Dict[str, Any],
        similarity: float,
        criteria_met: Dict[str, bool]
    ) -> List[str]:
        """Generate detailed findings for a requirement."""
        findings = []
        
        # Check similarity threshold
        if similarity < self.similarity_threshold:
            findings.append(
                f"Content similarity ({similarity:.2f}) below threshold "
                f"({self.similarity_threshold})"
            )
        
        # Add criteria-specific findings
        for criterion, met in criteria_met.items():
            if not met:
                findings.append(f"Failed criterion: {criterion}")
        
        return findings
    
    def _determine_compliance_status(
        self,
        compliance_results: List[Dict[str, Any]]
    ) -> str:
        """Determine overall compliance status."""
        if not compliance_results:
            return "UNKNOWN"
        
        all_compliant = all(result["compliant"] for result in compliance_results)
        any_compliant = any(result["compliant"] for result in compliance_results)
        
        if all_compliant:
            return "COMPLIANT"
        elif any_compliant:
            return "PARTIALLY_COMPLIANT"
        return "NON_COMPLIANT"
    
    async def check_compliance(self, document_id: str, policy_id: str) -> ComplianceCheck:
        """Perform automated compliance check."""
        # Process document
        doc_content = await self.doc_processor.process_document(document_id)
        
        # Get policy requirements
        policy = await self._get_policy(policy_id)
        
        # Perform compliance analysis
        findings = await self._analyze_compliance(doc_content, policy)
        
        # Calculate compliance score
        score = self._calculate_score(findings)
        
        # Generate remediation suggestions
        remediation = self._generate_remediation(findings)
        
        # Determine status
        status = self._determine_status(score)
        
        # Create check result
        check = ComplianceCheck(
            document_id=document_id,
            policy_id=policy_id,
            timestamp=datetime.utcnow(),
            status=status,
            score=score,
            findings=findings,
            remediation=remediation
        )
        
        # Store check history
        self.check_history.append(check)
        
        # Track metrics
        track_compliance_check(status.value)
        
        return check
    
    async def _get_policy(self, policy_id: str) -> Dict:
        """Get policy requirements, using cache if available."""
        if policy_id in self.policy_cache:
            return self.policy_cache[policy_id]
        
        # Load policy from storage
        policy = await self._load_policy(policy_id)
        self.policy_cache[policy_id] = policy
        return policy
    
    async def _analyze_compliance(self, content: Dict, policy: Dict) -> List[Dict]:
        """Analyze document content against policy requirements."""
        findings = []
        
        for requirement in policy['requirements']:
            result = await self._check_requirement(content, requirement)
            if result:
                findings.append(result)
        
        return findings
    
    def _calculate_score(self, findings: List[Dict]) -> float:
        """Calculate compliance score based on findings."""
        if not findings:
            return 1.0
        
        total_weight = sum(f.get('weight', 1.0) for f in findings)
        if total_weight == 0:
            return 0.0
        
        weighted_sum = sum(
            f.get('score', 0.0) * f.get('weight', 1.0)
            for f in findings
        )
        
        return weighted_sum / total_weight
    
    def _generate_remediation(self, findings: List[Dict]) -> List[str]:
        """Generate remediation suggestions based on findings."""
        remediation = []
        
        for finding in findings:
            if finding.get('score', 1.0) < 1.0:
                suggestion = self._get_remediation_suggestion(finding)
                if suggestion:
                    remediation.append(suggestion)
        
        return remediation
    
    def _determine_status(self, score: float) -> ComplianceStatus:
        """Determine compliance status based on score."""
        if score >= 0.95:
            return ComplianceStatus.COMPLIANT
        elif score >= 0.80:
            return ComplianceStatus.PARTIAL
        else:
            return ComplianceStatus.NON_COMPLIANT
    
    async def _periodic_cleanup(self):
        """Periodically clean up old check history."""
        while True:
            try:
                # Keep last 1000 checks
                if len(self.check_history) > 1000:
                    self.check_history = self.check_history[-1000:]
                
                # Clear old items from policy cache
                # TODO: Implement cache expiration
                
            except Exception as e:
                logger.error(f"Error in compliance analyzer cleanup: {str(e)}")
            
            await asyncio.sleep(3600)  # Run every hour
    
    async def _periodic_analysis(self):
        """Periodically analyze compliance trends."""
        while True:
            try:
                # Analyze compliance trends
                await self._analyze_trends()
                
                # Update compliance metrics
                await self._update_metrics()
                
            except Exception as e:
                logger.error(f"Error in compliance analyzer analysis: {str(e)}")
            
            await asyncio.sleep(3600)  # Run every hour
    
    async def _analyze_trends(self):
        """Analyze compliance trends across documents and policies."""
        # TODO: Implement trend analysis
        pass
    
    async def _update_metrics(self):
        """Update compliance metrics."""
        # TODO: Implement metrics updates
        pass 