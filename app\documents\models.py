"""Document-related database models."""

from datetime import datetime
from typing import List, Optional, Dict
from uuid import UUID, uuid4
from dataclasses import dataclass

from sqlalchemy import Column, DateTime, ForeignKey, String, Text, Integer, Enum, JSON
from sqlalchemy.orm import relationship

from app.core.database import Base
from app.models.base import TimestampMixin
from app.models.enums import DocumentType, ComplianceStatus, RiskLevel, MitigationStatus

from pydantic import BaseModel, Field


class Document(Base, TimestampMixin):
    """Document model representing a stored document."""
    
    __tablename__ = "documents"

    id = Column(String, primary_key=True)
    title = Column(String, nullable=False)
    document_type = Column(Enum(DocumentType), nullable=False, default=DocumentType.OTHER)
    content = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    document_metadata = Column(JSON, default=dict)
    
    # Document classification
    compliance_status = Column(Enum(ComplianceStatus), nullable=False, default=ComplianceStatus.UNKNOWN)
    risk_level = Column(Enum(RiskLevel), nullable=True)
    mitigation_status = Column(Enum(MitigationStatus), nullable=True)
    
    # Review information
    review_date = Column(DateTime, nullable=True)
    review_notes = Column(Text, nullable=True)
    
    # Extracted content
    text_content = Column(Text, nullable=True)
    
    # User relationship
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=True)
    user = relationship("User", back_populates="documents")
    
    # Version relationship
    versions = relationship("DocumentVersion", back_populates="document", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Document {self.title} ({self.document_type})>"

    def create_version(
        self,
        content: str,
        version_number: str,
        changes: Optional[Dict] = None,
        compliance_status: Optional[ComplianceStatus] = None,
        review_notes: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> "DocumentVersion":
        """Create a new version of this document.
        
        Args:
            content: The content of the new version
            version_number: Version identifier (e.g., timestamp or sequence)
            changes: Optional description of changes from previous version
            compliance_status: Optional compliance status for this version
            review_notes: Optional review notes
            metadata: Optional additional metadata
            
        Returns:
            The created DocumentVersion instance
        """
        from app.documents.version_control import VersionControlService
        from app.db.session import SessionLocal
        
        db = SessionLocal()
        try:
            version_control = VersionControlService(db)
            version = version_control.create_version(
                document=self,
                content=content,
                version_number=version_number,
                changes=changes,
                compliance_status=compliance_status,
                review_notes=review_notes,
                metadata=metadata
            )
            return version
        finally:
            db.close()


class DocumentVersion(Base):
    """Model for tracking document versions."""
    
    __tablename__ = "document_versions"

    id = Column(String, primary_key=True)
    document_id = Column(String, ForeignKey("documents.id"), nullable=False)
    version_number = Column(String, nullable=False)
    storage_path = Column(String, nullable=False)
    content_hash = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    changes = Column(String)  # JSON string of changes
    compliance_status = Column(Enum(ComplianceStatus))
    review_notes = Column(String)
    version_metadata = Column(String)  # JSON string of metadata
    
    # Relationships
    document = relationship("Document", back_populates="versions")

    def __repr__(self):
        return f"<DocumentVersion {self.version_number} of document {self.document_id}>"


@dataclass
class DocumentData:
    """Document data model."""
    id: str
    title: str
    content: str
    metadata: Dict
    created_at: datetime
    updated_at: datetime
    status: str
    version: str
    owner: str
    tags: List[str]
    file_type: str
    file_size: int
    checksum: str
    path: str
    is_template: bool = False
    parent_id: Optional[str] = None
    review_status: Optional[str] = None
    review_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None
    compliance_status: Optional[ComplianceStatus] = None
    risk_level: Optional[RiskLevel] = None
    mitigation_status: Optional[MitigationStatus] = None


@dataclass
class ComplianceCheck:
    """Result of a compliance check."""
    id: str
    document_id: str
    check_type: str
    status: ComplianceStatus
    details: Dict
    timestamp: datetime
    confidence: float
    risk_level: RiskLevel
    mitigation_required: bool = False
    mitigation_status: Optional[MitigationStatus] = None
    mitigation_notes: Optional[str] = None
    reviewer: Optional[str] = None
    review_date: Optional[datetime] = None


@dataclass
class ComplianceReport:
    """Comprehensive compliance report."""
    id: str
    document_id: str
    status: ComplianceStatus
    risk_level: RiskLevel
    checks: List[ComplianceCheck]
    timestamp: datetime
    summary: str
    recommendations: List[str]
    next_review_date: datetime
    generated_by: str
    approved_by: Optional[str] = None
    approval_date: Optional[datetime] = None
    notes: Optional[str] = None


class ComplianceCheck(BaseModel):
    """Result of a single compliance check."""
    
    document_id: UUID
    check_type: str
    status: ComplianceStatus
    confidence: float = Field(ge=0.0, le=1.0)
    details: str
    timestamp: datetime


class ComplianceReport(BaseModel):
    """Comprehensive compliance report for a document."""
    
    document_id: UUID
    status: ComplianceStatus
    risk_level: RiskLevel
    checks: List[ComplianceCheck]
    timestamp: datetime
    summary: str
    recommendations: List[str]


class MitigationTask(BaseModel):
    """Task for mitigating compliance issues."""
    
    type: str
    issue: str
    assignee: str
    due_date: datetime
    status: MitigationStatus
    notes: Optional[str] = None


class MitigationPlan(BaseModel):
    """Plan for addressing compliance issues."""
    
    document_id: UUID
    status: MitigationStatus
    tasks: List[MitigationTask]
    assignee: str
    due_date: datetime
    created_at: datetime
    completed_at: Optional[datetime] = None
    notes: Optional[str] = None 