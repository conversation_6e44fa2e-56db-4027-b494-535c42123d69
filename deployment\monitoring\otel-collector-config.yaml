apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-collector-config
  namespace: monitoring
data:
  otel-collector-config: |
    receivers:
      otlp:
        protocols:
          grpc:
            endpoint: 0.0.0.0:4317
          http:
            endpoint: 0.0.0.0:4318
      prometheus:
        config:
          scrape_configs:
            - job_name: 'compliancemax'
              scrape_interval: 10s
              static_configs:
                - targets: ['localhost:8080']

    processors:
      batch:
        timeout: 1s
        send_batch_size: 1024
      memory_limiter:
        check_interval: 1s
        limit_mib: 1024
        spike_limit_mib: 128
      resourcedetection:
        detectors: [env, system]
        timeout: 2s
      k8sattributes:
        auth_type: "serviceAccount"
        passthrough: false
        filter:
          node_from_env_var: KUBE_NODE_NAME

    exporters:
      jaeger:
        endpoint: jaeger-collector.monitoring:14250
        tls:
          insecure: true
      prometheus:
        endpoint: 0.0.0.0:8889
        namespace: compliancemax
      logging:
        loglevel: debug
      zipkin:
        endpoint: "http://zipkin.monitoring:9411/api/v2/spans"
        format: proto

    extensions:
      health_check:
        endpoint: 0.0.0.0:13133
      pprof:
        endpoint: 0.0.0.0:1777
      zpages:
        endpoint: 0.0.0.0:55679

    service:
      extensions: [health_check, pprof, zpages]
      pipelines:
        traces:
          receivers: [otlp]
          processors: [memory_limiter, batch, resourcedetection, k8sattributes]
          exporters: [jaeger, zipkin, logging]
        metrics:
          receivers: [otlp, prometheus]
          processors: [memory_limiter, batch, resourcedetection, k8sattributes]
          exporters: [prometheus, logging] 