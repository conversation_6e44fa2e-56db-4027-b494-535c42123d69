import logging
import json
from logging.handlers import RotatingFileHandler
import uuid

class ContextLogger(logging.Logger):
    def __init__(self, name: str, level: int = logging.NOTSET):
        super().__init__(name, level)
        self.request_id = None

    def set_request_id(self, request_id: str):
        self.request_id = request_id

    def _log(self, level, msg, args, exc_info=None, extra=None, stack_info=False):
        extra = extra or {}
        extra['request_id'] = self.request_id or 'no-request'
        super()._log(level, msg, args, exc_info, extra, stack_info)

logging.setLoggerClass(ContextLogger)

def setup_logging():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    formatter = logging.Formatter(
        '{"time": "%(asctime)s", "level": "%(levelname)s", "request_id": "%(request_id)s", "message": "%(message)s"}'
    )

    file_handler = RotatingFileHandler(
        'app.log',
        maxBytes=10*1024*1024,  # 10 MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger

logger = setup_logging()