from typing import Dict, List, Optional
import httpx
from fastapi import HTTPException
from app.core.config import settings
import asyncio
from datetime import datetime, timedelta

class FEMAAPIService:
    def __init__(self):
        self.base_url = settings.FEMA_API_BASE_URL
        self._rate_limit = 10  # requests per second
        self._last_request = datetime.min
        
    async def _rate_limit_delay(self):
        """Implement rate limiting."""
        now = datetime.utcnow()
        if (now - self._last_request) < timedelta(seconds=1/self._rate_limit):
            await asyncio.sleep(1/self._rate_limit)
        self._last_request = now

    async def get_disaster_declarations(self, state: Optional[str] = None) -> List[Dict]:
        """Fetch disaster declarations from FEMA API."""
        await self._rate_limit_delay()
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                params = {"state": state} if state else {}
                response = await client.get(
                    f"{self.base_url}/DisasterDeclarationsSummaries",
                    params=params
                )
                response.raise_for_status()
                data = response.json()
                return data.get("DisasterDeclarationsSummaries", [])
        except httpx.TimeoutException:
            raise HTTPException(
                status_code=504,
                detail="FEMA API request timed out"
            )
        except httpx.HTTPError as e:
            raise HTTPException(
                status_code=e.response.status_code if hasattr(e, 'response') else 500,
                detail=f"FEMA API error: {str(e)}"
            )
            
    async def get_public_assistance_data(self, disaster_number: str) -> List[Dict]:
        """Fetch public assistance data for a specific disaster."""
        await self._rate_limit_delay()
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/PublicAssistanceFundedProjectsSummaries",
                    params={"disasterNumber": disaster_number}
                )
                response.raise_for_status()
                data = response.json()
                return data.get("PublicAssistanceFundedProjectsSummaries", [])
        except httpx.TimeoutException:
            raise HTTPException(
                status_code=504,
                detail="FEMA API request timed out"
            )
        except httpx.HTTPError as e:
            raise HTTPException(
                status_code=e.response.status_code if hasattr(e, 'response') else 500,
                detail=f"FEMA API error: {str(e)}"
            )
            
    async def validate_disaster_declaration(self, disaster_number: str) -> bool:
        """Validate if a disaster declaration exists and is active."""
        await self._rate_limit_delay()
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/DisasterDeclarationsSummaries",
                    params={"disasterNumber": disaster_number}
                )
                response.raise_for_status()
                data = response.json()
                declarations = data.get("DisasterDeclarationsSummaries", [])
                return len(declarations) > 0 and any(
                    d.get("disasterNumber") == disaster_number for d in declarations
                )
        except (httpx.TimeoutException, httpx.HTTPError):
            return False
