"""Document processing orchestrator module.

This module provides the DocumentProcessor class which orchestrates the entire
document processing pipeline, utilizing the specialized services for OCR,
metadata extraction, and requirement analysis.
"""

import logging
import time
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
from prometheus_client import Histogram, Counter
import structlog

# Metrics: latency by processing step and total
STEP_LATENCY = Histogram('document_processing_step_latency_seconds', 'Latency by document processing steps', ['step'])
PROCESSED_DOCUMENTS = Counter('document_processing_total', 'Total documents processed', ['status'])

from app.core.config import settings
from app.core.cache import get_document_cache, set_document_cache
from app.documents.ocr_service import OCRService
from app.documents.metadata_service import MetadataService
from app.documents.requirement_service import RequirementService
from app.documents.models import Document
from app.documents.exceptions import OCRProcessingError, MetadataExtractionError, RequirementExtractionError, CrossReferenceExtractionError, StructureAnalysisError, DocumentProcessingError
from app.utils.document_utils import chunk_text_blocks, merge_chunked_results

# Configure structured logger
logger = structlog.get_logger(__name__)

class ProcessingResult:
    """Class representing the result of document processing."""
    
    def __init__(self, document_id: str):
        """Initialize processing result."""
        self.document_id = document_id
        self.metadata = {}
        self.requirements = []
        self.temporal_info = {}
        self.structure = {}
        self.cross_references = []
        self.text_blocks = []
        self.tables = []
        self.processing_history = []
        self.errors = []
        self.processing_time = 0
        self.start_time = time.time()
        
    def add_processing_step(self, step_name: str, status: str, details: Optional[Dict] = None):
        """Add a processing step to the history."""
        step = {
            "step": step_name,
            "timestamp": datetime.now().isoformat(),
            "status": status
        }
        if details:
            step["details"] = details
        self.processing_history.append(step)
        
    def add_error(self, step_name: str, error_message: str):
        """Add an error to the result."""
        self.errors.append({
            "step": step_name,
            "timestamp": datetime.now().isoformat(),
            "message": error_message
        })
        
    def complete(self):
        """Mark processing as complete and calculate processing time."""
        self.processing_time = time.time() - self.start_time
        self.add_processing_step("complete", "success", {
            "processing_time": self.processing_time
        })
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "document_id": self.document_id,
            "metadata": self.metadata,
            "requirements": self.requirements,
            "temporal_info": self.temporal_info,
            "structure": self.structure,
            "cross_references": self.cross_references,
            "text_blocks": self.text_blocks,
            "tables": self.tables,
            "processing_history": self.processing_history,
            "errors": self.errors,
            "processing_time": self.processing_time
        }

class DocumentProcessor:
    """Orchestrates the document processing workflow.
    
    This class coordinates the different processing services (OCR, metadata,
    requirements) to analyze documents in a structured pipeline.
    """
    
    def __init__(self, ocr_service: OCRService=None, metadata_service: MetadataService=None, requirement_service: RequirementService=None):
        """Initialize document processor with injected services and backward-compatible defaults."""
        from app.documents.ocr_service import OCRService
        from app.documents.metadata_service import MetadataService
        from app.documents.requirement_service import RequirementService
        self.ocr_service = ocr_service or OCRService()
        self.metadata_service = metadata_service or MetadataService()
        self.requirement_service = requirement_service or RequirementService()
        logger.info("Document processor initialized with injected services")
    
    async def _time_step(self, step_name: str, coro):
        """Time a processing step and return its result."""
        with STEP_LATENCY.labels(step_name).time():
            return await coro

    @STEP_LATENCY.labels('total_processing').time()
    async def process_document(self, file_path: str, document_id: str) -> Dict[str, Any]:
        # Create structured logger with context
        log = logger.bind(document_id=document_id, file_path=file_path)
        log.info("Starting document processing")
        
        # Check cache first
        cached_result = await get_document_cache(document_id)
        if cached_result:
            log.info("Retrieved document from cache", cached=True)
            PROCESSED_DOCUMENTS.labels('cached').inc()
            return cached_result
        
        result = ProcessingResult(document_id)
        try:
            path = Path(file_path)
            if not path.exists():
                log.error("Document file not found")
                raise FileNotFoundError(f"Document file not found: {file_path}")
                
            file_size = path.stat().st_size
            result.add_processing_step('initialization', 'success', {'file_path': str(path), 'file_size': file_size})
            log.info("Document initialized", file_size=file_size)

            # Step 1: OCR Processing
            result.add_processing_step('ocr_processing', 'started')
            try:
                log.info("Starting OCR processing")
                ocr_res = await self._time_step('ocr_processing', self.ocr_service.process_document(file_path))
                result.text_blocks = ocr_res.get('text_blocks', [])
                result.tables = ocr_res.get('tables', [])
                
                blocks_count = len(result.text_blocks)
                tables_count = len(result.tables)
                result.add_processing_step('ocr_processing', 'completed', {
                    'text_blocks_count': blocks_count,
                    'tables_count': tables_count
                })
                log.info("OCR processing completed", text_blocks=blocks_count, tables=tables_count)
            except Exception as e:
                log.error("OCR processing failed", error=str(e), exc_info=True)
                result.add_error('ocr_processing', str(e))
                PROCESSED_DOCUMENTS.labels('ocr_failed').inc()
                raise OCRProcessingError(f"OCR processing failed: {str(e)}") from e

            # Chunk text blocks for efficient processing if large
            if len(result.text_blocks) > 50:  # Only chunk if enough blocks to warrant it
                log.info("Chunking large document", block_count=len(result.text_blocks))
                chunks = chunk_text_blocks(result.text_blocks)
                log.info("Document chunked", chunk_count=len(chunks))
                
                # Process each chunk and merge results
                chunk_results = []
                for i, chunk in enumerate(chunks):
                    log.info(f"Processing chunk {i+1}/{len(chunks)}")
                    chunk_result = await self._process_text_chunk(chunk, file_path, f"{document_id}_chunk{i}")
                    chunk_results.append(chunk_result)
                
                # Merge results from all chunks
                merged = merge_chunked_results(chunk_results)
                
                # Copy merged data to result
                result.metadata = merged.get('metadata', {})
                result.temporal_info = merged.get('temporal_info', {})
                result.requirements = merged.get('requirements', [])
                result.cross_references = merged.get('cross_references', [])
                result.structure = merged.get('structure', {})
                
                log.info("Merged chunk results", 
                         requirements_count=len(result.requirements),
                         cross_references_count=len(result.cross_references))
            else:
                # Standard parallel processing for smaller documents
                log.info("Processing document in parallel")
                tasks = {
                    'metadata_extraction': self._time_step('metadata_extraction', 
                                                           self.metadata_service.extract_metadata(result.text_blocks, file_path)),
                    'document_type': self._time_step('document_type', 
                                                    self.metadata_service.identify_document_type(result.text_blocks)),
                    'temporal_extraction': self._time_step('temporal_extraction', 
                                                           self.metadata_service.extract_temporal_info(result.text_blocks)),
                    'requirement_extraction': self._time_step('requirement_extraction', 
                                                              self.requirement_service.extract_requirements(result.text_blocks)),
                    'cross_reference_extraction': self._time_step('cross_reference_extraction', 
                                                                  self.requirement_service.extract_cross_references(result.text_blocks)),
                    'structure_analysis': self._time_step('structure_analysis', 
                                                          self.requirement_service.analyze_structure(result.text_blocks)),
                }
                
                # Gather all results, with error handling
                results = await asyncio.gather(*tasks.values(), return_exceptions=True)
                
                # Process results with proper error handling
                for (step, _), res in zip(tasks.items(), results):
                    if isinstance(res, Exception):
                        log.error(f"Error in {step}", error=str(res))
                        result.add_error(step, str(res))
                        # assign fallbacks
                        if step=='metadata_extraction':      result.metadata={}
                        elif step=='document_type':        result.metadata['document_type']=None
                        elif step=='temporal_extraction':   result.temporal_info={}
                        elif step=='requirement_extraction':result.requirements=[]
                        elif step=='cross_reference_extraction':result.cross_references=[]
                        elif step=='structure_analysis':    result.structure={}
                    else:
                        # assign successes
                        if step=='metadata_extraction':      result.metadata=res
                        elif step=='document_type':        result.metadata['document_type']=res
                        elif step=='temporal_extraction':   result.temporal_info=res
                        elif step=='requirement_extraction':result.requirements=res
                        elif step=='cross_reference_extraction':result.cross_references=res
                        elif step=='structure_analysis':    result.structure=res
                        
                        # Log successful step completion
                        if step == 'requirement_extraction':
                            log.info(f"{step} completed", count=len(res))
                        elif step == 'cross_reference_extraction':
                            log.info(f"{step} completed", count=len(res))
                        else:
                            log.info(f"{step} completed")
            
            # Mark processing as complete            
            result.complete()
            final_result = result.to_dict()
            
            # Cache the result
            await set_document_cache(document_id, final_result)
            log.info("Document processing completed", 
                     processing_time=result.processing_time,
                     error_count=len(result.errors))
            
            # Update metrics
            if result.errors:
                PROCESSED_DOCUMENTS.labels('completed_with_errors').inc()
            else:
                PROCESSED_DOCUMENTS.labels('success').inc()
                
            return final_result
            
        except FileNotFoundError:
            log.error("File not found error", exc_info=True)
            PROCESSED_DOCUMENTS.labels('file_not_found').inc()
            raise
        except OCRProcessingError:
            log.error("Critical OCR error - returning partial results", exc_info=True)
            result.complete()
            PROCESSED_DOCUMENTS.labels('ocr_critical_error').inc()
            return result.to_dict()
        except Exception as e:
            log.error("Unhandled exception during processing", error=str(e), exc_info=True)
            result.add_error('processing', str(e))
            result.add_processing_step('complete', 'error', {'error': str(e)})
            result.complete()
            PROCESSED_DOCUMENTS.labels('unhandled_error').inc()
            return result.to_dict()
            
    async def _process_text_chunk(self, text_chunk: List[Dict], file_path: str, chunk_id: str) -> Dict[str, Any]:
        """Process a single chunk of text blocks.
        
        Args:
            text_chunk: List of text block dictionaries for this chunk
            file_path: Original document file path
            chunk_id: Unique identifier for this chunk
            
        Returns:
            Dictionary with processing results for this chunk
        """
        log = logger.bind(chunk_id=chunk_id)
        log.info("Processing text chunk", block_count=len(text_chunk))
        
        # Process this chunk with parallel tasks
        tasks = {
            'metadata_extraction': self._time_step('chunk_metadata', 
                                                   self.metadata_service.extract_metadata(text_chunk, file_path)),
            'temporal_extraction': self._time_step('chunk_temporal', 
                                                   self.metadata_service.extract_temporal_info(text_chunk)),
            'requirement_extraction': self._time_step('chunk_requirements', 
                                                      self.requirement_service.extract_requirements(text_chunk)),
            'cross_reference_extraction': self._time_step('chunk_xrefs', 
                                                          self.requirement_service.extract_cross_references(text_chunk)),
        }
        
        results = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        # Compile results with error handling
        chunk_result = {}
        for (step, _), res in zip(tasks.items(), results):
            step_name = step.replace('chunk_', '')
            
            if isinstance(res, Exception):
                log.error(f"Error in chunk {step}", error=str(res))
                # Provide empty result for this step
                if step == 'metadata_extraction': chunk_result['metadata'] = {}
                elif step == 'temporal_extraction': chunk_result['temporal_info'] = {}
                elif step == 'requirement_extraction': chunk_result['requirements'] = []
                elif step == 'cross_reference_extraction': chunk_result['cross_references'] = []
            else:
                # Store successful result
                if step == 'metadata_extraction': chunk_result['metadata'] = res
                elif step == 'temporal_extraction': chunk_result['temporal_info'] = res
                elif step == 'requirement_extraction': chunk_result['requirements'] = res
                elif step == 'cross_reference_extraction': chunk_result['cross_references'] = res
                
                # Log success
                if isinstance(res, list):
                    log.info(f"Chunk {step} completed", count=len(res))
                else:
                    log.info(f"Chunk {step} completed")
                    
        return chunk_result
    
    async def close(self):
        """Close all services."""
        # Currently no close methods are required for our services
        # But this method is included for future extensibility
        logger.info("Document processor closed")
