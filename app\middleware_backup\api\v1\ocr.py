from fastapi import APIRouter, UploadFile, File, Depends, HTTPException
from typing import List
from app.models.document import ProcessedDocument, DocumentType
from app.services.ocr_processor import OCRProcessor
from app.core.deps import get_current_user
import os
import shutil
from datetime import datetime

router = APIRouter()
ocr_processor = OCRProcessor()

@router.post("/upload/", response_model=ProcessedDocument)
async def upload_document(
    file: UploadFile = File(...),
    document_type: DocumentType = DocumentType.APPLICANT_UPLOAD,
    dr_number: str = None,
    current_user = Depends(get_current_user)
):
    """Upload and process a document through OCR."""
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
        
    # Create uploads directory if it doesn't exist
    upload_dir = "uploads"
    os.makedirs(upload_dir, exist_ok=True)
    
    # Save the uploaded file
    file_path = os.path.join(upload_dir, file.filename)
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    # Create document record
    document = ProcessedDocument(
        filename=file.filename,
        document_type=document_type,
        upload_date=datetime.utcnow(),
        file_path=file_path,
        dr_number=dr_number,
        uploader_id=str(current_user.id),
        mime_type=file.content_type,
        file_size=os.path.getsize(file_path)
    )
    await document.save()
    
    # Process document asynchronously
    try:
        processed_document = await ocr_processor.process_document(document)
        return processed_document
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/", response_model=List[ProcessedDocument])
async def get_processed_documents(
    current_user = Depends(get_current_user),
    dr_number: str = None
):
    """Get list of processed documents."""
    query = {}
    if dr_number:
        query["dr_number"] = dr_number
    
    documents = await ProcessedDocument.find(query).to_list()
    return documents
