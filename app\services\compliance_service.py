from datetime import datetime
from typing import List, Optional, Dict, <PERSON>ple
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from fastapi import HTTPException
from app.models.compliance import ComplianceReview, ReviewStatus, ReviewPriority, ComplianceResult, ComplianceRequirement, RiskLevel, ValidationStatus, ComplianceStatus
from app.models.project import Project
from app.models.document import Document
from app.models.policy import PolicyDocument, PolicyVersion, PolicyRequirement
from app.models.analysis import DocumentAnalysis, RequirementExtraction, SectionMapping
from app.core.config import get_settings
from app.services.document_analyzer import DocumentAnalyzer
import logging
import uuid

logger = logging.getLogger(__name__)

settings = get_settings()

class ComplianceService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.analyzer = DocumentAnalyzer(db)
        self.validator_version = "1.0.0"  # Update this when validation logic changes

    async def create_review(
        self,
        project_id: int,
        reviewer_id: int,
        priority: ReviewPriority,
        due_date: datetime,
        notes: Optional[str] = None
    ) -> ComplianceReview:
        """Create a new compliance review"""
        try:
            # Verify project exists
            project = await self.db.get(Project, project_id)
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            review = ComplianceReview(
                project_id=project_id,
                reviewer_id=reviewer_id,
                priority=priority,
                due_date=due_date,
                notes=notes
            )
            self.db.add(review)
            await self.db.commit()
            await self.db.refresh(review)
            return review

        except Exception as e:
            logger.error(f"Error creating compliance review: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def get_review(self, review_id: int) -> ComplianceReview:
        """Get a compliance review by ID"""
        stmt = select(ComplianceReview).where(
            ComplianceReview.id == review_id
        ).options(
            joinedload(ComplianceReview.project),
            joinedload(ComplianceReview.reviewer),
            joinedload(ComplianceReview.documents)
        )
        result = await self.db.execute(stmt)
        review = result.unique().scalar_one_or_none()
        if not review:
            raise HTTPException(status_code=404, detail="Review not found")
        return review

    async def update_status(
        self,
        review_id: int,
        status: ReviewStatus,
        findings: Optional[List[dict]] = None,
        recommendations: Optional[List[dict]] = None
    ) -> ComplianceReview:
        """Update the status of a compliance review"""
        review = await self.get_review(review_id)
        
        review.status = status
        if findings is not None:
            review.findings = findings
        if recommendations is not None:
            review.recommendations = recommendations
            
        if status in [ReviewStatus.COMPLIANT, ReviewStatus.NON_COMPLIANT]:
            review.completed_date = datetime.utcnow()
            
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def add_document(
        self,
        review_id: int,
        document_id: int
    ) -> ComplianceReview:
        """Add a document to a compliance review"""
        review = await self.get_review(review_id)
        document = await self.db.get(Document, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
            
        review.documents.append(document)
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def remove_document(
        self,
        review_id: int,
        document_id: int
    ) -> ComplianceReview:
        """Remove a document from a compliance review"""
        review = await self.get_review(review_id)
        document = await self.db.get(Document, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
            
        review.documents.remove(document)
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def get_project_reviews(
        self,
        project_id: int,
        status: Optional[ReviewStatus] = None
    ) -> List[ComplianceReview]:
        """Get all compliance reviews for a project"""
        stmt = select(ComplianceReview).where(
            ComplianceReview.project_id == project_id
        )
        if status:
            stmt = stmt.where(ComplianceReview.status == status)
            
        stmt = stmt.options(
            joinedload(ComplianceReview.reviewer),
            joinedload(ComplianceReview.documents)
        )
        
        result = await self.db.execute(stmt)
        reviews = result.unique().scalars().all()
        return list(reviews)

    async def get_overdue_reviews(self) -> List[ComplianceReview]:
        """Get all overdue compliance reviews"""
        now = datetime.utcnow()
        stmt = select(ComplianceReview).where(
            ComplianceReview.due_date < now,
            ComplianceReview.status.in_([
                ReviewStatus.PENDING,
                ReviewStatus.IN_PROGRESS,
                ReviewStatus.NEEDS_CLARIFICATION
            ])
        ).options(
            joinedload(ComplianceReview.project),
            joinedload(ComplianceReview.reviewer)
        )
        
        result = await self.db.execute(stmt)
        reviews = result.unique().scalars().all()
        return list(reviews)

    async def validate_document(
        self,
        document_id: str,
        application_id: str,
        policy_version_id: Optional[str] = None
    ) -> ComplianceResult:
        """
        Validate a document against compliance requirements.
        
        Args:
            document_id: ID of the document to validate
            application_id: ID of the application this document belongs to
            policy_version_id: Optional specific policy version to validate against
        
        Returns:
            ComplianceResult object with validation results
        """
        # Create new compliance result
        result_id = str(uuid.uuid4())
        result = ComplianceResult(
            id=result_id,
            document_id=document_id,
            application_id=application_id,
            validator_version=self.validator_version,
            validation_status=ValidationStatus.IN_PROGRESS
        )
        self.db.add(result)
        self.db.commit()

        try:
            # Analyze document content
            analysis = await self.analyzer.analyze_document(document_id)
            
            # Get policy requirements
            if policy_version_id:
                policy_version = self.db.query(PolicyVersion).filter(
                    PolicyVersion.id == policy_version_id
                ).first()
            else:
                policy_version = self.db.query(PolicyVersion).filter(
                    PolicyVersion.is_current == True
                ).first()
            
            if not policy_version:
                raise ValueError("No policy version found for validation")

            # Extract and validate requirements
            requirements = self.db.query(PolicyRequirement).filter(
                PolicyRequirement.policy_version_id == policy_version.id
            ).all()

            findings = {}
            deficiencies = {}
            total_score = 0.0
            total_weight = 0.0
            risk_level = RiskLevel.LOW

            for req in requirements:
                score, finding, deficiency = await self._validate_requirement(
                    analysis,
                    req
                )
                
                if finding:
                    findings[req.section] = finding
                if deficiency:
                    deficiencies[req.section] = deficiency
                    
                total_score += score * req.scoring_weight
                total_weight += req.scoring_weight
                
                # Update risk level based on deficiencies
                if deficiency and req.scoring_weight > 0.5:
                    risk_level = max(risk_level, RiskLevel.HIGH)
                elif deficiency:
                    risk_level = max(risk_level, RiskLevel.MEDIUM)

            # Calculate final score and determine status
            final_score = (total_score / total_weight) if total_weight > 0 else 0.0
            compliance_status = self._determine_compliance_status(final_score, len(deficiencies))

            # Update result
            result.validation_status = ValidationStatus.COMPLETED
            result.compliance_status = compliance_status
            result.risk_level = risk_level
            result.confidence_score = final_score
            result.findings = findings
            result.deficiencies = deficiencies
            result.recommendations = self._generate_recommendations(deficiencies)
            result.validated_at = datetime.utcnow()

            self.db.commit()
            return result

        except Exception as e:
            result.validation_status = ValidationStatus.FAILED
            result.error_details = {"error": str(e)}
            self.db.commit()
            raise

    async def _validate_requirement(
        self,
        analysis: DocumentAnalysis,
        requirement: PolicyRequirement
    ) -> Tuple[float, Dict, Optional[Dict]]:
        """
        Validate a single requirement against document analysis.
        
        Returns:
            Tuple of (score, finding, deficiency)
        """
        score = 0.0
        finding = {
            "requirement": requirement.requirement_text,
            "status": "not_found",
            "matches": []
        }
        deficiency = None

        # Check for matching sections
        matches = self.db.query(SectionMapping).filter(
            SectionMapping.document_id == analysis.document_id,
            SectionMapping.policy_requirement_id == requirement.id
        ).all()

        if matches:
            finding["status"] = "found"
            finding["matches"] = [
                {
                    "text": match.section_text,
                    "score": match.match_score,
                    "validated": match.is_validated
                }
                for match in matches
            ]
            
            # Calculate score based on best match
            best_match = max(matches, key=lambda m: m.match_score)
            score = best_match.match_score

            # Check if score meets minimum threshold
            if score < requirement.scoring_criteria.get("min_score", 0.7):
                deficiency = {
                    "type": "low_confidence",
                    "requirement": requirement.requirement_text,
                    "score": score,
                    "threshold": requirement.scoring_criteria.get("min_score", 0.7)
                }
        else:
            deficiency = {
                "type": "missing",
                "requirement": requirement.requirement_text
            }

        return score, finding, deficiency

    def _determine_compliance_status(self, score: float, deficiency_count: int) -> ComplianceStatus:
        """Determine compliance status based on score and deficiencies."""
        if score >= 0.9 and deficiency_count == 0:
            return ComplianceStatus.COMPLIANT
        elif score >= 0.7:
            return ComplianceStatus.PARTIALLY_COMPLIANT
        elif score < 0.5:
            return ComplianceStatus.NON_COMPLIANT
        return ComplianceStatus.NEEDS_REVIEW

    def _generate_recommendations(self, deficiencies: Dict) -> List[str]:
        """Generate recommendations based on deficiencies."""
        recommendations = []
        
        for section, deficiency in deficiencies.items():
            if deficiency["type"] == "missing":
                recommendations.append(
                    f"Add documentation for requirement: {deficiency['requirement']}"
                )
            elif deficiency["type"] == "low_confidence":
                recommendations.append(
                    f"Improve documentation clarity for: {deficiency['requirement']} "
                    f"(current confidence: {deficiency['score']:.2f})"
                )
        
        return recommendations

    async def get_compliance_result(self, result_id: str) -> ComplianceResult:
        """Get a compliance result by ID."""
        result = self.db.query(ComplianceResult).filter(
            ComplianceResult.id == result_id
        ).first()
        if not result:
            raise ValueError(f"Compliance result {result_id} not found")
        return result

    async def get_document_compliance_results(
        self,
        document_id: str,
        latest_only: bool = True
    ) -> List[ComplianceResult]:
        """Get compliance results for a document."""
        query = self.db.query(ComplianceResult).filter(
            ComplianceResult.document_id == document_id
        )
        
        if latest_only:
            return [query.order_by(ComplianceResult.created_at.desc()).first()]
        
        return query.order_by(ComplianceResult.created_at.desc()).all()

    async def get_application_compliance_results(
        self,
        application_id: str,
        latest_only: bool = True
    ) -> List[ComplianceResult]:
        """Get compliance results for an application."""
        query = self.db.query(ComplianceResult).filter(
            ComplianceResult.application_id == application_id
        )
        
        if latest_only:
            # Get latest result for each document
            subquery = query.distinct(
                ComplianceResult.document_id
            ).order_by(
                ComplianceResult.document_id,
                ComplianceResult.created_at.desc()
            )
            return subquery.all()
        
        return query.order_by(ComplianceResult.created_at.desc()).all()
