from datetime import datetime
from typing import List, Optional, Dict
from pydantic import BaseModel, Field
from app.models.ehp import EHPReviewStatus, EHPReviewType

class EHPReviewDocumentBase(BaseModel):
    document_id: int
    document_type: str

class EHPReviewDocumentCreate(EHPReviewDocumentBase):
    pass

class EHPReviewDocumentResponse(EHPReviewDocumentBase):
    review_id: int
    added_at: datetime

    class Config:
        from_attributes = True

class EHPReviewBase(BaseModel):
    project_id: int
    reviewer_id: int
    review_type: EHPReviewType
    notes: Optional[str] = None

class EHPReviewCreate(EHPReviewBase):
    pass

class EHPReviewUpdate(BaseModel):
    status: EHPReviewStatus
    environmental_impacts: Optional[List[Dict]] = None
    historical_considerations: Optional[List[Dict]] = None
    required_permits: Optional[List[Dict]] = None
    mitigation_measures: Optional[List[Dict]] = None

class EHPReviewResponse(EHPReviewBase):
    id: int
    status: EHPReviewStatus
    environmental_impacts: List[Dict] = Field(default_factory=list)
    historical_considerations: List[Dict] = Field(default_factory=list)
    required_permits: List[Dict] = Field(default_factory=list)
    mitigation_measures: List[Dict] = Field(default_factory=list)
    review_date: datetime
    completed_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    documents: List[EHPReviewDocumentResponse] = Field(default_factory=list)

    class Config:
        from_attributes = True

class EHPReviewList(BaseModel):
    total: int
    items: List[EHPReviewResponse]

    class Config:
        from_attributes = True

class EnvironmentalImpact(BaseModel):
    category: str
    description: str
    severity: str
    mitigation_required: bool
    notes: Optional[str] = None

class HistoricalConsideration(BaseModel):
    resource_type: str
    description: str
    significance: str
    preservation_required: bool
    notes: Optional[str] = None

class RequiredPermit(BaseModel):
    permit_type: str
    issuing_agency: str
    description: str
    deadline: Optional[datetime] = None
    status: str
    notes: Optional[str] = None

class MitigationMeasure(BaseModel):
    category: str
    description: str
    cost_estimate: float
    timeline: str
    responsible_party: str
    status: str
    notes: Optional[str] = None
