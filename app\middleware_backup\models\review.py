from datetime import datetime
from typing import Optional, List, Dict
from sqlalchemy import String, Integer, DateTime, JSON, ForeignKey, Enum, Text, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from app.models.base import Base
import enum

class ReviewStatus(str, enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REJECTED = "rejected"

class ReviewType(str, enum.Enum):
    MITIGATION = "mitigation"
    EHP = "ehp"
    COST = "cost"
    COMPLIANCE = "compliance"

class BaseReview(Base):
    __tablename__ = "reviews"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    type: Mapped[ReviewType] = mapped_column(Enum(ReviewType))
    status: Mapped[ReviewStatus] = mapped_column(Enum(ReviewStatus), default=ReviewStatus.PENDING)
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.id"))
    reviewer_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    findings: Mapped[Dict] = mapped_column(JSON)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, onupdate=func.now())

    project: Mapped["Project"] = relationship("Project", back_populates="compliance_reviews")
    reviewer: Mapped["User"] = relationship("User", back_populates="compliance_reviews")
    history: Mapped[List["ReviewHistory"]] = relationship("ReviewHistory", back_populates="review")

    __mapper_args__ = {
        "polymorphic_identity": "base",
        "polymorphic_on": type,
    }

class MitigationReview(BaseReview):
    __tablename__ = "mitigation_reviews"
    
    id: Mapped[int] = mapped_column(ForeignKey("reviews.id"), primary_key=True)
    cost_estimate: Mapped[float] = mapped_column(Float)
    recommendations: Mapped[List[str]] = mapped_column(JSON)
    implementation_timeline: Mapped[int] = mapped_column(Integer)  # days

    __mapper_args__ = {
        "polymorphic_identity": ReviewType.MITIGATION,
    }

class EHPReview(BaseReview):
    __tablename__ = "ehp_reviews"
    
    id: Mapped[int] = mapped_column(ForeignKey("reviews.id"), primary_key=True)
    environmental_impacts: Mapped[List[Dict]] = mapped_column(JSON)
    historical_considerations: Mapped[List[Dict]] = mapped_column(JSON)
    required_permits: Mapped[List[Dict]] = mapped_column(JSON)
    mitigation_measures: Mapped[List[Dict]] = mapped_column(JSON)

    __mapper_args__ = {
        "polymorphic_identity": ReviewType.EHP,
    }

class CostReview(BaseReview):
    __tablename__ = "cost_reviews"
    
    id: Mapped[int] = mapped_column(ForeignKey("reviews.id"), primary_key=True)
    total_cost: Mapped[float] = mapped_column(Float)
    cost_breakdown: Mapped[Dict] = mapped_column(JSON)
    alternatives: Mapped[List[Dict]] = mapped_column(JSON)

    __mapper_args__ = {
        "polymorphic_identity": ReviewType.COST,
    }

class ComplianceReview(BaseReview):
    __tablename__ = "compliance_reviews"
    
    id: Mapped[int] = mapped_column(ForeignKey("reviews.id"), primary_key=True)
    policy_version_id: Mapped[int] = mapped_column(ForeignKey("policy_versions.id"))
    compliance_score: Mapped[float] = mapped_column(Float)
    violations: Mapped[List[Dict]] = mapped_column(JSON)
    remediation_plan: Mapped[Dict] = mapped_column(JSON)

    policy_version: Mapped["PolicyVersion"] = relationship("PolicyVersion", back_populates="compliance_reviews")

    __mapper_args__ = {
        "polymorphic_identity": ReviewType.COMPLIANCE,
    }
