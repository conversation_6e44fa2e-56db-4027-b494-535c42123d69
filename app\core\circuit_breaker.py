"""
Circuit breaker implementation for ComplianceMax.
"""

import time
import asyncio
from enum import Enum
from typing import Any, Callable, Optional, Dict
from functools import wraps
from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import MetricsCollector

logger = get_logger(__name__)

class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"  # Normal operation
    OPEN = "open"     # Service unavailable
    HALF_OPEN = "half_open"  # Testing if service is back

class CircuitBreaker:
    """Circuit breaker implementation."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 30,
        half_open_timeout: int = 5,
        name: str = "default"
    ):
        """Initialize circuit breaker."""
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_timeout = half_open_timeout
        self.name = name
        
        self.state = CircuitState.CLOSED
        self.failures = 0
        self.last_failure_time = 0
        self.half_open_start = 0
        self._lock = asyncio.Lock()
    
    async def _update_state(self, new_state: CircuitState) -> None:
        """Update circuit state with metrics tracking."""
        async with self._lock:
            old_state = self.state
            self.state = new_state
            
            if old_state != new_state:
                logger.info(f"Circuit {self.name} state changed from {old_state.value} to {new_state.value}")
                MetricsCollector.track_circuit_breaker_state(
                    circuit_name=self.name,
                    old_state=old_state.value,
                    new_state=new_state.value
                )
    
    async def _handle_success(self) -> None:
        """Handle successful operation."""
        async with self._lock:
            if self.state == CircuitState.HALF_OPEN:
                await self._update_state(CircuitState.CLOSED)
            self.failures = 0
            self.last_failure_time = 0
    
    async def _handle_failure(self) -> None:
        """Handle failed operation."""
        async with self._lock:
            self.failures += 1
            self.last_failure_time = time.time()
            
            if self.failures >= self.failure_threshold:
                await self._update_state(CircuitState.OPEN)
                MetricsCollector.track_circuit_breaker_trip(self.name)
    
    async def _check_state(self) -> bool:
        """Check if operation should be allowed."""
        current_time = time.time()
        
        if self.state == CircuitState.OPEN:
            if current_time - self.last_failure_time >= self.recovery_timeout:
                await self._update_state(CircuitState.HALF_OPEN)
                self.half_open_start = current_time
                return True
            return False
            
        elif self.state == CircuitState.HALF_OPEN:
            if current_time - self.half_open_start >= self.half_open_timeout:
                await self._update_state(CircuitState.OPEN)
                return False
            return True
            
        return True
    
    def __call__(self, func: Callable) -> Callable:
        """Decorator for circuit breaker pattern."""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not await self._check_state():
                logger.warning(f"Circuit {self.name} is {self.state.value}, request rejected")
                raise CircuitBreakerError(f"Circuit {self.name} is {self.state.value}")
            
            start_time = time.time()
            success = True
            
            try:
                result = await func(*args, **kwargs)
                await self._handle_success()
                return result
            except Exception as e:
                success = False
                await self._handle_failure()
                raise
            finally:
                duration = time.time() - start_time
                MetricsCollector.track_circuit_breaker_call(
                    circuit_name=self.name,
                    duration=duration,
                    success=success
                )
        
        return wrapper

class CircuitBreakerError(Exception):
    """Exception raised when circuit is open."""
    pass

# Redis circuit breaker instance
redis_circuit = CircuitBreaker(
    failure_threshold=settings.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
    recovery_timeout=settings.CIRCUIT_BREAKER_RECOVERY_TIMEOUT,
    name="redis"
)

class CircuitBreakerRegistry:
    """Registry for managing multiple circuit breakers."""
    
    _circuits: Dict[str, CircuitBreaker] = {}
    
    @classmethod
    def register(cls, name: str, circuit: CircuitBreaker) -> None:
        """Register a circuit breaker."""
        cls._circuits[name] = circuit
    
    @classmethod
    def get(cls, name: str) -> Optional[CircuitBreaker]:
        """Get a circuit breaker by name."""
        return cls._circuits.get(name)
    
    @classmethod
    def get_all_states(cls) -> Dict[str, str]:
        """Get states of all circuit breakers."""
        return {
            name: circuit.state.value
            for name, circuit in cls._circuits.items()
        }

# Register Redis circuit breaker
CircuitBreakerRegistry.register("redis", redis_circuit) 