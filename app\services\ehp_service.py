from datetime import datetime
from typing import List, Optional, Dict
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from fastapi import HTTPException
from app.models.ehp import EHPReview, EHPReviewStatus, EHPReviewType
from app.models.project import Project
from app.models.document import Document
import logging

logger = logging.getLogger(__name__)

class EHPService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_review(
        self,
        project_id: int,
        reviewer_id: int,
        review_type: EHPReviewType,
        notes: Optional[str] = None
    ) -> EHPReview:
        """Create a new EHP review"""
        try:
            # Verify project exists
            project = await self.db.get(Project, project_id)
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            review = EHPReview(
                project_id=project_id,
                reviewer_id=reviewer_id,
                review_type=review_type,
                notes=notes
            )
            self.db.add(review)
            await self.db.commit()
            await self.db.refresh(review)
            return review

        except Exception as e:
            logger.error(f"Error creating EHP review: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def get_review(self, review_id: int) -> EHPReview:
        """Get an EHP review by ID"""
        stmt = select(EHPReview).where(
            EHPReview.id == review_id
        ).options(
            joinedload(EHPReview.project),
            joinedload(EHPReview.reviewer),
            joinedload(EHPReview.documents)
        )
        result = await self.db.execute(stmt)
        review = result.unique().scalar_one_or_none()
        if not review:
            raise HTTPException(status_code=404, detail="Review not found")
        return review

    async def update_status(
        self,
        review_id: int,
        status: EHPReviewStatus,
        environmental_impacts: Optional[List[Dict]] = None,
        historical_considerations: Optional[List[Dict]] = None,
        required_permits: Optional[List[Dict]] = None,
        mitigation_measures: Optional[List[Dict]] = None
    ) -> EHPReview:
        """Update the status and findings of an EHP review"""
        review = await self.get_review(review_id)
        
        review.status = status
        if environmental_impacts is not None:
            review.environmental_impacts = environmental_impacts
        if historical_considerations is not None:
            review.historical_considerations = historical_considerations
        if required_permits is not None:
            review.required_permits = required_permits
        if mitigation_measures is not None:
            review.mitigation_measures = mitigation_measures
            
        if status in [EHPReviewStatus.APPROVED, EHPReviewStatus.DENIED]:
            review.completed_date = datetime.utcnow()
            
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def add_document(
        self,
        review_id: int,
        document_id: int,
        document_type: str
    ) -> EHPReview:
        """Add a document to an EHP review"""
        review = await self.get_review(review_id)
        document = await self.db.get(Document, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
            
        review.documents.append(document)
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def remove_document(
        self,
        review_id: int,
        document_id: int
    ) -> EHPReview:
        """Remove a document from an EHP review"""
        review = await self.get_review(review_id)
        document = await self.db.get(Document, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
            
        review.documents.remove(document)
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def get_project_reviews(
        self,
        project_id: int,
        status: Optional[EHPReviewStatus] = None
    ) -> List[EHPReview]:
        """Get all EHP reviews for a project"""
        stmt = select(EHPReview).where(
            EHPReview.project_id == project_id
        )
        if status:
            stmt = stmt.where(EHPReview.status == status)
            
        stmt = stmt.options(
            joinedload(EHPReview.reviewer),
            joinedload(EHPReview.documents)
        )
        
        result = await self.db.execute(stmt)
        reviews = result.unique().scalars().all()
        return list(reviews)

    async def get_pending_reviews(self) -> List[EHPReview]:
        """Get all pending EHP reviews"""
        stmt = select(EHPReview).where(
            EHPReview.status.in_([
                EHPReviewStatus.PENDING,
                EHPReviewStatus.IN_PROGRESS,
                EHPReviewStatus.NEEDS_INFO
            ])
        ).options(
            joinedload(EHPReview.project),
            joinedload(EHPReview.reviewer)
        )
        
        result = await self.db.execute(stmt)
        reviews = result.unique().scalars().all()
        return list(reviews)
