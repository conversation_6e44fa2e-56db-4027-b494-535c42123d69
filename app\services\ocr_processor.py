from typing import List, Dict, Optional
import pytesseract
import easyocr
from paddleocr import PaddleOC<PERSON>
from PIL import Image
import numpy as np
from pdf2image import convert_from_path
import cv2
from app.core.config import settings
import logging
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from datetime import datetime
import re

logger = logging.getLogger(__name__)

@dataclass
class OCRResult:
    text: str
    confidence: float
    bbox: Optional[List[float]] = None
    page_number: int = 0
    engine: str = ""

class EnhancedOCRProcessor:
    def __init__(self):
        self.config = settings.OCR_SETTINGS
        # Initialize OCR engines
        self.engines = {
            "easyocr": self._init_easyocr(),
            "tesseract": self._init_tesseract(),
            "paddleocr": self._init_paddleocr() if self.config["enable_layout_analysis"] else None
        }
        
    def _init_easyocr(self):
        return easyocr.Reader(['en'])
    
    def _init_tesseract(self):
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        return pytesseract
    
    def _init_paddleocr(self):
        return PaddleOCR(use_angle_cls=True, lang='en')

    async def process_document(self, file_path: str) -> Dict:
        """Process document with multiple OCR engines and advanced analysis."""
        try:
            # Convert PDF to images
            images = convert_from_path(file_path)
            results = []
            
            for page_num, image in enumerate(images, 1):
                page_result = await self._process_page(image, page_num)
                results.append(page_result)
            
            # Aggregate and analyze results
            final_result = self._aggregate_results(results)
            
            # Enhance with layout analysis if enabled
            if self.config["enable_layout_analysis"]:
                final_result = await self._enhance_with_layout(final_result, images)
            
            return final_result
            
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {str(e)}")
            raise

    async def _process_page(self, image: Image, page_num: int) -> Dict:
        """Process a single page with multiple OCR engines."""
        # Convert PIL Image to numpy array for OpenCV
        image_np = np.array(image)
        
        results = {
            "text": [],
            "tables": [],
            "forms": [],
            "metadata": {
                "page_number": page_num,
                "processed_date": datetime.utcnow().isoformat()
            }
        }
        
        # Process with primary engine
        primary_results = await self._run_primary_ocr(image_np)
        results["text"].extend(primary_results)
        
        # Detect and process tables if enabled
        if self.config["enable_table_detection"]:
            tables = await self._detect_tables(image_np)
            results["tables"].extend(tables)
        
        # Detect and process forms if enabled
        if self.config["enable_form_detection"]:
            forms = await self._detect_forms(image_np)
            results["forms"].extend(forms)
        
        return results

    async def _run_primary_ocr(self, image_np: np.ndarray) -> List[OCRResult]:
        """Run OCR with primary engine and fallback if needed."""
        primary_engine = self.config["primary_engine"]
        results = []
        
        try:
            if primary_engine == "easyocr":
                raw_results = self.engines["easyocr"].readtext(image_np)
                for bbox, text, conf in raw_results:
                    results.append(OCRResult(text=text, confidence=conf, bbox=bbox, engine="easyocr"))
            
            elif primary_engine == "tesseract":
                text = self.engines["tesseract"].image_to_string(image_np)
                results.append(OCRResult(text=text, confidence=1.0, engine="tesseract"))
                
        except Exception as e:
            logger.error(f"Primary OCR engine failed: {str(e)}")
            # Fallback to secondary engine
            fallback_engine = self.config["fallback_engine"]
            if fallback_engine and fallback_engine != primary_engine:
                results = await self._run_fallback_ocr(image_np, fallback_engine)
        
        return results

    async def _detect_tables(self, image_np: np.ndarray) -> List[Dict]:
        """Detect and extract tables from the image."""
        tables = []
        try:
            # Use contour detection to find table-like structures
            gray = cv2.cvtColor(image_np, cv2.COLOR_BGR2GRAY)
            thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                # Filter for rectangular shapes
                if len(cv2.approxPolyDP(contour, 0.01 * cv2.arcLength(contour, True), True)) == 4:
                    x, y, w, h = cv2.boundingRect(contour)
                    if w > 100 and h > 100:  # Filter small rectangles
                        table_region = image_np[y:y+h, x:x+w]
                        table_text = await self._extract_table_content(table_region)
                        tables.append({
                            "bbox": [x, y, w, h],
                            "content": table_text,
                            "confidence": self.config["table_confidence"]
                        })
        
        except Exception as e:
            logger.error(f"Table detection failed: {str(e)}")
        
        return tables

    async def _detect_forms(self, image_np: np.ndarray) -> List[Dict]:
        """Detect and extract form fields from the image."""
        forms = []
        try:
            # Use PaddleOCR for form field detection
            if self.engines["paddleocr"]:
                result = self.engines["paddleocr"].ocr(image_np)
                
                for line in result:
                    for word_info in line:
                        bbox, (text, confidence) = word_info
                        # Check if text looks like a form field
                        if self._is_form_field(text):
                            forms.append({
                                "field_name": text,
                                "bbox": bbox,
                                "confidence": confidence,
                                "type": self._detect_field_type(text)
                            })
        
        except Exception as e:
            logger.error(f"Form detection failed: {str(e)}")
        
        return forms

    def _is_form_field(self, text: str) -> bool:
        """Determine if text represents a form field."""
        # Common form field patterns
        field_patterns = [
            r"^\s*[A-Za-z ]+:+\s*$",  # Label followed by colon
            r"^\[\s*\]",  # Checkbox
            r"^□|☐|▢",    # Square box
            r"^○|⃝|◯"     # Circle
        ]
        return any(re.match(pattern, text) for pattern in field_patterns)

    def _detect_field_type(self, text: str) -> str:
        """Detect the type of form field."""
        if re.match(r"^\[\s*\]|□|☐|▢", text):
            return "checkbox"
        elif re.match(r"^○|⃝|◯", text):
            return "radio"
        elif re.search(r"date|birth|expiration", text.lower()):
            return "date"
        elif re.search(r"email|e-mail", text.lower()):
            return "email"
        elif re.search(r"phone|tel|fax", text.lower()):
            return "phone"
        else:
            return "text"

    def _aggregate_results(self, page_results: List[Dict]) -> Dict:
        """Aggregate and clean up results from all pages."""
        return {
            "pages": page_results,
            "metadata": {
                "total_pages": len(page_results),
                "processed_date": datetime.utcnow().isoformat(),
                "ocr_engine": self.config["primary_engine"],
                "confidence_scores": self._calculate_confidence_scores(page_results)
            }
        }

    def _calculate_confidence_scores(self, page_results: List[Dict]) -> Dict:
        """Calculate confidence scores for different components."""
        scores = {
            "text": 0.0,
            "tables": 0.0,
            "forms": 0.0
        }
        
        for page in page_results:
            if page["text"]:
                scores["text"] += sum(r.confidence for r in page["text"]) / len(page["text"])
            if page["tables"]:
                scores["tables"] += sum(t["confidence"] for t in page["tables"]) / len(page["tables"])
            if page["forms"]:
                scores["forms"] += sum(f["confidence"] for f in page["forms"]) / len(page["forms"])
        
        total_pages = len(page_results)
        return {k: v/total_pages for k, v in scores.items() if v > 0}

    async def _enhance_with_layout(self, results: Dict, images: List[Image]) -> Dict:
        """Enhance results with layout analysis."""
        # Use PaddleOCR for layout analysis
        if self.engines["paddleocr"]:
            for page_num, image in enumerate(images, 1):
                result = self.engines["paddleocr"].ocr(image)
                # Update results with layout information
                results["pages"][page_num-1]["layout"] = result
        
        return results

    async def _extract_table_content(self, table_region: np.ndarray) -> str:
        """Extract content from a table region."""
        # Use Tesseract for table content extraction
        return self.engines["tesseract"].image_to_string(table_region)

    async def _run_fallback_ocr(self, image_np: np.ndarray, fallback_engine: str) -> List[OCRResult]:
        """Run OCR with fallback engine."""
        results = []
        
        try:
            if fallback_engine == "easyocr":
                raw_results = self.engines["easyocr"].readtext(image_np)
                for bbox, text, conf in raw_results:
                    results.append(OCRResult(text=text, confidence=conf, bbox=bbox, engine="easyocr"))
            
            elif fallback_engine == "tesseract":
                text = self.engines["tesseract"].image_to_string(image_np)
                results.append(OCRResult(text=text, confidence=1.0, engine="tesseract"))
                
        except Exception as e:
            logger.error(f"Fallback OCR engine failed: {str(e)}")
        
        return results
