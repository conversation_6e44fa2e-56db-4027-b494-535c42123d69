from typing import List, Dict
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, BackgroundTasks, Response
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from app.core.deps import get_current_active_user, get_db
from app.models.compliance import FEMAApplication, ComplianceDocument, PolicyRequirement, ApplicationStatus, ComplianceStatus
from app.models.user import User
from app.schemas.compliance import (
    ApplicationCreate,
    ApplicationUpdate,
    ApplicationResponse,
    PolicyCreate,
    PolicyUpdate,
    PolicyResponse,
    DocumentCreate,
    DocumentResponse,
    DocumentUpdate
)
from datetime import datetime
import logging
import uuid
import os
import aiofiles
import hashlib
import shutil
from app.core.config import settings
import magic  # for file type validation

logger = logging.getLogger(__name__)

# Constants for file validation
ALLOWED_MIME_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png',
    'image/tiff'
]
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

router = APIRouter()

async def calculate_file_checksum(file_path: str) -> str:
    """Calculate SHA-256 checksum of a file."""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

async def archive_document(document: ComplianceDocument):
    """Archive a document by moving it to an archive directory."""
    try:
        archive_dir = os.path.join(settings.UPLOAD_DIR, "archived_documents")
        os.makedirs(archive_dir, exist_ok=True)
        
        # Create archive filename with version
        archive_filename = f"{document.id}_v{document.version}{os.path.splitext(document.original_filename)[1]}"
        archive_path = os.path.join(archive_dir, archive_filename)
        
        # Move file to archive
        shutil.move(document.file_path, archive_path)
        
        # Update document record
        document.archive_path = archive_path
        document.archive_date = datetime.utcnow()
        document.is_archived = True
        document.file_path = archive_path
        
        return True
    except Exception as e:
        logger.error(f"Error archiving document {document.id}: {str(e)}")
        return False

@router.post("/applications", response_model=ApplicationResponse)
async def create_application(
    application_in: ApplicationCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> FEMAApplication:
    """Create a new FEMA application."""
    try:
        application = FEMAApplication(
            id=str(uuid.uuid4()),
            **application_in.model_dump(),
            status=ApplicationStatus.PENDING,
            submission_date=datetime.utcnow(),
            last_updated=datetime.utcnow(),
            compliance_status=ComplianceStatus.UNDER_REVIEW,
            user_id=current_user.id
        )
        db.add(application)
        db.commit()
        db.refresh(application)
        return application
    except Exception as e:
        logger.error(f"Error creating application: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Application creation failed: {str(e)}")

@router.get("/applications", response_model=List[ApplicationResponse])
async def list_applications(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    status: ApplicationStatus = None,
    compliance_status: ComplianceStatus = None
) -> List[FEMAApplication]:
    """List FEMA applications with optional status filters."""
    try:
        query = db.query(FEMAApplication).filter(FEMAApplication.user_id == current_user.id)
        if status:
            query = query.filter(FEMAApplication.status == status)
        if compliance_status:
            query = query.filter(FEMAApplication.compliance_status == compliance_status)
        
        return query.offset(skip).limit(limit).all()
    except Exception as e:
        logger.error(f"Error listing applications: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list applications: {str(e)}")

@router.put("/applications/{application_id}/status", response_model=ApplicationResponse)
async def update_application_status(
    application_id: str,
    update_data: ApplicationUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> FEMAApplication:
    """Update the status of a FEMA application."""
    try:
        # Find the application
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Update status fields
        if update_data.status:
            application.status = update_data.status
        if update_data.compliance_status:
            application.compliance_status = update_data.compliance_status
        
        application.last_updated = datetime.utcnow()
        db.commit()
        db.refresh(application)
        return application
    except Exception as e:
        logger.error(f"Error updating application status: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to update application status: {str(e)}")

@router.get("/applications/{application_id}", response_model=ApplicationResponse)
async def get_application(
    application_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> FEMAApplication:
    """Get a specific FEMA application."""
    try:
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        return application
    except Exception as e:
        logger.error(f"Error retrieving application: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve application: {str(e)}")

@router.delete("/applications/{application_id}")
async def delete_application(
    application_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a FEMA application."""
    try:
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        db.delete(application)
        db.commit()
        return {"message": "Application deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting application: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete application: {str(e)}")

@router.get("/applications/{application_id}/documents", response_model=List[DocumentResponse])
async def get_application_documents(
    application_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> List[ComplianceDocument]:
    """Get documents associated with a FEMA application."""
    try:
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Update last accessed time for all documents
        for doc in application.documents:
            doc.last_accessed = datetime.utcnow()
        db.commit()
        
        return application.documents
    except Exception as e:
        logger.error(f"Error retrieving application documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve application documents: {str(e)}")

@router.post("/applications/{application_id}/documents/{document_id}")
async def associate_document(
    application_id: str,
    document_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Associate a document with a FEMA application."""
    try:
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        document = db.query(ComplianceDocument).filter(
            ComplianceDocument.id == document_id
        ).first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document.application_id = application_id
        db.commit()
        return {"message": "Document associated successfully"}
    except Exception as e:
        logger.error(f"Error associating document: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to associate document: {str(e)}")

@router.post("/applications/{application_id}/documents", response_model=DocumentResponse)
async def create_document(
    application_id: str,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    document_type: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> ComplianceDocument:
    """Create a new document and associate it with a FEMA application."""
    try:
        # Find the application
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Validate file
        if not file:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Check file size
        file_size = 0
        content = await file.read()
        file_size = len(content)
        if file_size > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File size exceeds maximum limit of {MAX_FILE_SIZE/1024/1024}MB"
            )
        
        # Validate file type
        mime = magic.Magic(mime=True)
        file_type = mime.from_buffer(content)
        if file_type not in ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_type} not allowed. Allowed types: {', '.join(ALLOWED_MIME_TYPES)}"
            )
        
        # Reset file pointer
        await file.seek(0)
            
        # Create upload directory if it doesn't exist
        upload_dir = os.path.join(settings.UPLOAD_DIR, "compliance_documents")
        os.makedirs(upload_dir, exist_ok=True)
        
        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as out_file:
            await out_file.write(content)
        
        # Calculate checksum
        checksum = await calculate_file_checksum(file_path)
        
        # Create the document
        document = ComplianceDocument(
            id=str(uuid.uuid4()),
            application_id=application_id,
            document_type=document_type or "other",
            file_path=file_path,
            original_filename=file.filename,
            file_size=file_size,
            mime_type=file_type,
            upload_date=datetime.utcnow(),
            last_accessed=datetime.utcnow(),
            compliance_status=ComplianceStatus.UNDER_REVIEW,
            checksum=checksum
        )
        
        db.add(document)
        db.commit()
        db.refresh(document)
        
        # Schedule background task to verify file integrity
        background_tasks.add_task(verify_document_integrity, application_id, document.id)
        
        return document
    except Exception as e:
        logger.error(f"Error creating document: {str(e)}")
        db.rollback()
        # Clean up uploaded file if it exists
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except OSError:
                pass
        raise HTTPException(status_code=500, detail=f"Failed to create document: {str(e)}")

@router.put("/applications/{application_id}/documents/{document_id}/archive", response_model=DocumentResponse)
async def archive_document_endpoint(
    application_id: str,
    document_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> ComplianceDocument:
    """Archive a document."""
    try:
        # Find the application
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Find the document
        document = db.query(ComplianceDocument).filter(
            ComplianceDocument.id == document_id,
            ComplianceDocument.application_id == application_id
        ).first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        if document.is_archived:
            raise HTTPException(status_code=400, detail="Document is already archived")
        
        # Archive the document
        success = await archive_document(document)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to archive document")
        
        db.commit()
        db.refresh(document)
        
        return document
    except Exception as e:
        logger.error(f"Error archiving document: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to archive document: {str(e)}")

@router.put("/applications/{application_id}/documents/{document_id}/verify", response_model=DocumentResponse)
async def verify_document_integrity(
    application_id: str,
    document_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> ComplianceDocument:
    """Verify the integrity of a document using its checksum."""
    try:
        # Find the application
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Find the document
        document = db.query(ComplianceDocument).filter(
            ComplianceDocument.id == document_id,
            ComplianceDocument.application_id == application_id
        ).first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Calculate current checksum
        current_checksum = await calculate_file_checksum(document.file_path)
        
        # Compare with stored checksum
        if current_checksum != document.checksum:
            raise HTTPException(status_code=400, detail="Document integrity check failed")
        
        # Update last accessed time
        document.last_accessed = datetime.utcnow()
        db.commit()
        db.refresh(document)
        
        return document
    except Exception as e:
        logger.error(f"Error verifying document integrity: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to verify document integrity: {str(e)}")

@router.delete("/applications/{application_id}/documents/{document_id}")
async def delete_document(
    application_id: str,
    document_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a document from a FEMA application."""
    try:
        # Find the application
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Find the document
        document = db.query(ComplianceDocument).filter(
            ComplianceDocument.id == document_id,
            ComplianceDocument.application_id == application_id
        ).first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Delete the file
        if os.path.exists(document.file_path):
            try:
                os.remove(document.file_path)
            except OSError as e:
                logger.warning(f"Failed to delete file {document.file_path}: {str(e)}")
        
        # Delete the document record
        db.delete(document)
        db.commit()
        
        return {"message": "Document deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete document: {str(e)}")

@router.get("/applications/{application_id}/documents/{document_id}", response_model=DocumentResponse)
async def get_document(
    application_id: str,
    document_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> ComplianceDocument:
    """Get a specific document from a FEMA application."""
    try:
        # Find the application
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Find the document
        document = db.query(ComplianceDocument).filter(
            ComplianceDocument.id == document_id,
            ComplianceDocument.application_id == application_id
        ).first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return document
    except Exception as e:
        logger.error(f"Error retrieving document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve document: {str(e)}")

@router.put("/applications/{application_id}/documents/{document_id}", response_model=DocumentResponse)
async def update_document(
    application_id: str,
    document_id: str,
    document_update: DocumentUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> ComplianceDocument:
    """Update a document in a FEMA application."""
    try:
        # Find the application
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Find the document
        document = db.query(ComplianceDocument).filter(
            ComplianceDocument.id == document_id,
            ComplianceDocument.application_id == application_id
        ).first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Update the document
        update_data = document_update.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(document, key, value)
        
        db.commit()
        db.refresh(document)
        
        return document
    except Exception as e:
        logger.error(f"Error updating document: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to update document: {str(e)}")

@router.get("/policy-requirements", response_model=List[PolicyResponse])
async def list_policy_requirements(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100
) -> List[PolicyRequirement]:
    """List all policy requirements."""
    try:
        return db.query(PolicyRequirement).offset(skip).limit(limit).all()
    except Exception as e:
        logger.error(f"Error listing policy requirements: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list policy requirements: {str(e)}")

@router.post("/policy-requirements", response_model=PolicyResponse)
async def create_policy_requirement(
    policy_in: PolicyCreate,
    db: Session = Depends(get_db)
) -> PolicyRequirement:
    """Create a new policy requirement."""
    try:
        policy = PolicyRequirement(
            id=str(uuid.uuid4()),
            **policy_in.model_dump()
        )
        db.add(policy)
        db.commit()
        db.refresh(policy)
        return policy
    except Exception as e:
        logger.error(f"Error creating policy requirement: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create policy requirement: {str(e)}")

@router.get("/policy-requirements/{policy_id}", response_model=PolicyResponse)
async def get_policy_requirement(
    policy_id: str,
    db: Session = Depends(get_db)
) -> PolicyRequirement:
    """Get a specific policy requirement."""
    try:
        policy = db.query(PolicyRequirement).filter(
            PolicyRequirement.id == policy_id
        ).first()
        
        if not policy:
            raise HTTPException(status_code=404, detail="Policy requirement not found")
        
        return policy
    except Exception as e:
        logger.error(f"Error retrieving policy requirement: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve policy requirement: {str(e)}")

@router.put("/policy-requirements/{policy_id}", response_model=PolicyResponse)
async def update_policy_requirement(
    policy_id: str,
    policy_in: PolicyUpdate,
    db: Session = Depends(get_db)
) -> PolicyRequirement:
    """Update a policy requirement."""
    try:
        policy = db.query(PolicyRequirement).filter(
            PolicyRequirement.id == policy_id
        ).first()
        
        if not policy:
            raise HTTPException(status_code=404, detail="Policy requirement not found")
        
        # Update fields
        for key, value in policy_in.model_dump(exclude_unset=True).items():
            setattr(policy, key, value)
        
        policy.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(policy)
        
        return policy
    except Exception as e:
        logger.error(f"Error updating policy requirement: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to update policy requirement: {str(e)}")

@router.delete("/policy-requirements/{policy_id}")
async def delete_policy_requirement(
    policy_id: str,
    db: Session = Depends(get_db)
):
    """Delete a policy requirement."""
    try:
        policy = db.query(PolicyRequirement).filter(
            PolicyRequirement.id == policy_id
        ).first()
        
        if not policy:
            raise HTTPException(status_code=404, detail="Policy requirement not found")
        
        db.delete(policy)
        db.commit()
        
        return {"message": "Policy requirement deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting policy requirement: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete policy requirement: {str(e)}")

# Placeholder for xAI API integration (compliance chatbot)
@router.post("/chat")
async def chat_with_compliance_assistant(
    query: str,
    context: str = "",
    current_user: User = Depends(get_current_active_user)
) -> Dict:
    """Chat with a compliance assistant powered by xAI API (placeholder)."""
    # To be implemented after transitioning to the new machine
    return {
        "status": "pending",
        "message": "xAI API integration for compliance chatbot to be implemented after machine upgrade"
    }

@router.get("/applications/{application_id}/documents/{document_id}/download")
async def download_document(
    application_id: str,
    document_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Response:
    """Download a document from a FEMA application."""
    try:
        # Find the application
        application = db.query(FEMAApplication).filter(
            FEMAApplication.id == application_id,
            FEMAApplication.user_id == current_user.id
        ).first()
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Find the document
        document = db.query(ComplianceDocument).filter(
            ComplianceDocument.id == document_id,
            ComplianceDocument.application_id == application_id
        ).first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Check if file exists
        if not os.path.exists(document.file_path):
            raise HTTPException(status_code=404, detail="Document file not found")
        
        # Update last accessed time
        document.last_accessed = datetime.utcnow()
        db.commit()
        
        # Return file response
        return FileResponse(
            path=document.file_path,
            filename=document.original_filename,
            media_type=document.mime_type
        )
    except Exception as e:
        logger.error(f"Error downloading document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to download document: {str(e)}")
