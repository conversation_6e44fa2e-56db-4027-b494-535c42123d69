"""
Security middleware for ComplianceMax application.
"""
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from typing import Callable
import secrets
import uuid
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers to all responses."""
    
    async def dispatch(self, request, call_next):
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data:; "
            "font-src 'self'; "
            "connect-src 'self'"
        )
        response.headers["Content-Security-Policy"] = csp
        
        # Permissions Policy
        permissions = (
            "accelerometer=(), "
            "camera=(), "
            "geolocation=(), "
            "microphone=(), "
            "payment=(), "
            "usb=()"
        )
        response.headers["Permissions-Policy"] = permissions
        
        return response

def setup_security(app: FastAPI) -> None:
    """Configure security middleware and CORS."""
    
    # CORS configuration
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000"],  # Frontend URL
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Security headers
    app.add_middleware(SecurityHeadersMiddleware)
    
    # Generate and store CSRF token
    @app.middleware("http")
    async def csrf_token_middleware(request, call_next):
        response = await call_next(request)
        if "text/html" in response.headers.get("content-type", ""):
            token = secrets.token_urlsafe(32)
            response.set_cookie(
                "csrf_token",
                token,
                httponly=True,
                secure=True,
                samesite="strict"
            )
        return response

class CSRFMiddleware(BaseHTTPMiddleware):
    """CSRF protection middleware."""
    
    async def dispatch(self, request: Request, call_next) -> Response:
        if request.method in ["POST", "PUT", "DELETE", "PATCH"]:
            try:
                csrf_token = request.headers.get("X-CSRF-Token")
                session_token = request.session.get("csrf_token")
                
                if not csrf_token or not session_token or csrf_token != session_token:
                    logger.warning(f"CSRF validation failed for {request.url.path}")
                    return Response(
                        status_code=403,
                        content={"detail": "CSRF token missing or invalid"}
                    )
                    
            except Exception as e:
                logger.error(f"CSRF validation error: {str(e)}", exc_info=True)
                return Response(
                    status_code=500,
                    content={"detail": "Security validation failed"}
                )
        
        return await call_next(request)

class SessionSecurityMiddleware(BaseHTTPMiddleware):
    """Session security middleware."""
    
    async def dispatch(self, request: Request, call_next) -> Response:
        response = await call_next(request)
        
        try:
            # Secure session cookie settings
            if hasattr(response, "set_cookie"):
                response.set_cookie(
                    settings.SESSION_COOKIE_NAME,
                    response.cookies.get(settings.SESSION_COOKIE_NAME, ""),
                    httponly=True,
                    secure=not settings.DEBUG,  # True in production
                    samesite="Lax",
                    max_age=settings.SESSION_COOKIE_AGE
                )
        except Exception as e:
            logger.error(f"Session security error: {str(e)}", exc_info=True)
        
        return response

class SecurityAuditMiddleware(BaseHTTPMiddleware):
    """Security audit logging middleware."""
    
    async def dispatch(self, request: Request, call_next) -> Response:
        # Log security-relevant requests
        if self._is_security_relevant(request):
            logger.info(
                "Security audit",
                extra={
                    "path": request.url.path,
                    "method": request.method,
                    "client_ip": request.client.host if request.client else "unknown",
                    "user_agent": request.headers.get("user-agent"),
                    "referrer": request.headers.get("referer")
                }
            )
        
        return await call_next(request)
    
    def _is_security_relevant(self, request: Request) -> bool:
        """Determine if a request is security-relevant."""
        security_paths = [
            "/api/v1/auth",
            "/api/v1/users",
            "/admin",
            "/security"
        ]
        return any(path in request.url.path for path in security_paths) 