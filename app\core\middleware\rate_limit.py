"""
Rate limiting middleware for ComplianceMax.
"""

import time
from typing import Op<PERSON>, <PERSON><PERSON>, Dict
import redis
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.datastructures import MutableHeaders
from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import MetricsCollector

logger = get_logger(__name__)

class RateLimiter:
    """Redis-based rate limiter implementation."""
    
    def __init__(self) -> None:
        """Initialize Redis connection."""
        self.redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_DB,
            ssl=settings.REDIS_SSL,
            socket_timeout=settings.REDIS_TIMEOUT,
            decode_responses=True,
            health_check_interval=30  # Add health check
        )
        self.window = settings.RATE_LIMIT_WINDOW
        self.max_requests = settings.RATE_LIMIT_REQUESTS
    
    async def is_rate_limited(self, key: str) -> Tuple[bool, int, int]:
        """
        Check if request should be rate limited.
        
        Args:
            key: Rate limit key (e.g., IP address or user ID)
            
        Returns:
            Tuple of (is_limited, remaining_requests, retry_after)
        """
        try:
            pipe = self.redis.pipeline()
            now = int(time.time())
            window_key = f"{key}:{now // self.window}"
            
            # Get current count
            pipe.get(window_key)
            pipe.ttl(window_key)
            current_count, ttl = pipe.execute()
            
            # Convert count to int, default to 0 if None
            current_count = int(current_count) if current_count else 0
            
            # Check if limit exceeded
            if current_count >= self.max_requests:
                retry_after = ttl if ttl > 0 else self.window
                return True, 0, retry_after
            
            # Increment counter
            pipe.incr(window_key)
            if ttl < 0:
                pipe.expire(window_key, self.window)
            pipe.execute()
            
            remaining = self.max_requests - (current_count + 1)
            return False, remaining, 0
            
        except redis.RedisError as e:
            logger.error(f"Redis error in rate limiter: {str(e)}")
            MetricsCollector.track_error("rate_limiter", "redis_error")
            # Fail open if Redis is down
            return False, self.max_requests, 0

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware."""
    
    def __init__(self, app):
        """Initialize middleware."""
        super().__init__(app)
        self.limiter = RateLimiter()
        self.enabled = settings.RATE_LIMIT_ENABLED
    
    def get_key(self, request: Request) -> str:
        """Get rate limit key based on configuration."""
        if settings.RATE_LIMIT_BY == "ip":
            return str(request.client.host)
        elif settings.RATE_LIMIT_BY == "user":
            # Get user ID from request state (set by auth middleware)
            return str(getattr(request.state, "user_id", request.client.host))
        elif settings.RATE_LIMIT_BY == "token":
            # Get token from authorization header
            auth = request.headers.get("Authorization", "")
            return auth.split(" ")[-1] if auth else str(request.client.host)
        return str(request.client.host)
    
    async def dispatch(self, request: Request, call_next):
        """Process request with rate limiting."""
        if not self.enabled:
            return await call_next(request)
        
        # Check whitelist
        client_ip = request.client.host
        if client_ip in settings.RATE_LIMIT_WHITELIST:
            return await call_next(request)
        
        # Get rate limit key
        key = self.get_key(request)
        
        # Check rate limit
        is_limited, remaining, retry_after = await self.limiter.is_rate_limited(key)
        
        # Track metrics
        MetricsCollector.active_requests.inc()
        
        try:
            if is_limited:
                logger.warning(f"Rate limit exceeded for {key}")
                raise HTTPException(
                    status_code=429,
                    detail={
                        "message": "Too many requests",
                        "retry_after": retry_after
                    }
                )
            
            # Process request
            response = await call_next(request)
            
            # Add rate limit headers if enabled
            if settings.RATE_LIMIT_HEADERS:
                response.headers["X-RateLimit-Limit"] = str(settings.RATE_LIMIT_REQUESTS)
                response.headers["X-RateLimit-Remaining"] = str(remaining)
                response.headers["X-RateLimit-Reset"] = str(int(time.time()) + retry_after)
            
            return response
            
        finally:
            MetricsCollector.active_requests.dec()
            
        return response 