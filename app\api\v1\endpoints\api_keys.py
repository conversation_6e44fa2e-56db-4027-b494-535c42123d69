"""
API Key management endpoints.
"""
from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from typing import List, Dict, Any
from datetime import datetime
from pydantic import BaseModel

from app.core.auth.api_key import API<PERSON>eyManager, API<PERSON>ey, get_api_key
from app.core.auth.permissions import requires_scope
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

class APIKeyCreate(BaseModel):
    """API Key creation request."""
    owner_id: str
    scopes: List[str]

class APIKeyResponse(BaseModel):
    """API Key response model."""
    key_id: str
    api_key: str
    expires_at: str

@router.post("/keys", response_model=APIKeyResponse)
@requires_scope("admin")
async def create_api_key(
    request: APIKeyCreate,
    current_key: APIKey = Depends(get_api_key)
) -> Dict[str, str]:
    """Create a new API key."""
    try:
        key_manager = APIKeyManager()
        key_data = key_manager.generate_key(
            owner_id=request.owner_id,
            scopes=request.scopes
        )
        logger.info(
            f"API key created",
            extra={
                "owner_id": request.owner_id,
                "key_id": key_data["key_id"]
            }
        )
        return key_data
    except Exception as e:
        logger.error(f"API key creation failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Failed to create API key"
        )

@router.post("/keys/{key_id}/rotate", response_model=APIKeyResponse)
@requires_scope("admin")
async def rotate_api_key(
    key_id: str,
    current_key: APIKey = Depends(get_api_key)
) -> Dict[str, str]:
    """Rotate an existing API key."""
    try:
        key_manager = APIKeyManager()
        new_key = key_manager.rotate_key(key_id)
        
        if not new_key:
            raise HTTPException(
                status_code=404,
                detail="API key not found"
            )
        
        logger.info(
            f"API key rotated",
            extra={
                "old_key_id": key_id,
                "new_key_id": new_key["key_id"]
            }
        )
        return new_key
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API key rotation failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Failed to rotate API key"
        )

@router.delete("/keys/{key_id}")
@requires_scope("admin")
async def revoke_api_key(
    key_id: str,
    current_key: APIKey = Depends(get_api_key)
) -> Dict[str, str]:
    """Revoke an API key."""
    try:
        key_manager = APIKeyManager()
        success = key_manager.revoke_key(key_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="API key not found"
            )
        
        logger.info(
            f"API key revoked",
            extra={"key_id": key_id}
        )
        return {"status": "success", "message": "API key revoked"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API key revocation failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Failed to revoke API key"
        )

@router.get("/keys/status/{key_id}")
@requires_scope("admin")
async def get_key_status(
    key_id: str,
    current_key: APIKey = Depends(get_api_key)
) -> Dict[str, Any]:
    """Get API key status."""
    try:
        key_manager = APIKeyManager()
        key_data = key_manager.redis_client.hgetall(f"api_key:{key_id}")
        
        if not key_data:
            raise HTTPException(
                status_code=404,
                detail="API key not found"
            )
        
        key = APIKey(**key_data)
        return {
            "key_id": key.key_id,
            "owner_id": key.owner_id,
            "created_at": key.created_at.isoformat(),
            "expires_at": key.expires_at.isoformat(),
            "is_active": key.is_active,
            "rotation_due": key.rotation_due.isoformat(),
            "scopes": key.scopes
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API key status check failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Failed to get API key status"
        ) 