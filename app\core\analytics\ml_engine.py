"""
Machine learning engine for advanced compliance analytics.
"""
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import asyncio
import json
import logging
import numpy as np
from dataclasses import dataclass
from enum import Enum
import hashlib
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
from queue import Queue

from app.core.monitoring.metrics import track_ml_prediction
from app.core.compliance_analyzer import ComplianceCheck, ComplianceStatus

logger = logging.getLogger(__name__)

class PredictionType(Enum):
    """Types of ML predictions."""
    RISK_SCORE = "risk_score"
    COMPLIANCE_FORECAST = "compliance_forecast"
    RESOURCE_OPTIMIZATION = "resource_optimization"
    ANOMALY_DETECTION = "anomaly_detection"

@dataclass
class MLPrediction:
    """Machine learning prediction result."""
    prediction_type: PredictionType
    timestamp: datetime
    target_id: str
    score: float
    confidence: float
    features: Dict
    explanation: List[str]

@dataclass
class ModelVersion:
    """Model version metadata."""
    version_id: str
    model_type: str
    created_at: datetime
    features: Dict[str, Any]
    metrics: Dict[str, float]
    parameters: Dict[str, Any]
    hash: str

@dataclass
class WorkerTask:
    """Task for worker processing."""
    task_id: str
    task_type: str
    input_data: Dict[str, Any]
    model_version: str
    priority: int = 1

class MLEngine:
    """Machine learning engine for advanced analytics."""
    
    def __init__(self):
        self.prediction_history = []
        self.model_cache = {}
        self.feature_importance = {}
        self.model_versions = {}
        self.active_versions = {}
        self.task_queue = Queue()
        self.worker_pool = ThreadPoolExecutor(
            max_workers=multiprocessing.cpu_count()
        )
        self.task_results = {}
        
        # Start background tasks
        self.training_task = asyncio.create_task(self._periodic_training())
        self.cleanup_task = asyncio.create_task(self._periodic_cleanup())
        self.worker_task = asyncio.create_task(self._process_worker_queue())
    
    async def predict_risk(self, document_id: str, compliance_history: List[ComplianceCheck]) -> MLPrediction:
        """Predict compliance risk score."""
        # Extract features
        features = await self._extract_features(document_id, compliance_history)
        
        # Load risk prediction model
        model = await self._get_model('risk_prediction')
        
        # Make prediction
        score, confidence = await self._predict(model, features)
        
        # Generate explanation
        explanation = self._generate_explanation(model, features, score)
        
        # Create prediction result
        prediction = MLPrediction(
            prediction_type=PredictionType.RISK_SCORE,
            timestamp=datetime.utcnow(),
            target_id=document_id,
            score=score,
            confidence=confidence,
            features=features,
            explanation=explanation
        )
        
        # Store prediction history
        self.prediction_history.append(prediction)
        
        # Track metrics
        track_ml_prediction('risk_score', score, confidence)
        
        return prediction
    
    async def forecast_compliance(self, policy_id: str, time_horizon: int) -> MLPrediction:
        """Forecast future compliance status."""
        # Extract time series features
        features = await self._extract_time_series_features(policy_id)
        
        # Load forecasting model
        model = await self._get_model('compliance_forecast')
        
        # Make prediction
        score, confidence = await self._forecast(model, features, time_horizon)
        
        # Generate explanation
        explanation = self._generate_forecast_explanation(model, features, score)
        
        # Create prediction result
        prediction = MLPrediction(
            prediction_type=PredictionType.COMPLIANCE_FORECAST,
            timestamp=datetime.utcnow(),
            target_id=policy_id,
            score=score,
            confidence=confidence,
            features=features,
            explanation=explanation
        )
        
        # Store prediction history
        self.prediction_history.append(prediction)
        
        # Track metrics
        track_ml_prediction('compliance_forecast', score, confidence)
        
        return prediction
    
    async def detect_anomalies(self, compliance_checks: List[ComplianceCheck]) -> List[MLPrediction]:
        """Detect anomalies in compliance patterns."""
        # Extract features for anomaly detection
        features = await self._extract_anomaly_features(compliance_checks)
        
        # Load anomaly detection model
        model = await self._get_model('anomaly_detection')
        
        # Detect anomalies
        anomalies = []
        for feature_set in features:
            score, confidence = await self._detect_anomaly(model, feature_set)
            if score > 0.8:  # Anomaly threshold
                explanation = self._generate_anomaly_explanation(model, feature_set)
                
                prediction = MLPrediction(
                    prediction_type=PredictionType.ANOMALY_DETECTION,
                    timestamp=datetime.utcnow(),
                    target_id=feature_set['id'],
                    score=score,
                    confidence=confidence,
                    features=feature_set,
                    explanation=explanation
                )
                
                anomalies.append(prediction)
                
                # Track metrics
                track_ml_prediction('anomaly_detection', score, confidence)
        
        return anomalies
    
    async def optimize_resources(self, resource_usage: Dict) -> MLPrediction:
        """Optimize resource allocation."""
        # Extract optimization features
        features = await self._extract_optimization_features(resource_usage)
        
        # Load optimization model
        model = await self._get_model('resource_optimization')
        
        # Generate optimization recommendation
        score, confidence = await self._optimize(model, features)
        
        # Generate explanation
        explanation = self._generate_optimization_explanation(model, features, score)
        
        # Create prediction result
        prediction = MLPrediction(
            prediction_type=PredictionType.RESOURCE_OPTIMIZATION,
            timestamp=datetime.utcnow(),
            target_id='resource_allocation',
            score=score,
            confidence=confidence,
            features=features,
            explanation=explanation
        )
        
        # Store prediction history
        self.prediction_history.append(prediction)
        
        # Track metrics
        track_ml_prediction('resource_optimization', score, confidence)
        
        return prediction
    
    async def _get_model(self, model_type: str):
        """Get ML model, loading from cache if available."""
        if model_type in self.model_cache:
            return self.model_cache[model_type]
        
        # Load model from storage
        model = await self._load_model(model_type)
        self.model_cache[model_type] = model
        return model
    
    async def _periodic_training(self):
        """Periodically retrain ML models."""
        while True:
            try:
                # Retrain models with new data
                await self._retrain_models()
                
                # Update feature importance
                await self._update_feature_importance()
                
            except Exception as e:
                logger.error(f"Error in ML model training: {str(e)}")
            
            await asyncio.sleep(86400)  # Run daily
    
    async def _periodic_cleanup(self):
        """Periodically clean up old prediction history."""
        while True:
            try:
                # Keep last 10000 predictions
                if len(self.prediction_history) > 10000:
                    self.prediction_history = self.prediction_history[-10000:]
                
            except Exception as e:
                logger.error(f"Error in ML engine cleanup: {str(e)}")
            
            await asyncio.sleep(3600)  # Run hourly
    
    async def save_model_version(self, model_type: str, model: Any, metrics: Dict[str, float]) -> ModelVersion:
        """Save a new model version with metadata."""
        # Generate version ID
        timestamp = datetime.utcnow()
        version_id = f"{model_type}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        # Calculate model hash
        model_bytes = self._serialize_model(model)
        model_hash = hashlib.sha256(model_bytes).hexdigest()
        
        # Create version metadata
        version = ModelVersion(
            version_id=version_id,
            model_type=model_type,
            created_at=timestamp,
            features=model.feature_names if hasattr(model, 'feature_names') else {},
            metrics=metrics,
            parameters=model.get_params() if hasattr(model, 'get_params') else {},
            hash=model_hash
        )
        
        # Store version metadata
        self.model_versions[version_id] = version
        
        # Update active version if metrics are better
        if self._is_better_version(model_type, metrics):
            self.active_versions[model_type] = version_id
        
        return version
    
    def _is_better_version(self, model_type: str, metrics: Dict[str, float]) -> bool:
        """Check if new metrics are better than current active version."""
        if model_type not in self.active_versions:
            return True
            
        current_version = self.model_versions[self.active_versions[model_type]]
        
        # Compare key metrics (customize based on model type)
        if model_type == "risk_prediction":
            return metrics.get('auc_roc', 0) > current_version.metrics.get('auc_roc', 0)
        elif model_type == "anomaly_detection":
            return metrics.get('precision', 0) > current_version.metrics.get('precision', 0)
        
        return True
    
    def _serialize_model(self, model: Any) -> bytes:
        """Serialize model for hash calculation."""
        try:
            return json.dumps(model.get_params()).encode()
        except:
            return str(model.__dict__).encode()
    
    async def get_active_model(self, model_type: str) -> Tuple[Any, ModelVersion]:
        """Get the currently active model version."""
        if model_type not in self.active_versions:
            raise ValueError(f"No active model found for type: {model_type}")
            
        version_id = self.active_versions[model_type]
        version = self.model_versions[version_id]
        model = await self._load_model(model_type, version_id)
        
        return model, version

    async def list_model_versions(self, model_type: str) -> List[ModelVersion]:
        """List all versions for a model type."""
        return [
            version for version in self.model_versions.values()
            if version.model_type == model_type
        ]

    async def _process_worker_queue(self):
        """Process tasks in the worker queue."""
        while True:
            try:
                while not self.task_queue.empty():
                    task = self.task_queue.get()
                    future = self.worker_pool.submit(
                        self._execute_task,
                        task
                    )
                    future.add_done_callback(
                        lambda f: self._handle_task_result(task.task_id, f)
                    )
                
                await asyncio.sleep(0.1)  # Prevent busy waiting
                
            except Exception as e:
                logger.error(f"Error processing worker queue: {str(e)}")
                await asyncio.sleep(1)  # Back off on error
    
    def _execute_task(self, task: WorkerTask) -> Dict[str, Any]:
        """Execute a worker task."""
        try:
            if task.task_type == "prediction":
                return self._execute_prediction(task)
            elif task.task_type == "training":
                return self._execute_training(task)
            elif task.task_type == "feature_extraction":
                return self._execute_feature_extraction(task)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
                
        except Exception as e:
            logger.error(f"Error executing task {task.task_id}: {str(e)}")
            raise
    
    def _handle_task_result(self, task_id: str, future):
        """Handle completed task result."""
        try:
            result = future.result()
            self.task_results[task_id] = {
                "status": "completed",
                "result": result,
                "error": None
            }
        except Exception as e:
            self.task_results[task_id] = {
                "status": "failed",
                "result": None,
                "error": str(e)
            }
    
    async def submit_task(self, task: WorkerTask) -> str:
        """Submit a task for processing."""
        self.task_queue.put(task)
        return task.task_id
    
    async def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the result of a task."""
        return self.task_results.get(task_id)
    
    def _execute_prediction(self, task: WorkerTask) -> Dict[str, Any]:
        """Execute a prediction task."""
        # Load model
        model, version = self._get_cached_model(
            task.model_version
        )
        
        # Prepare features
        features = self._prepare_features(
            task.input_data
        )
        
        # Make prediction
        prediction = model.predict(features)
        
        return {
            "prediction": prediction.tolist(),
            "model_version": version.version_id,
            "features_used": list(features.keys())
        }
    
    def _execute_training(self, task: WorkerTask) -> Dict[str, Any]:
        """Execute a model training task."""
        # Prepare training data
        X, y = self._prepare_training_data(
            task.input_data
        )
        
        # Train model
        model = self._train_model(
            task.task_type,
            X,
            y,
            task.input_data.get("parameters", {})
        )
        
        # Evaluate model
        metrics = self._evaluate_model(
            model,
            X,
            y
        )
        
        # Save new version
        version = self.save_model_version(
            task.task_type,
            model,
            metrics
        )
        
        return {
            "model_version": version.version_id,
            "metrics": metrics,
            "training_size": len(X)
        }
    
    def _execute_feature_extraction(self, task: WorkerTask) -> Dict[str, Any]:
        """Execute feature extraction task."""
        # Extract features
        features = self._extract_features(
            task.input_data
        )
        
        return {
            "features": features,
            "feature_count": len(features)
        } 