from passlib.context import CryptContext
import re
import logging
from fastapi import HTTPEx<PERSON>, status

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def validate_password_complexity(password: str) -> bool:
    """
    Validate that a password meets complexity requirements.
    
    Requirements:
    - At least 8 characters long
    - At least one uppercase letter
    - At least one lowercase letter
    - At least one digit
    - At least one special character
    
    Args:
        password (str): The password to validate.
    
    Returns:
        bool: True if the password meets the requirements, False otherwise.
    
    Raises:
        HTTPException: If the password does not meet the requirements.
    """
    if len(password) < 8:
        logger.warning("Password validation failed: Password must be at least 8 characters long")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be at least 8 characters long"
        )
    
    if not re.search(r"[A-Z]", password):
        logger.warning("Password validation failed: Password must contain at least one uppercase letter")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one uppercase letter"
        )
    
    if not re.search(r"[a-z]", password):
        logger.warning("Password validation failed: Password must contain at least one lowercase letter")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one lowercase letter"
        )
    
    if not re.search(r"\d", password):
        logger.warning("Password validation failed: Password must contain at least one digit")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one digit"
        )
    
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
        logger.warning("Password validation failed: Password must contain at least one special character")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one special character"
        )
    
    return True 