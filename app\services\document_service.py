"""Document service for handling document operations."""

from typing import List, Dict, Optional, Any, Union
from fastapi import UploadFile, HTTPException
from datetime import datetime
import os
import hashlib
try:
    import pytesseract
except ImportError:
    pytesseract = None
from sqlalchemy.orm import Session
from PIL import Image

from app.core.config import settings
from app.models.document import Document
from app.models.enums import DocumentType
from app.schemas.document import DocumentCreate, DocumentUpdate
from app.services.storage_service import StorageService

class DocumentService:
    def __init__(self, db: Session, storage: StorageService):
        self.db = db
        self.storage = storage

    async def create_document(
        self,
        file: UploadFile,
        document_data: DocumentCreate,
    ) -> Document:
        """Create a new document."""
        try:
            # Save file to storage
            file_path = await self.storage.save_file(file)
            
            # Calculate file size and checksum
            file_size = os.path.getsize(file_path)
            with open(file_path, "rb") as f:
                file_content = f.read()
                checksum = hashlib.sha256(file_content).hexdigest()
            
            # Create document in database
            db_document = Document(
                title=document_data.title,
                content=document_data.content,
                document_type=document_data.document_type,
                file_path=file_path,
                original_filename=file.filename,
                mime_type=file.content_type,
                file_size=file_size,
                checksum=checksum,
            )
            
            self.db.add(db_document)
            self.db.commit()
            self.db.refresh(db_document)
            
            return db_document
            
        except Exception as e:
            # Clean up file if database operation fails
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(
                status_code=500,
                detail=f"Error creating document: {str(e)}"
            )

    async def get_document(self, document_id: int) -> Document:
        """Get a document by ID."""
        document = self.db.query(Document).filter(Document.id == document_id).first()
        if not document:
            raise HTTPException(
                status_code=404,
                detail="Document not found"
            )
        return document

    async def get_documents(
        self,
        skip: int = 0,
        limit: int = 100,
        document_type: Optional[DocumentType] = None
    ) -> List[Document]:
        """Get a list of documents with optional filtering."""
        query = self.db.query(Document)
        if document_type:
            query = query.filter(Document.document_type == document_type)
        return query.offset(skip).limit(limit).all()

    async def update_document(
        self,
        document_id: int,
        document_data: DocumentUpdate,
    ) -> Document:
        """Update a document."""
        document = await self.get_document(document_id)
        
        # Update document attributes
        for field, value in document_data.dict(exclude_unset=True).items():
            setattr(document, field, value)
        
        self.db.commit()
        self.db.refresh(document)
        return document

    async def delete_document(self, document_id: int) -> bool:
        """Delete a document."""
        document = await self.get_document(document_id)
        
        # Delete file from storage
        if document.file_path and os.path.exists(document.file_path):
            os.remove(document.file_path)
        
        # Delete document from database
        self.db.delete(document)
        self.db.commit()
        return True

    def _process_image(self, image: Image.Image) -> str:
        """Process an image using OCR."""
        if pytesseract is None:
            raise HTTPException(status_code=500, detail="OCR engine (pytesseract) not available")
        try:
            text = pytesseract.image_to_string(image)
            return text
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Error processing image: {str(e)}"
            )
