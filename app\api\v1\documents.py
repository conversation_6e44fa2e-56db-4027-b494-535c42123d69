"""Document-related API endpoints."""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.document import Document
from app.models.enums import DocumentType
from app.schemas.document import DocumentCreate, DocumentResponse, DocumentUpdate
from app.services.document_service import DocumentService
from app.services.storage_service import StorageService

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/", response_model=DocumentResponse)
async def create_document(
    file: UploadFile = File(...),
    document_data: DocumentCreate = None,
    db: Session = Depends(get_db)
) -> Document:
    """Create a new document."""
    storage = StorageService()
    document_service = DocumentService(db, storage)
    return await document_service.create_document(file, document_data)

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: int,
    db: Session = Depends(get_db)
) -> Document:
    """Get a document by ID."""
    document_service = DocumentService(db, StorageService())
    return await document_service.get_document(document_id)

@router.get("/", response_model=List[DocumentResponse])
async def list_documents(
    skip: int = 0,
    limit: int = 100,
    document_type: Optional[DocumentType] = None,
    db: Session = Depends(get_db)
) -> List[Document]:
    """Get a list of documents."""
    document_service = DocumentService(db, StorageService())
    return await document_service.get_documents(skip, limit, document_type)

@router.patch("/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: int,
    document_data: DocumentUpdate,
    db: Session = Depends(get_db)
) -> Document:
    """Update a document."""
    document_service = DocumentService(db, StorageService())
    return await document_service.update_document(document_id, document_data)

@router.delete("/{document_id}")
async def delete_document(
    document_id: int,
    db: Session = Depends(get_db)
) -> bool:
    """Delete a document."""
    document_service = DocumentService(db, StorageService())
    return await document_service.delete_document(document_id)