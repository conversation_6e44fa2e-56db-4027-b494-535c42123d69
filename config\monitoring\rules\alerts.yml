groups:
  - name: critical_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High error rate detected
          description: "Error rate is above 10% for 5 minutes"

      - alert: SystemOverload
        expr: cpu_usage_percent > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: System CPU usage critical
          description: "CPU usage is above 90% for 5 minutes"

      - alert: HighMemoryUsage
        expr: memory_usage_percent > 85
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: High memory usage
          description: "Memory usage is above 85% for 10 minutes"

      - alert: DatabaseConnectionsExhausted
        expr: pg_stat_activity_count > pg_settings_max_connections * 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: Database connections nearly exhausted
          description: "More than 90% of database connections are in use"

  - name: warning_alerts
    rules:
      - alert: ElevatedLatency
        expr: http_request_latency_seconds > 2
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: Elevated request latency
          description: "Request latency is above 2 seconds"

      - alert: CacheHitRateLow
        expr: rate(cache_hits_total[5m]) / rate(cache_requests_total[5m]) < 0.7
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: Low cache hit rate
          description: "Cache hit rate is below 70%"

      - alert: HighRateLimiting
        expr: rate(rate_limit_exceeded_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High rate of rate limiting
          description: "Many requests are being rate limited"

      - alert: SecurityIncidents
        expr: sum(rate(security_incidents_total[1h])) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Elevated security incidents
          description: "Multiple security incidents detected"

  - name: availability_alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: Service is down
          description: "Service {{ $labels.instance }} is down"

      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.9"} > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High response time
          description: "90th percentile of response time is above 5 seconds"

      - alert: APIKeyRotationDelay
        expr: time() - api_key_last_rotation_timestamp > 86400 * 35
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: API key rotation overdue
          description: "API keys have not been rotated in over 35 days" 