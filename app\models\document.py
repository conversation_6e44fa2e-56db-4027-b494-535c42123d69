"""Document model with enhanced compliance tracking."""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base
from app.models.enums import ComplianceStatus, RiskLevel, DocumentType

class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=True)
    document_type = Column(Enum(DocumentType), nullable=False, default=DocumentType.OTHER)
    file_path = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    mime_type = Column(String)
    file_size = Column(Integer)  # Size in bytes
    is_processed = Column(Boolean, default=False)
    is_archived = Column(Boolean, default=False)
    archive_path = Column(String)
    archive_date = Column(DateTime)
    
    # Version tracking
    version = Column(Integer, nullable=False, server_default='1')
    parent_version_id = Column(Integer, ForeignKey("documents.id"), nullable=True)
    checksum = Column(String, nullable=True)
    
    # Review tracking
    last_reviewed_at = Column(DateTime, nullable=True)
    review_status = Column(String, nullable=True)
    risk_level = Column(String, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    parent_version = relationship("Document", remote_side=[id], backref="child_versions")
    compliance_results = relationship("ComplianceResult", back_populates="document", cascade="all, delete-orphan")
    links = relationship("DocumentLink", back_populates="document", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Document {self.id} ({self.title})>"

    @property
    def latest_review(self):
        """Get the most recent compliance review."""
        if not self.compliance_reviews:
            return None
        return max(self.compliance_reviews, key=lambda r: r.review_date)

    @property
    def latest_cbcs_review(self):
        """Get the most recent CBCS review."""
        if not self.cbcs_reviews:
            return None
        return max(self.cbcs_reviews, key=lambda r: r.review_date)

    @property
    def latest_scan(self):
        """Get the most recent CBCS scan."""
        if not self.cbcs_scans:
            return None
        return max(self.cbcs_scans, key=lambda s: s.scan_date or datetime.min)

    @property
    def compliance_status(self):
        """Get the current compliance status based on latest review."""
        latest = self.latest_review
        if not latest:
            return ComplianceStatus.UNDER_REVIEW
        if latest.score and latest.score >= 0.9:
            return ComplianceStatus.COMPLIANT
        elif latest.score and latest.score >= 0.7:
            return ComplianceStatus.REQUIRES_REVIEW
        return ComplianceStatus.NON_COMPLIANT

    @property
    def current_risk_level(self):
        """Get the current risk level based on latest review."""
        latest = self.latest_review
        if not latest or not latest.risk_level:
            return RiskLevel.MEDIUM
        return latest.risk_level 