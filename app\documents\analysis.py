from typing import List, Dict, Optional, Any
from datetime import datetime
import asyncio
import logging
import easyocr
import pytesseract
from paddleocr import PaddleOCR
import pdf2image
import spacy
from transformers import pipeline
from sentence_transformers import SentenceTransformer
import numpy as np
from pathlib import Path
import cv2
import re
from app.core.config import settings
from dataclasses import dataclass
from enum import Enum
from .models import Document

logger = logging.getLogger(__name__)

class ProcessingStatus(Enum):
    """Document processing status."""
    QUEUED = "queued"
    PROCESSING = "processing"
    ANALYZING = "analyzing"
    COMPLIANCE_CHECK = "compliance_check"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class AnalysisResult:
    """Results of document analysis."""
    document_id: str
    requirements: List[Dict]
    temporal_info: Dict
    sections: Dict
    cross_references: List[Dict]
    compliance_status: Dict
    confidence_score: float
    processing_history: List[Dict]


class DocumentAnalyzer:
    def __init__(self):
        self.ocr_engine = settings.OCR_ENGINE
        self.enable_gpu = settings.ENABLE_GPU
        
        # Lazy load attributes
        self._reader = None
        self._nlp = None
        self._sentence_model = None
        self._qa_pipeline = None
        
        # Compile regex patterns - these are lightweight and can be initialized immediately
        self.date_pattern = re.compile(r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b')
        self.requirement_patterns = [
            re.compile(r'\bmust\s+', re.I),
            re.compile(r'\bshall\s+', re.I),
            re.compile(r'\brequired\s+to\s+', re.I),
            re.compile(r'\brequirements?\s+include\s+', re.I)
        ]
        
    @property
    def reader(self):
        """Lazy load OCR reader when needed."""
        if self._reader is None:
            logger.info(f"Initializing OCR engine: {self.ocr_engine}")
            if self.ocr_engine == "easyocr":
                self._reader = easyocr.Reader(['en'], gpu=self.enable_gpu)
            elif self.ocr_engine == "paddleocr":
                self._reader = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=self.enable_gpu)
            else:
                # Default to Tesseract (which doesn't need initialization here)
                self._reader = None
        return self._reader
        
    @property
    def nlp(self):
        """Lazy load spaCy model when needed."""
        if self._nlp is None:
            logger.info("Initializing spaCy NLP model")
            self._nlp = spacy.load("en_core_web_sm")
        return self._nlp
        
    @property
    def sentence_model(self):
        """Lazy load sentence transformer model when needed."""
        if self._sentence_model is None:
            logger.info("Initializing sentence transformer model")
            self._sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        return self._sentence_model
        
    @property
    def qa_pipeline(self):
        """Lazy load question answering pipeline when needed."""
        if self._qa_pipeline is None:
            logger.info("Initializing question answering pipeline")
            self._qa_pipeline = pipeline("question-answering", model="distilbert-base-cased-distilled-squad")
        return self._qa_pipeline
    
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """Process a document through the complete analysis pipeline."""
        try:
            # Validate file exists
            if not Path(file_path).exists():
                raise FileNotFoundError(f"Document file not found: {file_path}")
                
            # Convert PDF to images
            if file_path.lower().endswith('.pdf'):
                logger.info(f"Converting PDF to images: {file_path}")
                images = await self._pdf_to_images(file_path)
            else:
                logger.info(f"Loading image file: {file_path}")
                image = cv2.imread(file_path)
                if image is None:
                    raise ValueError(f"Failed to load image file: {file_path}")
                images = [image]
            
            processing_results = {}
            
            try:
                # Extract text using OCR
                logger.info(f"Extracting text from {len(images)} images")
                text_blocks = await self._extract_text(images)
                processing_results["text_blocks"] = text_blocks
                
                # Extract metadata
                logger.info("Extracting document metadata")
                metadata = await self._extract_metadata(text_blocks, file_path)
                processing_results["metadata"] = metadata
                
                # Extract requirements
                logger.info("Extracting document requirements")
                requirements = await self._extract_requirements(text_blocks)
                processing_results["requirements"] = requirements
                
                # Extract temporal information
                logger.info("Extracting temporal information")
                temporal_info = await self._extract_temporal_info(text_blocks)
                processing_results["temporal_info"] = temporal_info
                
                # Detect tables
                logger.info("Detecting tables in document")
                tables = await self._detect_tables(images)
                processing_results["tables"] = tables
                
                # Analyze document structure
                logger.info("Analyzing document structure")
                structure = await self._analyze_structure(text_blocks)
                processing_results["structure"] = structure
                
                return processing_results
                
            except Exception as e:
                # Catch exceptions in individual processing steps but still return partial results
                logger.error(f"Error during document processing step: {str(e)}")
                processing_results["error"] = {
                    "message": str(e),
                    "type": type(e).__name__,
                    "processing_stage": e.__traceback__.tb_frame.f_code.co_name if e.__traceback__ else "unknown"
                }
                return processing_results
            
        except FileNotFoundError as e:
            logger.error(f"File not found: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {str(e)}", exc_info=True)
            raise RuntimeError(f"Document processing failed: {str(e)}") from e
    
    async def _pdf_to_images(self, pdf_path: str) -> List[np.ndarray]:
        """Convert PDF pages to images."""
        try:
            return pdf2image.convert_from_path(pdf_path)
        except Exception as e:
            logger.error(f"Error converting PDF to images: {str(e)}")
            raise
    
    async def _extract_text(self, images: List[np.ndarray]) -> List[Dict[str, Any]]:
        """Extract text from images using the configured OCR engine."""
        text_blocks = []
        
        try:
            for page_num, image in enumerate(images, 1):
                if self.ocr_engine == "easyocr":
                    results = self.reader.readtext(image)
                    for box, text, conf in results:
                        text_blocks.append({
                            "page": page_num,
                            "text": text,
                            "confidence": conf,
                            "bbox": box,
                            "type": "text"
                        })
                        
                elif self.ocr_engine == "paddleocr":
                    result = self.reader.ocr(image)
                    for line in result:
                        box, (text, conf) = line
                        text_blocks.append({
                            "page": page_num,
                            "text": text,
                            "confidence": conf,
                            "bbox": box,
                            "type": "text"
                        })
                        
                else:  # Default to Tesseract
                    text = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
                    for i in range(len(text['text'])):
                        if text['conf'][i] > 0:  # Filter out low confidence results
                            text_blocks.append({
                                "page": page_num,
                                "text": text['text'][i],
                                "confidence": text['conf'][i] / 100,
                                "bbox": (text['left'][i], text['top'][i], 
                                        text['left'][i] + text['width'][i], 
                                        text['top'][i] + text['height'][i]),
                                "type": "text"
                            })
            
            return text_blocks
            
        except Exception as e:
            logger.error(f"Error extracting text: {str(e)}")
            raise
    
    async def _extract_metadata(self, text_blocks: List[Dict], file_path: str) -> Dict[str, Any]:
        """Extract metadata from the document."""
        try:
            # Get basic file metadata
            path = Path(file_path)
            metadata = {
                "filename": path.name,
                "file_type": path.suffix.lower(),
                "file_size": path.stat().st_size,
                "created": datetime.fromtimestamp(path.stat().st_ctime),
                "modified": datetime.fromtimestamp(path.stat().st_mtime)
            }
            
            # Extract document title (usually in the first few blocks)
            title_candidates = [block["text"] for block in text_blocks[:5] 
                              if len(block["text"]) > 10 and block["confidence"] > 0.8]
            if title_candidates:
                metadata["title"] = title_candidates[0]
            
            # Extract dates
            dates = []
            for block in text_blocks:
                dates.extend(self.date_pattern.findall(block["text"]))
            if dates:
                metadata["dates"] = dates
            
            # Extract organizations using spaCy
            text = " ".join(block["text"] for block in text_blocks)
            doc = self.nlp(text)
            orgs = [ent.text for ent in doc.ents if ent.label_ == "ORG"]
            if orgs:
                metadata["organizations"] = list(set(orgs))
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata: {str(e)}")
            raise
    
    async def _extract_requirements(self, text_blocks: List[Dict]) -> List[Dict[str, Any]]:
        """Extract requirements from text blocks."""
        requirements = []
        
        try:
            for block in text_blocks:
                text = block["text"]
                
                # Check for requirement patterns
                if any(pattern.search(text) for pattern in self.requirement_patterns):
                    # Use spaCy for better sentence segmentation
                    doc = self.nlp(text)
                    for sent in doc.sents:
                        if any(pattern.search(sent.text) for pattern in self.requirement_patterns):
                            requirements.append({
                                "text": sent.text,
                                "page": block["page"],
                                "confidence": block["confidence"],
                                "type": "requirement",
                                "source_block": block
                            })
            
            return requirements
            
        except Exception as e:
            logger.error(f"Error extracting requirements: {str(e)}")
            raise
    
    async def _extract_temporal_info(self, text_blocks: List[Dict]) -> List[Dict[str, Any]]:
        """Extract temporal information from text blocks."""
        temporal_info = []
        
        try:
            for block in text_blocks:
                dates = self.date_pattern.findall(block["text"])
                if dates:
                    # Use spaCy to get the context around dates
                    doc = self.nlp(block["text"])
                    for date in dates:
                        # Find the sentence containing the date
                        for sent in doc.sents:
                            if date in sent.text:
                                temporal_info.append({
                                    "date": date,
                                    "context": sent.text,
                                    "page": block["page"],
                                    "confidence": block["confidence"],
                                    "type": "temporal"
                                })
            
            return temporal_info
            
        except Exception as e:
            logger.error(f"Error extracting temporal information: {str(e)}")
            raise
    
    async def _detect_tables(self, images: List[np.ndarray]) -> List[Dict[str, Any]]:
        """Detect and extract tables from document images."""
        tables = []
        
        try:
            for page_num, image in enumerate(images, 1):
                # Convert to grayscale
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                
                # Apply adaptive thresholding
                thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                            cv2.THRESH_BINARY_INV, 11, 2)
                
                # Detect horizontal and vertical lines
                horizontal = np.copy(thresh)
                vertical = np.copy(thresh)
                
                # Specify size on horizontal axis
                cols = horizontal.shape[1]
                horizontal_size = cols // 30
                horizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
                horizontal = cv2.erode(horizontal, horizontalStructure)
                horizontal = cv2.dilate(horizontal, horizontalStructure)
                
                # Specify size on vertical axis
                rows = vertical.shape[0]
                vertical_size = rows // 30
                verticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, vertical_size))
                vertical = cv2.erode(vertical, verticalStructure)
                vertical = cv2.dilate(vertical, verticalStructure)
                
                # Combine horizontal and vertical lines
                table_mask = cv2.add(horizontal, vertical)
                
                # Find contours
                contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # Filter and process potential tables
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    if w > 100 and h > 100:  # Filter small boxes
                        table_region = image[y:y+h, x:x+w]
                        
                        # Extract text from table region
                        if self.ocr_engine == "easyocr":
                            table_text = self.reader.readtext(table_region)
                        elif self.ocr_engine == "paddleocr":
                            table_text = self.reader.ocr(table_region)
                        else:
                            table_text = pytesseract.image_to_string(table_region)
                        
                        tables.append({
                            "page": page_num,
                            "bbox": (x, y, x+w, y+h),
                            "text": table_text,
                            "type": "table"
                        })
            
            return tables
            
        except Exception as e:
            logger.error(f"Error detecting tables: {str(e)}")
            raise
    
    async def _analyze_structure(self, text_blocks: List[Dict]) -> Dict[str, Any]:
        """Analyze the document structure."""
        try:
            structure = {
                "sections": [],
                "headers": [],
                "paragraphs": [],
                "lists": []
            }
            
            current_section = None
            current_list = []
            
            for block in text_blocks:
                text = block["text"]
                
                # Detect headers (all caps, numbered, etc.)
                if text.isupper() or re.match(r'^\d+\.?\s+[A-Z]', text):
                    if current_section:
                        structure["sections"].append(current_section)
                    current_section = {
                        "header": text,
                        "content": [],
                        "page": block["page"]
                    }
                    structure["headers"].append({
                        "text": text,
                        "page": block["page"],
                        "level": 1 if text.isupper() else 2
                    })
                
                # Detect list items
                elif re.match(r'^\s*[\u2022\u2023\u25E6\u2043\u2219]\s+|^\s*\d+\.\s+|^\s*[a-z]\)\s+', text):
                    current_list.append({
                        "text": text,
                        "page": block["page"]
                    })
                
                # Regular paragraph
                else:
                    if current_list:
                        structure["lists"].append(current_list)
                        current_list = []
                    
                    if len(text.strip()) > 50:  # Minimum length for paragraphs
                        structure["paragraphs"].append({
                            "text": text,
                            "page": block["page"]
                        })
                    
                    if current_section:
                        current_section["content"].append(text)
            
            # Add final section if exists
            if current_section:
                structure["sections"].append(current_section)
            
            # Add final list if exists
            if current_list:
                structure["lists"].append(current_list)
            
            return structure
            
        except Exception as e:
            logger.error(f"Error analyzing document structure: {str(e)}")
            raise

    async def analyze_document(self, document: Document) -> AnalysisResult:
        """Perform comprehensive document analysis."""
        if not document.text_content:
            raise ValueError("Document text content is required for analysis")
            
        text = document.text_content
        
        # Perform all analyses
        requirements = await self.extract_requirements(text)
        temporal_info = await self.analyze_temporal_info(text)
        sections = await self.classify_sections(text)
        cross_references = await self.detect_cross_references(text)
        
        # Calculate overall confidence
        confidence_score = self._calculate_overall_confidence(
            requirements, temporal_info, sections, cross_references
        )
        
        return AnalysisResult(
            document_id=document.id,
            requirements=requirements,
            temporal_info=temporal_info,
            sections=sections,
            cross_references=cross_references,
            compliance_status={},  # Will be filled by compliance checker
            confidence_score=confidence_score,
            processing_history=[{
                "timestamp": document.updated_at.isoformat(),
                "status": ProcessingStatus.COMPLETED.value,
                "confidence": confidence_score
            }]
        )

    async def extract_requirements(self, text: str) -> List[Dict]:
        """Extract policy requirements using NLP."""
        requirements = []
        requirement_patterns = [
            r"must\s+\w+",
            r"shall\s+\w+",
            r"required\s+to\s+\w+",
            r"requirements?\s+include\s+",
            r"mandatory\s+\w+",
            r"necessary\s+to\s+\w+"
        ]
        
        sentences = text.split('.')
        for sentence in sentences:
            if any(re.search(pattern, sentence, re.IGNORECASE) for pattern in requirement_patterns):
                requirements.append({
                    "text": sentence.strip(),
                    "type": self._classify_requirement(sentence),
                    "confidence": self._calculate_confidence(sentence)
                })
        
        return requirements

    async def analyze_temporal_info(self, text: str) -> Dict:
        """Extract and analyze temporal information."""
        date_patterns = [
            r"\d{1,2}/\d{1,2}/\d{2,4}",
            r"\d{4}-\d{2}-\d{2}",
            r"(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}"
        ]
        
        temporal_info = {
            "dates": [],
            "periods": [],
            "deadlines": []
        }
        
        for pattern in date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                date_str = match.group()
                context = text[max(0, match.start()-50):min(len(text), match.end()+50)]
                
                date_info = {
                    "date": date_str,
                    "context": context.strip(),
                    "type": self._classify_date_type(context)
                }
                
                if "deadline" in context.lower() or "due" in context.lower():
                    temporal_info["deadlines"].append(date_info)
                elif "period" in context.lower():
                    temporal_info["periods"].append(date_info)
                else:
                    temporal_info["dates"].append(date_info)
        
        return temporal_info

    async def classify_sections(self, text: str) -> Dict:
        """Classify document sections."""
        sections = {}
        current_section = None
        
        section_patterns = [
            r"^(?:Section|SECTION)\s+\d+",
            r"^(?:Article|ARTICLE)\s+\d+",
            r"^\d+\.\d+\s+[A-Z]",
            r"^[A-Z][A-Z\s]+:"
        ]
        
        lines = text.split('\n')
        for line in lines:
            for pattern in section_patterns:
                if re.match(pattern, line):
                    current_section = line.strip()
                    sections[current_section] = {
                        "content": [],
                        "type": self._classify_section_type(line),
                        "importance": self._calculate_section_importance(line)
                    }
                    break
            
            if current_section and not any(re.match(p, line) for p in section_patterns):
                sections[current_section]["content"].append(line.strip())
        
        return sections

    async def detect_cross_references(self, text: str) -> List[Dict]:
        """Detect cross-references to other documents."""
        cross_references = []
        reference_patterns = [
            r"(?:see|refer to|as per|according to|as defined in)\s+(?:section|policy|document)\s+[\w\-\.]+",
            r"(?:Policy|Document|Form)\s+#?\s*[\w\-\.]+",
            r"\b\d{2}\s*CFR\s*\d+(?:\.\d+)*\b"
        ]
        
        for pattern in reference_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                reference = match.group()
                context = text[max(0, match.start()-50):min(len(text), match.end()+50)]
                
                cross_references.append({
                    "reference": reference,
                    "context": context.strip(),
                    "type": self._classify_reference_type(reference),
                    "confidence": self._calculate_confidence(context)
                })
        
        return cross_references

    def _classify_requirement(self, text: str) -> str:
        """Classify the type of requirement."""
        if "must" in text.lower():
            return "mandatory"
        elif "should" in text.lower():
            return "recommended"
        elif "may" in text.lower():
            return "optional"
        return "undefined"

    def _classify_date_type(self, context: str) -> str:
        """Classify the type of date based on context."""
        context_lower = context.lower()
        if any(word in context_lower for word in ["deadline", "due", "by"]):
            return "deadline"
        elif any(word in context_lower for word in ["start", "begin", "from"]):
            return "start_date"
        elif any(word in context_lower for word in ["end", "until", "through"]):
            return "end_date"
        return "reference_date"

    def _classify_section_type(self, text: str) -> str:
        """Classify the type of section."""
        text_lower = text.lower()
        if any(word in text_lower for word in ["requirement", "must", "shall"]):
            return "requirements"
        elif any(word in text_lower for word in ["procedure", "process", "step"]):
            return "procedure"
        elif any(word in text_lower for word in ["definition", "term"]):
            return "definitions"
        return "general"

    def _classify_reference_type(self, reference: str) -> str:
        """Classify the type of cross-reference."""
        reference_lower = reference.lower()
        if "cfr" in reference_lower:
            return "regulation"
        elif "policy" in reference_lower:
            return "policy"
        elif "form" in reference_lower:
            return "form"
        elif "section" in reference_lower:
            return "internal"
        return "other"

    def _calculate_section_importance(self, text: str) -> float:
        """Calculate importance score for a section."""
        importance_factors = {
            "requirement": 1.0,
            "mandatory": 1.0,
            "must": 0.9,
            "shall": 0.9,
            "important": 0.8,
            "critical": 0.8,
            "should": 0.7,
            "may": 0.5
        }
        
        text_lower = text.lower()
        score = 0.5  # Base score
        
        for word, weight in importance_factors.items():
            if word in text_lower:
                score = max(score, weight)
                
        return score

    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence score for extracted information."""
        factors = {
            "length": min(1.0, len(text.split()) / 20),  # Longer context = higher confidence
            "structure": 0.8 if any(char in text for char in ".:;()[]") else 0.5,  # Well-structured text
            "keywords": 0.9 if any(word in text.lower() for word in ["must", "shall", "required"]) else 0.6
        }
        return sum(factors.values()) / len(factors)

    def _calculate_overall_confidence(
        self,
        requirements: List[Dict],
        temporal_info: Dict,
        sections: Dict,
        cross_references: List[Dict]
    ) -> float:
        """Calculate overall confidence score for the analysis."""
        scores = []
        
        # Requirements confidence
        if requirements:
            scores.append(sum(r["confidence"] for r in requirements) / len(requirements))
            
        # Temporal information confidence
        temporal_count = len(temporal_info["dates"]) + len(temporal_info["periods"]) + len(temporal_info["deadlines"])
        if temporal_count > 0:
            scores.append(0.8)  # High confidence in date extraction
            
        # Sections confidence
        if sections:
            section_scores = [s.get("importance", 0.5) for s in sections.values()]
            scores.append(sum(section_scores) / len(section_scores))
            
        # Cross-references confidence
        if cross_references:
            scores.append(sum(r["confidence"] for r in cross_references) / len(cross_references))
            
        return sum(scores) / len(scores) if scores else 0.5
