from datetime import datetime
from typing import List, Optional, Dict
from pydantic import BaseModel, <PERSON>, constr
from app.models.compliance import ReviewStatus, RiskLevel

class ReviewDocumentBase(BaseModel):
    document_id: int

class ReviewDocumentCreate(ReviewDocumentBase):
    pass

class ReviewDocumentResponse(ReviewDocumentBase):
    review_id: int
    added_at: datetime

    class Config:
        from_attributes = True

class ComplianceReviewBase(BaseModel):
    """Base schema for compliance review data."""
    application_id: str = Field(..., description="Associated FEMA application ID")
    document_id: str = Field(..., description="Document being reviewed")
    review_type: str = Field(..., description="Type of compliance review")
    findings: Dict[str, str] = Field(default_factory=dict, description="Review findings by category")
    recommendations: List[str] = Field(default_factory=list, description="Recommendations for compliance")

class ComplianceReviewCreate(ComplianceReviewBase):
    """Schema for creating a new compliance review."""
    pass

class ComplianceReviewUpdate(BaseModel):
    """Schema for updating an existing compliance review."""
    findings: Optional[Dict[str, str]] = None
    recommendations: Optional[List[str]] = None
    status: Optional[ReviewStatus] = None
    risk_level: Optional[RiskLevel] = None
    compliance_score: Optional[float] = Field(None, ge=0, le=100)
    reviewer_notes: Optional[str] = None

class ComplianceReviewResponse(ComplianceReviewBase):
    """Schema for compliance review response."""
    id: str
    status: ReviewStatus
    risk_level: RiskLevel
    compliance_score: float = Field(..., ge=0, le=100)
    reviewer_id: int
    reviewer_notes: Optional[str] = None
    review_date: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ComplianceReviewWithDetails(ComplianceReviewResponse):
    """Schema for compliance review with related details."""
    document: "ComplianceDocumentResponse"
    application: "FEMAApplicationResponse"
    validation_results: Dict[str, Dict] = Field(
        default_factory=dict,
        description="Detailed validation results by category"
    )

    class Config:
        from_attributes = True

class ComplianceReviewList(BaseModel):
    total: int
    items: List[ComplianceReviewResponse]

    class Config:
        from_attributes = True
