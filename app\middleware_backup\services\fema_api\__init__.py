import aiohttp
from typing import Dict, List, Optional
from datetime import datetime
from fastapi import HTTPException
from app.core.config import settings
from app.models.disaster import DisasterDeclaration

class FEMAAPIService:
    BASE_URL = "https://www.fema.gov/api/open/v1"
    
    async def fetch_disaster_data(self, dr_number: str) -> Dict:
        async with aiohttp.ClientSession() as session:
            url = f"{self.BASE_URL}/DisasterDeclarations/{dr_number}"
            async with session.get(url) as response:
                if response.status == 404:
                    raise HTTPException(status_code=404, detail="Disaster not found")
                data = await response.json()
                return data["DisasterDeclaration"]
    
    async def sync_disaster_data(self, dr_number: str, db) -> DisasterDeclaration:
        data = await self.fetch_disaster_data(dr_number)
        disaster = DisasterDeclaration(
            dr_number=data["disasterNumber"],
            incident_type=data["incidentType"],
            incident_period_start=datetime.fromisoformat(data["incidentBeginDate"]),
            incident_period_end=datetime.fromisoformat(data["incidentEndDate"]),
            declaration_date=datetime.fromisoformat(data["declarationDate"]),
            state=data["state"],
            affected_areas=data["designatedAreas"],
            fema_incident_id=data["femaIncidentId"]
        )
        db.add(disaster)
        await db.commit()
        await db.refresh(disaster)
        return disaster
