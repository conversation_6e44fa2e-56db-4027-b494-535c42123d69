"""Pydantic schemas for CBCS scanning."""

from datetime import datetime
from typing import Dict, List, Optional
from pydantic import BaseModel, Field

from app.models.compliance import ScanStatus

class ScanBase(BaseModel):
    """Base schema for CBCS scans."""
    document_id: int
    status: ScanStatus
    scan_date: Optional[datetime] = None
    error_message: Optional[str] = None

class ScanCreate(ScanBase):
    """Schema for creating a new scan."""
    pass

class ScanUpdate(BaseModel):
    """Schema for updating a scan."""
    status: Optional[ScanStatus] = None
    results: Optional[Dict] = None
    error_message: Optional[str] = None

class ScanResponse(ScanBase):
    """Schema for scan responses."""
    id: int
    results: Optional[Dict] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Finding(BaseModel):
    """Schema for scan findings."""
    type: str = Field(..., description="Type of finding (e.g., 'risk', 'compliance', 'security')")
    severity: str = Field(..., description="Severity level (e.g., 'high', 'medium', 'low')")
    description: str = Field(..., description="Detailed description of the finding")
    recommendation: Optional[str] = Field(None, description="Recommended action to address the finding")

class ScanAnalysis(BaseModel):
    """Schema for scan analysis results."""
    status: str = Field(..., description="Analysis status ('success' or 'error')")
    document_id: int = Field(..., description="ID of the analyzed document")
    scan_date: datetime = Field(..., description="Date when the scan was performed")
    findings: List[Finding] = Field(default_factory=list, description="List of findings from the scan")
    risk_level: str = Field(..., description="Overall risk level assessment")
    recommendations: List[str] = Field(default_factory=list, description="List of recommendations")
