from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime

from app.core.deps import get_db, get_current_user
from app.models.user import User
from app.models.flood_map import FloodMap, FirmetteRequest
from app.services.msc_service import MSCService
from app.schemas.flood_map import (
    FloodMapCreate,
    FloodMapResponse,
    FirmetteRequestCreate,
    FirmetteRequestResponse
)

router = APIRouter()
msc_service = MSCService()

@router.post("/firmette", response_model=FirmetteRequestResponse)
async def generate_firmette(
    request: FirmetteRequestCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Generate a Firmette map for the specified location"""
    firmette_request = FirmetteRequest(
        latitude=request.latitude,
        longitude=request.longitude,
        address=request.address,
        status="pending",
        user_id=current_user.id
    )
    db.add(firmette_request)
    db.commit()
    
    try:
        # Get flood zone data first
        flood_zone_data = await msc_service.get_flood_zone(
            request.latitude,
            request.longitude
        )
        
        # Then get Firmette
        firmette_data = await msc_service.get_firmette(
            request.latitude,
            request.longitude
        )
        
        # Create flood map record
        flood_map = FloodMap(
            firm_panel_id=flood_zone_data['panel_id'],
            effective_date=datetime.fromtimestamp(flood_zone_data['effective_date']/1000) if flood_zone_data.get('effective_date') else None,
            map_scale=firmette_data.get('map_scale'),
            flood_zone=flood_zone_data['flood_zone'],
            latitude=request.latitude,
            longitude=request.longitude,
            firmette_url=firmette_data['url'],
            user_id=current_user.id
        )
        
        db.add(flood_map)
        db.flush()  # Get the flood_map.id
        
        # Update firmette request
        firmette_request.status = "completed"
        firmette_request.flood_map_id = flood_map.id
        firmette_request.completed_at = datetime.utcnow()
        db.commit()
        
        return firmette_request
    except Exception as e:
        firmette_request.status = "failed"
        db.commit()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/flood-zone")
async def get_flood_zone(
    latitude: float = Query(..., ge=-90, le=90),
    longitude: float = Query(..., ge=-180, le=180),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get flood zone information for the specified coordinates"""
    try:
        return await msc_service.get_flood_zone(latitude, longitude)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history", response_model=List[FloodMapResponse])
async def get_flood_map_history(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get the user's flood map request history"""
    return db.query(FloodMap).filter(FloodMap.user_id == current_user.id).all()
