from datetime import datetime
from typing import List, Optional, Dict
from sqlalchemy.orm import Session
import logging
from app.core.celery_app import celery_app
from app.core.cache import RedisCache
from app.models.disaster import DisasterDeclaration, DisasterType

logger = logging.getLogger(__name__)

class DisasterService:
    def __init__(self, db_session: Session):
        self.db = db_session
        self.cache = RedisCache()
        self.cache_ttl = 3600  # 1 hour

    async def create_declaration(
        self,
        dr_number: str,
        incident_type: DisasterType,
        incident_period_start: datetime,
        incident_period_end: datetime,
        declaration_date: datetime,
        state: str,
        affected_areas: List[str],
        fema_incident_id: str
    ) -> DisasterDeclaration:
        """Create a new disaster declaration."""
        try:
            declaration = DisasterDeclaration(
                dr_number=dr_number,
                incident_type=incident_type,
                incident_period_start=incident_period_start,
                incident_period_end=incident_period_end,
                declaration_date=declaration_date,
                state=state,
                affected_areas=affected_areas,
                fema_incident_id=fema_incident_id
            )

            self.db.add(declaration)
            await self.db.commit()
            
            # Invalidate cache
            await self._invalidate_cache(dr_number)
            
            return declaration

        except Exception as e:
            logger.error(f"Error creating disaster declaration: {str(e)}")
            await self.db.rollback()
            raise

    async def get_declaration(
        self,
        dr_number: str
    ) -> Optional[DisasterDeclaration]:
        """Get a disaster declaration by DR number."""
        try:
            cache_key = f"disaster:{dr_number}"
            
            # Try to get from cache
            cached_data = await self.cache.get(cache_key)
            if cached_data:
                return DisasterDeclaration(**cached_data)

            # Get from database
            declaration = self.db.query(DisasterDeclaration)\
                .filter(DisasterDeclaration.dr_number == dr_number)\
                .first()

            if declaration:
                # Cache the result
                await self.cache.set(
                    cache_key,
                    {
                        "dr_number": declaration.dr_number,
                        "incident_type": declaration.incident_type,
                        "incident_period_start": declaration.incident_period_start.isoformat(),
                        "incident_period_end": declaration.incident_period_end.isoformat(),
                        "declaration_date": declaration.declaration_date.isoformat(),
                        "state": declaration.state,
                        "affected_areas": declaration.affected_areas,
                        "fema_incident_id": declaration.fema_incident_id
                    },
                    self.cache_ttl
                )

            return declaration

        except Exception as e:
            logger.error(f"Error getting disaster declaration: {str(e)}")
            raise

    async def get_active_declarations(
        self,
        state: Optional[str] = None
    ) -> List[DisasterDeclaration]:
        """Get all active disaster declarations."""
        try:
            cache_key = f"active_disasters:{state or 'all'}"
            
            # Try to get from cache
            cached_data = await self.cache.get(cache_key)
            if cached_data:
                return [DisasterDeclaration(**d) for d in cached_data]

            # Build query
            query = self.db.query(DisasterDeclaration)\
                .filter(DisasterDeclaration.incident_period_end >= datetime.utcnow())

            if state:
                query = query.filter(DisasterDeclaration.state == state)

            # Get from database
            declarations = query.all()

            if declarations:
                # Cache the result
                await self.cache.set(
                    cache_key,
                    [{
                        "dr_number": d.dr_number,
                        "incident_type": d.incident_type,
                        "incident_period_start": d.incident_period_start.isoformat(),
                        "incident_period_end": d.incident_period_end.isoformat(),
                        "declaration_date": d.declaration_date.isoformat(),
                        "state": d.state,
                        "affected_areas": d.affected_areas,
                        "fema_incident_id": d.fema_incident_id
                    } for d in declarations],
                    self.cache_ttl
                )

            return declarations

        except Exception as e:
            logger.error(f"Error getting active declarations: {str(e)}")
            raise

    @celery_app.task(name="disaster.sync_fema_data")
    async def sync_fema_data(self, dr_number: str) -> Dict:
        """Synchronize disaster data with FEMA API."""
        try:
            # This would integrate with FEMA's API
            # For now, it's a placeholder
            logger.info(f"Syncing data for DR-{dr_number} with FEMA API")
            
            return {
                "status": "success",
                "dr_number": dr_number,
                "sync_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error syncing FEMA data: {str(e)}")
            raise

    async def update_declaration(
        self,
        dr_number: str,
        updates: Dict
    ) -> DisasterDeclaration:
        """Update a disaster declaration."""
        try:
            declaration = await self.get_declaration(dr_number)
            if not declaration:
                raise ValueError(f"No declaration found for DR-{dr_number}")

            # Update fields
            for key, value in updates.items():
                setattr(declaration, key, value)

            # Save changes
            self.db.add(declaration)
            await self.db.commit()
            
            # Invalidate cache
            await self._invalidate_cache(dr_number)
            
            return declaration

        except Exception as e:
            logger.error(f"Error updating disaster declaration: {str(e)}")
            await self.db.rollback()
            raise

    async def get_applicable_policies(
        self,
        dr_number: str,
        date: Optional[datetime] = None
    ) -> List[Dict]:
        """Get policies applicable to a disaster declaration."""
        try:
            declaration = await self.get_declaration(dr_number)
            if not declaration:
                raise ValueError(f"No declaration found for DR-{dr_number}")

            target_date = date or datetime.utcnow()
            
            # Get policies from relationship
            policies = declaration.applicable_policies
            
            # Filter by date if specified
            if date:
                policies = [
                    p for p in policies
                    if p.effective_date <= target_date and
                    (not p.expiration_date or p.expiration_date >= target_date)
                ]

            return [{
                "id": p.id,
                "policy_number": p.policy_number,
                "title": p.title,
                "effective_date": p.effective_date.isoformat(),
                "expiration_date": p.expiration_date.isoformat() if p.expiration_date else None,
                "version_number": p.version_number
            } for p in policies]

        except Exception as e:
            logger.error(f"Error getting applicable policies: {str(e)}")
            raise

    async def _invalidate_cache(self, dr_number: str):
        """Invalidate cache entries for a disaster declaration."""
        keys_to_delete = [
            f"disaster:{dr_number}",
            "active_disasters:*"
        ]
        for key in keys_to_delete:
            await self.cache.delete(key)
