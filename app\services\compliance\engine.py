"""
Compliance Rule Engine
Evaluates documents against compliance rules.
"""

import os
import json
import logging
from typing import Dict, List, Optional
from pathlib import Path
import re

logger = logging.getLogger(__name__)

class RuleEngine:
    """Evaluates documents against compliance rules."""
    
    def __init__(self, rules_directory: str):
        """
        Initialize the rule engine.
        
        Args:
            rules_directory: Directory containing rule definitions
        """
        self.rules_directory = Path(rules_directory)
        self.rules = self._load_rules()
        logger.info(f"Loaded {len(self.rules)} compliance rules")
    
    def _load_rules(self) -> List[Dict]:
        """
        Load compliance rules from rule files.
        
        Returns:
            List of rule definitions
        """
        rules = []
        
        try:
            if not self.rules_directory.exists():
                logger.warning(f"Rules directory not found: {self.rules_directory}")
                return rules
            
            for rule_file in self.rules_directory.glob("*.json"):
                try:
                    with open(rule_file) as f:
                        rule_set = json.load(f)
                        if "rules" in rule_set:
                            rules.extend(rule_set["rules"])
                except Exception as e:
                    logger.error(f"Error loading rule file {rule_file}: {e}")
            
            return rules
            
        except Exception as e:
            logger.error(f"Error loading rules: {e}")
            return rules
    
    def evaluate_document(self, document_path: str, classification: Dict) -> Dict:
        """
        Evaluate a document against compliance rules.
        
        Args:
            document_path: Path to the document
            classification: Document classification results
            
        Returns:
            Dictionary containing compliance evaluation results
        """
        try:
            doc_type = classification["document_type"]
            applicable_rules = [
                rule for rule in self.rules
                if rule["type"] == doc_type
            ]
            
            if not applicable_rules:
                logger.info(f"No applicable rules found for document type: {doc_type}")
                return {
                    "status": "not_applicable",
                    "message": f"No rules defined for document type: {doc_type}",
                    "evaluation": []
                }
            
            # Evaluate each rule
            evaluations = []
            for rule in applicable_rules:
                evaluation = self._evaluate_rule(rule, document_path, classification)
                evaluations.append(evaluation)
            
            # Determine overall status
            if all(e["compliant"] for e in evaluations):
                status = "compliant"
            elif any(e["compliant"] for e in evaluations):
                status = "partial"
            else:
                status = "non_compliant"
            
            return {
                "status": status,
                "evaluation": evaluations,
                "rule_count": len(applicable_rules)
            }
            
        except Exception as e:
            logger.error(f"Error evaluating document {document_path}: {e}")
            return {
                "status": "error",
                "message": str(e),
                "evaluation": []
            }
    
    def _evaluate_rule(self, rule: Dict, document_path: str, classification: Dict) -> Dict:
        """
        Evaluate a single rule against a document.
        
        Args:
            rule: Rule definition
            document_path: Path to the document
            classification: Document classification results
            
        Returns:
            Dictionary containing rule evaluation results
        """
        try:
            # Check pattern match
            if "pattern" in rule:
                pattern = re.compile(rule["pattern"], re.IGNORECASE)
                if not any(
                    pattern.search(text)
                    for text in [
                        classification.get("summary", ""),
                        *classification.get("keywords", [])
                    ]
                ):
                    return {
                        "rule_id": rule["id"],
                        "compliant": False,
                        "message": f"Document does not match pattern: {rule['pattern']}"
                    }
            
            # Check required fields
            if "required_fields" in rule:
                missing_fields = [
                    field for field in rule["required_fields"]
                    if field not in classification.get("metadata", {})
                ]
                if missing_fields:
                    return {
                        "rule_id": rule["id"],
                        "compliant": False,
                        "message": f"Missing required fields: {', '.join(missing_fields)}"
                    }
            
            # Check content requirements
            if "content_requirements" in rule:
                for req in rule["content_requirements"]:
                    if req["type"] == "contains":
                        pattern = re.compile(req["value"], re.IGNORECASE)
                        if not pattern.search(classification.get("summary", "")):
                            return {
                                "rule_id": rule["id"],
                                "compliant": False,
                                "message": f"Document does not contain required content: {req['value']}"
                            }
            
            return {
                "rule_id": rule["id"],
                "compliant": True,
                "message": "Document complies with all rule requirements"
            }
            
        except Exception as e:
            logger.error(f"Error evaluating rule {rule['id']}: {e}")
            return {
                "rule_id": rule["id"],
                "compliant": False,
                "message": f"Error evaluating rule: {str(e)}"
            }
    
    def get_rules_for_type(self, document_type: str) -> List[Dict]:
        """
        Get all rules applicable to a document type.
        
        Args:
            document_type: Type of document
            
        Returns:
            List of applicable rules
        """
        return [
            rule for rule in self.rules
            if rule["type"] == document_type
        ]
    
    def add_rule(self, rule: Dict) -> None:
        """
        Add a new compliance rule.
        
        Args:
            rule: Rule definition
        """
        try:
            if "id" not in rule or "type" not in rule:
                raise ValueError("Rule must have 'id' and 'type' fields")
            
            # Create rules directory if it doesn't exist
            self.rules_directory.mkdir(parents=True, exist_ok=True)
            
            # Add rule to type-specific file
            rule_file = self.rules_directory / f"{rule['type']}_rules.json"
            
            if rule_file.exists():
                with open(rule_file) as f:
                    rule_set = json.load(f)
            else:
                rule_set = {"rules": []}
            
            # Check for duplicate rule ID
            if any(r["id"] == rule["id"] for r in rule_set["rules"]):
                raise ValueError(f"Rule with ID {rule['id']} already exists")
            
            rule_set["rules"].append(rule)
            
            with open(rule_file, "w") as f:
                json.dump(rule_set, f, indent=2)
            
            # Reload rules
            self.rules = self._load_rules()
            logger.info(f"Added rule {rule['id']} for type {rule['type']}")
            
        except Exception as e:
            logger.error(f"Error adding rule: {e}")
            raise
    
    def remove_rule(self, rule_id: str) -> None:
        """
        Remove a compliance rule.
        
        Args:
            rule_id: ID of the rule to remove
        """
        try:
            removed = False
            
            for rule_file in self.rules_directory.glob("*.json"):
                try:
                    with open(rule_file) as f:
                        rule_set = json.load(f)
                    
                    # Remove rule if found
                    original_count = len(rule_set["rules"])
                    rule_set["rules"] = [
                        r for r in rule_set["rules"]
                        if r["id"] != rule_id
                    ]
                    
                    if len(rule_set["rules"]) < original_count:
                        with open(rule_file, "w") as f:
                            json.dump(rule_set, f, indent=2)
                        removed = True
                        break
                        
                except Exception as e:
                    logger.error(f"Error processing rule file {rule_file}: {e}")
            
            if not removed:
                raise ValueError(f"Rule {rule_id} not found")
            
            # Reload rules
            self.rules = self._load_rules()
            logger.info(f"Removed rule {rule_id}")
            
        except Exception as e:
            logger.error(f"Error removing rule: {e}")
            raise 