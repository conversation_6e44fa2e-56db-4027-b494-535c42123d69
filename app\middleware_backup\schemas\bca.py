from datetime import datetime
from typing import List, Dict, Optional
from uuid import UUID
from pydantic import BaseModel, Field, validator
from app.models.bca import BCRStatus

class BCAMethodologyBase(BaseModel):
    discount_rate: float = Field(..., gt=0, le=1)
    price_level: datetime
    calculation_method: str = "fema_standard"
    special_considerations: Optional[str] = None

class BCAMethodologyCreate(BCAMethodologyBase):
    pass

class BCAMethodologyResponse(BCAMethodologyBase):
    id: UUID
    analysis_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class BCABenefitBase(BaseModel):
    category: str
    annual_value: float = Field(..., gt=0)
    documentation: List[Dict]
    assumptions: str

class BCABenefitCreate(BCABenefitBase):
    pass

class BCABenefitResponse(BCABenefitBase):
    id: UUID
    analysis_id: UUID
    present_value: float
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class BCACostBase(BaseModel):
    category: str
    amount: float = Field(..., gt=0)
    recurring: bool = False
    frequency: Optional[str] = None
    documentation: List[Dict]

class BCACostCreate(BCACostBase):
    pass

class BCACostResponse(BCACostBase):
    id: UUID
    analysis_id: UUID
    present_value: float
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class BCAAnalysisBase(BaseModel):
    project_id: int
    dr_number: str
    project_useful_life: int = Field(..., gt=0)
    discount_rate: float = Field(..., gt=0, le=1)
    price_level: datetime

class BCAAnalysisCreate(BCAAnalysisBase):
    pass

class BCAAnalysisResponse(BaseModel):
    id: UUID
    project_id: int
    dr_number: str
    version: int
    status: BCRStatus
    bcr: float
    npv: float
    project_useful_life: int
    analysis_date: datetime
    created_by_id: int
    approved_by_id: Optional[int]
    created_at: datetime
    updated_at: datetime
    benefits: List[BCABenefitResponse]
    costs: List[BCACostResponse]
    methodology: BCAMethodologyResponse

    class Config:
        from_attributes = True

    @validator("bcr")
    def validate_bcr(cls, v):
        if v < 0:
            raise ValueError("BCR cannot be negative")
        return round(v, 3)
