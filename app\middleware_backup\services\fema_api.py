import httpx
from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.disaster import DisasterDeclaration, DisasterType
from app.models.policy import PolicyVersion
from app.core.config import settings

class FEMAAPIService:
    def __init__(self):
        self.base_url = "https://www.fema.gov/api/open/v1"
        self.headers = {
            "Accept": "application/json",
            "User-Agent": "ComplianceMax/1.0"
        }

    async def fetch_disaster_data(self, dr_number: str) -> Dict:
        """Fetch disaster declaration data from FEMA API."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/DisasterDeclarations/{dr_number}",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

    async def sync_disaster_data(self, dr_number: str, db: AsyncSession) -> DisasterDeclaration:
        """Sync disaster data from FEMA API to local database."""
        fema_data = await self.fetch_disaster_data(dr_number)
        
        stmt = select(DisasterDeclaration).where(DisasterDeclaration.dr_number == dr_number)
        result = await db.execute(stmt)
        disaster = result.scalar_one_or_none()
        
        if not disaster:
            disaster = DisasterDeclaration(
                dr_number=dr_number,
                incident_type=self._map_incident_type(fema_data["incidentType"]),
                incident_period_start=datetime.fromisoformat(fema_data["incidentBeginDate"]),
                incident_period_end=datetime.fromisoformat(fema_data["incidentEndDate"]),
                declaration_date=datetime.fromisoformat(fema_data["declarationDate"]),
                state=fema_data["state"],
                affected_areas=fema_data["designatedAreas"],
                fema_incident_id=fema_data["disasterNumber"]
            )
            db.add(disaster)
        else:
            disaster.incident_type = self._map_incident_type(fema_data["incidentType"])
            disaster.incident_period_start = datetime.fromisoformat(fema_data["incidentBeginDate"])
            disaster.incident_period_end = datetime.fromisoformat(fema_data["incidentEndDate"])
            disaster.declaration_date = datetime.fromisoformat(fema_data["declarationDate"])
            disaster.state = fema_data["state"]
            disaster.affected_areas = fema_data["designatedAreas"]
            disaster.fema_incident_id = fema_data["disasterNumber"]
        
        await db.commit()
        await db.refresh(disaster)
        return disaster

    async def sync_policy_updates(self, db: AsyncSession) -> List[PolicyVersion]:
        """Sync policy updates from FEMA API."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/PolicyDocuments",
                headers=self.headers
            )
            response.raise_for_status()
            policies = response.json()
            
            updated_policies = []
            for policy_data in policies:
                policy_version = await self._update_policy(policy_data, db)
                if policy_version:
                    updated_policies.append(policy_version)
                    
            await db.commit()
            return updated_policies

    async def _update_policy(self, policy_data: Dict, db: AsyncSession) -> Optional[PolicyVersion]:
        """Update a single policy from FEMA API data."""
        stmt = select(PolicyVersion).where(
            PolicyVersion.policy.has(policy_number=policy_data["policyNumber"])
        )
        result = await db.execute(stmt)
        existing_version = result.scalar_one_or_none()
        
        if not existing_version or existing_version.version_number != policy_data["version"]:
            new_version = PolicyVersion(
                version_number=policy_data["version"],
                content=policy_data["content"],
                effective_date=datetime.fromisoformat(policy_data["effectiveDate"]),
                expiration_date=datetime.fromisoformat(policy_data["expirationDate"])
                if policy_data.get("expirationDate") else None,
                document_url=policy_data.get("documentUrl")
            )
            db.add(new_version)
            return new_version
        return None

    def _map_incident_type(self, fema_type: str) -> DisasterType:
        """Map FEMA incident type to internal DisasterType enum."""
        type_mapping = {
            "Hurricane": DisasterType.HURRICANE,
            "Flood": DisasterType.FLOOD,
            "Earthquake": DisasterType.EARTHQUAKE,
            "Tornado": DisasterType.TORNADO,
            "Fire": DisasterType.WILDFIRE
        }
        return type_mapping.get(fema_type, DisasterType.OTHER)
