"""
Logging configuration for ComplianceMax.
"""

import os
import json
import logging
import logging.handlers
from datetime import datetime
from typing import Any, Dict, Optional
from pythonjsonlogger import jsonlogger
from app.core.config import settings
import uuid
import time

class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with additional fields."""

    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]) -> None:
        """Add custom fields to log record."""
        super().add_fields(log_record, record, message_dict)

        # Add timestamp
        log_record['timestamp'] = datetime.utcnow().isoformat()
        log_record['level'] = record.levelname
        log_record['logger'] = record.name

        # Add correlation ID if available
        if hasattr(record, 'correlation_id'):
            log_record['correlation_id'] = record.correlation_id

        # Add request information if available
        if hasattr(record, 'request_id'):
            log_record['request_id'] = record.request_id
        if hasattr(record, 'user_id'):
            log_record['user_id'] = record.user_id
        if hasattr(record, 'ip_address'):
            log_record['ip_address'] = record.ip_address

        # Add exception information if available
        if record.exc_info:
            log_record['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }

class ContextLogger(logging.LoggerAdapter):
    """Logger adapter that adds context information to log records."""

    def process(self, msg: str, kwargs: Dict[str, Any]) -> tuple:
        """Process log record with context."""
        context = self.extra.copy()

        # Update context with request-specific information
        if hasattr(self, 'request'):
            context.update({
                'request_id': getattr(self.request.state, 'request_id', None),
                'user_id': getattr(self.request.state, 'user_id', None),
                'ip_address': getattr(self.request.client, 'host', None)
            })

        # Add context to extra
        kwargs['extra'] = {**kwargs.get('extra', {}), **context}
        return msg, kwargs

def setup_logging(
    log_level: str = settings.LOG_LEVEL,
    log_dir: str = "logs",
    app_name: str = settings.APP_NAME
) -> None:
    """Set up logging configuration."""
    # Create logs directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)

    # Base configuration
    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'json': {
                '()': CustomJsonFormatter,
                'format': '%(timestamp)s %(level)s %(name)s %(message)s'
            },
            'standard': {
                'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'standard',
                'level': log_level
            },
            'file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': os.path.join(log_dir, f'{app_name}.log'),
                'formatter': 'json',
                'maxBytes': 10 * 1024 * 1024,  # 10MB
                'backupCount': 5,
                'level': log_level
            },
            'error_file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': os.path.join(log_dir, f'{app_name}_error.log'),
                'formatter': 'json',
                'maxBytes': 10 * 1024 * 1024,  # 10MB
                'backupCount': 5,
                'level': 'ERROR'
            },
            'audit_file': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'filename': os.path.join(log_dir, f'{app_name}_audit.log'),
                'formatter': 'json',
                'when': 'midnight',
                'interval': 1,
                'backupCount': 30,
                'level': 'INFO'
            }
        },
        'loggers': {
            '': {  # Root logger
                'handlers': ['console', 'file'],
                'level': log_level,
                'propagate': True
            },
            'app': {  # Application logger
                'handlers': ['console', 'file', 'error_file'],
                'level': log_level,
                'propagate': False
            },
            'audit': {  # Audit logger
                'handlers': ['audit_file'],
                'level': 'INFO',
                'propagate': False
            }
        }
    }

    # Apply configuration
    logging.config.dictConfig(logging_config)

def get_logger(name: str, context: Optional[Dict[str, Any]] = None) -> logging.Logger:
    """Get logger with optional context."""
    logger = logging.getLogger(name)

    if context:
        return ContextLogger(logger, context)
    return logger

def get_audit_logger() -> logging.Logger:
    """Get audit logger for security events."""
    return logging.getLogger('audit')
def log_performance_metric(name: str, value: float) -> None:
    """
    Record a performance metric.
    This stub satisfies the import in app.main and can be fleshed out later.
    """
    pass
