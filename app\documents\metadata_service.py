"""Metadata extraction service for documents.

This module provides functionality to extract metadata from documents,
including dates, references, organizations, and other structured information.
"""

import re
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import spacy
from app.core.config import settings

logger = logging.getLogger(__name__)

class MetadataService:
    """Service for extracting metadata from documents."""
    
    def __init__(self):
        """Initialize metadata service."""
        # Lazy load attributes
        self._nlp = None
        
        # Compile regex patterns - these are lightweight and can be initialized immediately
        self.date_pattern = re.compile(settings.METADATA_DATE_PATTERN)  # Updated to use configurable pattern from config
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.url_pattern = re.compile(r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+')
        self.phone_pattern = re.compile(r'\b(?:\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b')
        self.reference_pattern = re.compile(r'\b(?:ref|reference|see):?\s+([A-Za-z0-9\s\-\.]+)', re.IGNORECASE)
    
    @property
    def nlp(self):
        """Lazy load spaCy NLP model."""
        if self._nlp is None:
            logger.info("Initializing spaCy NLP model for metadata extraction")
            self._nlp = spacy.load("en_core_web_sm")
        return self._nlp
    
    async def extract_metadata(self, text_blocks: List[Dict], file_path: str) -> Dict[str, Any]:
        """Extract metadata from text blocks and file information.
        
        Args:
            text_blocks: List of text blocks from OCR
            file_path: Path to the original file
            
        Returns:
            Dictionary of extracted metadata
        """
        try:
            # Get basic file metadata
            path = Path(file_path)
            metadata = {
                "filename": path.name,
                "file_type": path.suffix.lower().replace('.', ''),
                "file_size": path.stat().st_size,
                "created": datetime.fromtimestamp(path.stat().st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(path.stat().st_mtime).isoformat()
            }
            
            # Extract document title (usually in the first few blocks)
            title_candidates = [block["text"] for block in text_blocks[:5] 
                               if len(block["text"]) > 10 and block["confidence"] > 0.8]
            if title_candidates:
                metadata["title"] = title_candidates[0]
            
            # Join all text for entity extraction
            text = " ".join(block["text"] for block in text_blocks)
            
            # Extract dates
            dates = []
            for match in self.date_pattern.finditer(text):
                dates.append(match.group())
            if dates:
                metadata["dates"] = list(set(dates))
            
            # Extract emails
            emails = []
            for match in self.email_pattern.finditer(text):
                emails.append(match.group())
            if emails:
                metadata["emails"] = list(set(emails))
            
            # Extract URLs
            urls = []
            for match in self.url_pattern.finditer(text):
                urls.append(match.group())
            if urls:
                metadata["urls"] = list(set(urls))
            
            # Extract phone numbers
            phones = []
            for match in self.phone_pattern.finditer(text):
                phones.append(match.group())
            if phones:
                metadata["phone_numbers"] = list(set(phones))
            
            # Extract references
            references = []
            for match in self.reference_pattern.finditer(text):
                if match.group(1):
                    references.append(match.group(1).strip())
            if references:
                metadata["references"] = list(set(references))
            
            # Use spaCy for named entity recognition
            doc = self.nlp(text[:10000])  # Limit size for performance
            
            # Extract entities
            entities = {}
            for ent in doc.ents:
                if ent.label_ not in entities:
                    entities[ent.label_] = []
                if ent.text not in entities[ent.label_]:
                    entities[ent.label_].append(ent.text)
            
            # Add specific entity categories to metadata
            if 'ORG' in entities:
                metadata["organizations"] = entities['ORG']
            if 'PERSON' in entities:
                metadata["people"] = entities['PERSON']
            if 'GPE' in entities or 'LOC' in entities:
                locations = entities.get('GPE', []) + entities.get('LOC', [])
                metadata["locations"] = list(set(locations))
            
            # Add all entities
            metadata["entities"] = entities
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata: {str(e)}")
            # Return basic metadata if extraction fails
            return {
                "filename": Path(file_path).name,
                "file_type": Path(file_path).suffix.lower(),
                "extraction_error": str(e)
            }
    
    async def extract_temporal_info(self, text_blocks: List[Dict]) -> Dict[str, Any]:
        """Extract temporal information from text blocks.
        
        Args:
            text_blocks: List of text blocks from OCR
            
        Returns:
            Dictionary of temporal information
        """
        try:
            # Join all text for processing
            text = " ".join(block["text"] for block in text_blocks)
            
            # Process with spaCy for date entities
            doc = self.nlp(text[:10000])  # Limit size for performance
            
            date_entities = []
            for ent in doc.ents:
                if ent.label_ == "DATE" or ent.label_ == "TIME":
                    date_entities.append({
                        "text": ent.text,
                        "type": ent.label_
                    })
            
            # Extract dates with regex
            regex_dates = []
            for match in self.date_pattern.finditer(text):
                regex_dates.append(match.group())
            
            # Find potentially effective dates - looking for pattern like "effective date" nearby
            effective_dates = []
            effective_pattern = re.compile(r'(?:effective|start|commencement|beginning)(?:\s+date)?(?:\s+of)?(?:\s+is)?(?:\s*:)?\s*([A-Za-z0-9\s,]+\d{4})', re.IGNORECASE)
            for match in effective_pattern.finditer(text):
                if match.group(1):
                    effective_dates.append(match.group(1).strip())
            
            # Find expiration dates
            expiration_dates = []
            expiration_pattern = re.compile(r'(?:expiration|end|termination|expiry)(?:\s+date)?(?:\s+of)?(?:\s+is)?(?:\s*:)?\s*([A-Za-z0-9\s,]+\d{4})', re.IGNORECASE)
            for match in expiration_pattern.finditer(text):
                if match.group(1):
                    expiration_dates.append(match.group(1).strip())
            
            return {
                "dates": list(set(regex_dates)),
                "date_entities": date_entities,
                "effective_dates": effective_dates,
                "expiration_dates": expiration_dates
            }
            
        except Exception as e:
            logger.error(f"Error extracting temporal information: {str(e)}")
            return {"dates": [], "extraction_error": str(e)}
    
    async def identify_document_type(self, text_blocks: List[Dict]) -> str:
        """Attempt to identify the document type based on content.
        
        Args:
            text_blocks: List of text blocks from OCR
            
        Returns:
            String indicating the document type
        """
        try:
            # Take first few blocks for document type identification
            intro_text = " ".join([block["text"] for block in text_blocks[:10]])
            intro_text = intro_text.lower()
            
            # Check for common document type indicators
            if any(term in intro_text for term in ["agreement", "contract", "terms"]):
                return "agreement"
            elif any(term in intro_text for term in ["policy", "procedure", "guidelines"]):
                return "policy"
            elif any(term in intro_text for term in ["invoice", "bill", "payment"]):
                return "invoice"
            elif any(term in intro_text for term in ["report", "analysis", "assessment"]):
                return "report"
            elif any(term in intro_text for term in ["memo", "memorandum"]):
                return "memo"
            elif any(term in intro_text for term in ["letter", "correspondence"]):
                return "letter"
            elif any(term in intro_text for term in ["manual", "guide", "handbook"]):
                return "manual"
            elif any(term in intro_text for term in ["form", "application"]):
                return "form"
            else:
                return "unknown"
                
        except Exception as e:
            logger.error(f"Error identifying document type: {str(e)}")
            return "unknown"
