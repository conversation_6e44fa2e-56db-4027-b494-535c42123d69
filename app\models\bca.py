from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy import String, Integer, DateTime, JSON, ForeignKey, Enum, Text, Float, <PERSON>olean
from sqlalchemy.orm import Mapped, mapped_column, relationship
from uuid import UUID, uuid4
from app.core.database import Base
import enum

class BCRStatus(str, enum.Enum):
    DRAFT = "draft"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"

class BCAAnalysis(Base):
    __tablename__ = "bca_analyses"

    id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.id"))
    dr_number: Mapped[str] = mapped_column(String(50), index=True)
    version: Mapped[int] = mapped_column(Integer, default=1)
    status: Mapped[BCRStatus] = mapped_column(Enum(BCRStatus), default=BCRStatus.DRAFT)
    bcr: Mapped[float] = mapped_column(Float)
    npv: Mapped[float] = mapped_column(Float)
    project_useful_life: Mapped[int] = mapped_column(Integer)
    analysis_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    created_by_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    approved_by_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    benefits: Mapped[List["BCABenefit"]] = relationship("BCABenefit", back_populates="analysis", cascade="all, delete-orphan")
    costs: Mapped[List["BCACost"]] = relationship("BCACost", back_populates="analysis", cascade="all, delete-orphan")
    methodology: Mapped["BCAMethodology"] = relationship("BCAMethodology", back_populates="analysis", uselist=False, cascade="all, delete-orphan")
    created_by: Mapped["User"] = relationship("User", foreign_keys=[created_by_id])
    approved_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[approved_by_id])
    project: Mapped["Project"] = relationship("Project", back_populates="bca_analyses")

class BCABenefit(Base):
    __tablename__ = "bca_benefits"

    id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    analysis_id: Mapped[UUID] = mapped_column(ForeignKey("bca_analyses.id"))
    category: Mapped[str] = mapped_column(String(100))
    annual_value: Mapped[float] = mapped_column(Float)
    present_value: Mapped[float] = mapped_column(Float)
    documentation: Mapped[List[Dict]] = mapped_column(JSON, default=list)
    assumptions: Mapped[str] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    analysis: Mapped["BCAAnalysis"] = relationship("BCAAnalysis", back_populates="benefits")

class BCACost(Base):
    __tablename__ = "bca_costs"

    id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    analysis_id: Mapped[UUID] = mapped_column(ForeignKey("bca_analyses.id"))
    category: Mapped[str] = mapped_column(String(100))
    amount: Mapped[float] = mapped_column(Float)
    recurring: Mapped[bool] = mapped_column(Boolean, default=False)
    frequency: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    present_value: Mapped[float] = mapped_column(Float)
    documentation: Mapped[List[Dict]] = mapped_column(JSON, default=list)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    analysis: Mapped["BCAAnalysis"] = relationship("BCAAnalysis", back_populates="costs")

class BCAMethodology(Base):
    __tablename__ = "bca_methodologies"

    id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    analysis_id: Mapped[UUID] = mapped_column(ForeignKey("bca_analyses.id"))
    discount_rate: Mapped[float] = mapped_column(Float)
    price_level: Mapped[datetime] = mapped_column(DateTime)
    calculation_method: Mapped[str] = mapped_column(String(50))
    special_considerations: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    analysis: Mapped["BCAAnalysis"] = relationship("BCAAnalysis", back_populates="methodology")
