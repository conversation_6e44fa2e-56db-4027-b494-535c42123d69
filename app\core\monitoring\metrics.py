"""
Metrics collection middleware and utilities for the ComplianceMax application.
"""
from prometheus_client import Counter, Histogram, Gauge, Info
from prometheus_client.exposition import generate_latest
from prometheus_client.core import CollectorRegistry
from typing import Dict, Any
import time
import psutil
import os

# Create a registry for our custom metrics
registry = CollectorRegistry()

# Request metrics
http_requests_total = Counter(
    'http_requests_total',
    'Total number of HTTP requests',
    ['method', 'endpoint', 'status'],
    registry=registry
)

http_request_duration_seconds = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint'],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0],
    registry=registry
)

# Business metrics
compliance_checks_total = Counter(
    'compliance_checks_total',
    'Total number of compliance checks performed',
    ['status'],
    registry=registry
)

document_processing_duration_seconds = Histogram(
    'document_processing_duration_seconds',
    'Document processing duration in seconds',
    ['document_type'],
    buckets=[1.0, 5.0, 10.0, 30.0, 60.0],
    registry=registry
)

# System metrics
cpu_usage_percent = Gauge(
    'cpu_usage_percent',
    'CPU usage percentage',
    registry=registry
)

memory_usage_percent = Gauge(
    'memory_usage_percent',
    'Memory usage percentage',
    registry=registry
)

# Cache metrics
cache_hits_total = Counter(
    'cache_hits_total',
    'Total number of cache hits',
    registry=registry
)

cache_misses_total = Counter(
    'cache_misses_total',
    'Total number of cache misses',
    registry=registry
)

# Security metrics
security_incidents_total = Counter(
    'security_incidents_total',
    'Total number of security incidents',
    ['incident_type'],
    registry=registry
)

rate_limit_exceeded_total = Counter(
    'rate_limit_exceeded_total',
    'Total number of rate limit exceeded events',
    ['endpoint'],
    registry=registry
)

# Database metrics
db_connections_active = Gauge(
    'db_connections_active',
    'Number of active database connections',
    registry=registry
)

db_query_duration_seconds = Histogram(
    'db_query_duration_seconds',
    'Database query duration in seconds',
    ['query_type'],
    buckets=[0.01, 0.05, 0.1, 0.5, 1.0],
    registry=registry
)

# Application info
app_info = Info(
    'compliancemax_info',
    'ComplianceMax application information',
    registry=registry
)

class MetricsMiddleware:
    """Middleware for collecting request metrics."""
    
    def __init__(self, app):
        self.app = app
        # Set application info
        app_info.info({
            'version': os.getenv('APP_VERSION', 'unknown'),
            'environment': os.getenv('APP_ENV', 'development')
        })
    
    async def __call__(self, scope, receive, send):
        if scope['type'] != 'http':
            return await self.app(scope, receive, send)
        
        path = scope['path']
        method = scope['method']
        
        # Skip metrics endpoint to avoid recursion
        if path == '/metrics':
            return await self.app(scope, receive, send)
        
        start_time = time.time()
        status_code = 500  # Default to error unless explicitly set
        
        try:
            # Create a wrapper for the send function to capture the status code
            original_send = send
            
            async def send_wrapper(message):
                nonlocal status_code
                if message['type'] == 'http.response.start':
                    status_code = message['status']
                return await original_send(message)
            
            # Call the application with our wrapped send function
            await self.app(scope, receive, send_wrapper)
            
        except Exception as e:
            status_code = 500
            raise e
        
        finally:
            # Record request metrics
            duration = time.time() - start_time
            http_requests_total.labels(
                method=method,
                endpoint=path,
                status=status_code
            ).inc()
            
            http_request_duration_seconds.labels(
                method=method,
                endpoint=path
            ).observe(duration)
            
            # Update system metrics
            self._update_system_metrics()
    
    def _update_system_metrics(self):
        """Update system metrics."""
        try:
            cpu_usage_percent.set(psutil.cpu_percent())
            memory_usage_percent.set(psutil.virtual_memory().percent)
        except Exception:
            # Log error but don't fail the request
            pass

def track_compliance_check(status: str):
    """Track a compliance check execution."""
    compliance_checks_total.labels(status=status).inc()

def track_document_processing(document_type: str, duration: float):
    """Track document processing duration."""
    document_processing_duration_seconds.labels(
        document_type=document_type
    ).observe(duration)

def track_cache_operation(hit: bool):
    """Track cache hit/miss."""
    if hit:
        cache_hits_total.inc()
    else:
        cache_misses_total.inc()

def track_security_incident(incident_type: str):
    """Track security incident."""
    security_incidents_total.labels(incident_type=incident_type).inc()

def track_rate_limit(endpoint: str):
    """Track rate limit exceeded event."""
    rate_limit_exceeded_total.labels(endpoint=endpoint).inc()

def track_db_connection(active_connections: int):
    """Track active database connections."""
    db_connections_active.set(active_connections)

def track_db_query(query_type: str, duration: float):
    """Track database query duration."""
    db_query_duration_seconds.labels(query_type=query_type).observe(duration)

async def metrics_endpoint(request):
    """Endpoint to expose metrics for Prometheus scraping."""
    metrics_data = generate_latest(registry)
    return Response(
        content=metrics_data,
        media_type='text/plain; version=0.0.4; charset=utf-8'
    ) 