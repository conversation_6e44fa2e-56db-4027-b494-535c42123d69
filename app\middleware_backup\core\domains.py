from enum import Enum
from typing import List, Dict, Optional
from pydantic import BaseModel
from fastapi import Request, HTTPException

class InterfaceType(str, Enum):
    MAIN = "main"
    QUICK_CHECK = "quick_check"
    COMPLIANCE = "compliance"
    TECHNICAL = "technical"

class DomainConfig(BaseModel):
    domain: str
    interface: InterfaceType
    title: str
    description: str
    features: List[str]
    allowed_paths: List[str]

class DomainManager:
    """Manages domain-specific configurations and routing"""
    
    def __init__(self):
        self.domain_configs: Dict[str, DomainConfig] = {
            "disastermax.com": DomainConfig(
                domain="disastermax.com",
                interface=InterfaceType.MAIN,
                title="DisasterMax",
                description="FEMA Compliance Management System",
                features=["All Services", "Dashboard", "System Integration"],
                allowed_paths=["/api/v1/auth", "/api/v1/documents"]
            ),
            "pa-check.com": DomainConfig(
                domain="pa-check.com",
                interface=InterfaceType.QUICK_CHECK,
                title="PA Quick Check",
                description="Rapid compliance validation tool",
                features=["Quick Checks", "Document Validation", "Policy Lookup"],
                allowed_paths=["/api/v1/check"]
            ),
            "pa-compliance.com": DomainConfig(
                domain="pa-compliance.com",
                interface=InterfaceType.COMPLIANCE,
                title="PA Compliance Portal",
                description="Comprehensive compliance management",
                features=["Full Reviews", "Audit Trails", "Document Management"],
                allowed_paths=["/api/v1/compliance"]
            ),
            "maxcompliance.net": DomainConfig(
                domain="maxcompliance.net",
                interface=InterfaceType.TECHNICAL,
                title="ComplianceMax API",
                description="Technical integration portal",
                features=["API Access", "Documentation", "Integration Tools"],
                allowed_paths=["/api/v1/flood-maps", "/api/v1/bca"]
            )
        }

    def get_domain_config(self, domain: str) -> Optional[DomainConfig]:
        """Get configuration for a specific domain"""
        return self.domain_configs.get(domain)

    def get_interface(self, request: Request) -> InterfaceType:
        """Get interface type based on request host"""
        host = request.headers.get("host", "").split(":")[0]
        config = self.get_domain_config(host)
        return config.interface if config else InterfaceType.MAIN

    def validate_path_access(self, request: Request) -> bool:
        """Validate if the requested path is allowed for the domain"""
        host = request.headers.get("host", "").split(":")[0]
        config = self.get_domain_config(host)
        
        if not config:
            return True  # Allow access on unknown domains (will be handled by main interface)
            
        path = request.url.path
        return any(path.startswith(allowed_path) for allowed_path in config.allowed_paths)

    def get_landing_page_info(self, request: Request) -> Dict:
        """Get landing page information based on domain"""
        interface = self.get_interface(request)
        host = request.headers.get("host", "").split(":")[0]
        config = self.get_domain_config(host)
        
        if config:
            return {
                "name": config.title,
                "description": config.description,
                "features": config.features
            }
        
        # Default response for unknown domains
        return {
            "name": "DisasterMax",
            "description": "FEMA Compliance Management System",
            "features": ["All Services", "Dashboard", "System Integration"]
        }

# Create global instance
domain_manager = DomainManager()
