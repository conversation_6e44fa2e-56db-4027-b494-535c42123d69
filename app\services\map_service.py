from sqlalchemy.orm import Session
from app.models.mapping import GeoLocation, FloodZone, RiskMap
from app.schemas.mapping import Location<PERSON><PERSON>, FloodZoneCreate, RiskMapCreate
from datetime import datetime
from geoalchemy2.shape import from_shape
from shapely.geometry import Point, Polygon

class MapService:
    def __init__(self, db: Session):
        self.db = db

    def create_location(self, location: LocationCreate) -> GeoLocation:
        point = Point(location.longitude, location.latitude)
        db_location = GeoLocation(
            project_id=location.project_id,
            location_type=location.location_type,
            coordinates=from_shape(point, srid=4326),
            address=location.address
        )
        self.db.add(db_location)
        self.db.commit()
        self.db.refresh(db_location)
        return db_location

    def create_flood_zone(self, flood_zone: FloodZoneCreate) -> FloodZone:
        polygon = Polygon(flood_zone.coordinates)
        db_flood_zone = FloodZone(
            zone_code=flood_zone.zone_code,
            description=flood_zone.description,
            risk_level=flood_zone.risk_level,
            geometry=from_shape(polygon, srid=4326)
        )
        self.db.add(db_flood_zone)
        self.db.commit()
        self.db.refresh(db_flood_zone)
        return db_flood_zone

    def create_risk_map(self, risk_map: RiskMapCreate) -> RiskMap:
        db_risk_map = RiskMap(
            name=risk_map.name,
            description=risk_map.description,
            map_data=risk_map.map_data,
            last_updated=datetime.utcnow(),
            risk_level=risk_map.risk_level
        )
        self.db.add(db_risk_map)
        self.db.commit()
        self.db.refresh(db_risk_map)
        return db_risk_map

    def get_flood_zones(self) -> list[FloodZone]:
        return self.db.query(FloodZone).all()

    def get_risk_maps(self) -> list[RiskMap]:
        return self.db.query(RiskMap).all()

    def get_locations_by_project(self, project_id: int) -> list[GeoLocation]:
        return self.db.query(GeoLocation).filter(GeoLocation.project_id == project_id).all()
