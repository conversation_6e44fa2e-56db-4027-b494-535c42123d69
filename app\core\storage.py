"""
Storage service for ComplianceMax.
Implements document storage with multiple backend support.
"""

import os
from typing import Binary<PERSON>, Dict, Any, Optional
from pathlib import Path
import boto3
import aiofiles
from azure.storage.blob import BlobServiceClient
import json
from datetime import datetime
import hashlib
from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import track_performance
from app.core.exceptions import DocumentProcessingError

logger = get_logger(__name__)

class StorageBackend:
    """Base class for storage backends."""
    
    async def save(self, file_path: str, metadata: Dict[str, Any]) -> str:
        """Save file to storage and return document ID."""
        raise NotImplementedError
    
    async def get(self, document_id: str) -> Dict[str, Any]:
        """Get document content and metadata."""
        raise NotImplementedError
    
    async def delete(self, document_id: str) -> None:
        """Delete document from storage."""
        raise NotImplementedError
    
    async def list_documents(self, prefix: str = "") -> List[Dict[str, Any]]:
        """List documents in storage."""
        raise NotImplementedError

class LocalStorage(StorageBackend):
    """Local filesystem storage backend."""
    
    def __init__(self):
        """Initialize local storage."""
        self.storage_path = Path(settings.LOCAL_STORAGE_PATH)
        self.storage_path.mkdir(exist_ok=True)
        self.metadata_path = self.storage_path / "metadata"
        self.metadata_path.mkdir(exist_ok=True)
    
    async def save(self, file_path: str, metadata: Dict[str, Any]) -> str:
        """Save file to local storage."""
        try:
            # Generate document ID
            document_id = self._generate_id(file_path)
            target_path = self.storage_path / document_id
            
            # Copy file
            async with aiofiles.open(file_path, 'rb') as source:
                content = await source.read()
                async with aiofiles.open(target_path, 'wb') as target:
                    await target.write(content)
            
            # Save metadata
            metadata.update({
                "document_id": document_id,
                "storage_path": str(target_path),
                "created_at": datetime.now().isoformat()
            })
            
            metadata_path = self.metadata_path / f"{document_id}.json"
            async with aiofiles.open(metadata_path, 'w') as f:
                await f.write(json.dumps(metadata))
            
            return document_id
            
        except Exception as e:
            logger.error(f"Local storage save failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message="Failed to save document to local storage",
                details={"file_path": file_path}
            )
    
    async def get(self, document_id: str) -> Dict[str, Any]:
        """Get document from local storage."""
        try:
            # Get metadata
            metadata_path = self.metadata_path / f"{document_id}.json"
            async with aiofiles.open(metadata_path, 'r') as f:
                metadata = json.loads(await f.read())
            
            # Get content
            file_path = metadata["storage_path"]
            async with aiofiles.open(file_path, 'rb') as f:
                content = await f.read()
            
            return {
                "content": content,
                "metadata": metadata
            }
            
        except FileNotFoundError:
            raise DocumentProcessingError(
                message="Document not found",
                details={"document_id": document_id}
            )
        except Exception as e:
            logger.error(f"Local storage get failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message="Failed to get document from local storage",
                details={"document_id": document_id}
            )
    
    async def delete(self, document_id: str) -> None:
        """Delete document from local storage."""
        try:
            # Get metadata
            metadata_path = self.metadata_path / f"{document_id}.json"
            async with aiofiles.open(metadata_path, 'r') as f:
                metadata = json.loads(await f.read())
            
            # Delete file and metadata
            os.remove(metadata["storage_path"])
            os.remove(metadata_path)
            
        except FileNotFoundError:
            raise DocumentProcessingError(
                message="Document not found",
                details={"document_id": document_id}
            )
        except Exception as e:
            logger.error(f"Local storage delete failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message="Failed to delete document from local storage",
                details={"document_id": document_id}
            )
    
    async def list_documents(self, prefix: str = "") -> List[Dict[str, Any]]:
        """List documents in local storage."""
        try:
            documents = []
            for metadata_file in self.metadata_path.glob(f"{prefix}*.json"):
                async with aiofiles.open(metadata_file, 'r') as f:
                    metadata = json.loads(await f.read())
                    documents.append(metadata)
            return documents
            
        except Exception as e:
            logger.error(f"Local storage list failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message="Failed to list documents in local storage"
            )
    
    def _generate_id(self, file_path: str) -> str:
        """Generate unique document ID."""
        timestamp = datetime.now().isoformat()
        content = f"{file_path}_{timestamp}"
        return hashlib.sha256(content.encode()).hexdigest()[:32]

class S3Storage(StorageBackend):
    """AWS S3 storage backend."""
    
    def __init__(self):
        """Initialize S3 client."""
        self.s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )
        self.bucket = settings.STORAGE_BUCKET
    
    async def save(self, file_path: str, metadata: Dict[str, Any]) -> str:
        """Save file to S3."""
        try:
            # Generate document ID
            document_id = self._generate_id(file_path)
            
            # Upload file
            with open(file_path, 'rb') as f:
                self.s3.upload_fileobj(
                    f,
                    self.bucket,
                    f"documents/{document_id}"
                )
            
            # Save metadata
            metadata.update({
                "document_id": document_id,
                "bucket": self.bucket,
                "created_at": datetime.now().isoformat()
            })
            
            self.s3.put_object(
                Bucket=self.bucket,
                Key=f"metadata/{document_id}.json",
                Body=json.dumps(metadata)
            )
            
            return document_id
            
        except Exception as e:
            logger.error(f"S3 storage save failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message="Failed to save document to S3",
                details={"file_path": file_path}
            )
    
    async def get(self, document_id: str) -> Dict[str, Any]:
        """Get document from S3."""
        try:
            # Get metadata
            metadata_obj = self.s3.get_object(
                Bucket=self.bucket,
                Key=f"metadata/{document_id}.json"
            )
            metadata = json.loads(metadata_obj['Body'].read())
            
            # Get content
            content_obj = self.s3.get_object(
                Bucket=self.bucket,
                Key=f"documents/{document_id}"
            )
            content = content_obj['Body'].read()
            
            return {
                "content": content,
                "metadata": metadata
            }
            
        except self.s3.exceptions.NoSuchKey:
            raise DocumentProcessingError(
                message="Document not found",
                details={"document_id": document_id}
            )
        except Exception as e:
            logger.error(f"S3 storage get failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message="Failed to get document from S3",
                details={"document_id": document_id}
            )
    
    async def delete(self, document_id: str) -> None:
        """Delete document from S3."""
        try:
            # Delete file and metadata
            self.s3.delete_object(
                Bucket=self.bucket,
                Key=f"documents/{document_id}"
            )
            self.s3.delete_object(
                Bucket=self.bucket,
                Key=f"metadata/{document_id}.json"
            )
            
        except self.s3.exceptions.NoSuchKey:
            raise DocumentProcessingError(
                message="Document not found",
                details={"document_id": document_id}
            )
        except Exception as e:
            logger.error(f"S3 storage delete failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message="Failed to delete document from S3",
                details={"document_id": document_id}
            )
    
    async def list_documents(self, prefix: str = "") -> List[Dict[str, Any]]:
        """List documents in S3."""
        try:
            documents = []
            paginator = self.s3.get_paginator('list_objects_v2')
            
            async for page in paginator.paginate(
                Bucket=self.bucket,
                Prefix=f"metadata/{prefix}"
            ):
                for obj in page.get('Contents', []):
                    response = self.s3.get_object(
                        Bucket=self.bucket,
                        Key=obj['Key']
                    )
                    metadata = json.loads(response['Body'].read())
                    documents.append(metadata)
            
            return documents
            
        except Exception as e:
            logger.error(f"S3 storage list failed: {str(e)}", exc_info=True)
            raise DocumentProcessingError(
                message="Failed to list documents in S3"
            )
    
    def _generate_id(self, file_path: str) -> str:
        """Generate unique document ID."""
        timestamp = datetime.now().isoformat()
        content = f"{file_path}_{timestamp}"
        return hashlib.sha256(content.encode()).hexdigest()[:32]

class StorageService:
    """Main storage service class."""
    
    def __init__(self):
        """Initialize storage backend based on configuration."""
        storage_type = settings.STORAGE_TYPE.lower()
        
        if storage_type == "s3":
            self.backend = S3Storage()
        elif storage_type == "azure":
            # Azure implementation would go here
            raise NotImplementedError("Azure storage not implemented")
        else:
            self.backend = LocalStorage()
    
    @track_performance("storage_operation")
    async def save_document(
        self,
        file_path: str,
        metadata: Dict[str, Any]
    ) -> str:
        """Save document to storage."""
        return await self.backend.save(file_path, metadata)
    
    @track_performance("storage_operation")
    async def get_document(
        self,
        document_id: str
    ) -> Dict[str, Any]:
        """Get document from storage."""
        return await self.backend.get(document_id)
    
    @track_performance("storage_operation")
    async def delete_document(
        self,
        document_id: str
    ) -> None:
        """Delete document from storage."""
        await self.backend.delete(document_id)
    
    @track_performance("storage_operation")
    async def list_documents(
        self,
        prefix: str = ""
    ) -> List[Dict[str, Any]]:
        """List documents in storage."""
        return await self.backend.list_documents(prefix) 