<!DOCTYPE html>
<html lang="en" dir="ltr">
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta content="Gmail is available across all your devices Android, iOS, and desktop devices. Sort, collaborate or call a friend without leaving your inbox."
  name="description">
<meta property="og:type" content="website" />
<meta property="og:url" content="http://www.google.com/gmail/about/" />
<meta property="og:title" content="Gmail - Free Storage and Email from Google" />
<meta property="og:image" content="//www.google.com/gmail/about/static/images/social.jpg" />
<meta property="og:site_name" content="Gmail - Free Storage and Email from Google" />
<link href="https://www.google.com/gmail/about/policy/" rel="canonical">

<head>
  <link href="/gmail/about/images/favicon.ico" rel="icon" type="image/ico">
  <title>Gmail - Free Storage and Email from Google</title>
  <link rel="stylesheet" href="/gmail/about/static/css/main.min.css" nonce="th1xZY9sBbqZsEUeq1eZvw" />
<style>

.feature-highlight-content__desktop {
  background-image: url(/gmail/about/images/bettercompose.jpg)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .feature-highlight-content__desktop {
    background-image: url(/gmail/about/images/bettercompose_2x.jpg)
  }
}

.hero_home__image {
  background-image: url(/gmail/about/images/home-hero_2x.jpg)
}

.hero_for-work__image {
  background-image: url(/gmail/about/images/for-work-hero_2x.jpg)
}

@media only screen and (max-width: 777px) {
  .hero_home__image {
    background-image: url(/gmail/about/images/hero-mobile.jpg)
  }
}

@media only screen and (max-width: 777px) and (-webkit-min-device-pixel-ratio: 2), only screen and (max-width: 777px) and (min-resolution: 2dppx) {
  .hero_home__image {
    background-image: url(/gmail/about/images/hero-mobile_2x.jpg)
  }
}

.inbox-action__image {
  background-image: url(/gmail/about/images/action-4_2x.png)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .inbox-action__image {
    background-image: url(/gmail/about/images/action-4_2x.png)
  }
}

@media only screen and (max-width: 777px) {
  .inbox-action__image {
    background-image: url(/gmail/about/images/take-action-mobile_2x.jpg)
  }
}

.themes-for-gmail .theme1 {
  background-image: url(/gmail/about/images/themes1.jpg)
}

.themes-for-gmail .theme2 {
  background-image: url(/gmail/about/images/themes2.jpg)
}

.themes-for-gmail .theme3 {
  background-image: url(/gmail/about/images/themes3.jpg)
}

.themes-for-gmail .theme4 {
  background-image: url(/gmail/about/images/themes4.jpg)
}

@media (max-width: 777px) {
  .attachments-for-gmail__screen-feature-attachments {
    background-image: url(/gmail/about/images/attachments-tablet.png)
  }
}

.attachments-for-gmail__screen-feature-attachments {
  background-image: url(/gmail/about/images/attachments-tablet_2x.png)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .attachments-for-gmail__screen-feature-attachments {
    background-image: url(/gmail/about/images/attachments-tablet_2x.png)
  }
}

.inbox-tabs-for-gmail__image {
  background-image: url(/gmail/about/images/inboxtabs.gif)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .inbox-tabs-for-gmail__image {
    background-image: url(/gmail/about/images/inboxtabs_2x.jpg)
  }
}

.for-work-feature-highlights__block__card:nth-of-type(1) .for-work-feature-highlights__block__card-tinyhero {
  background-image: url(/gmail/about/images/branded.png)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .for-work-feature-highlights__block__card:nth-of-type(1) .for-work-feature-highlights__block__card-tinyhero {
    background-image: url(/gmail/about/images/branded_2x.png)
  }
}

.for-work-feature-highlights__block__card:nth-of-type(2) .for-work-feature-highlights__block__card-tinyhero {
  background-image: url(/gmail/about/images/workfromnewhere.png)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .for-work-feature-highlights__block__card:nth-of-type(2) .for-work-feature-highlights__block__card-tinyhero {
    background-image: url(/gmail/about/images/workfromnewhere_2x.png)
  }
}

.for-work-feature-highlights__block__card:nth-of-type(3) .for-work-feature-highlights__block__card-tinyhero {
  background-image: url(/gmail/about/images/techsupport.png)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .for-work-feature-highlights__block__card:nth-of-type(3) .for-work-feature-highlights__block__card-tinyhero {
    background-image: url(/gmail/about/images/techsupport_2x.png)
  }
}

.for-work-feature-highlights__block__card:nth-of-type(4) .for-work-feature-highlights__block__card-tinyhero {
  background-image: url(/gmail/about/images/hangout.png)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .for-work-feature-highlights__block__card:nth-of-type(4) .for-work-feature-highlights__block__card-tinyhero {
    background-image: url(/gmail/about/images/hangout_2x.png)
  }
}

.hero_for-work__image {
  background-image: url(/gmail/about/images/for-work-hero.jpg)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .hero_for-work__image {
    background-image: url(/gmail/about/images/for-work-hero_2x.jpg)
  }
}

.gmail-devices__images-desktop {
  background-image: url(/gmail/about/images/any-device.jpg)
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .gmail-devices__images-desktop {
    background-image: url(/gmail/about/images/any-device_2x.jpg)
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 2dppx) {
  .gmail-devices__copy-play {
    background-image: url(/gmail/about/images/playstore.png)
  }
  .gmail-devices__copy-app {
    background-image: url(/gmail/about/images/appstore.svg)
  }
}

@media screen and (max-width: 777px) {
  .gmail-devices__images-mobile {
    background-image: url(/gmail/about/images/any-device-mobile.jpg)
  }
}

@media only screen and (max-width: 777px) and (-webkit-min-device-pixel-ratio: 2), only screen and (max-width: 777px) and (min-resolution: 2dppx) {
  .gmail-devices__images-mobile {
    background-image: url(/gmail/about/images/any-device-mobile_2x.jpg)
  }
}

</style>
  <script src="//www.google.com/js/google.js" nonce="th1xZY9sBbqZsEUeq1eZvw"></script>
  <script nonce="th1xZY9sBbqZsEUeq1eZvw">
    new gweb.analytics.AutoTrack({ profile: "UA-992684-1" });
  </script>
  <script type="application/ld+json" nonce="th1xZY9sBbqZsEUeq1eZvw">
    {
      "@context": "http://schema.org",
      "@type": "Product",
      "brand": "Google Inc.",
      "name": "Gmail",
      "description": "Gmail is available across all your devices Android, iOS, and desktop devices. Sort, collaborate or call a friend without leaving your inbox.",
      "url": "https://www.google.com/gmail/",
      "logo": "https://www.google.com/images/icons/product/googlemail-128.png",
      "sameAs": [
        "https://twitter.com/gmail",
        "https://gmail.googleblog.com/",
        "https://www.youtube.com/channel/UCbnagt1TEDo9U0yihgKNJ0g"
      ]
    }
  </script>
</head>

<body class="policy-body ltr">
<nav class="gmail-nav standard scrolled loaded" data-ng-controller="NavController as navCtrl">
  <a class="gmail-nav__home-link" href="../"><div class="gmail-nav__logo gmail-logo" ng-click="navCtrl.trackNavClick('Navigation', 'home')">Gmail</div></a>
  <div class="gmail-nav__nav-links-wrap">
      <a
        class="gmail-nav__nav-link gmail-nav__nav-link__sign-in"
        ng-click="navCtrl.trackNavClick('Navigation', 'sign in')"
        data-g-label="Sign in"        href="https://accounts.google.com/AccountChooser?service=mail&continue=https://mail.google.com/mail/"
        rel="nofollow"
        tabindex="1"
      >Sign In</a>
      <a
        class="gmail-nav__nav-link gmail-nav__nav-link__create-account"
        ng-click="navCtrl.trackNavClick('Create Account', 'desktop')"
        data-g-label="Create an account button"        href="https://accounts.google.com/SignUp?service=mail&continue=https://mail.google.com/mail/?pc=topnav-about-en"
        target="_blank"
        rel="nofollow"
        tabindex="2"
      >Create an account</a>
      <a
        class="gmail-nav__nav-link gmail-nav__nav-link__get-gmail"
        ng-click="navCtrl.trackNavClick('Create Account', 'mobile')"
        data-g-label="Create an account button"        href="https://accounts.google.com/SignUp?service=mail&continue=https://mail.google.com/mail/?pc=topnav-about-en"
        target="_blank"
        data-page-location="web_about-bar"
        rel="nofollow"
        tabindex="2"
      >Get Gmail</a>
  </div>
</nav>
  <main>
<section>

  <section class="policy">
    <div class="policy__content">
      <h2>Gmail Program Policies</h2>
      <p><p>The Program Policies below apply to Gmail. The policies play an important role in maintaining a positive experience for everyone using Gmail.</p>
<p>If you use Gmail with a consumer (e.g., @gmail.com) account, please also refer to <a href="https://www.google.com/policies/terms/">Google’s Terms of Service</a> for more information. If you’re using an account through work, school, or another organization, terms may apply based on your organization’s agreement with Google or other policies. Your <a href="https://support.google.com/a/answer/6208960?hl=en">administrator</a> may be able to provide more information.</p>
<p>We need to curb abuses that threaten our ability to provide these services, and we ask that everyone abide by the policies below to help us achieve this goal. After we are notified of a potential policy violation, we may review the content and take action, including limiting or terminating a user’s access to Google products. If your account is disabled, and you believe it was a mistake, please follow the instructions on <a href="https://support.google.com/accounts/answer/40695">this page</a>.</p>
<p>We may take action on accounts that go above storage quota limits. For example, we may prohibit the sending or receiving of messages if you exceed your storage quota. We may also delete content from your account if you fail to reduce your storage or fail to obtain sufficient additional storage. Read more about storage quotas <a href="https://support.google.com/mail/answer/9312312#changes">here</a>.</p>
<p>Be sure to check back from time to time, as these policies may change.</p></p>
      <h3>Report abuse</h3>
        <p>If you believe an account has violated our Program Policies, there are multiple ways to report it:</p>
          <ul>
            <li>For general abuse use <a href="https://support.google.com/mail/contact/abuse?rd=1">this form</a></li></li>
            <li>For child grooming use <a href="https://support.google.com/families/contact/report_child_grooming">this form</a></li></li>
            <li>For copyright violations use <a href="https://support.google.com/legal/contact/lr_dmca?product=gmail">this form</a></li></li>
          </ul>
        <p>Make sure to read the policies below to understand how we define abusive behavior. Google may disable accounts that are found to be in violation of our policies. If your account is disabled, and you believe it was a mistake, please follow the instructions on this page.</p>
      <h3>Account Inactivity</h3>
        <p>Use the product to remain active. Activity includes accessing the product or its content at least every 2 years. We may take action on inactive accounts, which may include deleting your messages from the product. Read more <a href="https://support.google.com/mail/answer/9312312#activity">here</a>.</p>
      <h3>Spam and Bulk Mail</h3>
        <p>Don’t use Gmail to distribute spam or unsolicited commercial mail.</p>
        <p>You are not allowed to use Gmail to send email in violation of the CAN-SPAM Act or other anti-spam laws; to send unauthorized email via open, third-party servers; or to distribute the email addresses of any person without their consent.</p>
        <p>You are not allowed to automate the Gmail interface, whether to send, delete, or filter emails, in a manner that misleads or deceives users.</p>
        <p>Please keep in mind that your definition of “unsolicited” or “unwanted” mail may differ from your email recipients’ perception. Exercise judgment when sending email to a large number of recipients, even if the recipients elected to receive emails from you in the past. When Gmail users mark emails as spam, it increases the likelihood that future messages you send will also be classified as spam by our anti-abuse systems.</p>
      <h3>The Creation and Use of Multiple Gmail Accounts</h3>
        <p>Don’t create or use multiple accounts to abuse Google policies, bypass Gmail account limitations, circumvent filters, or otherwise subvert restrictions placed on your account. (For example, if you’ve been blocked by another user or have had your Gmail account disabled due to abuse, don’t create a replacement account that engages in similar activity.)</p>
        <p>You’re also not allowed to create Gmail accounts by automated means or buy, sell, trade, or re-sell Gmail accounts to others.</p>
      <h3>Malware</h3>
        <p>Don’t use Gmail to transmit viruses, malware, worms, defects, Trojan horses, corrupted files, or any other items of a destructive or deceptive nature. In addition, don’t distribute content that harms or interferes with the operation of networks, servers, or other infrastructure belonging to Google or others.</p>
      <h3>Fraud, Phishing, and other Deceptive Practices</h3>
        <p>You may not access another user’s Gmail account without their explicit permission.</p>
        <p>Don’t use Gmail for phishing. Refrain from soliciting or collecting sensitive data, including but not limited to passwords, financial details, and Social Security numbers.</p>
        <p>Don’t send messages to trick, mislead, or deceive other users into sharing information under false pretenses. This includes impersonating another person, company, or entity with the intent to deceive or mislead.</p>
      <h3>Child Safety</h3>
        <p>Google has a zero-tolerance policy against child sexual abuse imagery. If we become aware of such content, we will report it to the National Center for Missing and Exploited Children as required by law. We may also take disciplinary action, including termination, against the Gmail accounts of those involved.</p>
        <p>Google prohibits the grooming of children using Gmail, defined as a set of actions aimed at establishing a connection with a child to lower the child's inhibitions in preparation for sexual abuse, trafficking, or other exploitation.</p>
        <p>If you believe a child is in danger of or has been subject to abuse, exploitation, or been trafficked, contact your local law enforcement immediately.</p>
        <p>If you have already made a report to law enforcement and still need help, or you have concerns a child is being or was endangered using Gmail, you can report the behavior to Google using <a href="https://support.google.com/families/contact/report_child_grooming">this form</a>. Please remember that you can always block any person you do not want to be contacted by on Gmail.</p>
      <h3>Copyright</h3>
        <p>Respect copyright laws. Don’t infringe on the intellectual property rights of others, including patent, trademark, trade secret, or other proprietary rights. You are also not allowed to encourage or induce others to violate intellectual property rights. You can report copyright infringement to Google by using <a href="https://support.google.com/legal/contact/lr_dmca?product=gmail">this form</a>.</p>
      <h3>Harassment</h3>
        <p>Don’t use Gmail to harass, intimidate or threaten others. Anyone found to be using Gmail for these purposes may have their account disabled.</p>
      <h3>Illegal Activity</h3>
        <p>Keep it legal. Don’t use Gmail to promote, organize, or engage in unlawful activities.</p>

    </div>
  </section>

</section>

<script type="text/ng-template" id="step-scroller.html" nonce="th1xZY9sBbqZsEUeq1eZvw">
<div class="step-scroller">
  <div class="dot" ng-click="scrollClick()" ng-class="{boost: (nextCount() === 0 && allLoaded()), loaded: allLoaded()}">
    <i id="scroll-caret" class="caret" ng-class="{open: nextCount() === scrollTargetCount() - 1, closed: nextCount() !== scrollTargetCount() - 1}">&nbsp;</i>
  </div>
</div></script>   </main>
 <footer>
  <div class="standard-footer">
    <div class="standard-footer__bar">

    </div>
    <div class="standard-footer__signature"></div>
    <div class="standard-footer__bar__social-icons">
      <a href="https://gmail.googleblog.com/" class="link-blogger" target="_blank"></a>
      <a href="https://www.facebook.com/Gmail" class="link-face" target="_blank"></a>
      <a href="https://twitter.com/gmail" class="link-twitter" target="_blank"></a>
    </div>
    <div class="standard-footer__links">
      <a href="/gmail/about/policy/" data-g-action="Our policy" data-g-event="Site Nav" data-g-label="about.html">Our policy</a>
      <a href="https://support.google.com/mail/" data-g-action="Help" data-g-event="Site Nav" data-g-label="about.html">Help</a>
    </div>
    <div class="standard-footer__bottom">
      <div class="standard-footer__bottom__links">
        <a href="//www.google.com?hl=en">Google</a>
        <a href="//www.google.com/about/?hl=en">About Google</a>
        <a href="//www.google.com/policies/privacy/?hl=en">Privacy</a>
        <a href="//www.google.com/policies/terms/?hl=en">Terms</a>
      </div>
      <div class="standard-footer__language">
<select id="language-switcher" class="language">
      <option value="/intl/am/gmail/about/policy/"  >
        አማርኛ
      </option>
      <option value="/intl/ar/gmail/about/policy/"  >
        العربية
      </option>
      <option value="/intl/az/gmail/about/policy/"  >
        azərbaycan
      </option>
      <option value="/intl/bg/gmail/about/policy/"  >
        български
      </option>
      <option value="/intl/bn/gmail/about/policy/"  >
        বাংলা
      </option>
      <option value="/intl/ca/gmail/about/policy/"  >
        català
      </option>
      <option value="/intl/cs/gmail/about/policy/"  >
        čeština
      </option>
      <option value="/intl/da/gmail/about/policy/"  >
        dansk
      </option>
      <option value="/intl/de/gmail/about/policy/"  >
        Deutsch
      </option>
      <option value="/intl/el/gmail/about/policy/"  >
        Ελληνικά
      </option>
      <option value="/gmail/about/policy/"  selected >
        English
      </option>
      <option value="/intl/en_uk/gmail/about/policy/"  >
        English (United Kingdom)
      </option>
      <option value="/intl/en_in/gmail/about/policy/"  >
        English (India)
      </option>
      <option value="/intl/es/gmail/about/policy/"  >
        español
      </option>
      <option value="/intl/es-419/gmail/about/policy/"  >
        español (Latinoamérica)
      </option>
      <option value="/intl/et/gmail/about/policy/"  >
        eesti
      </option>
      <option value="/intl/eu/gmail/about/policy/"  >
        euskara
      </option>
      <option value="/intl/fa/gmail/about/policy/"  >
        فارسی
      </option>
      <option value="/intl/fi/gmail/about/policy/"  >
        suomi
      </option>
      <option value="/intl/fil/gmail/about/policy/"  >
        Filipino
      </option>
      <option value="/intl/fr/gmail/about/policy/"  >
        français
      </option>
      <option value="/intl/fr_CA/gmail/about/policy/"  >
        français (Canada)
      </option>
      <option value="/intl/gl/gmail/about/policy/"  >
        galego
      </option>
      <option value="/intl/gu/gmail/about/policy/"  >
        ગુજરાતી
      </option>
      <option value="/intl/iw/gmail/about/policy/"  >
        עברית (ישראל)
      </option>
      <option value="/intl/hi/gmail/about/policy/"  >
        हिन्दी
      </option>
      <option value="/intl/hr/gmail/about/policy/"  >
        hrvatski
      </option>
      <option value="/intl/hu/gmail/about/policy/"  >
        magyar
      </option>
      <option value="/intl/id/gmail/about/policy/"  >
        Indonesia
      </option>
      <option value="/intl/is/gmail/about/policy/"  >
        íslenska
      </option>
      <option value="/intl/it/gmail/about/policy/"  >
        italiano
      </option>
      <option value="/intl/ja/gmail/about/policy/"  >
        日本語
      </option>
      <option value="/intl/km/gmail/about/policy/"  >
        ខ្មែរ
      </option>
      <option value="/intl/kn/gmail/about/policy/"  >
        ಕನ್ನಡ
      </option>
      <option value="/intl/ko/gmail/about/policy/"  >
        한국어
      </option>
      <option value="/intl/lt/gmail/about/policy/"  >
        lietuvių
      </option>
      <option value="/intl/lv/gmail/about/policy/"  >
        latviešu
      </option>
      <option value="/intl/ml/gmail/about/policy/"  >
        മലയാളം
      </option>
      <option value="/intl/mr/gmail/about/policy/"  >
        मराठी
      </option>
      <option value="/intl/ms/gmail/about/policy/"  >
        Melayu
      </option>
      <option value="/intl/my/gmail/about/policy/"  >
        မြန်မာ
      </option>
      <option value="/intl/no/gmail/about/policy/"  >
        norsk bokmål
      </option>
      <option value="/intl/nl/gmail/about/policy/"  >
        Nederlands
      </option>
      <option value="/intl/pl/gmail/about/policy/"  >
        polski
      </option>
      <option value="/intl/pt_br/gmail/about/policy/"  >
        português (Brasil)
      </option>
      <option value="/intl/pt_pt/gmail/about/policy/"  >
        português (Portugal)
      </option>
      <option value="/intl/ro/gmail/about/policy/"  >
        română
      </option>
      <option value="/intl/ru/gmail/about/policy/"  >
        русский
      </option>
      <option value="/intl/sk/gmail/about/policy/"  >
        slovenčina
      </option>
      <option value="/intl/sl/gmail/about/policy/"  >
        slovenščina
      </option>
      <option value="/intl/sr/gmail/about/policy/"  >
        српски
      </option>
      <option value="/intl/sv/gmail/about/policy/"  >
        svenska
      </option>
      <option value="/intl/sw/gmail/about/policy/"  >
        Kiswahili
      </option>
      <option value="/intl/ta/gmail/about/policy/"  >
        தமிழ்
      </option>
      <option value="/intl/te/gmail/about/policy/"  >
        తెలుగు
      </option>
      <option value="/intl/th/gmail/about/policy/"  >
        ไทย
      </option>
      <option value="/intl/tr/gmail/about/policy/"  >
        Türkçe
      </option>
      <option value="/intl/uk/gmail/about/policy/"  >
        українська
      </option>
      <option value="/intl/ur/gmail/about/policy/"  >
        اردو
      </option>
      <option value="/intl/vi/gmail/about/policy/"  >
        Tiếng Việt
      </option>
      <option value="/intl/zh-CN_cn/gmail/about/policy/"  >
        中文 (简体, 中国)
      </option>
      <option value="/intl/zh-HK_hk/gmail/about/policy/"  >
        中文 (繁體字, 中國香港特別行政區)
      </option>
      <option value="/intl/zh-TW_tw/gmail/about/policy/"  >
        中文 (繁體, 台灣)
      </option>
</select>

<script nonce="th1xZY9sBbqZsEUeq1eZvw">
  var languageSwitcher = document.getElementById('language-switcher');
  if (languageSwitcher) {
    languageSwitcher.addEventListener('change', function() {
      window.location.href = languageSwitcher.value;
    });
  }
</script>      </div>
    </div>
  </div>
</footer> 
  <script nonce="th1xZY9sBbqZsEUeq1eZvw">
    (function() {
      var link = null;
      var isiOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
      var isAndroid = navigator.userAgent.toLowerCase().indexOf("android") > -1;
      var appstoreBtn = document.getElementById('gmail-devices-appstore-button');
      var playBtn = document.getElementById('gmail-devices-play-button');
      var desktopBtn = document.getElementById('gmail-devices-send-button');
      if (isiOS) {
        link = 'https://itunes.apple.com/app/apple-store/id422689480?pt=9008';
        updateBadges();
      } else if (isAndroid) {
        link = 'https://play.google.com/store/apps/details?id=com.google.android.gm&referrer=utm_source%3Dweb_about';
        updateBadges();
      } else {
        // We are in desktop
        hideButton(desktopBtn);
      }

      if (link !== null) {
        processLinks(['hero_home__link__mobile', 'gmail-nav__nav-link__get-gmail']);
      }
      function processLinks(classes) {
        classes.forEach(function(selector) {
          var nodeList = document.getElementsByClassName(selector);
          for (var i = 0; i < nodeList.length; i++) {
            var elem = nodeList[i];
            elem.href = link;
            elem.href += '&ct=' + elem.dataset.pageLocation;
            if (isiOS) {
              elem.removeAttribute("target");
            }
          }
        });
      }
      function updateBadges() {
        hideButton(desktopBtn);
        if (isiOS) {
          if (appstoreBtn != null) {
            appstoreBtn.removeAttribute("target");
          }
          hideButton(playBtn);
        } else {
          hideButton(appstoreBtn);
        }
      }
      function hideButton(btn) {
        if (btn != null) { btn.style.display = 'none'; }
      }
    })();
  </script>

  <script src="//www.google.com/js/gweb/analytics/autotrack.js" nonce="th1xZY9sBbqZsEUeq1eZvw"></script>

  <script src="//ajax.googleapis.com/ajax/libs/webfont/1.5.10/webfont.js" nonce="th1xZY9sBbqZsEUeq1eZvw"></script>
  <script src="//ajax.googleapis.com/ajax/libs/angularjs/1.5.9/angular.min.js" nonce="th1xZY9sBbqZsEUeq1eZvw"></script>
  <script src="//ajax.googleapis.com/ajax/libs/angularjs/1.5.9/angular-animate.min.js" nonce="th1xZY9sBbqZsEUeq1eZvw"></script>
  <script src="/gmail/about/static/js/main.min.js" nonce="th1xZY9sBbqZsEUeq1eZvw"></script>

  <script nonce="th1xZY9sBbqZsEUeq1eZvw">
    gmail.main();
  </script>
</body>

</html>