import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Alert, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { getEHPReviews } from '../services/ehp';

const EHPReview: React.FC = () => {
    const [reviews, setReviews] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchReviews = async () => {
            try {
                const response = await getEHPReviews();
                setReviews(response);
            } catch (err: any) {
                setError(err.response?.data?.detail || 'Failed to load EHP reviews');
            } finally {
                setLoading(false);
            }
        };
        fetchReviews();
    }, []);

    if (loading) return <CircularProgress aria-label="Loading EHP reviews" />;
    if (error) return <Alert severity="error" role="alert">{error}</Alert>;

    return (
        <Box sx={{ p: 3 }} role="main">
            <Typography variant="h4" gutterBottom>
                EHP Reviews
            </Typography>
            {reviews.length === 0 ? (
                <Typography>No EHP reviews found.</Typography>
            ) : (
                <Table aria-label="EHP reviews table">
                    <TableHead>
                        <TableRow>
                            <TableCell>Project ID</TableCell>
                            <TableCell>Review Date</TableCell>
                            <TableCell>Status</TableCell>
                            <TableCell>Summary</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {reviews.map((review) => (
                            <TableRow key={review.id}>
                                <TableCell>{review.project_id}</TableCell>
                                <TableCell>{review.review_date}</TableCell>
                                <TableCell>{review.status}</TableCell>
                                <TableCell>{review.summary}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            )}
        </Box>
    );
};

export default EHPReview;