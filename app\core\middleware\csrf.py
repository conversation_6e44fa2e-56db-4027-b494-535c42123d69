"""
CSRF protection middleware for ComplianceMax.
"""

import secrets
import hmac
import hashlib
import time
from typing import Optional
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.datastructures import MutableHeaders
from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import MetricsCollector

logger = get_logger(__name__)

class CSRFMiddleware(BaseHTTPMiddleware):
    """CSRF protection middleware with enhanced security."""
    
    def __init__(self, app):
        """Initialize middleware."""
        super().__init__(app)
        self.secret = settings.CSRF_SECRET or secrets.token_urlsafe(32)
        self.enabled = settings.CSRF_ENABLED
        self.methods = settings.CSRF_METHODS
        self.token_length = 32  # Length of the random component
        self.max_age = 3600  # 1 hour token validity
    
    def generate_csrf_token(self, request_id: str) -> str:
        """
        Generate CSRF token with enhanced security.
        
        Format: random:timestamp:request_id:signature
        """
        timestamp = str(int(time.time()))
        random_component = secrets.token_urlsafe(self.token_length)
        msg = f"{random_component}:{timestamp}:{request_id}"
        
        # Create HMAC signature
        signature = hmac.new(
            self.secret.encode(),
            msg.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return f"{msg}:{signature}"
    
    def verify_csrf_token(self, token: str, request_id: str) -> bool:
        """
        Verify CSRF token with timing attack protection.
        """
        try:
            # Split token into parts
            random_component, timestamp, token_request_id, signature = token.split(":")
            
            # Verify request ID using constant-time comparison
            if not hmac.compare_digest(token_request_id, request_id):
                logger.warning("CSRF token request ID mismatch")
                return False
            
            # Verify timestamp
            try:
                token_age = int(time.time()) - int(timestamp)
                if token_age > self.max_age or token_age < 0:
                    logger.warning("CSRF token expired or from future")
                    return False
            except ValueError:
                logger.warning("Invalid CSRF token timestamp")
                return False
            
            # Verify signature
            msg = f"{random_component}:{timestamp}:{request_id}"
            expected_signature = hmac.new(
                self.secret.encode(),
                msg.encode(),
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(signature, expected_signature):
                logger.warning("CSRF token signature mismatch")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"CSRF token verification failed: {str(e)}")
            MetricsCollector.track_error("csrf", "verification_error")
            return False
    
    async def get_csrf_token_from_headers(self, request: Request) -> Optional[str]:
        """Get CSRF token from request headers with fallback to form data."""
        token = request.headers.get("X-CSRF-Token")
        if not token and request.method == "POST":
            # Try to get token from form data
            try:
                form = await request.form()
                token = form.get("csrf_token")
            except Exception:
                pass
        return token
    
    def get_request_id(self, request: Request) -> str:
        """Get or generate request ID with session support."""
        if not hasattr(request.state, "request_id"):
            # Try to get from session if available
            session = getattr(request.state, "session", None)
            if session and "request_id" in session:
                request.state.request_id = session["request_id"]
            else:
                request.state.request_id = secrets.token_urlsafe(16)
                if session:
                    session["request_id"] = request.state.request_id
        return request.state.request_id
    
    async def set_csrf_cookie(
        self,
        request: Request,
        response_headers: MutableHeaders,
        secure: bool = True
    ) -> None:
        """Set CSRF token cookie with security options."""
        token = self.generate_csrf_token(self.get_request_id(request))
        cookie_options = [
            f"csrf_token={token}",
            "Path=/",
            "HttpOnly",
            "SameSite=Strict"
        ]
        
        if secure and not settings.DEBUG:
            cookie_options.append("Secure")
        
        response_headers["Set-Cookie"] = "; ".join(cookie_options)
        # Also set token in header for AJAX requests
        response_headers["X-CSRF-Token"] = token
    
    async def dispatch(self, request: Request, call_next):
        """Process request with CSRF protection and metrics."""
        if not self.enabled:
            return await call_next(request)
        
        start_time = time.time()
        success = True
        
        try:
            # Skip CSRF check for safe methods
            if request.method not in self.methods:
                response = await call_next(request)
                # Set CSRF token cookie for GET requests
                if request.method == "GET":
                    await self.set_csrf_cookie(request, response.headers)
                return response
            
            # Get CSRF token
            csrf_token = await self.get_csrf_token_from_headers(request)
            if not csrf_token:
                success = False
                logger.warning("CSRF token missing")
                raise HTTPException(
                    status_code=403,
                    detail="CSRF token missing"
                )
            
            # Verify token
            request_id = self.get_request_id(request)
            if not self.verify_csrf_token(csrf_token, request_id):
                success = False
                raise HTTPException(
                    status_code=403,
                    detail="Invalid CSRF token"
                )
            
            # Process request
            response = await call_next(request)
            
            # Refresh CSRF token
            await self.set_csrf_cookie(request, response.headers)
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            success = False
            logger.error(f"CSRF middleware error: {str(e)}")
            MetricsCollector.track_error("csrf", "middleware_error")
            raise HTTPException(
                status_code=500,
                detail="Internal server error during CSRF check"
            )
        finally:
            duration = time.time() - start_time
            MetricsCollector.track_performance(
                operation="csrf_check",
                duration=duration,
                success=success
            ) 