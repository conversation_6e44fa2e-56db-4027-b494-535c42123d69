from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel
from app.models.policy import PolicyType

class PolicyVersionBase(BaseModel):
    version_number: str
    content: str
    effective_date: datetime
    expiration_date: Optional[datetime] = None
    document_url: Optional[str] = None

class PolicyVersionCreate(PolicyVersionBase):
    pass

class PolicyVersionResponse(PolicyVersionBase):
    id: int
    policy_id: int
    created_at: datetime

    class Config:
        from_attributes = True

class PolicyDocumentBase(BaseModel):
    policy_number: str
    title: str
    description: str
    policy_type: PolicyType

class PolicyDocumentCreate(PolicyDocumentBase):
    version_number: str
    content: str
    effective_date: datetime
    expiration_date: Optional[datetime] = None
    document_url: Optional[str] = None

class PolicyDocumentUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    policy_type: Optional[PolicyType] = None

class PolicyDocumentResponse(PolicyDocumentBase):
    id: int
    created_at: datetime
    updated_at: datetime
    versions: List[PolicyVersionResponse]

    class Config:
        from_attributes = True

class PolicyDocumentList(BaseModel):
    total: int
    items: List[PolicyDocumentResponse]

    class Config:
        from_attributes = True

class PolicySearchParams(BaseModel):
    query: str
    policy_type: Optional[PolicyType] = None
    effective_date: Optional[datetime] = None
