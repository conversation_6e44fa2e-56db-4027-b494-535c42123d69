from typing import List, Dict, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import HTTPException
from app.models.review import (
    BaseReview, MitigationReview, EHPReview, 
    CostReview, ComplianceReview, ReviewStatus
)
from app.services.websocket import websocket_manager

class ReviewService:
    async def create_mitigation_review(
        self,
        project_id: int,
        reviewer_id: int,
        cost_estimate: float,
        recommendations: List[str],
        implementation_timeline: int,
        db: Session
    ) -> MitigationReview:
        """Create a new mitigation review"""
        review = MitigationReview(
            project_id=project_id,
            reviewer_id=reviewer_id,
            cost_estimate=cost_estimate,
            recommendations=recommendations,
            implementation_timeline=implementation_timeline,
            findings={},
            status=ReviewStatus.PENDING
        )
        db.add(review)
        db.commit()
        db.refresh(review)
        
        # Notify connected clients
        await websocket_manager.send_update(
            str(project_id),
            "mitigation_review_created",
            {"review_id": review.id}
        )
        return review

    async def create_ehp_review(
        self,
        project_id: int,
        reviewer_id: int,
        environmental_impact: Dict,
        historical_considerations: Dict,
        required_permits: List[str],
        db: Session
    ) -> EHPReview:
        """Create a new EHP review"""
        review = EHPReview(
            project_id=project_id,
            reviewer_id=reviewer_id,
            environmental_impact=environmental_impact,
            historical_considerations=historical_considerations,
            required_permits=required_permits,
            findings={},
            status=ReviewStatus.PENDING
        )
        db.add(review)
        db.commit()
        db.refresh(review)
        
        await websocket_manager.send_update(
            str(project_id),
            "ehp_review_created",
            {"review_id": review.id}
        )
        return review

    async def create_cost_review(
        self,
        project_id: int,
        reviewer_id: int,
        total_cost: float,
        cost_breakdown: Dict,
        alternatives: List[Dict],
        db: Session
    ) -> CostReview:
        """Create a new cost review"""
        review = CostReview(
            project_id=project_id,
            reviewer_id=reviewer_id,
            total_cost=total_cost,
            cost_breakdown=cost_breakdown,
            alternatives=alternatives,
            findings={},
            status=ReviewStatus.PENDING
        )
        db.add(review)
        db.commit()
        db.refresh(review)
        
        await websocket_manager.send_update(
            str(project_id),
            "cost_review_created",
            {"review_id": review.id}
        )
        return review

    async def create_compliance_review(
        self,
        project_id: int,
        reviewer_id: int,
        policy_version_id: int,
        compliance_score: float,
        violations: List[Dict],
        remediation_plan: Dict,
        db: Session
    ) -> ComplianceReview:
        """Create a new compliance review"""
        review = ComplianceReview(
            project_id=project_id,
            reviewer_id=reviewer_id,
            policy_version_id=policy_version_id,
            compliance_score=compliance_score,
            violations=violations,
            remediation_plan=remediation_plan,
            findings={},
            status=ReviewStatus.PENDING
        )
        db.add(review)
        db.commit()
        db.refresh(review)
        
        await websocket_manager.send_update(
            str(project_id),
            "compliance_review_created",
            {"review_id": review.id}
        )
        return review

    async def update_review_status(
        self,
        review_id: int,
        status: ReviewStatus,
        db: Session
    ) -> BaseReview:
        """Update the status of a review"""
        review = db.query(BaseReview).filter_by(id=review_id).first()
        if not review:
            raise HTTPException(status_code=404, detail="Review not found")
        
        review.status = status
        review.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(review)
        
        await websocket_manager.send_update(
            str(review.project_id),
            "review_status_updated",
            {
                "review_id": review.id,
                "status": status
            }
        )
        return review

    async def add_findings(
        self,
        review_id: int,
        findings: Dict,
        db: Session
    ) -> BaseReview:
        """Add findings to a review"""
        review = db.query(BaseReview).filter_by(id=review_id).first()
        if not review:
            raise HTTPException(status_code=404, detail="Review not found")
        
        review.findings = findings
        review.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(review)
        
        await websocket_manager.send_update(
            str(review.project_id),
            "review_findings_updated",
            {
                "review_id": review.id,
                "findings": findings
            }
        )
        return review

    def get_project_reviews(
        self,
        project_id: int,
        db: Session
    ) -> List[BaseReview]:
        """Get all reviews for a project"""
        return db.query(BaseReview).filter_by(project_id=project_id).all()

    def get_review(
        self,
        review_id: int,
        db: Session
    ) -> Optional[BaseReview]:
        """Get a specific review by ID"""
        return db.query(BaseReview).filter_by(id=review_id).first()
