"""
Document version control service.

This module provides version control functionality for documents, including:
- Creating new versions
- Retrieving specific versions
- Comparing versions
- Managing version metadata
"""

import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, List
import json
import hashlib
import logging
from dataclasses import dataclass, asdict
from uuid import UUID

from sqlalchemy.orm import Session

from app.core.config import settings
from .models import Document, DocumentVersion
from .storage import StorageService
from app.models.enums import ComplianceStatus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='document_versioning.log'
)

@dataclass
class VersionMetadata:
    """Metadata for a document version."""
    version_id: str
    document_id: str
    filename: str
    timestamp: datetime
    author: str
    changes: List[str]
    checksum: str
    size: int
    content_type: str
    status: str = "active"
    tags: List[str] = None
    notes: Optional[str] = None

class VersionControlService:
    """Service for managing document versions."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_version(
        self,
        document: Document,
        content: str,
        version_number: str,
        changes: Optional[Dict] = None,
        compliance_status: Optional[ComplianceStatus] = None,
        review_notes: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> DocumentVersion:
        """Create a new version of a document.
        
        Args:
            document: The document to version
            content: The content of the new version
            version_number: Version identifier (e.g., timestamp or sequence)
            changes: Optional description of changes from previous version
            compliance_status: Optional compliance status for this version
            review_notes: Optional review notes
            metadata: Optional additional metadata
            
        Returns:
            The created DocumentVersion instance
        """
        # Calculate content hash
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        
        # Create version record
        version = DocumentVersion(
            document_id=document.id,
            version_number=version_number,
            storage_path=f"versions/{document.id}/{version_number}",
            changes=json.dumps(changes) if changes else None,
            compliance_status=compliance_status,
            review_notes=review_notes,
            version_metadata=json.dumps(metadata) if metadata else None
        )
        
        self.db.add(version)
        self.db.commit()
        self.db.refresh(version)
        
        logger.info(f"Created version {version_number} for document {document.id}")
        return version
    
    def get_version(self, document_id: str, version_number: str) -> Optional[DocumentVersion]:
        """Retrieve a specific version of a document.
        
        Args:
            document_id: ID of the document
            version_number: Version identifier
            
        Returns:
            The DocumentVersion instance if found, None otherwise
        """
        return self.db.query(DocumentVersion).filter(
            DocumentVersion.document_id == document_id,
            DocumentVersion.version_number == version_number
        ).first()
    
    def list_versions(self, document_id: str) -> List[DocumentVersion]:
        """List all versions of a document.
        
        Args:
            document_id: ID of the document
            
        Returns:
            List of DocumentVersion instances, ordered by creation date
        """
        return self.db.query(DocumentVersion).filter(
            DocumentVersion.document_id == document_id
        ).order_by(DocumentVersion.created_at.desc()).all()
    
    def compare_versions(
        self,
        document_id: str,
        version1: str,
        version2: str
    ) -> Dict:
        """Compare two versions of a document.
        
        Args:
            document_id: ID of the document
            version1: First version identifier
            version2: Second version identifier
            
        Returns:
            Dictionary containing comparison results
        """
        v1 = self.get_version(document_id, version1)
        v2 = self.get_version(document_id, version2)
        
        if not v1 or not v2:
            return {"error": "One or both versions not found"}
        
        # Compare metadata
        metadata1 = json.loads(v1.version_metadata) if v1.version_metadata else {}
        metadata2 = json.loads(v2.version_metadata) if v2.version_metadata else {}
        
        return {
            "versions": {
                "from": version1,
                "to": version2
            },
            "changes": json.loads(v2.changes) if v2.changes else {},
            "compliance_status": {
                "from": v1.compliance_status,
                "to": v2.compliance_status
            },
            "metadata_changes": {
                "added": {k: v for k, v in metadata2.items() if k not in metadata1},
                "removed": {k: v for k, v in metadata1.items() if k not in metadata2},
                "changed": {
                    k: {"from": metadata1[k], "to": metadata2[k]}
                    for k in metadata1.keys() & metadata2.keys()
                    if metadata1[k] != metadata2[k]
                }
            }
        }

class DocumentVersionControl:
    """Service for managing document versions."""
    
    def __init__(self, base_path: Optional[str] = None):
        self.base_path = Path(base_path or settings.STORAGE_PATH)
        self.metadata_path = self.base_path / "metadata" / "versions"
        self.metadata_path.mkdir(parents=True, exist_ok=True)

    def _generate_version_path(self, document_id: str, version_id: str) -> Path:
        """Generate path for a specific version of a document."""
        return self.base_path / "documents" / document_id / "versions" / version_id

    def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate SHA-256 checksum of a file."""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()

    def _save_metadata(self, metadata: VersionMetadata) -> None:
        """Save version metadata to file."""
        metadata_path = self.metadata_path / f"{metadata.document_id}_{metadata.version_id}.json"
        with open(metadata_path, 'w') as f:
            json.dump(asdict(metadata), f, default=str)

    def _load_metadata(self, document_id: str, version_id: str) -> Optional[VersionMetadata]:
        """Load version metadata from file."""
        metadata_path = self.metadata_path / f"{document_id}_{version_id}.json"
        if not metadata_path.exists():
            return None
        with open(metadata_path, 'r') as f:
            data = json.load(f)
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
            return VersionMetadata(**data)

    def create_version(self, 
                      document_id: str,
                      source_path: Path,
                      author: str,
                      changes: List[str],
                      tags: List[str] = None,
                      notes: Optional[str] = None) -> VersionMetadata:
        """Create a new version of a document."""
        try:
            # Generate version ID and paths
            version_id = datetime.now().strftime("%Y%m%d_%H%M%S")
            version_path = self._generate_version_path(document_id, version_id)
            version_path.parent.mkdir(parents=True, exist_ok=True)

            # Copy file to version directory
            shutil.copy2(source_path, version_path)

            # Calculate file metadata
            checksum = self._calculate_checksum(version_path)
            size = version_path.stat().st_size

            # Create metadata
            metadata = VersionMetadata(
                version_id=version_id,
                document_id=document_id,
                filename=source_path.name,
                timestamp=datetime.now(),
                author=author,
                changes=changes,
                checksum=checksum,
                size=size,
                content_type=source_path.suffix[1:],  # Remove leading dot
                tags=tags or [],
                notes=notes
            )

            # Save metadata
            self._save_metadata(metadata)
            logging.info(f"Created new version {version_id} for document {document_id}")

            return metadata
        except Exception as e:
            logging.error(f"Error creating version: {str(e)}")
            raise

    def get_version(self, document_id: str, version_id: str) -> Optional[Path]:
        """Get the file path for a specific version."""
        version_path = self._generate_version_path(document_id, version_id)
        if not version_path.exists():
            return None
        return version_path

    def get_version_metadata(self, document_id: str, version_id: str) -> Optional[VersionMetadata]:
        """Get metadata for a specific version."""
        return self._load_metadata(document_id, version_id)

    def list_versions(self, document_id: str) -> List[VersionMetadata]:
        """List all versions of a document."""
        versions = []
        metadata_files = self.metadata_path.glob(f"{document_id}_*.json")
        for file in metadata_files:
            version_id = file.stem.split('_')[-1]
            metadata = self._load_metadata(document_id, version_id)
            if metadata:
                versions.append(metadata)
        return sorted(versions, key=lambda v: v.timestamp, reverse=True)

    def compare_versions(self, document_id: str, version_id1: str, version_id2: str) -> Dict:
        """Compare two versions of a document."""
        v1 = self.get_version_metadata(document_id, version_id1)
        v2 = self.get_version_metadata(document_id, version_id2)
        
        if not v1 or not v2:
            raise ValueError("One or both versions not found")

        return {
            "version1": asdict(v1),
            "version2": asdict(v2),
            "differences": {
                "size": v2.size - v1.size,
                "changes": list(set(v2.changes) - set(v1.changes)),
                "checksum_different": v1.checksum != v2.checksum
            }
        }

    def restore_version(self, document_id: str, version_id: str, target_path: Path) -> bool:
        """Restore a specific version to a target location."""
        try:
            version_path = self.get_version(document_id, version_id)
            if not version_path:
                return False

            shutil.copy2(version_path, target_path)
            logging.info(f"Restored version {version_id} of document {document_id} to {target_path}")
            return True
        except Exception as e:
            logging.error(f"Error restoring version: {str(e)}")
            return False

    def delete_version(self, document_id: str, version_id: str) -> bool:
        """Delete a specific version."""
        try:
            version_path = self.get_version(document_id, version_id)
            if not version_path:
                return False

            # Delete version file
            version_path.unlink()

            # Delete metadata
            metadata_path = self.metadata_path / f"{document_id}_{version_id}.json"
            if metadata_path.exists():
                metadata_path.unlink()

            logging.info(f"Deleted version {version_id} of document {document_id}")
            return True
        except Exception as e:
            logging.error(f"Error deleting version: {str(e)}")
            return False 