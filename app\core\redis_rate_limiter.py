from typing import Dict, Any, Optional, List, Tuple, Union
import time
import logging
import asyncio
import hashlib
from datetime import datetime
import aioredis
from app.core.config import settings

logger = logging.getLogger(__name__)

class RedisRateLimiter:
    """Redis-based rate limiter for distributed deployment environments.
    
    This implementation uses Redis for storing rate limiting data across multiple application instances.
    It provides the same functionality as the in-memory rate limiter but can be used in a distributed environment.
    """
    
    def __init__(self):
        """Initialize the Redis rate limiter."""
        self.redis_url = settings.REDIS_URL
        self.redis = None
        self.whitelisted_ips = settings.RATE_LIMIT_WHITELIST
        self.initialized = False
    
    async def init(self):
        """Initialize the Redis connection."""
        if not self.initialized:
            try:
                self.redis = await aioredis.from_url(
                    self.redis_url,
                    encoding="utf-8",
                    decode_responses=True
                )
                self.initialized = True
                logger.info("Redis rate limiter initialized successfully")
                return True
            except Exception as e:
                logger.error(f"Failed to initialize Redis rate limiter: {str(e)}")
                return False
        return True
    
    def _is_whitelisted(self, key: str) -> bool:
        """Check if a key (typically an IP) is whitelisted."""
        # Extract IP from key if it contains IP
        ip = key.split(":")[-1] if ":" in key else key
        return ip in self.whitelisted_ips or settings.DEBUG
    
    def _get_limit_for_endpoint(self, endpoint: str) -> Tuple[int, int]:
        """Get rate limit for a specific endpoint type."""
        endpoint_type = endpoint.split(":")[0] if ":" in endpoint else endpoint
        
        # Default limits
        default_limit = settings.RATE_LIMIT_DEFAULT_LIMIT
        default_period = settings.RATE_LIMIT_DEFAULT_PERIOD
        
        # Different limits for different endpoint types
        if endpoint_type == "login" or endpoint_type == "register":
            return 5, 60  # 5 requests per minute for auth endpoints
        elif endpoint_type == "password_reset":
            return 3, 300  # 3 requests per 5 minutes for password reset
        elif "upload" in endpoint_type:
            return 10, 60  # 10 uploads per minute
        elif "download" in endpoint_type or "document" in endpoint_type:
            return 30, 60  # 30 downloads/document requests per minute
        
        return default_limit, default_period
    
    def _generate_key_hash(self, key: str) -> str:
        """Generate a hash of the key to avoid storing sensitive information."""
        # Add salt to prevent rainbow table attacks
        salted_key = f"{key}:{settings.SECRET_KEY[:8]}"
        return hashlib.sha256(salted_key.encode()).hexdigest()[:16]
    
    async def check_rate_limit(
        self, 
        key: str, 
        limit: Optional[int] = None, 
        period: Optional[int] = None,
        endpoint: Optional[str] = None
    ) -> Tuple[bool, int]:
        """Check if rate limit is exceeded.

        Args:
            key (str): The key to check rate limit for (e.g., IP address or user ID)
            limit (Optional[int]): Maximum number of requests allowed in the period
            period (Optional[int]): Time period in seconds
            endpoint (Optional[str]): Endpoint type for custom limits

        Returns:
            Tuple[bool, int]: (allowed, retry_after_seconds)
        """
        # Make sure Redis connection is established
        if not self.initialized:
            await self.init()
            
        # If Redis init failed, allow the request to proceed
        if not self.initialized:
            logger.warning("Rate limiting disabled - Redis connection failed")
            return True, 0
        
        # Whitelist check
        if self._is_whitelisted(key):
            return True, 0
        
        # Get appropriate limits
        if limit is None or period is None:
            limit, period = self._get_limit_for_endpoint(endpoint or "default")
        
        # Apply burst multiplier for better user experience
        burst_limit = int(limit * settings.RATE_LIMIT_BURST_MULTIPLIER)
        
        try:
            # Hash the key for privacy and to avoid key collisions
            hashed_key = self._generate_key_hash(key)
            redis_key = f"ratelimit:{hashed_key}"
            block_key = f"ratelimit:block:{hashed_key}"
            
            # Check if key is blocked
            blocked_until = await self.redis.get(block_key)
            if blocked_until:
                blocked_until = float(blocked_until)
                current_time = time.time()
                if current_time < blocked_until:
                    retry_after = int(blocked_until - current_time)
                    return False, retry_after
                else:
                    # Block expired, remove it
                    await self.redis.delete(block_key)
            
            # Get current timestamp
            current_time = time.time()
            
            # Use Redis pipeline for atomic operations
            pipe = self.redis.pipeline()
            
            # Add current timestamp to sorted set with score as timestamp
            pipe.zadd(redis_key, {str(current_time): current_time})
            
            # Remove timestamps older than the time window
            pipe.zremrangebyscore(redis_key, 0, current_time - period)
            
            # Count the number of requests in the current window
            pipe.zcard(redis_key)
            
            # Set expiration on the key
            pipe.expire(redis_key, period * 2)  # Twice the period to ensure cleanup
            
            # Execute pipeline
            _, _, count, _ = await pipe.execute()
            
            # If under limit, allow
            if count <= limit:
                return True, 0
            
            # Check if burst limit exceeded
            if count > burst_limit:
                # Block for progressively longer times for repeated offenders
                block_duration = min(2 ** (count - burst_limit), 86400)  # Max 24 hours
                block_until = current_time + block_duration
                await self.redis.set(block_key, str(block_until), ex=int(block_duration))
                logger.warning(f"Rate limit blocked for {block_duration}s: {key}")
                return False, block_duration
            
            # Calculate retry after based on the oldest timestamp in our window
            oldest_timestamp = await self.redis.zrange(redis_key, 0, 0, withscores=True)
            if oldest_timestamp:
                oldest_score = oldest_timestamp[0][1]
                retry_after = int(oldest_score + period - current_time) + 1
                return False, max(1, retry_after)
            
            return False, period  # Fallback
        
        except Exception as e:
            logger.error(f"Error checking rate limit: {str(e)}")
            # In case of error, allow the request to proceed
            return True, 0
    
    async def reset_rate_limit(self, key: str) -> bool:
        """Reset rate limit for a key.
        
        Args:
            key (str): The key to reset rate limit for
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Make sure Redis connection is established
        if not self.initialized:
            await self.init()
            
        # If Redis init failed, return False
        if not self.initialized:
            return False
        
        try:
            # Hash the key for privacy
            hashed_key = self._generate_key_hash(key)
            redis_key = f"ratelimit:{hashed_key}"
            block_key = f"ratelimit:block:{hashed_key}"
            
            # Use pipeline for atomic operations
            pipe = self.redis.pipeline()
            pipe.delete(redis_key)
            pipe.delete(block_key)
            await pipe.execute()
            
            return True
        except Exception as e:
            logger.error(f"Error resetting rate limit: {str(e)}")
            return False
    
    async def close(self):
        """Close the Redis connection."""
        if self.initialized and self.redis:
            await self.redis.close()
            self.initialized = False
            logger.info("Redis rate limiter closed")
