from datetime import datetime
from typing import Optional
from sqlalchemy import String, Float, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base

class FloodMap(Base):
    __tablename__ = "flood_maps"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    firm_panel_id: Mapped[str] = mapped_column(String, index=True)
    effective_date: Mapped[datetime] = mapped_column(DateTime)
    map_scale: Mapped[int] = mapped_column()
    flood_zone: Mapped[str] = mapped_column(String)
    latitude: Mapped[float] = mapped_column(Float)
    longitude: Mapped[float] = mapped_column(Float)
    firmette_url: Mapped[str] = mapped_column(String)
    last_updated: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    
    # Relationships
    user = relationship("User", back_populates="flood_maps")
    
class FirmetteRequest(Base):
    __tablename__ = "firmette_requests"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    latitude: Mapped[float] = mapped_column(Float)
    longitude: Mapped[float] = mapped_column(Float)
    address: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    status: Mapped[str] = mapped_column(String)  # pending, completed, failed
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    flood_map_id: Mapped[Optional[int]] = mapped_column(ForeignKey("flood_maps.id"), nullable=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    
    # Relationships
    user = relationship("User", back_populates="firmette_requests")
    flood_map = relationship("FloodMap")
