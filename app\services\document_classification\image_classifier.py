"""
Image Document Classifier
Implements document classification for image files.
"""

import os
from typing import Dict, Any, List
from datetime import datetime
from PIL import Image
from PIL.ExifTags import TAGS
from .base import DocumentClassifier

class ImageClassifier(DocumentClassifier):
    """Classifier implementation for image files."""
    
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
    
    def classify(self, document_path: str) -> Dict[str, Any]:
        """
        Classify an image file and extract metadata.
        
        Args:
            document_path: Path to the image file
            
        Returns:
            Dictionary containing:
            - width: Image width in pixels
            - height: Image height in pixels
            - format: Image format (JPEG, PNG, etc.)
            - mode: Image mode (RGB, RGBA, etc.)
            - file_size: Size in bytes
            - dpi: Resolution in DPI
            - has_exif: Whether image contains EXIF data
            - creation_date: Image creation date from EXIF
            - modification_date: File modification date
            - is_animated: Whether image is animated (GIF)
        """
        if not self.validate_document(document_path):
            raise ValueError(f"Invalid image document: {document_path}")
            
        # Get file metadata
        stat = os.stat(document_path)
        modification_date = datetime.fromtimestamp(stat.st_mtime)
        
        # Open image
        with Image.open(document_path) as img:
            # Get basic image properties
            width, height = img.size
            format = img.format
            mode = img.mode
            
            # Get DPI
            dpi = img.info.get('dpi', (72, 72))  # Default to 72 DPI
            
            # Check for animation
            is_animated = False
            if format == 'GIF':
                try:
                    img.seek(1)
                    is_animated = True
                except EOFError:
                    pass
                img.seek(0)
            
            # Get EXIF data
            exif_data = {}
            has_exif = False
            creation_date = None
            
            try:
                exif = img._getexif()
                if exif:
                    has_exif = True
                    for tag_id in exif:
                        tag = TAGS.get(tag_id, tag_id)
                        data = exif.get(tag_id)
                        if isinstance(data, bytes):
                            data = data.decode()
                        exif_data[tag] = data
                        
                        # Extract creation date from EXIF
                        if tag == 'DateTimeOriginal':
                            try:
                                creation_date = datetime.strptime(data, '%Y:%m:%d %H:%M:%S')
                            except:
                                pass
            except:
                pass
        
        return {
            'width': width,
            'height': height,
            'format': format,
            'mode': mode,
            'file_size': stat.st_size,
            'dpi': dpi,
            'has_exif': has_exif,
            'creation_date': creation_date,
            'modification_date': modification_date,
            'is_animated': is_animated,
            'exif_data': exif_data
        }
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats.
        
        Returns:
            List containing ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
        """
        return self.supported_formats
    
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if a document is a valid image file.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if document is a valid image file, False otherwise
        """
        if not os.path.exists(document_path):
            return False
            
        if not any(document_path.lower().endswith(ext) for ext in self.supported_formats):
            return False
            
        try:
            with Image.open(document_path) as img:
                img.verify()
                return True
        except:
            return False 