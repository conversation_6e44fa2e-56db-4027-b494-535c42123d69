"""
Word Document Classifier
Implements document classification for Word documents.
"""

import os
from typing import Dict, Any, List
from docx import Document
from .base import DocumentClassifier

class WordClassifier(DocumentClassifier):
    """Classifier implementation for Word documents."""
    
    def __init__(self):
        self.supported_formats = ['.docx', '.doc']
    
    def classify(self, document_path: str) -> Dict[str, Any]:
        """
        Classify a Word document and extract metadata.
        
        Args:
            document_path: Path to the Word file
            
        Returns:
            Dictionary containing:
            - page_count: Estimated number of pages
            - title: Document title
            - author: Document author
            - subject: Document subject
            - creation_date: Document creation date
            - modification_date: Document modification date
            - word_count: Approximate word count
            - paragraph_count: Number of paragraphs
        """
        if not self.validate_document(document_path):
            raise ValueError(f"Invalid Word document: {document_path}")
            
        doc = Document(document_path)
        core_properties = doc.core_properties
        
        # Calculate word count and paragraph count
        word_count = 0
        for paragraph in doc.paragraphs:
            word_count += len(paragraph.text.split())
        
        return {
            'page_count': len(doc.sections),  # Rough estimate
            'title': core_properties.title,
            'author': core_properties.author,
            'subject': core_properties.subject,
            'creation_date': core_properties.created,
            'modification_date': core_properties.modified,
            'word_count': word_count,
            'paragraph_count': len(doc.paragraphs)
        }
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats.
        
        Returns:
            List containing ['.docx', '.doc']
        """
        return self.supported_formats
    
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if a document is a valid Word document.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if document is a valid Word document, False otherwise
        """
        if not os.path.exists(document_path):
            return False
            
        if not any(document_path.lower().endswith(ext) for ext in self.supported_formats):
            return False
            
        try:
            doc = Document(document_path)
            return len(doc.paragraphs) > 0
        except:
            return False 