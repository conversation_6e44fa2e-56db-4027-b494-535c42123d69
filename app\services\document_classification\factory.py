"""
Document Classifier Factory
Manages the creation and retrieval of document classifiers.
"""

import os
from typing import Dict, Type, Optional
from .base import DocumentClassifier
from .pdf_classifier import PDFClassifier
from .word_classifier import WordClassifier
from .excel_classifier import ExcelClassifier
from .powerpoint_classifier import PowerPointClassifier
from .text_classifier import TextClassifier
from .image_classifier import ImageClassifier

class DocumentClassifierFactory:
    """Factory class for creating document classifiers."""
    
    def __init__(self):
        # Register all available classifiers
        self._classifiers: Dict[str, Type[DocumentClassifier]] = {
            '.pdf': PDFClassifier,
            '.docx': WordClassifier,
            '.doc': WordClassifier,
            '.xlsx': ExcelClassifier,
            '.xls': ExcelClassifier,
            '.xlsm': ExcelClassifier,
            '.pptx': PowerPointClassifier,
            '.ppt': PowerPointClassifier,
            '.txt': TextClassifier,
            '.md': TextClassifier,
            '.csv': TextClassifier,
            '.log': TextClassifier,
            '.json': TextClassifier,
            '.xml': TextClassifier,
            '.yaml': TextClassifier,
            '.yml': TextClassifier,
            '.jpg': ImageClassifier,
            '.jpeg': ImageClassifier,
            '.png': ImageClassifier,
            '.gif': ImageClassifier,
            '.bmp': ImageClassifier,
            '.tiff': ImageClassifier,
            '.webp': ImageClassifier
        }
        
        # Cache for instantiated classifiers
        self._instances: Dict[str, DocumentClassifier] = {}
    
    def get_classifier(self, document_path: str) -> Optional[DocumentClassifier]:
        """
        Get the appropriate classifier for a document.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            DocumentClassifier instance if a suitable classifier is found,
            None otherwise
        """
        if not os.path.exists(document_path):
            return None
            
        # Get file extension
        _, ext = os.path.splitext(document_path.lower())
        
        # Check if we have a classifier for this extension
        if ext not in self._classifiers:
            return None
            
        # Return cached instance if available
        if ext in self._instances:
            return self._instances[ext]
            
        # Create new instance
        classifier_class = self._classifiers[ext]
        classifier = classifier_class()
        self._instances[ext] = classifier
        return classifier
    
    def get_supported_formats(self) -> list[str]:
        """
        Get list of all supported file formats.
        
        Returns:
            List of supported file extensions
        """
        return list(self._classifiers.keys())
    
    def is_supported(self, document_path: str) -> bool:
        """
        Check if a document format is supported.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if the document format is supported, False otherwise
        """
        _, ext = os.path.splitext(document_path.lower())
        return ext in self._classifiers
    
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if a document can be processed.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if document can be processed, False otherwise
        """
        classifier = self.get_classifier(document_path)
        if not classifier:
            return False
            
        return classifier.validate_document(document_path)
    
    def classify(self, document_path: str) -> dict:
        """
        Classify a document using the appropriate classifier.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            Dictionary containing classification results
            
        Raises:
            ValueError: If no suitable classifier is found
        """
        classifier = self.get_classifier(document_path)
        if not classifier:
            raise ValueError(f"No classifier found for document: {document_path}")
            
        return classifier.classify(document_path) 