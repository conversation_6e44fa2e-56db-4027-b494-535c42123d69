from datetime import datetime
from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from fastapi import HTTPException
from app.models.compliance import ComplianceReview, ReviewStatus, ReviewPriority
from app.models.project import Project
from app.models.document import Document
import logging

logger = logging.getLogger(__name__)

class ComplianceService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_review(
        self,
        project_id: int,
        reviewer_id: int,
        priority: ReviewPriority,
        due_date: datetime,
        notes: Optional[str] = None
    ) -> ComplianceReview:
        """Create a new compliance review"""
        try:
            # Verify project exists
            project = await self.db.get(Project, project_id)
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            review = ComplianceReview(
                project_id=project_id,
                reviewer_id=reviewer_id,
                priority=priority,
                due_date=due_date,
                notes=notes
            )
            self.db.add(review)
            await self.db.commit()
            await self.db.refresh(review)
            return review

        except Exception as e:
            logger.error(f"Error creating compliance review: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=str(e))

    async def get_review(self, review_id: int) -> ComplianceReview:
        """Get a compliance review by ID"""
        stmt = select(ComplianceReview).where(
            ComplianceReview.id == review_id
        ).options(
            joinedload(ComplianceReview.project),
            joinedload(ComplianceReview.reviewer),
            joinedload(ComplianceReview.documents)
        )
        result = await self.db.execute(stmt)
        review = result.unique().scalar_one_or_none()
        if not review:
            raise HTTPException(status_code=404, detail="Review not found")
        return review

    async def update_status(
        self,
        review_id: int,
        status: ReviewStatus,
        findings: Optional[List[dict]] = None,
        recommendations: Optional[List[dict]] = None
    ) -> ComplianceReview:
        """Update the status of a compliance review"""
        review = await self.get_review(review_id)
        
        review.status = status
        if findings is not None:
            review.findings = findings
        if recommendations is not None:
            review.recommendations = recommendations
            
        if status in [ReviewStatus.COMPLIANT, ReviewStatus.NON_COMPLIANT]:
            review.completed_date = datetime.utcnow()
            
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def add_document(
        self,
        review_id: int,
        document_id: int
    ) -> ComplianceReview:
        """Add a document to a compliance review"""
        review = await self.get_review(review_id)
        document = await self.db.get(Document, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
            
        review.documents.append(document)
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def remove_document(
        self,
        review_id: int,
        document_id: int
    ) -> ComplianceReview:
        """Remove a document from a compliance review"""
        review = await self.get_review(review_id)
        document = await self.db.get(Document, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
            
        review.documents.remove(document)
        await self.db.commit()
        await self.db.refresh(review)
        return review

    async def get_project_reviews(
        self,
        project_id: int,
        status: Optional[ReviewStatus] = None
    ) -> List[ComplianceReview]:
        """Get all compliance reviews for a project"""
        stmt = select(ComplianceReview).where(
            ComplianceReview.project_id == project_id
        )
        if status:
            stmt = stmt.where(ComplianceReview.status == status)
            
        stmt = stmt.options(
            joinedload(ComplianceReview.reviewer),
            joinedload(ComplianceReview.documents)
        )
        
        result = await self.db.execute(stmt)
        reviews = result.unique().scalars().all()
        return list(reviews)

    async def get_overdue_reviews(self) -> List[ComplianceReview]:
        """Get all overdue compliance reviews"""
        now = datetime.utcnow()
        stmt = select(ComplianceReview).where(
            ComplianceReview.due_date < now,
            ComplianceReview.status.in_([
                ReviewStatus.PENDING,
                ReviewStatus.IN_PROGRESS,
                ReviewStatus.NEEDS_CLARIFICATION
            ])
        ).options(
            joinedload(ComplianceReview.project),
            joinedload(ComplianceReview.reviewer)
        )
        
        result = await self.db.execute(stmt)
        reviews = result.unique().scalars().all()
        return list(reviews)
