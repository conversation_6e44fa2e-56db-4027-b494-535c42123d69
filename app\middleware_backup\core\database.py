from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from app.core.config import settings
import logging
import time

logger = logging.getLogger(__name__)

# Create the declarative base that will be used by all models
Base = declarative_base()

# Use the proper database URL from settings
DATABASE_URL = str(settings.SQLALCHEMY_DATABASE_URI or "sqlite:///./compliancemax.db")
is_sqlite = "sqlite" in DATABASE_URL

# Engine creation with improved configuration
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Verify connections before using them
    pool_timeout=30,  # Timeout after 30 seconds when waiting for connection
    pool_recycle=1800,  # Recycle connections after 30 minutes
    echo=settings.DEBUG,  # SQL echo for debugging
    connect_args={"check_same_thread": False} if is_sqlite else {}  # Required for SQLite
)

# SQLite performance optimizations
if is_sqlite:
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys=ON")
        # Set journal mode to WAL for better concurrency
        cursor.execute("PRAGMA journal_mode=WAL")
        # Set synchronous mode to NORMAL for better performance
        cursor.execute("PRAGMA synchronous=NORMAL")
        # Set temp_store to MEMORY
        cursor.execute("PRAGMA temp_store=MEMORY")
        # Set cache size to 10000 pages (approximately 40MB)
        cursor.execute("PRAGMA cache_size=-10000")
        cursor.close()

# Session factory
SessionLocal = sessionmaker(
    bind=engine,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)

def get_db():
    """Get database session with proper error handling."""
    db = SessionLocal()
    start_time = time.time()
    try:
        yield db
    except Exception as e:
        db.rollback()
        logger.error(f"Database error: {str(e)}")
        raise
    finally:
        query_time = time.time() - start_time
        if query_time > 1.0:  # Log slow queries (over 1 second)
            logger.warning(f"Slow database operation detected: {query_time:.2f} seconds")
        db.close()

def get_sync_db() -> Session:
    """Get a synchronous database session."""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise

def run_migrations():
    """Run database migrations"""
    try:
        # Import all models here
        # This is where circular imports can happen, so we import inside the function
        from app.models.user import User
        from app.models.document import Document
        from app.models.cbcs_scan import CBCSScan
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database migrations completed successfully")
    except Exception as e:
        logger.error(f"Error during migrations: {str(e)}")
        raise