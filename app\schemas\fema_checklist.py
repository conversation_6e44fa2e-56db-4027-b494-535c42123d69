from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from enum import Enum

class ComplianceStatus(str, Enum):
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PENDING = "pending"
    REQUIRES_REVIEW = "requires_review"

class RequirementStatus(str, Enum):
    COMPLETE = "complete"
    INCOMPLETE = "incomplete"
    IN_PROGRESS = "in_progress"
    NOT_APPLICABLE = "not_applicable"

class DocumentRequirementBase(BaseModel):
    category: str
    requirement: str
    status: RequirementStatus
    notes: Optional[str] = None

class DocumentRequirementCreate(DocumentRequirementBase):
    document_id: int

class DocumentRequirementResponse(DocumentRequirementBase):
    id: int
    document_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ComplianceReportBase(BaseModel):
    category: str
    total_requirements: int
    completed_requirements: int
    completion_percentage: float
    status: ComplianceStatus
    missing_requirements: List[str]

class ComplianceValidationResponse(BaseModel):
    document_id: int
    category: str
    requirements_met: List[str]
    requirements_missing: List[str]
    overall_status: ComplianceStatus
    validation_date: datetime
    score: Optional[float] = None
    risk_level: Optional[str] = None

class ComplianceCheckResult(BaseModel):
    requirement: str
    is_compliant: bool
    confidence_score: float
    evidence: Optional[str] = None
    risk_factors: Optional[List[str]] = None 