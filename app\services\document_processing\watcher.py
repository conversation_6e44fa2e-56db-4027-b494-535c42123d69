"""
Document Watcher Service
Monitors directories for document changes and triggers processing.
"""

import time
import logging
from typing import List, Dict, Optional, Callable
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEvent<PERSON>andler, FileCreatedEvent, FileModifiedEvent
import hashlib

logger = logging.getLogger(__name__)

class DocumentEventHandler(FileSystemEventHandler):
    """Handles file system events for document processing."""
    
    def __init__(self, on_document_found: Callable[[str], None]):
        """
        Initialize the event handler.
        
        Args:
            on_document_found: Callback function when a document is found
        """
        self.on_document_found = on_document_found
        self.logger = logging.getLogger('DocumentEventHandler')
        
        # Track recently processed files to avoid duplicates
        self.recent_files: Dict[str, float] = {}
        self.cooldown_seconds = 5  # Minimum time between processing same file
        
        # Track file hashes to detect content changes
        self.file_hashes: Dict[str, str] = {}
    
    def on_created(self, event: FileCreatedEvent) -> None:
        """Handle file creation events."""
        if not event.is_directory:
            self._process_file(event.src_path)
    
    def on_modified(self, event: FileModifiedEvent) -> None:
        """Handle file modification events."""
        if not event.is_directory:
            self._process_file(event.src_path)
    
    def _process_file(self, file_path: str) -> None:
        """
        Process a file if it hasn't been recently processed.
        
        Args:
            file_path: Path to the file to process
        """
        current_time = time.time()
        last_processed = self.recent_files.get(file_path, 0)
        
        # Check if file was recently processed
        if current_time - last_processed < self.cooldown_seconds:
            self.logger.debug(f"Skipping recently processed file: {file_path}")
            return
        
        try:
            # Check if content actually changed
            current_hash = self._get_file_hash(file_path)
            if current_hash != self.file_hashes.get(file_path):
                self.logger.info(f"Processing new/modified file: {file_path}")
                self.on_document_found(file_path)
                self.recent_files[file_path] = current_time
                self.file_hashes[file_path] = current_hash
            else:
                self.logger.debug(f"Skipping unchanged file: {file_path}")
                
        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {e}")
    
    def _get_file_hash(self, file_path: str) -> str:
        """
        Calculate SHA-256 hash of file contents.
        
        Args:
            file_path: Path to the file
            
        Returns:
            SHA-256 hash of file contents
        """
        try:
            with open(file_path, "rb") as f:
                return hashlib.sha256(f.read()).hexdigest()
        except Exception as e:
            self.logger.error(f"Error calculating file hash for {file_path}: {e}")
            return ""

class DocumentWatcher:
    """Monitors directories for document changes."""
    
    def __init__(
        self,
        watch_directories: List[str],
        on_document_found: Callable[[str], None],
        supported_extensions: Optional[List[str]] = None
    ):
        """
        Initialize the document watcher.
        
        Args:
            watch_directories: List of directories to monitor
            on_document_found: Callback function when a document is found
            supported_extensions: List of supported file extensions (optional)
        """
        self.watch_directories = [Path(d) for d in watch_directories]
        self.on_document_found = on_document_found
        self.supported_extensions = supported_extensions or ['.pdf', '.docx', '.txt', '.csv', '.xlsx']
        
        self.observer = Observer()
        self.event_handler = DocumentEventHandler(on_document_found)
        
        # Add watch directories
        for directory in self.watch_directories:
            path = Path(directory)
            if path.exists():
                self.observer.schedule(
                    self.event_handler,
                    str(path),
                    recursive=True
                )
                logger.info(f"Watching directory: {directory}")
            else:
                logger.warning(f"Watch directory not found: {directory}")
    
    def start(self) -> None:
        """Start watching for document changes."""
        logger.info("Starting document watcher")
        self.observer.start()
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self) -> None:
        """Stop watching for document changes."""
        logger.info("Stopping document watcher")
        self.observer.stop()
        self.observer.join()
    
    def process_existing_documents(self) -> None:
        """Process all existing documents in watch directories."""
        logger.info("Processing existing documents")
        
        for directory in self.watch_directories:
            if not directory.exists():
                continue
                
            for file_path in directory.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                    try:
                        self.on_document_found(str(file_path))
                    except Exception as e:
                        logger.error(f"Error processing existing file {file_path}: {e}") 