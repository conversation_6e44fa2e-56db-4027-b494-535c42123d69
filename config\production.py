"""
Production Configuration Module

Production-specific configuration settings that override base settings.
Focuses on security, performance, and reliability.
"""

from .base import *

# Ensure debug is disabled in production
SECURITY['debug'] = False

# Require proper secret key in production
if SECURITY['secret_key'] == 'default-dev-key-change-in-production':
    raise ValueError(
        'Production secret key not set. '
        'Please set SECRET_KEY environment variable.'
    )

# Enable SSL verification in production
SECURITY['ssl_verify'] = True

# Strict host checking
SECURITY['allowed_hosts'] = [
    'compliancemax.example.com',  # Replace with actual domain
    'api.compliancemax.example.com'  # Replace with actual API domain
]

# Production logging configuration
LOGGING['handlers']['file']['filename'] = '/var/log/compliancemax/app.log'
LOGGING['handlers']['file']['maxBytes'] = 10 * 1024 * 1024  # 10MB
LOGGING['handlers']['file']['backupCount'] = 5
LOGGING['loggers']['']['level'] = 'INFO'

# Production database configuration
DATABASE = {
    'engine': 'postgresql',
    'name': os.environ.get('DB_NAME', 'compliancemax'),
    'user': os.environ.get('DB_USER', 'compliancemax'),
    'password': os.environ.get('DB_PASSWORD'),
    'host': os.environ.get('DB_HOST', 'localhost'),
    'port': int(os.environ.get('DB_PORT', 5432))
}

# Production storage paths
STORAGE_DIR = Path('/var/lib/compliancemax/storage')
DOCUMENT_STORAGE = {
    'policies': STORAGE_DIR / 'policies',
    'requirements': STORAGE_DIR / 'requirements',
    'uploads': STORAGE_DIR / 'uploads',
    'results': STORAGE_DIR / 'results'
}

# Ensure storage directories exist with proper permissions
for path in DOCUMENT_STORAGE.values():
    path.mkdir(mode=0o750, parents=True, exist_ok=True)

# Production cache configuration
CACHE = {
    'enabled': True,
    'backend': 'redis',
    'url': os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
    'ttl': 3600,  # 1 hour
    'max_size': 10000
}

# Stricter rate limiting in production
API['rate_limit'] = {
    'enabled': True,
    'requests': 100,
    'window': 60,  # 1 minute
    'by_ip': True
}

# Production feature flags
FEATURES = {
    'enable_ai_matching': True,
    'enable_batch_processing': True,
    'enable_advanced_search': True,
    'enable_export': True,
    'enable_notifications': True
}

# Strict compliance checking in production
COMPLIANCE_CHECKER['confidence_threshold'] = 0.8
COMPLIANCE_CHECKER['required_match_percentage'] = 0.9
COMPLIANCE_CHECKER['store_results'] = True
COMPLIANCE_CHECKER['generate_report'] = True

# Strict requirement validation in production
REQUIREMENT_VALIDATOR['strict_mode'] = True
REQUIREMENT_VALIDATOR['validate_references'] = True

# Production policy matcher settings
POLICY_MATCHER['confidence_threshold'] = 0.8
POLICY_MATCHER['min_similarity_score'] = 0.6
POLICY_MATCHER['cache_enabled'] = True
POLICY_MATCHER['cache_ttl'] = 3600  # 1 hour

# Security headers
SECURITY['headers'] = {
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Content-Security-Policy': "default-src 'self'",
    'Referrer-Policy': 'strict-origin-when-cross-origin'
}

# File upload restrictions
DOCUMENT_PROCESSOR['max_file_size'] = 25 * 1024 * 1024  # 25MB in production
DOCUMENT_PROCESSOR['allowed_extensions'] = {'.pdf', '.docx', '.xlsx', '.txt'}
DOCUMENT_PROCESSOR['scan_uploads'] = True  # Enable malware scanning
DOCUMENT_PROCESSOR['backup_enabled'] = True 