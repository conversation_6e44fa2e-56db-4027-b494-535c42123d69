{{- if .Values.podDisruptionBudget }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "gateway.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway.labels" . | nindent 4}}
spec:
  selector:
    matchLabels:
  {{- include "gateway.selectorLabels" . | nindent 6 }}
  {{- with .Values.podDisruptionBudget }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
