import redis
from app.core.config import settings
import logging
from datetime import timedelta

logger = logging.getLogger(__name__)

def get_redis_client():
    try:
        client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
        client.ping()  # Test the connection
        return client
    except redis.ConnectionError as e:
        logger.error(f"Failed to connect to Redis: {str(e)}")
        raise

redis_client = get_redis_client()

def set_csrf_token(token: str, user_id: str, expire: int = 3600) -> None:
    """
    Store a CSRF token in Redis with an expiration time.
    
    Args:
        token (str): The CSRF token to store.
        user_id (str): The user ID associated with the token.
        expire (int): Expiration time in seconds (default: 1 hour).
    """
    try:
        key = f"csrf:{user_id}"
        redis_client.setex(key, timedelta(seconds=expire), token)
        logger.info(f"CSRF token set for user {user_id}")
    except redis.RedisError as e:
        logger.error(f"Failed to set CSRF token for user {user_id}: {str(e)}")
        raise

def validate_csrf_token(token: str, user_id: str) -> bool:
    """
    Validate a CSRF token by checking it against the stored token in Redis.
    
    Args:
        token (str): The CSRF token to validate.
        user_id (str): The user ID associated with the token.
    
    Returns:
        bool: True if the token is valid, False otherwise.
    """
    try:
        key = f"csrf:{user_id}"
        stored_token = redis_client.get(key)
        if stored_token is None:
            logger.warning(f"No CSRF token found for user {user_id}")
            return False
        if stored_token != token:
            logger.warning(f"Invalid CSRF token for user {user_id}")
            return False
        logger.info(f"CSRF token validated for user {user_id}")
        return True
    except redis.RedisError as e:
        logger.error(f"Failed to validate CSRF token for user {user_id}: {str(e)}")
        raise

def check_redis_health() -> bool:
    """
    Check the health of the Redis connection.
    
    Returns:
        bool: True if Redis is healthy, False otherwise.
    """
    try:
        redis_client.ping()
        logger.info("Redis connection is healthy")
        return True
    except redis.ConnectionError as e:
        logger.error(f"Redis health check failed: {str(e)}")
        return False