"""
Excel Document Classifier
Implements document classification for Excel spreadsheets.
"""

import os
from typing import Dict, Any, List
import pandas as pd
from openpyxl import load_workbook
from .base import DocumentClassifier

class ExcelClassifier(DocumentClassifier):
    """Classifier implementation for Excel spreadsheets."""
    
    def __init__(self):
        self.supported_formats = ['.xlsx', '.xls', '.xlsm']
    
    def classify(self, document_path: str) -> Dict[str, Any]:
        """
        Classify an Excel spreadsheet and extract metadata.
        
        Args:
            document_path: Path to the Excel file
            
        Returns:
            Dictionary containing:
            - sheet_count: Number of worksheets
            - sheet_names: List of worksheet names
            - author: Document author
            - creation_date: Document creation date
            - modification_date: Document modification date
            - row_count: Total number of rows across all sheets
            - column_count: Total number of columns across all sheets
            - has_formulas: Whether any cells contain formulas
        """
        if not self.validate_document(document_path):
            raise ValueError(f"Invalid Excel document: {document_path}")
            
        wb = load_workbook(document_path, read_only=True, data_only=False)
        properties = wb.properties
        
        # Calculate total rows and columns
        total_rows = 0
        total_cols = 0
        has_formulas = False
        
        for sheet in wb:
            df = pd.read_excel(document_path, sheet_name=sheet.title)
            total_rows += len(df)
            total_cols = max(total_cols, len(df.columns))
            
            # Check for formulas
            if not has_formulas:
                for row in sheet.iter_rows():
                    for cell in row:
                        if cell.value and str(cell.value).startswith('='):
                            has_formulas = True
                            break
                    if has_formulas:
                        break
        
        return {
            'sheet_count': len(wb.sheetnames),
            'sheet_names': wb.sheetnames,
            'author': properties.creator,
            'creation_date': properties.created,
            'modification_date': properties.modified,
            'row_count': total_rows,
            'column_count': total_cols,
            'has_formulas': has_formulas
        }
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats.
        
        Returns:
            List containing ['.xlsx', '.xls', '.xlsm']
        """
        return self.supported_formats
    
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if a document is a valid Excel spreadsheet.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if document is a valid Excel spreadsheet, False otherwise
        """
        if not os.path.exists(document_path):
            return False
            
        if not any(document_path.lower().endswith(ext) for ext in self.supported_formats):
            return False
            
        try:
            wb = load_workbook(document_path, read_only=True)
            return len(wb.sheetnames) > 0
        except:
            return False 