from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict
from app.core.deps import get_db, get_current_user
from app.services.review import ReviewService
from app.models.review import ReviewStatus
from app.schemas.review import (
    MitigationReviewCreate,
    EHPReviewCreate,
    CostReviewCreate,
    ComplianceReviewCreate,
    ReviewResponse,
    ReviewStatusUpdate,
    ReviewFindings
)

router = APIRouter()
review_service = ReviewService()

@router.post("/mitigation", response_model=ReviewResponse)
async def create_mitigation_review(
    review: MitigationReviewCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new mitigation review"""
    return await review_service.create_mitigation_review(
        project_id=review.project_id,
        reviewer_id=current_user.id,
        cost_estimate=review.cost_estimate,
        recommendations=review.recommendations,
        implementation_timeline=review.implementation_timeline,
        db=db
    )

@router.post("/ehp", response_model=ReviewResponse)
async def create_ehp_review(
    review: EHPReviewCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new EHP review"""
    return await review_service.create_ehp_review(
        project_id=review.project_id,
        reviewer_id=current_user.id,
        environmental_impact=review.environmental_impact,
        historical_considerations=review.historical_considerations,
        required_permits=review.required_permits,
        db=db
    )

@router.post("/cost", response_model=ReviewResponse)
async def create_cost_review(
    review: CostReviewCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new cost review"""
    return await review_service.create_cost_review(
        project_id=review.project_id,
        reviewer_id=current_user.id,
        total_cost=review.total_cost,
        cost_breakdown=review.cost_breakdown,
        alternatives=review.alternatives,
        db=db
    )

@router.post("/compliance", response_model=ReviewResponse)
async def create_compliance_review(
    review: ComplianceReviewCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new compliance review"""
    return await review_service.create_compliance_review(
        project_id=review.project_id,
        reviewer_id=current_user.id,
        policy_version_id=review.policy_version_id,
        compliance_score=review.compliance_score,
        violations=review.violations,
        remediation_plan=review.remediation_plan,
        db=db
    )

@router.patch("/{review_id}/status", response_model=ReviewResponse)
async def update_review_status(
    review_id: int,
    status_update: ReviewStatusUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Update the status of a review"""
    return await review_service.update_review_status(
        review_id=review_id,
        status=status_update.status,
        db=db
    )

@router.patch("/{review_id}/findings", response_model=ReviewResponse)
async def add_review_findings(
    review_id: int,
    findings: ReviewFindings,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Add findings to a review"""
    return await review_service.add_findings(
        review_id=review_id,
        findings=findings.findings,
        db=db
    )

@router.get("/project/{project_id}", response_model=List[ReviewResponse])
async def get_project_reviews(
    project_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get all reviews for a project"""
    return review_service.get_project_reviews(project_id, db)

@router.get("/{review_id}", response_model=ReviewResponse)
async def get_review(
    review_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get a specific review by ID"""
    review = review_service.get_review(review_id, db)
    if not review:
        raise HTTPException(status_code=404, detail="Review not found")
    return review
