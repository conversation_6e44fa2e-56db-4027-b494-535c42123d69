"""
API Endpoints for Document Processing and Compliance Evaluation
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from typing import List, Dict, Optional
from pathlib import Path
import shutil
import logging
from datetime import datetime, timedelta
import json

from ..services.document_processing.pipeline import DocumentPipeline
from ..services.document_processing.watcher import DocumentWatcher
from ..services.compliance.engine import RuleEngine
from .auth import (
    authenticate_user, create_access_token, get_current_active_user,
    require_role, Token, ACCESS_TOKEN_EXPIRE_MINUTES
)

# Initialize FastAPI app
app = FastAPI(
    title="Document Processing API",
    description="API for document processing and compliance evaluation",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
UPLOAD_DIR = Path("uploads")
PROCESSED_DIR = Path("processed")
RULES_DIR = Path("rules")
LOGS_DIR = Path("logs")

# Create necessary directories
for directory in [UPLOAD_DIR, PROCESSED_DIR, RULES_DIR, LOGS_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# Initialize services
pipeline = DocumentPipeline(
    watch_directories=[str(UPLOAD_DIR)],
    output_directory=str(PROCESSED_DIR),
    log_directory=str(LOGS_DIR)
)

rule_engine = RuleEngine(str(RULES_DIR))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOGS_DIR / "api.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("api")

@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    Get access token for authenticated user.
    
    Args:
        form_data: Login form data
        
    Returns:
        Access token
    """
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: User = Depends(require_role("user"))
) -> Dict:
    """
    Upload a document for processing.
    
    Args:
        file: Document file to upload
        background_tasks: Background tasks handler
        current_user: Current authenticated user
        
    Returns:
        Dictionary containing upload status and document ID
    """
    try:
        # Save uploaded file
        file_path = UPLOAD_DIR / file.filename
        with file_path.open("wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Process document in background
        background_tasks.add_task(process_document, str(file_path))
        
        return {
            "status": "success",
            "message": "Document uploaded and queued for processing",
            "document_id": str(file_path),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/documents/{document_id}")
async def get_document_status(
    document_id: str,
    current_user: User = Depends(require_role("user"))
) -> Dict:
    """
    Get processing status of a document.
    
    Args:
        document_id: ID of the document
        current_user: Current authenticated user
        
    Returns:
        Dictionary containing document status and results
    """
    try:
        status = pipeline.get_processing_status(document_id)
        if not status:
            raise HTTPException(status_code=404, detail="Document not found")
            
        return status
        
    except Exception as e:
        logger.error(f"Error getting document status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/documents")
async def list_documents(
    current_user: User = Depends(require_role("user"))
) -> List[Dict]:
    """
    List all processed documents.
    
    Returns:
        List of processed documents with their status
    """
    try:
        return pipeline.get_all_processed_documents()
        
    except Exception as e:
        logger.error(f"Error listing documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/rules")
async def add_rule(
    rule: Dict,
    current_user: User = Depends(require_role("admin"))
) -> Dict:
    """
    Add a new compliance rule.
    
    Args:
        rule: Rule configuration
        current_user: Current authenticated user
        
    Returns:
        Dictionary containing rule status
    """
    try:
        rule_engine.add_rule(rule)
        return {
            "status": "success",
            "message": "Rule added successfully",
            "rule_id": rule.get("id")
        }
        
    except Exception as e:
        logger.error(f"Error adding rule: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/rules")
async def list_rules(
    current_user: User = Depends(require_role("user"))
) -> Dict:
    """
    List all compliance rules.
    
    Returns:
        Dictionary of all rules
    """
    try:
        return rule_engine.get_all_rules()
        
    except Exception as e:
        logger.error(f"Error listing rules: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/rules/{rule_id}")
async def delete_rule(
    rule_id: str,
    current_user: User = Depends(require_role("admin"))
) -> Dict:
    """
    Delete a compliance rule.
    
    Args:
        rule_id: ID of the rule to delete
        current_user: Current authenticated user
        
    Returns:
        Dictionary containing deletion status
    """
    try:
        rule_engine.remove_rule(rule_id)
        return {
            "status": "success",
            "message": f"Rule {rule_id} deleted successfully"
        }
        
    except Exception as e:
        logger.error(f"Error deleting rule: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check() -> Dict:
    """
    Check API health status.
    
    Returns:
        Dictionary containing health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "pipeline": "running",
            "rule_engine": "running"
        }
    }

async def process_document(document_path: str) -> None:
    """
    Process a document through the pipeline.
    
    Args:
        document_path: Path to the document
    """
    try:
        # Process document
        result = pipeline.process_document(document_path)
        
        # Evaluate compliance
        compliance_results = rule_engine.evaluate_document(result)
        
        # Save results
        save_path = PROCESSED_DIR / f"{Path(document_path).stem}_results.json"
        with open(save_path, "w") as f:
            json.dump({
                "processing": result,
                "compliance": compliance_results
            }, f, indent=2)
            
        logger.info(f"Document processed: {document_path}")
        
    except Exception as e:
        logger.error(f"Error processing document {document_path}: {e}")
        raise 