from app.models.user import User
from app.core.security import get_password_hash

class UserService:
    @staticmethod
    async def create_test_user() -> User:
        """Create a test user if it doesn't exist."""
        test_user = await User.find_one({"username": "testuser"})
        if not test_user:
            test_user = User(
                username="testuser",
                hashed_password=get_password_hash("testpassword"),
                full_name="Test User",
                is_active=True
            )
            await test_user.insert()
        return test_user
