from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum
from app.models.cost import CostType

class CostType(str, Enum):
    MITIGATION = "mitigation"
    IMPLEMENTATION = "implementation"
    MAINTENANCE = "maintenance"
    CONSULTATION = "consultation"

class CostBase(BaseModel):
    project_id: int
    type: CostType
    amount: float = Field(gt=0)
    description: str
    date_incurred: datetime
    category: str
    vendor: Optional[str] = None
    invoice_number: Optional[str] = None

class CostCreate(CostBase):
    pass

class CostResponse(CostBase):
    id: int
    
    class Config:
        from_attributes = True

class BudgetBase(BaseModel):
    project_id: int
    fiscal_year: int
    total_amount: float = Field(gt=0)
    allocated_amount: float = Field(ge=0)
    remaining_amount: float = Field(ge=0)
    last_updated: datetime

class BudgetCreate(BudgetBase):
    pass

class BudgetResponse(BudgetBase):
    id: int
    
    class Config:
        from_attributes = True

class CostBenefitBase(BaseModel):
    project_id: int
    benefit_description: str
    estimated_savings: float = Field(ge=0)
    implementation_cost: float = Field(gt=0)
    roi_percentage: float = Field(ge=0)
    payback_period: float = Field(gt=0)  # in months

class CostBenefitCreate(CostBenefitBase):
    pass

class CostBenefitResponse(CostBenefitBase):
    id: int
    
    class Config:
        from_attributes = True

class CostSummary(BaseModel):
    total_costs: float
    costs_by_type: dict[str, float]
