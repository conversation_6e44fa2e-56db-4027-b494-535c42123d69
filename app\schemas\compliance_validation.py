"""Schemas for compliance validation endpoints."""

from typing import List, Dict, Optional
from datetime import datetime
from pydantic import BaseModel, Field, constr
from uuid import UUID

from app.models.compliance import ValidationStatus, ComplianceStatus, RiskLevel

class ComplianceResultBase(BaseModel):
    """Base schema for compliance validation results."""
    document_id: str = Field(..., description="ID of the validated document")
    application_id: str = Field(..., description="ID of the associated application")

class ComplianceResultCreate(ComplianceResultBase):
    """Schema for creating a compliance validation request."""
    policy_version_id: Optional[str] = Field(None, description="Optional specific policy version to validate against")

class ComplianceResultUpdate(BaseModel):
    """Schema for updating a compliance validation result."""
    validation_status: Optional[ValidationStatus] = None
    compliance_status: Optional[ComplianceStatus] = None
    risk_level: Optional[RiskLevel] = None
    findings: Optional[Dict] = None
    deficiencies: Optional[Dict] = None
    recommendations: Optional[List[str]] = None

class ComplianceResultResponse(ComplianceResultBase):
    """Schema for compliance validation result response."""
    id: str
    validation_status: ValidationStatus
    compliance_status: ComplianceStatus
    risk_level: RiskLevel
    confidence_score: float = Field(..., ge=0, le=1)
    findings: Dict
    deficiencies: Dict
    recommendations: List[str]
    metadata: Optional[Dict] = None
    validator_version: str
    validated_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class DocumentAnalysisBase(BaseModel):
    """Base schema for document analysis."""
    document_id: str = Field(..., description="ID of the document to analyze")

class DocumentAnalysisCreate(DocumentAnalysisBase):
    """Schema for creating a document analysis request."""
    pass

class DocumentAnalysisResponse(DocumentAnalysisBase):
    """Schema for document analysis response."""
    id: str
    analyzer_version: str
    content_type: str
    sections: Dict
    confidence_scores: Dict
    processing_status: str
    error_details: Optional[Dict] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class RequirementExtractionResponse(BaseModel):
    """Schema for requirement extraction response."""
    id: str
    requirement_text: str
    section: str
    subsection: Optional[str] = None
    page_number: Optional[int] = None
    confidence_score: float = Field(..., ge=0, le=1)
    metadata: Optional[Dict] = None
    validation_status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SectionMappingResponse(BaseModel):
    """Schema for section mapping response."""
    id: str
    document_id: str
    policy_requirement_id: str
    section_text: str
    match_score: float = Field(..., ge=0, le=1)
    is_validated: bool
    validator_notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ValidationError(BaseModel):
    """Schema for validation error response."""
    detail: str
    error_type: str
    metadata: Optional[Dict] = None

class ValidationResponse(BaseModel):
    """Schema for general validation response."""
    success: bool
    message: str
    data: Optional[Dict] = None
    errors: Optional[List[ValidationError]] = None 