"""
Custom exception handling for ComplianceMax.
"""

from typing import Dict, Any, Optional
from fastapi import HTTPException
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)
class ComplianceMaxError(Exception):
    """Base exception for ComplianceMax errors."""
    pass

class PermissionDeniedError(ComplianceMaxError):
    """Raised when a permission check fails."""
    pass

class IntegrityError(ComplianceMaxError):
    """Raised when an integrity check fails."""
    pass

class ServiceError(ComplianceMaxError):
    """Raised for all other service-layer errors."""
    pass
class DatabaseError(ComplianceMaxError):
    """Raised for generic database errors."""
    pass

class ComplianceMaxException(Exception):
    """Base exception for ComplianceMax."""

    def __init__(
        self,
        message: str,
        status_code: int = HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        """Initialize exception."""
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            "message": self.message,
            "status_code": self.status_code,
            "details": self.details
        }

    def to_http_exception(self) -> HTTPException:
        """Convert to FastAPI HTTPException."""
        return HTTPException(
            status_code=self.status_code,
            detail=self.to_dict()
        )

class DocumentProcessingError(ComplianceMaxException):
    """Document processing related errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = HTTP_422_UNPROCESSABLE_ENTITY
    ):
        super().__init__(message, status_code, details)

class StorageError(ComplianceMaxException):
    """Storage related errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = HTTP_500_INTERNAL_SERVER_ERROR
    ):
        super().__init__(message, status_code, details)

class ComplianceAnalysisError(ComplianceMaxException):
    """Compliance analysis related errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = HTTP_422_UNPROCESSABLE_ENTITY
    ):
        super().__init__(message, status_code, details)

class AuthenticationError(ComplianceMaxException):
    """Authentication related errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = HTTP_401_UNAUTHORIZED
    ):
        super().__init__(message, status_code, details)

class AuthorizationError(ComplianceMaxException):
    """Authorization related errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = HTTP_403_FORBIDDEN
    ):
        super().__init__(message, status_code, details)

class ValidationError(ComplianceMaxException):
    """Validation related errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = HTTP_400_BAD_REQUEST
    ):
        super().__init__(message, status_code, details)

class ResourceNotFoundError(ComplianceMaxException):
    """Resource not found errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = HTTP_404_NOT_FOUND
    ):
        super().__init__(message, status_code, details)

class ResourceConflictError(ComplianceMaxException):
    """Resource conflict errors."""

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = HTTP_409_CONFLICT
    ):
        super().__init__(message, status_code, details)

def handle_exception(exc: Exception) -> HTTPException:
    """Convert any exception to HTTPException."""
    if isinstance(exc, ComplianceMaxException):
        return exc.to_http_exception()

    # Handle unexpected exceptions
    return HTTPException(
        status_code=HTTP_500_INTERNAL_SERVER_ERROR,
        detail={
            "message": "An unexpected error occurred",
            "error": str(exc),
            "status_code": HTTP_500_INTERNAL_SERVER_ERROR
        }
    )
from fastapi import Request
from fastapi.responses import JSONResponse

def handle_compliance_max_error(request: Request, exc: ComplianceMaxError) -> JSONResponse:
    """
    Stub exception handler for ComplianceMaxError.
    """
    # In a real app you'd format/log the error; for now just return a 500 response.
    return JSONResponse({"detail": str(exc)}, status_code=500)
