from sqlalchemy import Column, Integer, String, Boolean
from sqlalchemy.orm import relationship
from app.core.database import Base
from app.models.base import TimestampMixin

class User(Base, TimestampMixin):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=True)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    totp_secret = Column(String, nullable=True)
    is_two_factor_enabled = Column(Boolean, default=False)

    # Relationships
    documents = relationship("Document", back_populates="user", cascade="all, delete-orphan")
    applications = relationship("FEMAApplication", back_populates="user", cascade="all, delete-orphan", passive_deletes=True)
    reviews = relationship("ComplianceReview", back_populates="reviewer", cascade="all, delete-orphan", passive_deletes=True)
    cbcs_compliance_reviews = relationship("CBCSComplianceReview", back_populates="reviewer", cascade="all, delete-orphan", passive_deletes=True)
    
    def __repr__(self):
        return f"<User {self.username}>"
    
    def record_login_attempt(self, success=True):
        """Record a login attempt."""
        # In a real implementation, you'd store login attempts
        # For now, this is just a placeholder
        pass