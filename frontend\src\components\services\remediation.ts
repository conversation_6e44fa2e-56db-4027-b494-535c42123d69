import axios from 'axios';

interface RemediationPlan {
  id: string;
  title: string;
  description: string;
  status: string;
  created_at: string;
  updated_at: string;
  steps: RemediationStep[];
}

interface RemediationStep {
  id: string;
  description: string;
  status: string;
  due_date: string;
  completed_date?: string;
}

const API_URL = 'http://localhost:8000';

export const getRemediationPlans = async (): Promise<RemediationPlan[]> => {
  try {
    const response = await axios.get(`${API_URL}/api/v1/remediation/plans`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching remediation plans:', error);
    return [];
  }
};

export const getRemediationPlanById = async (id: string): Promise<RemediationPlan | null> => {
  try {
    const response = await axios.get(`${API_URL}/api/v1/remediation/plans/${id}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching remediation plan ${id}:`, error);
    return null;
  }
}; 