"""
Database configuration and utilities.
"""
from contextlib import contextmanager
from typing import Generator
import logging

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.declarative import declarative_base

from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import MetricsCollector

logger = get_logger(__name__)
metrics = MetricsCollector()

# Create the declarative base that will be used by all models
Base = declarative_base()

# Database configuration
DATABASE_URL = str(settings.DATABASE_URL)
engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_pre_ping=True,
    pool_recycle=3600,  # Recycle connections after 1 hour
)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

@contextmanager
def get_db() -> Generator[Session, None, None]:
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    except SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        metrics.db_errors.inc()
        db.rollback()
        raise
    finally:
        db.close()

def init_db() -> None:
    """Initialize database."""
    try:
        # Import all models here
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")
    except SQLAlchemyError as e:
        logger.error(f"Error initializing database: {str(e)}")
        metrics.db_errors.inc()
        raise

def check_db_connection() -> bool:
    """Check database connection."""
    try:
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        return True
    except SQLAlchemyError as e:
        logger.error(f"Database connection error: {str(e)}")
        metrics.db_errors.inc()
        return False

def get_db_stats() -> dict:
    """Get database connection pool statistics."""
    return {
        "pool_size": engine.pool.size(),
        "checkedin": engine.pool.checkedin(),
        "checkedout": engine.pool.checkedout(),
        "overflow": engine.pool.overflow(),
        "checkedout_overflow": engine.pool.overflow_checkedout(),
    }