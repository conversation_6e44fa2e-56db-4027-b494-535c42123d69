import { useState, useEffect } from 'react';
import axios from 'axios';
import { websocketService } from '../services/websocket';

interface RemediationPlan {
  steps: Array<{
    action: string;
    deadline: string;
    assignee: string;
    status: 'pending' | 'in_progress' | 'completed';
  }>;
  target_completion_date: string | null;
}

interface Review {
  id: number;
  type: string;
  status: string;
  findings: Record<string, any>;
  created_at: string;
  updated_at?: string;
  // Cost Review specific fields
  total_cost?: number;
  cost_breakdown?: Array<{
    category: string;
    description: string;
    amount: number;
    justification: string;
  }>;
  alternatives?: Array<{
    description: string;
    estimatedCost: number;
    pros: string[];
    cons: string[];
  }>;
  // Compliance Review specific fields
  compliance_score?: number;
  violations?: Array<{
    policySection: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
    remediation: string;
  }>;
  remediation_plan?: RemediationPlan;
}

interface UseReviewResult {
  reviews: Review[];
  loading: boolean;
  error: Error | null;
  createReview: (type: string, data: any) => Promise<Review>;
  updateStatus: (reviewId: number, status: string) => Promise<Review>;
  addFindings: (reviewId: number, findings: Record<string, any>) => Promise<Review>;
}

export const useReview = (projectId: number): UseReviewResult => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/v1/reviews/project/${projectId}`);
      setReviews(response.data);
      setError(null);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();

    // Connect to WebSocket
    websocketService.connect(projectId.toString());

    // Add WebSocket message handlers
    websocketService.addMessageHandler('review_created', (data) => {
      setReviews(prev => [...prev, data.review]);
    });

    websocketService.addMessageHandler('review_updated', (data) => {
      setReviews(prev => prev.map(review => 
        review.id === data.review.id ? data.review : review
      ));
    });

    // Cleanup
    return () => {
      websocketService.removeMessageHandler('review_created');
      websocketService.removeMessageHandler('review_updated');
      websocketService.disconnect();
    };
  }, [projectId]);

  const createReview = async (type: string, data: any): Promise<Review> => {
    try {
      const response = await axios.post(`/api/v1/reviews/${type}`, {
        project_id: projectId,
        ...data
      });
      await fetchReviews(); // Refresh the list
      return response.data;
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  };

  const updateStatus = async (reviewId: number, status: string): Promise<Review> => {
    try {
      const response = await axios.patch(`/api/v1/reviews/${reviewId}/status`, {
        status
      });
      await fetchReviews(); // Refresh the list
      return response.data;
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  };

  const addFindings = async (reviewId: number, findings: Record<string, any>): Promise<Review> => {
    try {
      const response = await axios.patch(`/api/v1/reviews/${reviewId}/findings`, {
        findings
      });
      await fetchReviews(); // Refresh the list
      return response.data;
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  };

  return {
    reviews,
    loading,
    error,
    createReview,
    updateStatus,
    addFindings
  };
};
