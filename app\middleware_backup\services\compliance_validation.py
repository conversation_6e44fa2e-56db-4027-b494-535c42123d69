from typing import List, Dict, Optional, Any
from datetime import datetime
import logging
from app.services.document_analysis import DocumentAnalyzer
from app.services.document_comparison import DocumentComparator

logger = logging.getLogger(__name__)

class ComplianceValidator:
    def __init__(self):
        self.analyzer = DocumentAnalyzer()
        self.comparator = DocumentComparator()
    
    async def validate_compliance(
        self,
        applicant_doc_path: str,
        policy_docs: List[Dict[str, str]],
        dr_number: str
    ) -> Dict[str, Any]:
        """Validate applicant document compliance against policy documents."""
        try:
            # Analyze applicant document
            applicant_analysis = await self.analyzer.process_document(applicant_doc_path)
            
            # Compare with each policy document
            policy_comparisons = []
            for policy in policy_docs:
                comparison = await self.comparator.compare_documents(
                    applicant_doc_path,
                    policy["path"]
                )
                
                policy_comparisons.append({
                    "policy_id": policy["id"],
                    "policy_number": policy["policy_number"],
                    "comparison": comparison
                })
            
            # Analyze deficiencies
            deficiencies = await self._analyze_deficiencies(
                applicant_analysis,
                policy_comparisons
            )
            
            # Validate temporal requirements
            temporal_validation = await self._validate_temporal_requirements(
                applicant_analysis,
                policy_comparisons,
                dr_number
            )
            
            # Generate compliance report
            report = await self._generate_compliance_report(
                applicant_analysis,
                policy_comparisons,
                deficiencies,
                temporal_validation
            )
            
            return {
                "status": "completed",
                "timestamp": datetime.utcnow(),
                "dr_number": dr_number,
                "deficiencies": deficiencies,
                "temporal_validation": temporal_validation,
                "report": report,
                "policy_comparisons": policy_comparisons
            }
            
        except Exception as e:
            logger.error(f"Error validating compliance: {str(e)}")
            raise
    
    async def _analyze_deficiencies(
        self,
        applicant_analysis: Dict[str, Any],
        policy_comparisons: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Analyze compliance deficiencies."""
        try:
            deficiencies = []
            
            for policy_comp in policy_comparisons:
                comparison = policy_comp["comparison"]
                
                # Check unmatched requirements
                if "requirement_comparison" in comparison:
                    req_comp = comparison["requirement_comparison"]
                    
                    for req in req_comp.get("unmatched_doc2", []):
                        deficiencies.append({
                            "type": "missing_requirement",
                            "policy_id": policy_comp["policy_id"],
                            "policy_number": policy_comp["policy_number"],
                            "requirement": req,
                            "severity": "high",
                            "impact": "Required element not found in applicant document"
                        })
                
                # Check temporal mismatches
                if "temporal_comparison" in comparison:
                    temp_comp = comparison["temporal_comparison"]
                    
                    for date_comp in temp_comp.get("common_dates", []):
                        if date_comp["context_similarity"] < 0.7:
                            deficiencies.append({
                                "type": "temporal_mismatch",
                                "policy_id": policy_comp["policy_id"],
                                "policy_number": policy_comp["policy_number"],
                                "date": date_comp["date"],
                                "policy_context": date_comp["context2"],
                                "applicant_context": date_comp["context1"],
                                "severity": "medium",
                                "impact": "Date usage context differs from policy requirements"
                            })
            
            return deficiencies
            
        except Exception as e:
            logger.error(f"Error analyzing deficiencies: {str(e)}")
            raise
    
    async def _validate_temporal_requirements(
        self,
        applicant_analysis: Dict[str, Any],
        policy_comparisons: List[Dict[str, Any]],
        dr_number: str
    ) -> Dict[str, Any]:
        """Validate temporal requirements and policy applicability."""
        try:
            temporal_issues = []
            
            # Extract dates from applicant document
            applicant_dates = {
                item["date"]: item
                for item in applicant_analysis.get("temporal_info", [])
            }
            
            for policy_comp in policy_comparisons:
                comparison = policy_comp["comparison"]
                
                if "temporal_comparison" in comparison:
                    temp_comp = comparison["temporal_comparison"]
                    
                    # Check for missing required dates
                    for date_info in temp_comp.get("only_in_doc2", []):
                        temporal_issues.append({
                            "type": "missing_date",
                            "policy_id": policy_comp["policy_id"],
                            "policy_number": policy_comp["policy_number"],
                            "date": date_info["date"],
                            "required_context": date_info["info"]["context"],
                            "severity": "high",
                            "impact": "Required date reference missing from applicant document"
                        })
                    
                    # Check date context alignment
                    for date_comp in temp_comp.get("common_dates", []):
                        if date_comp["context_similarity"] < 0.7:
                            temporal_issues.append({
                                "type": "context_mismatch",
                                "policy_id": policy_comp["policy_id"],
                                "policy_number": policy_comp["policy_number"],
                                "date": date_comp["date"],
                                "policy_context": date_comp["context2"],
                                "applicant_context": date_comp["context1"],
                                "severity": "medium",
                                "impact": "Date usage does not align with policy requirements"
                            })
            
            return {
                "temporal_issues": temporal_issues,
                "total_issues": len(temporal_issues),
                "dr_number": dr_number
            }
            
        except Exception as e:
            logger.error(f"Error validating temporal requirements: {str(e)}")
            raise
    
    async def _generate_compliance_report(
        self,
        applicant_analysis: Dict[str, Any],
        policy_comparisons: List[Dict[str, Any]],
        deficiencies: List[Dict[str, Any]],
        temporal_validation: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a comprehensive compliance report."""
        try:
            # Calculate compliance scores
            requirement_score = await self._calculate_requirement_score(
                policy_comparisons,
                deficiencies
            )
            
            temporal_score = await self._calculate_temporal_score(
                temporal_validation
            )
            
            structure_score = await self._calculate_structure_score(
                policy_comparisons
            )
            
            # Calculate overall compliance score
            overall_score = (
                requirement_score * 0.5 +  # Requirements are most important
                temporal_score * 0.3 +     # Temporal compliance is next
                structure_score * 0.2      # Structure is least critical
            )
            
            # Generate findings
            findings = await self._generate_findings(
                deficiencies,
                temporal_validation
            )
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(
                findings,
                overall_score
            )
            
            return {
                "scores": {
                    "overall": overall_score,
                    "requirements": requirement_score,
                    "temporal": temporal_score,
                    "structure": structure_score
                },
                "findings": findings,
                "recommendations": recommendations,
                "deficiency_count": len(deficiencies),
                "temporal_issues_count": len(temporal_validation["temporal_issues"]),
                "timestamp": datetime.utcnow(),
                "status": "complete" if overall_score >= 0.8 else "needs_revision"
            }
            
        except Exception as e:
            logger.error(f"Error generating compliance report: {str(e)}")
            raise
    
    async def _calculate_requirement_score(
        self,
        policy_comparisons: List[Dict[str, Any]],
        deficiencies: List[Dict[str, Any]]
    ) -> float:
        """Calculate requirement compliance score."""
        try:
            total_requirements = 0
            matched_requirements = 0
            
            for policy_comp in policy_comparisons:
                comparison = policy_comp["comparison"]
                if "requirement_comparison" in comparison:
                    req_comp = comparison["requirement_comparison"]
                    total_requirements += req_comp["total_requirements2"]  # Policy requirements
                    matched_requirements += req_comp["matched_count"]
            
            return matched_requirements / total_requirements if total_requirements > 0 else 0
            
        except Exception as e:
            logger.error(f"Error calculating requirement score: {str(e)}")
            raise
    
    async def _calculate_temporal_score(
        self,
        temporal_validation: Dict[str, Any]
    ) -> float:
        """Calculate temporal compliance score."""
        try:
            issues = temporal_validation["temporal_issues"]
            if not issues:
                return 1.0
            
            # Weight issues by severity
            severity_weights = {
                "high": 1.0,
                "medium": 0.6,
                "low": 0.3
            }
            
            total_weight = sum(
                severity_weights[issue["severity"]]
                for issue in issues
            )
            
            # More issues = lower score
            return max(0, 1 - (total_weight * 0.2))
            
        except Exception as e:
            logger.error(f"Error calculating temporal score: {str(e)}")
            raise
    
    async def _calculate_structure_score(
        self,
        policy_comparisons: List[Dict[str, Any]]
    ) -> float:
        """Calculate structural compliance score."""
        try:
            structure_scores = []
            
            for policy_comp in policy_comparisons:
                comparison = policy_comp["comparison"]
                if "structure_comparison" in comparison:
                    struct_comp = comparison["structure_comparison"]
                    if "overall_similarity" in struct_comp:
                        structure_scores.append(struct_comp["overall_similarity"])
            
            return sum(structure_scores) / len(structure_scores) if structure_scores else 0
            
        except Exception as e:
            logger.error(f"Error calculating structure score: {str(e)}")
            raise
    
    async def _generate_findings(
        self,
        deficiencies: List[Dict[str, Any]],
        temporal_validation: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate detailed findings from deficiencies and temporal issues."""
        try:
            findings = []
            
            # Process requirement deficiencies
            for deficiency in deficiencies:
                findings.append({
                    "type": "deficiency",
                    "category": deficiency["type"],
                    "severity": deficiency["severity"],
                    "description": deficiency["impact"],
                    "policy_reference": {
                        "id": deficiency["policy_id"],
                        "number": deficiency["policy_number"]
                    },
                    "details": deficiency
                })
            
            # Process temporal issues
            for issue in temporal_validation["temporal_issues"]:
                findings.append({
                    "type": "temporal_issue",
                    "category": issue["type"],
                    "severity": issue["severity"],
                    "description": issue["impact"],
                    "policy_reference": {
                        "id": issue["policy_id"],
                        "number": issue["policy_number"]
                    },
                    "details": issue
                })
            
            return sorted(
                findings,
                key=lambda x: {"high": 0, "medium": 1, "low": 2}[x["severity"]]
            )
            
        except Exception as e:
            logger.error(f"Error generating findings: {str(e)}")
            raise
    
    async def _generate_recommendations(
        self,
        findings: List[Dict[str, Any]],
        overall_score: float
    ) -> List[Dict[str, Any]]:
        """Generate recommendations based on findings and scores."""
        try:
            recommendations = []
            
            # Group findings by category
            findings_by_category = {}
            for finding in findings:
                category = finding["category"]
                if category not in findings_by_category:
                    findings_by_category[category] = []
                findings_by_category[category].append(finding)
            
            # Generate category-specific recommendations
            for category, category_findings in findings_by_category.items():
                if category == "missing_requirement":
                    recommendations.append({
                        "category": "requirements",
                        "priority": "high",
                        "action": "Add missing required elements",
                        "details": [
                            f"Add required element: {f['details']['requirement']['text']}"
                            for f in category_findings
                        ]
                    })
                
                elif category == "temporal_mismatch":
                    recommendations.append({
                        "category": "temporal",
                        "priority": "high",
                        "action": "Update temporal references",
                        "details": [
                            f"Revise date context for {f['details']['date']}"
                            for f in category_findings
                        ]
                    })
                
                elif category == "context_mismatch":
                    recommendations.append({
                        "category": "context",
                        "priority": "medium",
                        "action": "Align content with policy context",
                        "details": [
                            f"Review and align context: {f['description']}"
                            for f in category_findings
                        ]
                    })
            
            # Add overall recommendation based on score
            if overall_score < 0.8:
                recommendations.append({
                    "category": "overall",
                    "priority": "high",
                    "action": "Major revision needed",
                    "details": [
                        "Document requires significant updates to meet compliance requirements",
                        f"Current compliance score: {overall_score:.2%}"
                    ]
                })
            elif overall_score < 0.9:
                recommendations.append({
                    "category": "overall",
                    "priority": "medium",
                    "action": "Minor revisions needed",
                    "details": [
                        "Document needs minor updates to improve compliance",
                        f"Current compliance score: {overall_score:.2%}"
                    ]
                })
            
            return sorted(
                recommendations,
                key=lambda x: {"high": 0, "medium": 1, "low": 2}[x["priority"]]
            )
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            raise
