"""
API Gateway for ComplianceMax integration framework.
"""
from datetime import datetime
from typing import Dict, List, Optional
import asyncio
import json
import logging
from dataclasses import dataclass
from enum import Enum

from fastapi import FastAPI, HTTPException, Depends
from fastapi.security import OAuth2Pass<PERSON><PERSON>earer
from starlette.middleware.cors import CORSMiddleware

from app.core.monitoring.metrics import track_api_request
from app.core.security.analytics import SecurityAnalytics
from app.core.compliance_analyzer import Compliance<PERSON>nalyzer
from app.core.analytics.ml_engine import MLEngine

logger = logging.getLogger(__name__)

class IntegrationType(Enum):
    """Types of external system integrations."""
    DOCUMENT_MANAGEMENT = "document_management"
    HR_SYSTEM = "hr_system"
    ERP = "erp"
    CLOUD_STORAGE = "cloud_storage"
    THIRD_PARTY = "third_party"

@dataclass
class IntegrationConfig:
    """External system integration configuration."""
    integration_type: IntegrationType
    system_id: str
    api_url: str
    auth_config: Dict
    sync_interval: int
    retry_config: Dict
    transform_rules: List[Dict]

class APIGateway:
    """API Gateway for external system integration."""
    
    def __init__(self):
        self.app = FastAPI(title="ComplianceMax API Gateway")
        self.security = OAuth2PasswordBearer(tokenUrl="token")
        self.integrations = {}
        self.analytics = SecurityAnalytics()
        self.compliance = ComplianceAnalyzer()
        self.ml_engine = MLEngine()
        
        # Configure CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Initialize routes
        self._init_routes()
        
        # Start background tasks
        self.sync_task = asyncio.create_task(self._periodic_sync())
        self.health_check_task = asyncio.create_task(self._periodic_health_check())
    
    def _init_routes(self):
        """Initialize API routes."""
        # Health check endpoint
        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy"}
        
        # Document management endpoints
        @self.app.post("/documents")
        async def create_document(document: Dict, token: str = Depends(self.security)):
            return await self._handle_document_creation(document, token)
        
        @self.app.get("/documents/{doc_id}")
        async def get_document(doc_id: str, token: str = Depends(self.security)):
            return await self._handle_document_retrieval(doc_id, token)
        
        # Compliance check endpoints
        @self.app.post("/compliance/check")
        async def check_compliance(request: Dict, token: str = Depends(self.security)):
            return await self._handle_compliance_check(request, token)
        
        @self.app.get("/compliance/status/{check_id}")
        async def get_compliance_status(check_id: str, token: str = Depends(self.security)):
            return await self._handle_compliance_status(check_id, token)
        
        # Analytics endpoints
        @self.app.post("/analytics/predict")
        async def predict_compliance(request: Dict, token: str = Depends(self.security)):
            return await self._handle_prediction(request, token)
        
        @self.app.get("/analytics/trends")
        async def get_trends(token: str = Depends(self.security)):
            return await self._handle_trends_analysis(token)
        
        # Integration management endpoints
        @self.app.post("/integrations")
        async def create_integration(config: IntegrationConfig, token: str = Depends(self.security)):
            return await self._handle_integration_creation(config, token)
        
        @self.app.get("/integrations/{integration_id}")
        async def get_integration(integration_id: str, token: str = Depends(self.security)):
            return await self._handle_integration_status(integration_id, token)
    
    async def _handle_document_creation(self, document: Dict, token: str) -> Dict:
        """Handle document creation request."""
        try:
            # Validate document
            await self._validate_document(document)
            
            # Process document
            doc_id = await self._process_document(document)
            
            # Track metrics
            track_api_request("document_creation", True)
            
            return {"status": "success", "doc_id": doc_id}
            
        except Exception as e:
            track_api_request("document_creation", False)
            raise HTTPException(status_code=400, detail=str(e))
    
    async def _handle_compliance_check(self, request: Dict, token: str) -> Dict:
        """Handle compliance check request."""
        try:
            # Validate request
            await self._validate_compliance_request(request)
            
            # Perform compliance check
            result = await self.compliance.check_compliance(
                request["document_id"],
                request["policy_id"]
            )
            
            # Track metrics
            track_api_request("compliance_check", True)
            
            return {
                "status": "success",
                "check_id": result.check_id,
                "compliance_status": result.status.value,
                "score": result.score
            }
            
        except Exception as e:
            track_api_request("compliance_check", False)
            raise HTTPException(status_code=400, detail=str(e))
    
    async def _handle_prediction(self, request: Dict, token: str) -> Dict:
        """Handle compliance prediction request."""
        try:
            # Validate request
            await self._validate_prediction_request(request)
            
            # Get prediction
            prediction = await self.ml_engine.predict_risk(
                request["document_id"],
                request.get("compliance_history", [])
            )
            
            # Track metrics
            track_api_request("prediction", True)
            
            return {
                "status": "success",
                "prediction_id": str(prediction.timestamp),
                "risk_score": prediction.score,
                "confidence": prediction.confidence,
                "explanation": prediction.explanation
            }
            
        except Exception as e:
            track_api_request("prediction", False)
            raise HTTPException(status_code=400, detail=str(e))
    
    async def _handle_integration_creation(self, config: IntegrationConfig, token: str) -> Dict:
        """Handle integration creation request."""
        try:
            # Validate configuration
            await self._validate_integration_config(config)
            
            # Create integration
            integration_id = await self._create_integration(config)
            
            # Track metrics
            track_api_request("integration_creation", True)
            
            return {
                "status": "success",
                "integration_id": integration_id
            }
            
        except Exception as e:
            track_api_request("integration_creation", False)
            raise HTTPException(status_code=400, detail=str(e))
    
    async def _periodic_sync(self):
        """Periodically synchronize with external systems."""
        while True:
            try:
                for integration_id, config in self.integrations.items():
                    await self._sync_integration(integration_id, config)
                    
            except Exception as e:
                logger.error(f"Error in integration sync: {str(e)}")
            
            await asyncio.sleep(300)  # Run every 5 minutes
    
    async def _periodic_health_check(self):
        """Periodically check integration health."""
        while True:
            try:
                for integration_id, config in self.integrations.items():
                    await self._check_integration_health(integration_id, config)
                    
            except Exception as e:
                logger.error(f"Error in integration health check: {str(e)}")
            
            await asyncio.sleep(60)  # Run every minute
    
    async def _sync_integration(self, integration_id: str, config: IntegrationConfig):
        """Synchronize with external system."""
        try:
            # Perform sync based on integration type
            if config.integration_type == IntegrationType.DOCUMENT_MANAGEMENT:
                await self._sync_documents(config)
            elif config.integration_type == IntegrationType.HR_SYSTEM:
                await self._sync_hr_data(config)
            elif config.integration_type == IntegrationType.ERP:
                await self._sync_erp_data(config)
            elif config.integration_type == IntegrationType.CLOUD_STORAGE:
                await self._sync_cloud_storage(config)
            elif config.integration_type == IntegrationType.THIRD_PARTY:
                await self._sync_third_party(config)
                
        except Exception as e:
            logger.error(f"Error syncing integration {integration_id}: {str(e)}")
    
    async def _check_integration_health(self, integration_id: str, config: IntegrationType):
        """Check health of external system integration."""
        try:
            # Perform health check
            is_healthy = await self._test_connection(config)
            
            if not is_healthy:
                logger.warning(f"Integration {integration_id} health check failed")
                
        except Exception as e:
            logger.error(f"Error checking integration {integration_id} health: {str(e)}")
    
    def start(self):
        """Start the API gateway."""
        import uvicorn
        uvicorn.run(self.app, host="0.0.0.0", port=8000) 