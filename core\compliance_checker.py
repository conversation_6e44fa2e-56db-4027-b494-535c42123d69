"""
Compliance Checker Module

Checks documents for compliance with requirements and policies.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple
from pathlib import Path
import json
from dataclasses import dataclass
from datetime import datetime
import re
from .document_processor import DocumentProcessor
from .policy_matcher import PolicyMatcher

@dataclass
class ComplianceResult:
    """Represents the result of a compliance check."""
    document_id: str
    is_compliant: bool
    matched_requirements: List[Dict]
    missing_requirements: List[Dict]
    issues: List[str]
    recommendations: List[str]
    timestamp: datetime

class ComplianceChecker:
    """Checks documents for compliance with requirements and policies."""
    
    def __init__(self, storage_path: Path):
        """
        Initialize the compliance checker.
        
        Args:
            storage_path: Path to the document storage directory
        """
        self.storage_path = Path(storage_path)
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        self.document_processor = DocumentProcessor(storage_path)
        self.policy_matcher = PolicyMatcher(storage_path)
        
        # Load standard requirements and policies
        self.requirements = {}
        self.policies = {}
        
        # Compliance thresholds
        self.confidence_threshold = 0.7
        self.required_match_percentage = 0.8

    def _setup_logging(self):
        """Configure logging for the compliance checker."""
        handler = logging.FileHandler('compliance_checker.log')
        handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def load_requirements(self, requirements_file: Path):
        """Load compliance requirements from file."""
        try:
            with open(requirements_file, 'r', encoding='utf-8') as f:
                self.requirements = json.load(f)
            self.logger.info(f"Loaded {len(self.requirements)} requirements")
        except Exception as e:
            self.logger.error(f"Error loading requirements: {str(e)}")
            raise

    def load_policies(self, policies_dir: Path):
        """Load policies from directory."""
        self.policy_matcher.load_policies(policies_dir)
        self.policies = self.policy_matcher.policies
        self.logger.info(f"Loaded {len(self.policies)} policies")

    def check_compliance(self, document_path: Path) -> ComplianceResult:
        """
        Check a document for compliance with requirements.
        
        Args:
            document_path: Path to the document to check
            
        Returns:
            ComplianceResult object with compliance check results
        """
        try:
            # Process the document
            doc_info = self.document_processor.process_document(document_path)
            
            # Match requirements
            matches = self._match_requirements(doc_info['content'])
            
            # Analyze compliance
            is_compliant, matched_reqs, missing_reqs = self._analyze_compliance(matches)
            
            # Generate recommendations
            issues, recommendations = self._generate_recommendations(matched_reqs, missing_reqs)
            
            return ComplianceResult(
                document_id=doc_info['metadata']['hash'],
                is_compliant=is_compliant,
                matched_requirements=matched_reqs,
                missing_requirements=missing_reqs,
                issues=issues,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error checking compliance for {document_path}: {str(e)}")
            raise

    def _match_requirements(self, document_content: str) -> List[Dict]:
        """Match document content against requirements."""
        matches = []
        
        for req_id, req_data in self.requirements.items():
            confidence = self._calculate_requirement_match(document_content, req_data)
            
            if confidence >= self.confidence_threshold:
                matches.append({
                    'requirement_id': req_id,
                    'requirement_data': req_data,
                    'confidence': confidence,
                    'matched_text': self._extract_matching_text(document_content, req_data)
                })
        
        return matches

    def _calculate_requirement_match(self, document_content: str, requirement: Dict) -> float:
        """Calculate how well a document matches a requirement."""
        # Check for explicit criteria matches
        criteria_matches = self._check_criteria_matches(document_content, requirement)
        
        # Check for policy references
        reference_matches = self._check_reference_matches(document_content, requirement)
        
        # Check for semantic similarity
        semantic_similarity = self._calculate_semantic_similarity(document_content, requirement)
        
        # Weighted average of different matching methods
        weights = {
            'criteria': 0.5,
            'references': 0.3,
            'semantic': 0.2
        }
        
        confidence = (
            criteria_matches * weights['criteria'] +
            reference_matches * weights['references'] +
            semantic_similarity * weights['semantic']
        )
        
        return confidence

    def _check_criteria_matches(self, document_content: str, requirement: Dict) -> float:
        """Check how many requirement criteria are met in the document."""
        criteria = requirement.get('criteria', [])
        if not criteria:
            return 0.0
            
        matches = 0
        for criterion in criteria:
            if self._matches_criterion(document_content, criterion):
                matches += 1
                
        return matches / len(criteria)

    def _matches_criterion(self, document_content: str, criterion: str) -> bool:
        """Check if a specific criterion is met in the document."""
        # Convert criterion to a regex pattern
        pattern = self._criterion_to_pattern(criterion)
        
        # Check for match
        return bool(re.search(pattern, document_content, re.IGNORECASE))

    def _criterion_to_pattern(self, criterion: str) -> str:
        """Convert a criterion to a regex pattern."""
        # Replace common requirement phrases with regex patterns
        patterns = {
            r'must\s+be': r'(?:must|shall|is required to)\s+be',
            r'shall\s+be': r'(?:must|shall|is required to)\s+be',
            r'is required to\s+be': r'(?:must|shall|is required to)\s+be',
            r'minimum': r'(?:minimum|at least|greater than or equal to|≥)',
            r'maximum': r'(?:maximum|at most|less than or equal to|≤)',
            r'equal to': r'(?:equal to|equals|=)',
            r'greater than': r'(?:greater than|>)',
            r'less than': r'(?:less than|<)'
        }
        
        pattern = criterion
        for key, value in patterns.items():
            pattern = re.sub(key, value, pattern, flags=re.IGNORECASE)
            
        return pattern

    def _check_reference_matches(self, document_content: str, requirement: Dict) -> float:
        """Check how many requirement references are found in the document."""
        references = requirement.get('references', [])
        if not references:
            return 0.0
            
        matches = 0
        for ref in references:
            if self._matches_reference(document_content, ref):
                matches += 1
                
        return matches / len(references)

    def _matches_reference(self, document_content: str, reference: str) -> bool:
        """Check if a specific reference is found in the document."""
        # Extract document and section from reference
        match = re.match(r'\[([\w\s-]+)\]\s+(?:Section|§)\s+([\d\.]+)', reference)
        if not match:
            return False
            
        document, section = match.groups()
        
        # Create patterns to match reference
        patterns = [
            rf"{document}\s+(?:Section|§)\s+{section}",
            rf"Section\s+{section}\s+of\s+{document}",
            rf"{document}(?:\s+|\s*,\s*)§\s*{section}"
        ]
        
        return any(re.search(pattern, document_content, re.IGNORECASE) for pattern in patterns)

    def _calculate_semantic_similarity(self, document_content: str, requirement: Dict) -> float:
        """Calculate semantic similarity between document and requirement."""
        # Use policy matcher's semantic matching capability
        requirement_text = requirement.get('description', '')
        if not requirement_text:
            return 0.0
            
        # Create temporary policy with requirement text
        temp_policy = {'content': requirement_text}
        
        # Use policy matcher to calculate similarity
        vectorizer = self.policy_matcher.vectorizer
        doc_vector = vectorizer.transform([document_content])
        req_vector = vectorizer.transform([requirement_text])
        
        similarity = float(doc_vector.dot(req_vector.T).toarray()[0][0])
        return min(similarity, 1.0)  # Cap at 1.0

    def _analyze_compliance(self, matches: List[Dict]) -> Tuple[bool, List[Dict], List[Dict]]:
        """Analyze matches to determine compliance status."""
        matched_requirements = []
        missing_requirements = []
        
        # Group matches by requirement
        req_matches = {}
        for match in matches:
            req_id = match['requirement_id']
            if req_id not in req_matches or match['confidence'] > req_matches[req_id]['confidence']:
                req_matches[req_id] = match
        
        # Check which requirements are met
        for req_id, req_data in self.requirements.items():
            if req_id in req_matches and req_matches[req_id]['confidence'] >= self.confidence_threshold:
                matched_requirements.append(req_matches[req_id])
            else:
                missing_requirements.append({
                    'requirement_id': req_id,
                    'requirement_data': req_data
                })
        
        # Calculate overall compliance
        is_compliant = (
            len(matched_requirements) / len(self.requirements) >= self.required_match_percentage
        )
        
        return is_compliant, matched_requirements, missing_requirements

    def _generate_recommendations(self, matched_reqs: List[Dict], missing_reqs: List[Dict]) -> Tuple[List[str], List[str]]:
        """Generate issues and recommendations based on compliance analysis."""
        issues = []
        recommendations = []
        
        # Add issues for missing requirements
        for req in missing_reqs:
            req_data = req['requirement_data']
            issues.append(f"Missing requirement {req['requirement_id']}: {req_data.get('title', 'Untitled')}")
            
            # Generate specific recommendations
            if 'criteria' in req_data:
                for criterion in req_data['criteria']:
                    recommendations.append(f"Add documentation to address: {criterion}")
                    
            if 'references' in req_data:
                for ref in req_data['references']:
                    recommendations.append(f"Include reference to {ref}")
        
        # Add recommendations for improving matched requirements
        for match in matched_reqs:
            if match['confidence'] < 0.9:  # High but not perfect match
                recommendations.append(
                    f"Strengthen documentation for requirement {match['requirement_id']} "
                    f"(current confidence: {match['confidence']:.2f})"
                )
        
        return issues, recommendations

    def _extract_matching_text(self, document_content: str, requirement: Dict) -> str:
        """Extract the relevant text from document that matches the requirement."""
        # Use policy matcher's context extraction
        req_text = requirement.get('description', '')
        if not req_text:
            return ""
            
        return self.policy_matcher._extract_matching_context(req_text, document_content)

    def export_results(self, results: List[ComplianceResult], output_file: Path):
        """Export compliance check results to a JSON file."""
        output_file = Path(output_file)
        
        # Convert results to dictionary format
        results_data = [
            {
                'document_id': r.document_id,
                'is_compliant': r.is_compliant,
                'matched_requirements': r.matched_requirements,
                'missing_requirements': r.missing_requirements,
                'issues': r.issues,
                'recommendations': r.recommendations,
                'timestamp': r.timestamp.isoformat()
            }
            for r in results
        ]
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2)
            self.logger.info(f"Exported compliance results to {output_file}")
        except Exception as e:
            self.logger.error(f"Error exporting results: {str(e)}")
            raise 