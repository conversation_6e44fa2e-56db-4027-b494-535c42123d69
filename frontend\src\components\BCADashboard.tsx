import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Alert, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { useApi } from '../services/api';

const BCADashboard: React.FC = () => {
    const { getBCAAnalyses } = useApi();
    const [analyses, setAnalyses] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchAnalyses = async () => {
            try {
                const response = await getBCAAnalyses();
                setAnalyses(response);
            } catch (err: any) {
                setError(err.response?.data?.detail || 'Failed to load BCA analyses');
            } finally {
                setLoading(false);
            }
        };
        fetchAnalyses();
    }, []);

    if (loading) return <CircularProgress aria-label="Loading BCA analyses" />;
    if (error) return <Alert severity="error">{error}</Alert>;

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom>
                BCA Dashboard
            </Typography>
            {analyses.length === 0 ? (
                <Typography>No BCA analyses found.</Typography>
            ) : (
                <Table aria-label="BCA analyses table">
                    <TableHead>
                        <TableRow>
                            <TableCell>Project Name</TableCell>
                            <TableCell>BCR</TableCell>
                            <TableCell>NPV</TableCell>
                            <TableCell>Status</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {analyses.map((analysis) => (
                            <TableRow key={analysis.id}>
                                <TableCell>{analysis.project_name}</TableCell>
                                <TableCell>{analysis.bcr}</TableCell>
                                <TableCell>{analysis.npv}</TableCell>
                                <TableCell>{analysis.status}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            )}
        </Box>
    );
};

export default BCADashboard;