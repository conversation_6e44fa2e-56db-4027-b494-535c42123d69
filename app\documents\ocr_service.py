"""OCR services for document processing.

This module provides OCR (Optical Character Recognition) services for extracting 
text from images and documents. It supports multiple OCR engines and provides
a consistent interface for text extraction.
"""

import logging
from enum import Enum
from typing import List, Dict, Any, Optional, Union
import numpy as np
from pathlib import Path
from app.core.config import settings
from app.utils.circuit_breaker import circuit_breaker

# Graceful degradation for dependencies
try:
    import cv2
except ImportError:
    cv2 = None
    
try:
    import pytesseract
except ImportError:
    pytesseract = None
    
try:
    import easyocr
except ImportError:
    easyocr = None
    
try:
    from paddleocr import PaddleOCR
except ImportError:
    PaddleOCR = None
    
try:
    import pdf2image
except ImportError:
    pdf2image = None

logger = logging.getLogger(__name__)

class OCREngineType(str, Enum):
    """Supported OCR engine types."""
    EASYOCR = "easyocr"
    PADDLEOCR = "paddleocr"
    TESSERACT = "tesseract"

class OCRService:
    """Service for OCR processing with multiple engine support."""
    
    def __init__(self, engine_type: Optional[str] = None, enable_gpu: Optional[bool] = None):
        """Initialize OCR service.
        
        Args:
            engine_type: OCR engine to use. If None, uses the one from settings.
            enable_gpu: Whether to use GPU. If None, uses the setting from settings.
        """
        self.engine_type = engine_type or settings.OCR_ENGINE
        self.enable_gpu = enable_gpu if enable_gpu is not None else settings.ENABLE_GPU
        
        # Lazy load attributes
        self._easyocr_reader = None
        self._paddleocr_reader = None
        
        logger.info(f"OCR Service initialized with engine type: {self.engine_type}")
        
    async def health_check(self) -> Dict[str, Any]:
        """Check health status of OCR service and dependencies.
        
        Returns:
            Dictionary with health status information
        """
        status = {
            "status": "healthy",
            "engine": self.engine_type,
            "dependencies": {
                "cv2": cv2 is not None,
                "pytesseract": pytesseract is not None,
                "easyocr": easyocr is not None,
                "paddleocr": PaddleOCR is not None,
                "pdf2image": pdf2image is not None
            },
            "gpu_enabled": self.enable_gpu
        }
        
        # Check if preferred engine is available
        if self.engine_type == OCREngineType.EASYOCR and easyocr is None:
            status["status"] = "degraded"
            status["message"] = "Preferred engine (EasyOCR) not available"
        elif self.engine_type == OCREngineType.PADDLEOCR and PaddleOCR is None:
            status["status"] = "degraded"
            status["message"] = "Preferred engine (PaddleOCR) not available"
        elif self.engine_type == OCREngineType.TESSERACT and pytesseract is None:
            status["status"] = "degraded"
            status["message"] = "Preferred engine (Tesseract) not available"
            
        # Check if any OCR engine is available
        if not any([pytesseract, easyocr, PaddleOCR]):
            status["status"] = "unhealthy"
            status["message"] = "No OCR engines available"
            
        # Check if PDF handling is available
        if pdf2image is None:
            if status["status"] == "healthy":
                status["status"] = "degraded"
            status["message"] = status.get("message", "") + " PDF processing unavailable."
                
        return status
        
    @property
    def easyocr_reader(self):
        """Lazy load EasyOCR reader."""
        if self._easyocr_reader is None:
            logger.info("Initializing EasyOCR reader")
            self._easyocr_reader = easyocr.Reader(['en'], gpu=self.enable_gpu)
        return self._easyocr_reader
        
    @property
    def paddleocr_reader(self):
        """Lazy load PaddleOCR reader."""
        if self._paddleocr_reader is None:
            logger.info("Initializing PaddleOCR reader")
            self._paddleocr_reader = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=self.enable_gpu)
        return self._paddleocr_reader
    
    async def pdf_to_images(self, pdf_path: str) -> List[np.ndarray]:
        """Convert PDF pages to images.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            List of images (numpy arrays)
            
        Raises:
            FileNotFoundError: If PDF file not found
            RuntimeError: If PDF conversion fails
        """
        try:
            if not Path(pdf_path).exists():
                raise FileNotFoundError(f"PDF file not found: {pdf_path}")
                
            logger.info(f"Converting PDF to images: {pdf_path}")
            return pdf2image.convert_from_path(pdf_path)
        except Exception as e:
            logger.error(f"Error converting PDF to images: {str(e)}")
            raise RuntimeError(f"Failed to convert PDF to images: {str(e)}") from e
    
    async def extract_text(self, images: List[np.ndarray], engine_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Extract text from images using the specified OCR engine.
        
        Args:
            images: List of images to extract text from
            engine_type: OCR engine to use. If None, uses the instance's engine_type.
            
        Returns:
            List of text blocks with metadata
            
        Raises:
            ValueError: If invalid engine type
            RuntimeError: If text extraction fails
        """
        engine = engine_type or self.engine_type
        text_blocks = []
        
        try:
            for page_num, image in enumerate(images, 1):
                logger.debug(f"Processing page {page_num} with {engine} engine")
                
                if engine == OCREngineType.EASYOCR:
                    results = self.easyocr_reader.readtext(image)
                    for box, text, conf in results:
                        text_blocks.append({
                            "page": page_num,
                            "text": text,
                            "confidence": conf,
                            "bbox": box,
                            "type": "text"
                        })
                        
                elif engine == OCREngineType.PADDLEOCR:
                    result = self.paddleocr_reader.ocr(image)
                    for line in result:
                        box, (text, conf) = line
                        text_blocks.append({
                            "page": page_num,
                            "text": text,
                            "confidence": conf,
                            "bbox": box,
                            "type": "text"
                        })
                        
                elif engine == OCREngineType.TESSERACT:
                    text = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
                    for i in range(len(text['text'])):
                        if text['conf'][i] > 0:  # Filter out low confidence results
                            text_blocks.append({
                                "page": page_num,
                                "text": text['text'][i],
                                "confidence": text['conf'][i] / 100,
                                "bbox": (text['left'][i], text['top'][i], 
                                        text['left'][i] + text['width'][i], 
                                        text['top'][i] + text['height'][i]),
                                "type": "text"
                            })
                else:
                    raise ValueError(f"Unsupported OCR engine: {engine}")
            
            return text_blocks
            
        except Exception as e:
            logger.error(f"Error extracting text with {engine}: {str(e)}")
            raise RuntimeError(f"OCR text extraction failed: {str(e)}") from e
    
    async def detect_tables(self, images: List[np.ndarray]) -> List[Dict[str, Any]]:
        """Detect tables in images.
        
        Args:
            images: List of images to detect tables in
            
        Returns:
            List of detected tables with metadata
        """
        tables = []
        
        try:
            for page_num, image in enumerate(images, 1):
                # Convert to grayscale
                if len(image.shape) == 3:
                    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = image
                
                # Apply thresholding
                thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                             cv2.THRESH_BINARY_INV, 11, 2)
                
                # Find contours
                contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # Filter contours
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    if w > settings.OCR_TABLE_MIN_WIDTH and h > settings.OCR_TABLE_MIN_HEIGHT:  # Updated to use configurable thresholds
                        aspect_ratio = w / h
                        if settings.OCR_ASPECT_RATIO_MIN_MAX[0] <= aspect_ratio <= settings.OCR_ASPECT_RATIO_MIN_MAX[1]:  # Updated to use configurable aspect ratio
                            tables.append({
                                "page": page_num,
                                "bbox": (x, y, x + w, y + h),
                                "type": "table"
                            })
            
            return tables
            
        except Exception as e:
            logger.error(f"Error detecting tables: {str(e)}")
            # Return empty list if detection fails instead of raising an exception
            return []
            
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """Process a document through OCR.
        
        Args:
            file_path: Path to the document
            
        Returns:
            Dictionary with extracted text blocks and detected tables
            
        Raises:
            FileNotFoundError: If document file not found
            RuntimeError: If document processing fails
        """
        try:
            # Validate file exists
            if not Path(file_path).exists():
                raise FileNotFoundError(f"Document file not found: {file_path}")
                
            # Convert PDF to images or load image file
            if file_path.lower().endswith('.pdf'):
                images = await self.pdf_to_images(file_path)
            else:
                logger.info(f"Loading image file: {file_path}")
                image = cv2.imread(file_path)
                if image is None:
                    raise ValueError(f"Failed to load image file: {file_path}")
                images = [image]
            
            # Extract text
            text_blocks = await self.extract_text(images)
            
            # Detect tables
            tables = await self.detect_tables(images)
            
            return {
                "text_blocks": text_blocks,
                "tables": tables,
                "page_count": len(images)
            }
            
        except FileNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {str(e)}", exc_info=True)
            raise RuntimeError(f"Document OCR processing failed: {str(e)}") from e
