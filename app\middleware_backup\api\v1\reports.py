from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.core.deps import get_db, get_current_active_user
from app.services.report_service import ReportService
from app.schemas.report import (
    ReportCreate, ReportResponse,
    ReportTemplateCreate, ReportTemplateResponse
)
from app.models.user import User

router = APIRouter()

@router.post("/reports/", response_model=ReportResponse)
def create_report(
    report: ReportCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = ReportService(db)
    return service.create_report(report, created_by=current_user.id)

@router.get("/reports/", response_model=List[ReportResponse])
def list_reports(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = ReportService(db)
    return service.get_reports(created_by=current_user.id, skip=skip, limit=limit)

@router.get("/reports/{report_id}", response_model=ReportResponse)
def get_report(
    report_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = ReportService(db)
    report = service.get_report(report_id)
    if report.created_by != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this report")
    return report

@router.post("/templates/", response_model=ReportTemplateResponse)
def create_template(
    template: ReportTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = ReportService(db)
    return service.create_template(template)

@router.get("/templates/", response_model=List[ReportTemplateResponse])
def list_templates(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = ReportService(db)
    return service.get_templates(skip=skip, limit=limit)

@router.get("/templates/{template_id}", response_model=ReportTemplateResponse)
def get_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = ReportService(db)
    return service.get_template(template_id)
