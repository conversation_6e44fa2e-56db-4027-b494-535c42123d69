{{/*Set up roles for Istio Gateway. Not required for gateway-api*/}}
{{- if .Values.rbac.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "gateway.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway.labels" . | nindent 4}}
  annotations:
    {{- .Values.annotations | toYaml | nindent 4 }}
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "gateway.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway.labels" . | nindent 4}}
  annotations:
    {{- .Values.annotations | toYaml | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "gateway.serviceAccountName" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "gateway.serviceAccountName" . }}
{{- end }}
