"""
Circuit breaker pattern implementation for resilient service calls.

This module provides a decorator-based circuit breaker to prevent
cascading failures when external services or dependencies are unavailable.
"""

import time
import logging
import functools
import asyncio
from typing import Callable, Optional, Any, TypeVar, cast
from app.core.config import settings

logger = logging.getLogger(__name__)

T = TypeVar('T')

class CircuitBreakerOpen(Exception):
    """Exception raised when the circuit is open."""
    pass

class CircuitBreaker:
    """Circuit breaker for preventing cascading failures."""
    
    def __init__(self, 
                 failure_threshold: int = None, 
                 recovery_timeout: int = None,
                 enabled: bool = None):
        """Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before trying to recover
            enabled: Whether the circuit breaker is active
        """
        self.failure_threshold = failure_threshold or settings.CIRCUIT_BREAKER_FAILURE_THRESHOLD
        self.recovery_timeout = recovery_timeout or settings.CIRCUIT_BREAKER_RECOVERY_TIMEOUT
        self.enabled = enabled if enabled is not None else settings.CIRCUIT_BREAKER_ENABLED
        
        self.failures = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF-OPEN
        self.last_failure_time = 0
        
    async def call(self, func: Callable[..., Any], *args, **kwargs) -> Any:
        """Call the function with circuit breaker protection."""
        if not self.enabled:
            return await func(*args, **kwargs)
            
        if self.state == "OPEN":
            elapsed = time.time() - self.last_failure_time
            if elapsed > self.recovery_timeout:
                logger.info(f"Circuit breaker entering half-open state after {elapsed:.2f}s")
                self.state = "HALF-OPEN"
            else:
                raise CircuitBreakerOpen(f"Circuit is open. Retry after {self.recovery_timeout - elapsed:.2f}s")
        
        try:
            result = await func(*args, **kwargs)
            
            # Reset on success if in half-open state
            if self.state == "HALF-OPEN":
                logger.info("Circuit breaker recovered, resetting to closed state")
                self.state = "CLOSED"
                self.failures = 0
                
            return result
            
        except Exception as e:
            self.failures += 1
            self.last_failure_time = time.time()
            
            logger.warning(f"Circuit breaker recorded failure {self.failures}/{self.failure_threshold}: {str(e)}")
            
            if self.failures >= self.failure_threshold:
                self.state = "OPEN"
                logger.error(f"Circuit breaker opened after {self.failures} failures")
                
            raise

def circuit_breaker(
    failure_threshold: Optional[int] = None,
    recovery_timeout: Optional[int] = None,
    enabled: Optional[bool] = None
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for applying circuit breaker pattern to functions.
    
    Args:
        failure_threshold: Number of failures before circuit opens
        recovery_timeout: Time in seconds before trying again
        enabled: Whether circuit breaker is active
        
    Returns:
        Decorator function
    """
    cb = CircuitBreaker(
        failure_threshold=failure_threshold,
        recovery_timeout=recovery_timeout,
        enabled=enabled
    )
    
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            return await cb.call(func, *args, **kwargs)
        return wrapper
    
    return decorator
