"""Requirement extraction service for documents.

This module provides functionality to extract requirements and compliance-related
information from documents using pattern matching and NLP techniques.
"""

import re
import logging
from typing import List, Dict, Any, Optional
from sentence_transformers import SentenceTransformer
from transformers import pipeline
import spacy
from app.core.config import settings

logger = logging.getLogger(__name__)

class RequirementService:
    """Service for extracting requirements from documents."""
    
    def __init__(self):
        """Initialize requirement extraction service."""
        # Lazy load attributes
        self._nlp = None
        self._sentence_model = None
        self._qa_pipeline = None
        
        # Compile regex patterns for requirements
        self.requirement_patterns = [
            re.compile(r'\bmust\s+', re.I),
            re.compile(r'\bshall\s+', re.I),
            re.compile(r'\brequired\s+to\s+', re.I),
            re.compile(r'\brequirements?\s+include\s+', re.I),
            re.compile(r'\bshould\s+', re.I),
            re.compile(r'\bnecessary\s+to\s+', re.I),
            re.compile(r'\bmandatory\s+', re.I),
            re.compile(r'\bobligated\s+to\s+', re.I)
        ]
        
        # Compile section header patterns
        self.section_header_pattern = re.compile(r'^\s*(\d+\.[\d\.]*\s+.+)$|^([A-Z][A-Z\s]+:)$|^(ARTICLE\s+[IVX]+\.?\s+.+)$', re.MULTILINE)
    
    @property
    def nlp(self):
        """Lazy load spaCy NLP model."""
        if self._nlp is None:
            logger.info("Initializing spaCy NLP model for requirement extraction")
            self._nlp = spacy.load("en_core_web_sm")
        return self._nlp
    
    @property
    def sentence_model(self):
        """Lazy load sentence transformer model."""
        if self._sentence_model is None:
            logger.info("Initializing sentence transformer model")
            self._sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        return self._sentence_model
    
    @property
    def qa_pipeline(self):
        """Lazy load question answering pipeline."""
        if self._qa_pipeline is None:
            logger.info("Initializing question answering pipeline")
            self._qa_pipeline = pipeline("question-answering", model="distilbert-base-cased-distilled-squad")
        return self._qa_pipeline
    
    async def extract_requirements(self, text_blocks: List[Dict]) -> List[Dict[str, Any]]:
        """Extract requirements from text blocks.
        
        Args:
            text_blocks: List of text blocks from OCR
            
        Returns:
            List of extracted requirements with metadata
        """
        try:
            # Combine text blocks into a single string
            full_text = " ".join(block["text"] for block in text_blocks)
            
            # Process with spaCy for sentence segmentation
            doc = self.nlp(full_text)
            sentences = [sent.text.strip() for sent in doc.sents]
            
            requirements = []
            for i, sentence in enumerate(sentences):
                # Check if sentence contains a requirement pattern
                if any(pattern.search(sentence) for pattern in self.requirement_patterns):
                    # Create context by getting surrounding sentences
                    context_start = max(0, i - 2)
                    context_end = min(len(sentences), i + 3)
                    context = " ".join(sentences[context_start:context_end])
                    
                    # Find the closest section header
                    section_header = self._find_section_for_sentence(sentences, i)
                    
                    # Add to requirements
                    requirements.append({
                        "text": sentence,
                        "context": context,
                        "confidence": 0.8,  # Default confidence for pattern-based extraction
                        "type": "requirement",
                        "section": section_header
                    })
            
            # Use NLP to identify additional requirements that may not match patterns
            additional_requirements = await self._identify_semantic_requirements(sentences, requirements)
            requirements.extend(additional_requirements)
            
            # Add unique IDs
            for i, req in enumerate(requirements):
                req["id"] = f"REQ-{i+1:04d}"
            
            return requirements
            
        except Exception as e:
            logger.error(f"Error extracting requirements: {str(e)}")
            return []
    
    def _find_section_for_sentence(self, sentences: List[str], sentence_index: int) -> Optional[str]:
        """Find the section header for a given sentence.
        
        Args:
            sentences: List of all sentences
            sentence_index: Index of the sentence to find section for
            
        Returns:
            Section header or None if not found
        """
        # Look backwards for the nearest section header
        for i in range(sentence_index, -1, -1):
            match = self.section_header_pattern.match(sentences[i])
            if match:
                # Get the matched group (different patterns use different groups)
                for group in match.groups():
                    if group:
                        return group.strip()
        return None
    
    async def _identify_semantic_requirements(
        self, sentences: List[str], existing_requirements: List[Dict]
    ) -> List[Dict[str, Any]]:
        """Identify requirements using semantic similarity.
        
        Args:
            sentences: List of all sentences
            existing_requirements: Already identified requirements (to avoid duplicates)
            
        Returns:
            List of additional requirements
        """
        # Avoid creating duplicate requirements
        existing_texts = {req["text"] for req in existing_requirements}
        
        # Example requirement patterns for embedding comparison
        requirement_examples = [
            "The system must encrypt all user data.",
            "Users are required to change passwords every 90 days.",
            "All facilities shall maintain visitor logs.",
            "Employees should complete training within 30 days of hire."
        ]
        
        # Skip if sentence model isn't available
        if self._sentence_model is None:
            return []
        
        try:
            # Create embeddings for example requirements
            example_embeddings = self.sentence_model.encode(requirement_examples)
            
            # Filter sentences to process (avoid processing too many for performance)
            # Skip sentences that are too short or already identified
            candidate_sentences = [
                s for s in sentences 
                if len(s.split()) > 5 and s not in existing_texts
            ]
            
            # Sample a subset if there are too many sentences
            max_sentences = 200  # Limit for performance
            if len(candidate_sentences) > max_sentences:
                import random
                candidate_sentences = random.sample(candidate_sentences, max_sentences)
            
            # Get embeddings for candidate sentences
            if not candidate_sentences:
                return []
                
            candidate_embeddings = self.sentence_model.encode(candidate_sentences)
            
            # Calculate similarity scores
            import numpy as np
            from sklearn.metrics.pairwise import cosine_similarity
            similarities = cosine_similarity(candidate_embeddings, example_embeddings)
            
            # Find sentences with high similarity to examples
            max_similarities = np.max(similarities, axis=1)
            
            additional_requirements = []
            for i, score in enumerate(max_similarities):
                if score > settings.REQUIREMENT_SIMILARITY_THRESHOLD:  # Updated to use configurable threshold
                    additional_requirements.append({
                        "text": candidate_sentences[i],
                        "context": candidate_sentences[i],  # No context for these
                        "confidence": float(score),  # Convert numpy float to Python float
                        "type": "inferred_requirement",
                        "section": self._find_section_for_sentence(
                            sentences, sentences.index(candidate_sentences[i])
                        )
                    })
            
            return additional_requirements
            
        except Exception as e:
            logger.error(f"Error identifying semantic requirements: {str(e)}")
            return []
    
    async def extract_cross_references(self, text_blocks: List[Dict]) -> List[Dict[str, Any]]:
        """Extract cross-references from text blocks.
        
        Args:
            text_blocks: List of text blocks from OCR
            
        Returns:
            List of cross-references
        """
        try:
            # Combine text blocks
            full_text = " ".join(block["text"] for block in text_blocks)
            
            # Regex patterns for different types of references
            reference_patterns = [
                (r'(?:see|refer to|reference)\s+section\s+(\d+\.[\d\.]*)', 'section'),
                (r'(?:see|refer to|reference)\s+appendix\s+([A-Z])', 'appendix'),
                (r'(?:see|refer to|reference)\s+figure\s+(\d+)', 'figure'),
                (r'(?:see|refer to|reference)\s+table\s+(\d+)', 'table'),
                (r'according to\s+([^,\.;]+)', 'source'),
                (r'as\s+(?:defined|specified|required)\s+(?:in|by)\s+([^,\.;]+)', 'specification')
            ]
            
            cross_references = []
            for pattern, ref_type in reference_patterns:
                for match in re.finditer(pattern, full_text, re.IGNORECASE):
                    cross_references.append({
                        "text": match.group(0),
                        "target": match.group(1),
                        "type": ref_type
                    })
            
            return cross_references
            
        except Exception as e:
            logger.error(f"Error extracting cross-references: {str(e)}")
            return []
    
    async def analyze_structure(self, text_blocks: List[Dict]) -> Dict[str, Any]:
        """Analyze document structure from text blocks.
        
        Args:
            text_blocks: List of text blocks from OCR
            
        Returns:
            Dictionary with document structure information
        """
        try:
            # Combine text blocks by page
            pages = {}
            for block in text_blocks:
                page_num = block.get("page", 1)
                if page_num not in pages:
                    pages[page_num] = []
                pages[page_num].append(block["text"])
            
            # Extract section headers by page
            sections = {}
            for page_num, texts in pages.items():
                page_text = " ".join(texts)
                section_matches = list(self.section_header_pattern.finditer(page_text))
                
                sections[page_num] = []
                for match in section_matches:
                    # Get the matched group (different patterns use different groups)
                    section_text = None
                    for group in match.groups():
                        if group:
                            section_text = group
                            break
                    
                    if section_text:
                        sections[page_num].append({
                            "text": section_text,
                            "position": match.start()
                        })
            
            # Build section hierarchy
            section_hierarchy = []
            all_sections = []
            
            for page_num in sorted(sections.keys()):
                all_sections.extend(sections[page_num])
            
            # Extract numbered sections and build hierarchy
            numbered_sections = []
            for section in all_sections:
                # Check if it's a numbered section (e.g., "1.2.3 Title")
                match = re.match(r'^(\d+\.[\d\.]*\s+.+)$', section["text"])
                if match:
                    number, title = match.groups()
                    depth = number.count(".")
                    numbered_sections.append({
                        "number": number,
                        "title": title,
                        "depth": depth,
                        "position": section["position"]
                    })
            
            return {
                "pages": len(pages),
                "sections_by_page": sections,
                "numbered_sections": numbered_sections,
                "total_sections": len(all_sections)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing document structure: {str(e)}")
            return {"pages": 0, "sections_by_page": {}, "numbered_sections": []}
