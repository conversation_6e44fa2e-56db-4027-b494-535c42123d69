from sqlalchemy import create_engine, text
from app.core.config import settings

def reset_database():
    engine = create_engine(settings.SQLITE_DB)
    with engine.connect() as conn:
        try:
            # Drop existing tables
            conn.execute(text('DROP TABLE IF EXISTS documents'))
            conn.execute(text('DROP TABLE IF EXISTS users'))
            conn.execute(text('DROP TABLE IF EXISTS alembic_version'))
            conn.commit()
            print("Database reset successful")
        except Exception as e:
            print("Error:", str(e))

if __name__ == "__main__":
    reset_database()
