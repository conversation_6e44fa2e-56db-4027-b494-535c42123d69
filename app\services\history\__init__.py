from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from app.models.history import ReviewHistory, ChangeType
from app.models.review import BaseReview

class HistoryService:
    @staticmethod
    async def record_change(
        db: Session,
        review: BaseReview,
        user_id: int,
        change_type: ChangeType,
        previous_state: Optional[Dict] = None,
        new_state: Dict = None,
        comment: Optional[str] = None
    ) -> ReviewHistory:
        """Record a change in the review history"""
        history_entry = ReviewHistory(
            review_id=review.id,
            user_id=user_id,
            change_type=change_type,
            previous_state=previous_state,
            new_state=new_state or {},
            comment=comment
        )
        db.add(history_entry)
        db.commit()
        db.refresh(history_entry)
        return history_entry

    @staticmethod
    def get_review_history(
        db: Session,
        review_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ReviewHistory]:
        """Get the history of a review within a date range"""
        query = db.query(ReviewHistory).filter(ReviewHistory.review_id == review_id)
        
        if start_date:
            query = query.filter(ReviewHistory.timestamp >= start_date)
        if end_date:
            query = query.filter(ReviewHistory.timestamp <= end_date)
            
        return query.order_by(ReviewHistory.timestamp.desc()).all()

    @staticmethod
    def get_user_changes(
        db: Session,
        user_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ReviewHistory]:
        """Get all changes made by a user within a date range"""
        query = db.query(ReviewHistory).filter(ReviewHistory.user_id == user_id)
        
        if start_date:
            query = query.filter(ReviewHistory.timestamp >= start_date)
        if end_date:
            query = query.filter(ReviewHistory.timestamp <= end_date)
            
        return query.order_by(ReviewHistory.timestamp.desc()).all()

    @staticmethod
    def generate_audit_trail(
        db: Session,
        review_id: int,
        include_document_changes: bool = True
    ) -> Dict:
        """Generate a complete audit trail for a review"""
        history_entries = db.query(ReviewHistory).filter(
            ReviewHistory.review_id == review_id
        ).order_by(ReviewHistory.timestamp.asc()).all()

        audit_trail = {
            "review_id": review_id,
            "creation_date": None,
            "last_modified": None,
            "changes": [],
            "document_changes": [] if include_document_changes else None
        }

        for entry in history_entries:
            change = {
                "timestamp": entry.timestamp.isoformat(),
                "user_id": entry.user_id,
                "type": entry.change_type,
                "comment": entry.comment
            }

            if entry.change_type == ChangeType.CREATE:
                audit_trail["creation_date"] = entry.timestamp.isoformat()
                change["details"] = "Review created"
            elif entry.change_type == ChangeType.UPDATE:
                audit_trail["last_modified"] = entry.timestamp.isoformat()
                change["details"] = "Review updated"
                change["changes"] = {
                    k: {
                        "from": entry.previous_state.get(k),
                        "to": entry.new_state.get(k)
                    }
                    for k in entry.new_state.keys()
                    if k in entry.previous_state
                    and entry.previous_state[k] != entry.new_state[k]
                }
            elif entry.change_type == ChangeType.STATUS_CHANGE:
                change["details"] = f"Status changed from {entry.previous_state.get('status')} to {entry.new_state.get('status')}"
            elif entry.change_type == ChangeType.FINDINGS_UPDATE:
                change["details"] = "Findings updated"
                change["findings_diff"] = {
                    "added": [k for k in entry.new_state.keys() if k not in entry.previous_state],
                    "removed": [k for k in entry.previous_state.keys() if k not in entry.new_state],
                    "modified": [k for k in entry.new_state.keys() if k in entry.previous_state and entry.previous_state[k] != entry.new_state[k]]
                }
            elif entry.change_type == ChangeType.DOCUMENT_UPLOAD and include_document_changes:
                audit_trail["document_changes"].append({
                    "timestamp": entry.timestamp.isoformat(),
                    "user_id": entry.user_id,
                    "document_id": entry.new_state.get("document_id"),
                    "document_type": entry.new_state.get("document_type"),
                    "action": "upload"
                })
                continue

            audit_trail["changes"].append(change)

        return audit_trail
