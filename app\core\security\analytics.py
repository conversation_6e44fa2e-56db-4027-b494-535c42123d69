"""
Security analytics and threat detection system for ComplianceMax.
"""
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import asyncio
import json
import logging
from collections import defaultdict
import ipaddress
from dataclasses import dataclass
from enum import Enum

from app.core.monitoring.metrics import (
    track_security_incident,
    security_incidents_total
)
from app.core.config import settings

logger = logging.getLogger(__name__)

class ThreatLevel(Enum):
    """Threat severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class EventType(Enum):
    """Security event types."""
    AUTH_FAILURE = "auth_failure"
    RATE_LIMIT = "rate_limit"
    API_KEY_MISUSE = "api_key_misuse"
    SUSPICIOUS_REQUEST = "suspicious_request"
    DATA_ACCESS = "data_access"
    CONFIG_CHANGE = "config_change"
    FILE_OPERATION = "file_operation"

@dataclass
class SecurityEvent:
    """Security event data structure."""
    event_type: EventType
    timestamp: datetime
    source_ip: str
    user_id: Optional[str]
    resource: str
    details: Dict
    threat_level: ThreatLevel

class SecurityAnalytics:
    """Security analytics and threat detection system."""
    
    def __init__(self):
        self.event_buffer = []
        self.event_patterns = defaultdict(list)
        self.ip_blacklist = set()
        self.suspicious_ips = defaultdict(int)
        self.auth_failures = defaultdict(int)
        self.rate_limit_violations = defaultdict(int)
        
        # Time windows for different analyses
        self.windows = {
            'auth_failure': timedelta(minutes=5),
            'rate_limit': timedelta(minutes=15),
            'pattern_match': timedelta(minutes=30),
            'correlation': timedelta(hours=1)
        }
        
        # Thresholds for different threat levels
        self.thresholds = {
            'auth_failure': {
                ThreatLevel.LOW: 3,
                ThreatLevel.MEDIUM: 5,
                ThreatLevel.HIGH: 10,
                ThreatLevel.CRITICAL: 20
            },
            'rate_limit': {
                ThreatLevel.LOW: 5,
                ThreatLevel.MEDIUM: 10,
                ThreatLevel.HIGH: 20,
                ThreatLevel.CRITICAL: 50
            }
        }
        
        # Start background tasks
        self.cleanup_task = asyncio.create_task(self._periodic_cleanup())
        self.analysis_task = asyncio.create_task(self._periodic_analysis())
    
    async def track_event(self, event: SecurityEvent):
        """Track a security event."""
        self.event_buffer.append(event)
        
        # Perform immediate analysis
        await self._analyze_event(event)
        
        # Update metrics
        track_security_incident(event.event_type.value)
    
    async def _analyze_event(self, event: SecurityEvent):
        """Analyze a single security event."""
        if event.event_type == EventType.AUTH_FAILURE:
            await self._analyze_auth_failure(event)
        elif event.event_type == EventType.RATE_LIMIT:
            await self._analyze_rate_limit(event)
        elif event.event_type == EventType.SUSPICIOUS_REQUEST:
            await self._analyze_suspicious_request(event)
        
        # Check for pattern matches
        await self._check_event_patterns(event)
    
    async def _analyze_auth_failure(self, event: SecurityEvent):
        """Analyze authentication failures."""
        key = (event.source_ip, event.user_id)
        self.auth_failures[key] += 1
        
        # Check thresholds
        count = self.auth_failures[key]
        for level, threshold in sorted(
            self.thresholds['auth_failure'].items(),
            key=lambda x: x[1],
            reverse=True
        ):
            if count >= threshold:
                await self._handle_threat(
                    level,
                    f"Multiple authentication failures from {event.source_ip}",
                    event
                )
                break
    
    async def _analyze_rate_limit(self, event: SecurityEvent):
        """Analyze rate limit violations."""
        self.rate_limit_violations[event.source_ip] += 1
        
        # Check thresholds
        count = self.rate_limit_violations[event.source_ip]
        for level, threshold in sorted(
            self.thresholds['rate_limit'].items(),
            key=lambda x: x[1],
            reverse=True
        ):
            if count >= threshold:
                await self._handle_threat(
                    level,
                    f"Excessive rate limit violations from {event.source_ip}",
                    event
                )
                break
    
    async def _analyze_suspicious_request(self, event: SecurityEvent):
        """Analyze suspicious requests."""
        self.suspicious_ips[event.source_ip] += 1
        
        if self._is_suspicious_pattern(event.details):
            await self._handle_threat(
                ThreatLevel.HIGH,
                f"Suspicious request pattern detected from {event.source_ip}",
                event
            )
    
    async def _check_event_patterns(self, event: SecurityEvent):
        """Check for known malicious patterns."""
        # Add event to patterns buffer
        self.event_patterns[event.source_ip].append(event)
        
        # Get recent events for this IP
        recent_events = [
            e for e in self.event_patterns[event.source_ip]
            if e.timestamp > datetime.utcnow() - self.windows['pattern_match']
        ]
        
        # Check for suspicious patterns
        if self._detect_attack_pattern(recent_events):
            await self._handle_threat(
                ThreatLevel.CRITICAL,
                f"Attack pattern detected from {event.source_ip}",
                event
            )
    
    def _detect_attack_pattern(self, events: List[SecurityEvent]) -> bool:
        """Detect known attack patterns in event sequence."""
        if len(events) < 3:
            return False
        
        # Check for brute force pattern
        auth_failures = [
            e for e in events
            if e.event_type == EventType.AUTH_FAILURE
        ]
        if len(auth_failures) >= 10:
            return True
        
        # Check for scanning pattern
        unique_resources = len(set(e.resource for e in events))
        if unique_resources >= 20:
            return True
        
        # Check for API abuse pattern
        rate_limits = [
            e for e in events
            if e.event_type == EventType.RATE_LIMIT
        ]
        if len(rate_limits) >= 5:
            return True
        
        return False
    
    def _is_suspicious_pattern(self, details: Dict) -> bool:
        """Check if request details match known suspicious patterns."""
        # Check for SQL injection attempts
        if any(
            pattern in str(details).lower()
            for pattern in ["union select", "or 1=1", "exec(", "eval("]
        ):
            return True
        
        # Check for XSS attempts
        if any(
            pattern in str(details).lower()
            for pattern in ["<script>", "javascript:", "onerror=", "onload="]
        ):
            return True
        
        # Check for path traversal
        if any(
            pattern in str(details)
            for pattern in ["../", "..\\", "/etc/passwd", "c:\\windows"]
        ):
            return True
        
        return False
    
    async def _handle_threat(
        self,
        level: ThreatLevel,
        description: str,
        event: SecurityEvent
    ):
        """Handle detected threats."""
        incident = {
            'timestamp': datetime.utcnow().isoformat(),
            'threat_level': level.value,
            'description': description,
            'source_ip': event.source_ip,
            'user_id': event.user_id,
            'event_type': event.event_type.value,
            'details': event.details
        }
        
        # Log the incident
        logger.warning(f"Security incident detected: {json.dumps(incident)}")
        
        # Update metrics
        track_security_incident(f"threat_{level.value}")
        
        # Take immediate action for critical threats
        if level == ThreatLevel.CRITICAL:
            self.ip_blacklist.add(event.source_ip)
            logger.error(f"IP {event.source_ip} blacklisted due to critical threat")
    
    async def _periodic_cleanup(self):
        """Periodically clean up old data."""
        while True:
            try:
                now = datetime.utcnow()
                
                # Clean up auth failures
                self.auth_failures = {
                    k: v
                    for k, v in self.auth_failures.items()
                    if k[1] > now - self.windows['auth_failure']
                }
                
                # Clean up rate limit violations
                self.rate_limit_violations = {
                    k: v
                    for k, v in self.rate_limit_violations.items()
                    if k > now - self.windows['rate_limit']
                }
                
                # Clean up event patterns
                for ip in list(self.event_patterns.keys()):
                    self.event_patterns[ip] = [
                        e for e in self.event_patterns[ip]
                        if e.timestamp > now - self.windows['pattern_match']
                    ]
                    if not self.event_patterns[ip]:
                        del self.event_patterns[ip]
                
                # Clean up event buffer
                self.event_buffer = [
                    e for e in self.event_buffer
                    if e.timestamp > now - self.windows['correlation']
                ]
                
            except Exception as e:
                logger.error(f"Error in security analytics cleanup: {str(e)}")
            
            await asyncio.sleep(300)  # Run every 5 minutes
    
    async def _periodic_analysis(self):
        """Periodically run correlation analysis."""
        while True:
            try:
                # Analyze event correlations
                await self._analyze_correlations()
                
                # Update threat intelligence
                await self._update_threat_intelligence()
                
            except Exception as e:
                logger.error(f"Error in security analytics analysis: {str(e)}")
            
            await asyncio.sleep(600)  # Run every 10 minutes
    
    async def _analyze_correlations(self):
        """Analyze event correlations for advanced threat detection."""
        now = datetime.utcnow()
        recent_events = [
            e for e in self.event_buffer
            if e.timestamp > now - self.windows['correlation']
        ]
        
        # Group events by source IP
        events_by_ip = defaultdict(list)
        for event in recent_events:
            events_by_ip[event.source_ip].append(event)
        
        # Analyze each IP's behavior
        for ip, events in events_by_ip.items():
            if len(events) < 3:
                continue
            
            # Check for distributed patterns
            if self._detect_distributed_attack(events):
                await self._handle_threat(
                    ThreatLevel.HIGH,
                    f"Distributed attack pattern detected from {ip}",
                    events[-1]
                )
            
            # Check for advanced persistence
            if self._detect_persistence(events):
                await self._handle_threat(
                    ThreatLevel.HIGH,
                    f"Persistent attack pattern detected from {ip}",
                    events[-1]
                )
    
    def _detect_distributed_attack(self, events: List[SecurityEvent]) -> bool:
        """Detect distributed attack patterns."""
        # Check for distributed brute force
        auth_failures = [
            e for e in events
            if e.event_type == EventType.AUTH_FAILURE
        ]
        if len(auth_failures) >= 5:
            unique_users = len(set(e.user_id for e in auth_failures))
            if unique_users >= 3:
                return True
        
        # Check for distributed scanning
        unique_resources = len(set(e.resource for e in events))
        unique_types = len(set(e.event_type for e in events))
        if unique_resources >= 10 and unique_types >= 3:
            return True
        
        return False
    
    def _detect_persistence(self, events: List[SecurityEvent]) -> bool:
        """Detect persistent attack patterns."""
        # Check time distribution
        timestamps = [e.timestamp for e in events]
        time_diffs = [
            (timestamps[i] - timestamps[i-1]).total_seconds()
            for i in range(1, len(timestamps))
        ]
        
        if len(time_diffs) >= 2:
            avg_diff = sum(time_diffs) / len(time_diffs)
            if avg_diff > 300:  # More than 5 minutes apart
                return True
        
        return False
    
    async def _update_threat_intelligence(self):
        """Update threat intelligence data."""
        # This would typically involve external threat intelligence feeds
        # For now, we'll just maintain our internal lists
        pass
    
    def is_ip_blacklisted(self, ip: str) -> bool:
        """Check if an IP is blacklisted."""
        return ip in self.ip_blacklist
    
    def get_threat_level(self, ip: str) -> ThreatLevel:
        """Get current threat level for an IP."""
        if ip in self.ip_blacklist:
            return ThreatLevel.CRITICAL
        
        auth_count = sum(
            1 for k in self.auth_failures
            if k[0] == ip
        )
        rate_count = self.rate_limit_violations.get(ip, 0)
        suspicious_count = self.suspicious_ips.get(ip, 0)
        
        total_score = auth_count + rate_count + suspicious_count
        
        if total_score >= 20:
            return ThreatLevel.CRITICAL
        elif total_score >= 10:
            return ThreatLevel.HIGH
        elif total_score >= 5:
            return ThreatLevel.MEDIUM
        else:
            return ThreatLevel.LOW 