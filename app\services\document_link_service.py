from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.document_link import DocumentLink
from app.schemas.document_link import DocumentLinkCreate, DocumentLinkUpdate
from app.core.exceptions import NotFoundException, ValidationError

class DocumentLinkService:
    def __init__(self, db: Session):
        self.db = db

    def create_link(self, link_data: DocumentLinkCreate) -> DocumentLink:
        """Create a new document link."""
        db_link = DocumentLink(**link_data.dict())
        self.db.add(db_link)
        self.db.commit()
        self.db.refresh(db_link)
        return db_link

    def get_link(self, link_id: str) -> DocumentLink:
        """Get a document link by ID."""
        link = self.db.query(DocumentLink).filter(DocumentLink.id == link_id).first()
        if not link:
            raise NotFoundException(f"Document link {link_id} not found")
        return link

    def get_links_by_document(self, document_id: str) -> List[DocumentLink]:
        """Get all links for a specific document."""
        return self.db.query(DocumentLink).filter(DocumentLink.document_id == document_id).all()

    def get_links_by_application(self, application_id: str) -> List[DocumentLink]:
        """Get all links for a specific application."""
        return self.db.query(DocumentLink).filter(DocumentLink.application_id == application_id).all()

    def get_links_by_category(self, category: str, application_id: Optional[str] = None) -> List[DocumentLink]:
        """Get all links for a specific category, optionally filtered by application."""
        query = self.db.query(DocumentLink).filter(DocumentLink.category == category)
        if application_id:
            query = query.filter(DocumentLink.application_id == application_id)
        return query.all()

    def update_link(self, link_id: str, link_data: DocumentLinkUpdate) -> DocumentLink:
        """Update a document link."""
        link = self.get_link(link_id)
        for field, value in link_data.dict(exclude_unset=True).items():
            setattr(link, field, value)
        self.db.commit()
        self.db.refresh(link)
        return link

    def delete_link(self, link_id: str) -> None:
        """Delete a document link."""
        link = self.get_link(link_id)
        self.db.delete(link)
        self.db.commit()

    def update_validation_status(self, link_id: str, status: str) -> DocumentLink:
        """Update the validation status of a document link."""
        link = self.get_link(link_id)
        link.validation_status = status
        self.db.commit()
        self.db.refresh(link)
        return link

    def update_compliance_status(self, link_id: str, status: str) -> DocumentLink:
        """Update the compliance status of a document link."""
        link = self.get_link(link_id)
        link.compliance_status = status
        self.db.commit()
        self.db.refresh(link)
        return link

    def set_primary_link(self, link_id: str) -> DocumentLink:
        """Set a document link as primary and unset any existing primary links for the same category."""
        link = self.get_link(link_id)
        
        # Unset any existing primary links for this category and application
        existing_primary = self.db.query(DocumentLink).filter(
            DocumentLink.category == link.category,
            DocumentLink.application_id == link.application_id,
            DocumentLink.is_primary == True,
            DocumentLink.id != link_id
        ).all()
        
        for existing in existing_primary:
            existing.is_primary = False
        
        link.is_primary = True
        self.db.commit()
        self.db.refresh(link)
        return link 