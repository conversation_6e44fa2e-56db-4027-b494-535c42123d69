from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.policy import PolicyDocument, PolicyVersion
from app.models.disaster import DisasterDeclaration
from app.services.fema_api import FEMAAPIService

class PolicyUpdateService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.fema_service = FEMAAPIService()

    async def monitor_policy_changes(self) -> List[PolicyVersion]:
        """Monitor and sync policy changes from FEMA."""
        return await self.fema_service.sync_policy_updates(self.db)

    async def update_policy_versions(self, policy_data: Dict) -> PolicyVersion:
        """Create or update a policy version."""
        stmt = select(PolicyDocument).where(PolicyDocument.policy_number == policy_data["policy_number"])
        result = await self.db.execute(stmt)
        policy = result.scalar_one_or_none()
        
        if not policy:
            policy = PolicyDocument(
                policy_number=policy_data["policy_number"],
                title=policy_data["title"],
                description=policy_data["description"],
                policy_type=policy_data["policy_type"]
            )
            self.db.add(policy)
            await self.db.flush()
        
        new_version = PolicyVersion(
            policy_id=policy.id,
            version_number=policy_data["version_number"],
            content=policy_data["content"],
            effective_date=policy_data["effective_date"],
            expiration_date=policy_data.get("expiration_date"),
            document_url=policy_data.get("document_url")
        )
        self.db.add(new_version)
        await self.db.commit()
        await self.db.refresh(new_version)
        return new_version

    async def notify_affected_projects(self, policy_version: PolicyVersion) -> List[Dict]:
        """Identify and notify projects affected by policy changes."""
        notifications = []
        for disaster in policy_version.applicable_disasters:
            # Get all active reviews for the disaster
            stmt = select(DisasterDeclaration).where(
                DisasterDeclaration.dr_number == disaster.dr_number
            )
            result = await self.db.execute(stmt)
            disaster_data = result.scalar_one_or_none()
            
            if disaster_data:
                notification = {
                    "dr_number": disaster.dr_number,
                    "policy_number": policy_version.policy.policy_number,
                    "version_number": policy_version.version_number,
                    "effective_date": policy_version.effective_date,
                    "changes": self._get_policy_changes(policy_version)
                }
                notifications.append(notification)
        
        return notifications

    async def validate_policy_conflicts(self, policy_version: PolicyVersion) -> List[Dict]:
        """Check for conflicts with other active policies."""
        conflicts = []
        stmt = select(PolicyVersion).where(
            PolicyVersion.effective_date <= policy_version.effective_date,
            PolicyVersion.expiration_date >= policy_version.effective_date,
            PolicyVersion.id != policy_version.id
        )
        result = await self.db.execute(stmt)
        overlapping_policies = result.scalars().all()
        
        for other_policy in overlapping_policies:
            if self._has_conflict(policy_version, other_policy):
                conflict = {
                    "policy_1": policy_version.policy.policy_number,
                    "version_1": policy_version.version_number,
                    "policy_2": other_policy.policy.policy_number,
                    "version_2": other_policy.version_number,
                    "conflict_type": "date_overlap",
                    "start_date": policy_version.effective_date,
                    "end_date": min(
                        policy_version.expiration_date or datetime.max,
                        other_policy.expiration_date or datetime.max
                    )
                }
                conflicts.append(conflict)
        
        return conflicts

    def _get_policy_changes(self, policy_version: PolicyVersion) -> Dict:
        """Extract key changes in the policy version."""
        # This is a placeholder for more sophisticated diff logic
        return {
            "type": "new_version",
            "summary": f"New version {policy_version.version_number} of policy {policy_version.policy.policy_number}"
        }

    def _has_conflict(self, policy1: PolicyVersion, policy2: PolicyVersion) -> bool:
        """Check if two policies have conflicting requirements."""
        # This is a placeholder for more sophisticated conflict detection logic
        return bool(
            policy1.effective_date <= (policy2.expiration_date or datetime.max) and
            policy2.effective_date <= (policy1.expiration_date or datetime.max)
        )
