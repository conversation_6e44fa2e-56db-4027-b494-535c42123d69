import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/lab';
import {
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { DocumentService } from '../../services/document';
import { formatDate } from '../../utils/dateUtils';

interface ComplianceReviewProps {
  documentId: string;
  drNumber: string;
}

interface ComplianceHistory {
  timestamp: string;
  dr_number: string;
  overall_score: number;
  deficiencies_count: number;
  temporal_issues_count: number;
  status: string;
  policy_comparisons: Array<{
    policy_id: string;
    policy_number: string;
    similarity_score: number;
  }>;
}

interface ValidationResult {
  timestamp: string;
  validation: {
    dr_number: string;
    scores: {
      overall: number;
      requirements: number;
      temporal: number;
      structure: number;
    };
    deficiencies: Array<{
      type: string;
      description: string;
      severity: string;
      policy_reference: string;
    }>;
    temporal_issues: Array<{
      type: string;
      description: string;
      date_range: {
        start: string;
        end: string;
      };
    }>;
    policy_matches: Array<{
      policy_id: string;
      policy_number: string;
      title: string;
      match_score: number;
      requirements_met: number;
      requirements_total: number;
    }>;
  };
}

const ComplianceReview: React.FC<ComplianceReviewProps> = ({ documentId, drNumber }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [history, setHistory] = useState<ComplianceHistory[]>([]);
  const [latestValidation, setLatestValidation] = useState<ValidationResult | null>(null);
  const [expandedDeficiencies, setExpandedDeficiencies] = useState(false);
  const [expandedPolicies, setExpandedPolicies] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const documentService = new DocumentService();
        
        // Fetch compliance history
        const historyResponse = await documentService.getComplianceHistory(documentId);
        setHistory(historyResponse);
        
        // Fetch latest validation result
        const validationResponse = await documentService.getLatestCompliance(documentId);
        setLatestValidation(validationResponse);
        
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        setLoading(false);
      }
    };
    
    fetchData();
  }, [documentId]);

  const getStatusColor = (score: number) => {
    if (score >= 0.8) return 'success';
    if (score >= 0.6) return 'warning';
    return 'error';
  };

  const getStatusIcon = (score: number) => {
    if (score >= 0.8) return <CheckCircleIcon color="success" />;
    if (score >= 0.6) return <WarningIcon color="warning" />;
    return <ErrorIcon color="error" />;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <Box sx={{ p: 2 }}>
      {/* Latest Validation Summary */}
      {latestValidation && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="h5" gutterBottom>
                  Compliance Summary
                </Typography>
                <Typography variant="subtitle2" color="textSecondary">
                  Last updated: {formatDate(latestValidation.timestamp)}
                </Typography>
              </Grid>
              
              {/* Compliance Scores */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Compliance Scores
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Box textAlign="center">
                        <Typography variant="subtitle2">Overall</Typography>
                        <Typography variant="h4" color={getStatusColor(latestValidation.validation.scores.overall)}>
                          {Math.round(latestValidation.validation.scores.overall * 100)}%
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box textAlign="center">
                        <Typography variant="subtitle2">Requirements</Typography>
                        <Typography variant="h4" color={getStatusColor(latestValidation.validation.scores.requirements)}>
                          {Math.round(latestValidation.validation.scores.requirements * 100)}%
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* Deficiencies Summary */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="h6">
                      Deficiencies ({latestValidation.validation.deficiencies.length})
                    </Typography>
                    <IconButton onClick={() => setExpandedDeficiencies(!expandedDeficiencies)}>
                      {expandedDeficiencies ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>
                  <Collapse in={expandedDeficiencies}>
                    <List>
                      {latestValidation.validation.deficiencies.map((def, index) => (
                        <ListItem key={index}>
                          <ListItemText
                            primary={def.description}
                            secondary={`${def.type} - Severity: ${def.severity}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Collapse>
                </Paper>
              </Grid>

              {/* Policy Matches */}
              <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="h6">
                      Policy Compliance
                    </Typography>
                    <IconButton onClick={() => setExpandedPolicies(!expandedPolicies)}>
                      {expandedPolicies ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>
                  <Collapse in={expandedPolicies}>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Policy</TableCell>
                            <TableCell align="right">Match Score</TableCell>
                            <TableCell align="right">Requirements Met</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {latestValidation.validation.policy_matches.map((policy) => (
                            <TableRow key={policy.policy_id}>
                              <TableCell>
                                <Typography variant="body2">{policy.policy_number}</Typography>
                                <Typography variant="caption" color="textSecondary">
                                  {policy.title}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                <Chip
                                  label={`${Math.round(policy.match_score * 100)}%`}
                                  color={getStatusColor(policy.match_score)}
                                  size="small"
                                />
                              </TableCell>
                              <TableCell align="right">
                                {policy.requirements_met} / {policy.requirements_total}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Collapse>
                </Paper>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Compliance History Timeline */}
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Compliance History
          </Typography>
          <Timeline>
            {history.map((entry, index) => (
              <TimelineItem key={index}>
                <TimelineSeparator>
                  <TimelineDot color={getStatusColor(entry.overall_score)}>
                    {getStatusIcon(entry.overall_score)}
                  </TimelineDot>
                  {index < history.length - 1 && <TimelineConnector />}
                </TimelineSeparator>
                <TimelineContent>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2">
                      {formatDate(entry.timestamp)}
                    </Typography>
                    <Typography variant="body2">
                      DR#{entry.dr_number} - Overall Score: {Math.round(entry.overall_score * 100)}%
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Deficiencies: {entry.deficiencies_count} | Temporal Issues: {entry.temporal_issues_count}
                    </Typography>
                  </Paper>
                </TimelineContent>
              </TimelineItem>
            ))}
          </Timeline>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ComplianceReview;
