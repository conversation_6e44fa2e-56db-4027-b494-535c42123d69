import axios from 'axios';
import { retry } from 'ts-retry-promise';

const api = axios.create({
    baseURL: 'http://127.0.0.1:8000/api/v1',
    timeout: 10000,
    withCredentials: true
});

api.interceptors.response.use(
    (response) => response.data,
    (error) => {
        if (error.response?.status === 401) {
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

const login = async (data: { username: string; password: string }) => {
    return retry(
        () => api.post('/auth/login', data),
        { retries: 3, delay: 1000 }
    );
};

const logout = async () => {
    return retry(
        () => api.post('/auth/logout'),
        { retries: 3, delay: 1000 }
    );
};

const getCurrentUser = async () => {
    return retry(
        () => api.get('/auth/me'),
        { retries: 3, delay: 1000 }
    );
};

export { login, logout, getCurrentUser };