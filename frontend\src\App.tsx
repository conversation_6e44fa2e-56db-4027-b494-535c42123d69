import React, { Suspense } from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Login from './components/auth/Login';
import Dashboard from './components/dashboard/Dashboard';
import BCADashboard from './components/BCADashboard';
import EHPReview from './components/EHP/EHPReview';
import RemediationPlan from './components/Remediation/RemediationPlan';
import FloodMap from './components/FloodMap/FloodMap';
import DocumentUpload from './components/documents/DocumentUpload';

i18n.use(initReactI18next).init({
    resources: {
        en: {
            translation: {
                "welcome": "Welcome to ComplianceMax",
                "login": "Login",
                "dashboard": "Dashboard",
                "bca_dashboard": "BCA Dashboard",
                "ehp_reviews": "EHP Reviews",
                "remediation_plans": "Remediation Plans",
                "flood_maps": "Flood Maps",
                "document_upload": "Document Upload"
            }
        },
        es: {
            translation: {
                "welcome": "Bienvenido a ComplianceMax",
                "login": "Iniciar sesión",
                "dashboard": "Tablero",
                "bca_dashboard": "Tablero de BCA",
                "ehp_reviews": "Revisiones de EHP",
                "remediation_plans": "Planes de remediación",
                "flood_maps": "Mapas de inundación",
                "document_upload": "Carga de documentos"
            }
        }
    },
    lng: "en",
    fallbackLng: "en",
    interpolation: {
        escapeValue: false
    }
});

const App: React.FC = () => {
    return (
        <I18nextProvider i18n={i18n}>
            <Suspense fallback={<div>Loading...</div>}>
                <Router>
                    <Routes>
                        <Route path="/login" element={<Login />} />
                        <Route path="/dashboard" element={<Dashboard />} />
                        <Route path="/bca" element={<BCADashboard />} />
                        <Route path="/ehp" element={<EHPReview />} />
                        <Route path="/remediation" element={<RemediationPlan />} />
                        <Route path="/flood-maps" element={<FloodMap />} />
                        <Route path="/document-upload" element={<DocumentUpload />} />
                        <Route path="/" element={<Login />} />
                    </Routes>
                </Router>
            </Suspense>
        </I18nextProvider>
    );
};

export default App;