from typing import Dict, Any, Optional, List, Tuple, Union
import time
import logging
import asyncio
from datetime import datetime
import hashlib
from app.core.config import settings

logger = logging.getLogger(__name__)

class InMemoryCache:
    _instance = None
    _cache: Dict[str, Any] = {}
    _expirations: Dict[str, float] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(InMemoryCache, cls).__new__(cls)
        return cls._instance

    async def init(self):
        """Initialize cache."""
        logger.info("In-memory cache initialized")
        # Start background cleanup task
        asyncio.create_task(self._cleanup_expired_keys())
        return True
    
    async def _cleanup_expired_keys(self):
        """Periodically clean up expired keys."""
        while True:
            try:
                current_time = time.time()
                keys_to_remove = []
                
                for key, expiration in self._expirations.items():
                    if current_time > expiration:
                        keys_to_remove.append(key)
                
                for key in keys_to_remove:
                    del self._cache[key]
                    del self._expirations[key]
                
                if keys_to_remove:
                    logger.debug(f"Cleaned up {len(keys_to_remove)} expired cache keys")
            except Exception as e:
                logger.error(f"Error during cache cleanup: {str(e)}")
            
            # Run cleanup every 60 seconds
            await asyncio.sleep(60)

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            if key in self._cache:
                if time.time() < self._expirations.get(key, float('inf')):
                    return self._cache[key]
                else:
                    # Remove expired item
                    del self._cache[key]
                    del self._expirations[key]
            return None
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {str(e)}")
            return None

    async def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """Set value in cache with expiration."""
        try:
            self._cache[key] = value
            self._expirations[key] = time.time() + expire
            return True
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {str(e)}")
            return False

    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            if key in self._cache:
                del self._cache[key]
                del self._expirations[key]
            return True
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {str(e)}")
            return False

    async def clear_all(self) -> bool:
        """Clear all cache entries."""
        try:
            self._cache.clear()
            self._expirations.clear()
            return True
        except Exception as e:
            logger.error(f"Failed to clear cache: {str(e)}")
            return False

    async def close(self):
        """Close cache."""
        await self.clear_all()
        logger.info("In-memory cache closed")

# Cache decorator for routes
def cached(*, expire: int = 3600):
    """Decorator for caching route responses."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            cache = InMemoryCache()
            # Generate cache key from function name and arguments
            key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
            
            # Try to get from cache
            cached_result = await cache.get(key)
            if cached_result is not None:
                return cached_result
            
            # If not in cache, execute function
            result = await func(*args, **kwargs)
            
            # Store in cache
            await cache.set(key, result, expire)
            return result
        return wrapper
    return decorator

class RateLimiter:
    """Advanced in-memory rate limiter with IP whitelisting and dynamic limits."""

    def __init__(self):
        self.requests: Dict[str, List[float]] = {}
        self.blocked_keys: Dict[str, float] = {}  # key -> block expiration time
        self.whitelisted_ips: List[str] = settings.RATE_LIMIT_WHITELIST
        
        # Start background cleanup task
        asyncio.create_task(self._cleanup_old_requests())
    
    async def _cleanup_old_requests(self):
        """Periodically clean up old requests and expired blocks."""
        while True:
            try:
                current = time.time()
                
                # Clean up old requests
                for key in list(self.requests.keys()):
                    self.requests[key] = [t for t in self.requests[key] if current - t < 3600]  # Keep last hour
                    if not self.requests[key]:
                        del self.requests[key]
                
                # Clean up expired blocks
                for key in list(self.blocked_keys.keys()):
                    if current > self.blocked_keys[key]:
                        del self.blocked_keys[key]
                
                logger.debug(f"Rate limiter cleanup: {len(self.requests)} active keys, {len(self.blocked_keys)} blocked keys")
            except Exception as e:
                logger.error(f"Error during rate limiter cleanup: {str(e)}")
            
            # Run cleanup every 5 minutes
            await asyncio.sleep(300)
    
    def _is_whitelisted(self, key: str) -> bool:
        """Check if a key (typically an IP) is whitelisted."""
        # Extract IP from key if it contains IP
        ip = key.split(":")[-1] if ":" in key else key
        return ip in self.whitelisted_ips or settings.DEBUG
    
    def _get_limit_for_endpoint(self, endpoint: str) -> Tuple[int, int]:
        """Get rate limit for a specific endpoint type."""
        endpoint_type = endpoint.split(":")[0] if ":" in endpoint else endpoint
        
        # Default limits
        default_limit = 60  # requests
        default_period = 60  # seconds
        
        # Different limits for different endpoint types
        if endpoint_type == "login" or endpoint_type == "register":
            return 5, 60  # 5 requests per minute for auth endpoints
        elif endpoint_type == "password_reset":
            return 3, 300  # 3 requests per 5 minutes for password reset
        elif "upload" in endpoint_type:
            return 10, 60  # 10 uploads per minute
        elif "download" in endpoint_type or "document" in endpoint_type:
            return 30, 60  # 30 downloads/document requests per minute
        
        return default_limit, default_period
    
    def _generate_key_hash(self, key: str) -> str:
        """Generate a hash of the key to avoid storing sensitive information."""
        # Add salt to prevent rainbow table attacks
        salted_key = f"{key}:{settings.SECRET_KEY[:8]}"
        return hashlib.sha256(salted_key.encode()).hexdigest()[:16]
    
    async def check_rate_limit(
        self, 
        key: str, 
        limit: Optional[int] = None, 
        period: Optional[int] = None,
        endpoint: Optional[str] = None
    ) -> Tuple[bool, int]:
        """Check if rate limit is exceeded.

        Args:
            key: Rate limit key (e.g., IP address or user ID)
            limit: Maximum number of requests (optional)
            period: Time period in seconds (optional)
            endpoint: Type of endpoint being accessed (optional)

        Returns:
            Tuple of (is_allowed, retry_after)
        """
        # Skip rate limiting for whitelisted IPs
        if self._is_whitelisted(key):
            return True, 0
        
        # Check if key is currently blocked
        current = time.time()
        hashed_key = self._generate_key_hash(key)
        
        if hashed_key in self.blocked_keys:
            if current < self.blocked_keys[hashed_key]:
                retry_after = int(self.blocked_keys[hashed_key] - current)
                return False, retry_after
            else:
                # Block expired, remove it
                del self.blocked_keys[hashed_key]
        
        # Get dynamic limits if not provided
        if endpoint and (limit is None or period is None):
            default_limit, default_period = self._get_limit_for_endpoint(endpoint)
            limit = limit or default_limit
            period = period or default_period
        
        # Ensure values are set
        limit = limit or 60  # Default: 60 requests 
        period = period or 60  # Default: per minute
        
        # Initialize request list for this key if it doesn't exist
        if hashed_key not in self.requests:
            self.requests[hashed_key] = []
        
        # Clean up old requests for this key
        self.requests[hashed_key] = [t for t in self.requests[hashed_key] if current - t < period]
        
        # Check if limit is exceeded
        if len(self.requests[hashed_key]) >= limit:
            # Calculate when the oldest request will expire
            oldest_request = self.requests[hashed_key][0]
            retry_after = int(period - (current - oldest_request))
            
            # If this is a repeated violation, block for progressively longer periods
            violation_count = len(self.requests[hashed_key]) - limit
            if violation_count > 5:  # Threshold for blocking
                # Block duration increases with violation count
                block_duration = min(3600, 60 * (2 ** (violation_count - 5)))  # Max 1 hour
                self.blocked_keys[hashed_key] = current + block_duration
                logger.warning(f"Rate limit key {hashed_key} blocked for {block_duration} seconds due to {violation_count} violations")
            
            return False, max(1, retry_after)
        
        # Record the current request
        self.requests[hashed_key].append(current)
        return True, 0
    
    async def reset_limits(self, key: str) -> bool:
        """Reset rate limits for a specific key."""
        try:
            hashed_key = self._generate_key_hash(key)
            if hashed_key in self.requests:
                del self.requests[hashed_key]
            if hashed_key in self.blocked_keys:
                del self.blocked_keys[hashed_key]
            return True
        except Exception as e:
            logger.error(f"Error resetting rate limits for {key}: {str(e)}")
            return False
            
    async def add_to_whitelist(self, ip: str) -> bool:
        """Add an IP to the whitelist."""
        if ip not in self.whitelisted_ips:
            self.whitelisted_ips.append(ip)
            return True
        return False
        
    async def remove_from_whitelist(self, ip: str) -> bool:
        """Remove an IP from the whitelist."""
        if ip in self.whitelisted_ips:
            self.whitelisted_ips.remove(ip)
            return True
        return False