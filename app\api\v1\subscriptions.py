from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.core.deps import get_db, get_current_active_user
from app.services.subscription_service import SubscriptionService
from app.schemas.subscription import (
    SubscriptionCreate, SubscriptionResponse,
    SubscriptionFeatureCreate, SubscriptionFeatureResponse,
    BillingCreate, BillingResponse
)
from app.models.user import User

router = APIRouter()

@router.post("/subscriptions/", response_model=SubscriptionResponse)
def create_subscription(
    subscription: SubscriptionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = SubscriptionService(db)
    return service.create_subscription(subscription)

@router.get("/subscriptions/current", response_model=SubscriptionResponse)
def get_current_subscription(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = SubscriptionService(db)
    subscription = service.get_active_subscription(user_id=current_user.id)
    if not subscription:
        raise HTTPException(status_code=404, detail="No active subscription found")
    return subscription

@router.post("/features/", response_model=SubscriptionFeatureResponse)
def create_feature(
    feature: SubscriptionFeatureCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = SubscriptionService(db)
    return service.create_feature(feature)

@router.get("/features/", response_model=List[SubscriptionFeatureResponse])
def list_features(
    tier: str = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = SubscriptionService(db)
    return service.get_features(tier=tier, skip=skip, limit=limit)

@router.post("/billing/", response_model=BillingResponse)
def create_billing(
    billing: BillingCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = SubscriptionService(db)
    return service.create_billing(billing)

@router.get("/billing/", response_model=List[BillingResponse])
def list_billing(
    subscription_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = SubscriptionService(db)
    return service.get_billing_records(subscription_id=subscription_id, skip=skip, limit=limit)
