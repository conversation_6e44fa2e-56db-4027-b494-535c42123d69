async def get_applicable_policies(self, dr_number: str, incident_date: datetime) -> List[Dict[str, Any]]:
    """Get policies applicable to a disaster number and date with improved caching and error handling."""
    cache_key = f"policies:{dr_number}:{incident_date.isoformat()}"
    cached_result = await self.cache.get(cache_key)
    
    if cached_result:
        return cached_result
    
    try:
        if not self.db:
            raise ValueError("Database session not initialized")

        stmt = select(PolicyDocument).where(
            and_(
                PolicyDocument.dr_numbers.contains([dr_number]),
                PolicyDocument.effective_date <= incident_date,
                or_(
                    PolicyDocument.expiration_date.is_(None),
                    PolicyDocument.expiration_date >= incident_date
                )
            )
        )
        
        result = await self.db.execute(stmt)
        policies = result.scalars().all()
        
        policy_data = [
            {
                'id': str(policy.id),
                'title': policy.title,
                'policy_number': policy.policy_number,
                'requirements': policy.policy_requirements,
                'effective_date': policy.effective_date,
                'expiration_date': policy.expiration_date,
                'metadata': policy.metadata,
                'priority': policy.priority or 'medium'
            }
            for policy in policies
        ]
        
        # Cache for 1 hour
        await self.cache.set(cache_key, policy_data, 3600)
        return policy_data
        
    except SQLAlchemyError as e:
        self._handle_db_error("fetching applicable policies", e)async def get_applicable_policies(self, dr_number: str, incident_date: datetime) -> List[Dict[str, Any]]:
    """Get policies applicable to a disaster number and date with improved caching and error handling."""
    cache_key = f"policies:{dr_number}:{incident_date.isoformat()}"
    cached_result = await self.cache.get(cache_key)
    
    if cached_result:
        return cached_result
    
    try:
        if not self.db:
            raise ValueError("Database session not initialized")

        stmt = select(PolicyDocument).where(
            and_(
                PolicyDocument.dr_numbers.contains([dr_number]),
                PolicyDocument.effective_date <= incident_date,
                or_(
                    PolicyDocument.expiration_date.is_(None),
                    PolicyDocument.expiration_date >= incident_date
                )
            )
        )
        
        result = await self.db.execute(stmt)
        policies = result.scalars().all()
        
        policy_data = [
            {
                'id': str(policy.id),
                'title': policy.title,
                'policy_number': policy.policy_number,
                'requirements': policy.policy_requirements,
                'effective_date': policy.effective_date,
                'expiration_date': policy.expiration_date,
                'metadata': policy.metadata,
                'priority': policy.priority or 'medium'
            }
            for policy in policies
        ]
        
        # Cache for 1 hour
        await self.cache.set(cache_key, policy_data, 3600)
        return policy_data
        
    except SQLAlchemyError as e:
        self._handle_db_error("fetching applicable policies", e)
        async def calculate_requirement_coverage(
            self,
            policy_requirements: List[Dict[str, Any]],
            document_text: str,
            document_sections: List[Dict[str, Any]]
        ) -> Dict[str, Any]:
            """Enhanced requirement coverage calculation with section matching and confidence scoring."""
            coverage_results = {
                'total_requirements': len(policy_requirements),
                'covered_requirements': 0,
                'partially_covered_requirements': 0,
                'missing_requirements': 0,
                'requirements_detail': [],
                'overall_coverage_score': 0.0
            }
            
            # Process document sections for better matching
            section_embeddings = {}
            for section in document_sections:
                section_embeddings[section['id']] = self.sentence_model.encode(section['text'])
            
            # Process full document text for fallback
            doc_embedding = self.sentence_model.encode(document_text)
            
            total_weight = 0
            total_weighted_score = 0
            
            for req in policy_requirements:
                req_embedding = self.sentence_model.encode(req['text'])
                
                # Try to find the best matching section first
                best_score = 0
                best_section = None
                
                for section_id, section_embedding in section_embeddings.items():
                    score = cosine_similarity([req_embedding], [section_embedding])[0][0]
                    if score > best_score:
                        best_score = score
                        best_section = section_id
                
                # If no good section match, compare against whole document
                if best_score < 0.6:
                    fallback_score = cosine_similarity([req_embedding], [doc_embedding])[0][0]
                    if fallback_score > best_score:
                        best_score = fallback_score
                        best_section = 'full_document'
                
                # Determine coverage status based on score
                status = 'missing'
                if best_score >= 0.8:
                    status = 'covered'
                    coverage_results['covered_requirements'] += 1
                elif best_score >= 0.6:
                    status = 'partially_covered'
                    coverage_results['partially_covered_requirements'] += 1
                else:
                    coverage_results['missing_requirements'] += 1
                
                # Calculate weighted score based on requirement priority
                weight = 1.0
                if 'priority' in req:
                    if req['priority'] == 'high':
                        weight = 3.0
                    elif req['priority'] == 'medium':
                        weight = 2.0
                
                total_weight += weight
                total_weighted_score += best_score * weight
                
                # Add detailed result
                coverage_results['requirements_detail'].append({
                    'requirement_id': req.get('id', ''),
                    'requirement_text': req['text'],
                    'priority': req.get('priority', 'medium'),
                    'status': status,
                    'confidence_score': best_score,
                    'matching_section': best_section,
                    'suggested_action': self._get_suggested_action(status, best_score, req)
                })
            
            # Calculate overall weighted coverage score
            if total_weight > 0:
                coverage_results['overall_coverage_score'] = total_weighted_score / total_weight
            
            return coverage_resultsasync def calculate_requirement_coverage(
    self,
    policy_requirements: List[Dict[str, Any]],
    document_text: str,
    document_sections: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Enhanced requirement coverage calculation with section matching and confidence scoring."""
    coverage_results = {
        'total_requirements': len(policy_requirements),
        'covered_requirements': 0,
        'partially_covered_requirements': 0,
        'missing_requirements': 0,
        'requirements_detail': [],
        'overall_coverage_score': 0.0
    }
    
    # Process document sections for better matching
    section_embeddings = {}
    for section in document_sections:
        section_embeddings[section['id']] = self.sentence_model.encode(section['text'])
    
    # Process full document text for fallback
    doc_embedding = self.sentence_model.encode(document_text)
    
    total_weight = 0
    total_weighted_score = 0
    
    for req in policy_requirements:
        req_embedding = self.sentence_model.encode(req['text'])
        
        # Try to find the best matching section first
        best_score = 0
        best_section = None
        
        for section_id, section_embedding in section_embeddings.items():
            score = cosine_similarity([req_embedding], [section_embedding])[0][0]
            if score > best_score:
                best_score = score
                best_section = section_id
        
        # If no good section match, compare against whole document
        if best_score < 0.6:
            fallback_score = cosine_similarity([req_embedding], [doc_embedding])[0][0]
            if fallback_score > best_score:
                best_score = fallback_score
                best_section = 'full_document'
        
        # Determine coverage status based on score
        status = 'missing'
        if best_score >= 0.8:
            status = 'covered'
            coverage_results['covered_requirements'] += 1
        elif best_score >= 0.6:
            status = 'partially_covered'
            coverage_results['partially_covered_requirements'] += 1
        else:
            coverage_results['missing_requirements'] += 1
        
        # Calculate weighted score based on requirement priority
        weight = 1.0
        if 'priority' in req:
            if req['priority'] == 'high':
                weight = 3.0
            elif req['priority'] == 'medium':
                weight = 2.0
        
        total_weight += weight
        total_weighted_score += best_score * weight
        
        # Add detailed result
        coverage_results['requirements_detail'].append({
            'requirement_id': req.get('id', ''),
            'requirement_text': req['text'],
            'priority': req.get('priority', 'medium'),
            'status': status,
            'confidence_score': best_score,
            'matching_section': best_section,
            'suggested_action': self._get_suggested_action(status, best_score, req)
        })
    
    # Calculate overall weighted coverage score
    if total_weight > 0:
        coverage_results['overall_coverage_score'] = total_weighted_score / total_weight
    
    return coverage_results
    from typing import List, Dict, Optional, Any, Union
from datetime import datetime
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import spacy
from transformers import pipeline
import re
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.exc import SQLAlchemyError, TimeoutError, OperationalError
from fastapi import HTTPException
import logging
from app.models.document import (
    Document, PolicyDocument, DocumentComparison,
    ApplicantDocument, FEMAAppeal
)

logger = logging.getLogger(__name__)

class DocumentComparator:
    def __init__(self):
        self.analyzer = DocumentAnalyzer()
        self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.nlp = spacy.load("en_core_web_sm")
        self.qa_pipeline = pipeline("question-answering", model="distilbert-base-cased-distilled-squad")
        self.db: Optional[AsyncSession] = None

        # Compile regex patterns for requirements
        self.requirement_patterns = [
            re.compile(r'\bmust\s+', re.I),
            re.compile(r'\bshall\s+', re.I),
            re.compile(r'\brequired\s+to\s+', re.I),
            re.compile(r'\brequirements?\s+include\s+', re.I),
            re.compile(r'\bmandatory\s+', re.I),
            re.compile(r'\bshould\s+', re.I)
        ]

    def _handle_db_error(self, operation: str, error: Exception):
        """Handle database errors with appropriate HTTP exceptions"""
        if isinstance(error, TimeoutError):
            logger.error(f"Database timeout during {operation}")
            raise HTTPException(
                status_code=503,
                detail="Database operation timed out"
            )
        elif isinstance(error, OperationalError):
            logger.error(f"Database operational error during {operation}: {str(error)}")
            raise HTTPException(
                status_code=503,
                detail="Database operational error"
            )
        elif isinstance(error, SQLAlchemyError):
            logger.error(f"Database error during {operation}: {str(error)}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred"
            )
        else:
            logger.error(f"Unexpected error during {operation}: {str(error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error during {operation}"
            )

    async def _get_applicable_policies(
        self,
        dr_number: str,
        incident_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get policies applicable to a disaster number and date."""
        try:
            if not self.db:
                raise ValueError("Database session not initialized")

            stmt = select(PolicyDocument).where(
                and_(
                    PolicyDocument.dr_numbers.contains([dr_number]),
                    PolicyDocument.effective_date <= incident_date,
                    or_(
                        PolicyDocument.expiration_date.is_(None),
                        PolicyDocument.expiration_date >= incident_date
                    )
                )
            )
            
            result = await self.db.execute(stmt)
            policies = result.scalars().all()
            
            return [
                {
                    'id': str(policy.id),
                    'title': policy.title,
                    'requirements': policy.policy_requirements,
                    'effective_date': policy.effective_date,
                    'expiration_date': policy.expiration_date,
                    'metadata': policy.metadata
                }
                for policy in policies
            ]

        except Exception as e:
            self._handle_db_error("getting applicable policies", e)

    async def compare_documents(
        self,
        doc1_path: str,
        doc2_path: str,
        dr_number: Optional[str] = None,
        incident_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Compare two documents and identify similarities, differences, and compliance."""
        try:
            if not self.db:
                raise ValueError("Database session not initialized")

            # Load and analyze documents
            doc1_analysis = await self.analyzer.analyze_document(doc1_path)
            doc2_analysis = await self.analyzer.analyze_document(doc2_path)

            # Validate input
            await self._validate_input(doc1_analysis, doc2_analysis, dr_number, incident_date)

            # Get applicable policies
            applicable_policies = []
            if dr_number and incident_date:
                applicable_policies = await self._get_applicable_policies(dr_number, incident_date)

            # Perform comparisons
            similarity_scores = await self._calculate_similarity_scores(doc1_analysis, doc2_analysis)
            requirement_coverage = await self._calculate_requirement_coverage(
                doc1_analysis.get('requirements', []),
                doc2_analysis.get('requirements', [])
            )
            temporal_results = await self._validate_temporal_context(
                {'doc1': doc1_analysis, 'doc2': doc2_analysis},
                incident_date,
                dr_number
            )

            # Analyze appeals if applicable
            appeals_analysis = None
            if dr_number:
                appeals_analysis = await self._analyze_relevant_appeals(
                    dr_number,
                    doc1_analysis,
                    doc2_analysis,
                    incident_date
                )

            # Calculate final scores
            compliance_scores = await self._calculate_compliance_scores(
                requirement_coverage,
                temporal_results,
                similarity_scores,
                appeals_analysis,
                applicable_policies
            )

            # Generate recommendations
            recommendations = await self._generate_recommendations(
                requirement_coverage,
                temporal_results,
                similarity_scores,
                appeals_analysis,
                applicable_policies
            )

            # Log metrics
            await self._log_comparison_metrics(doc1_path, doc2_path, compliance_scores)

            return {
                'similarity_scores': similarity_scores,
                'requirement_coverage': requirement_coverage,
                'temporal_validation': temporal_results,
                'compliance_scores': compliance_scores,
                'recommendations': recommendations,
                'appeals_analysis': appeals_analysis,
                'metadata': {
                    'doc1': await self._extract_metadata(doc1_analysis),
                    'doc2': await self._extract_metadata(doc2_analysis)
                }
            }

        except Exception as e:
            self._handle_db_error("comparing documents", e)

    async def set_db_session(self, db_session: AsyncSession):
        """Set the database session for database operations."""
        self.db = db_session

    async def _validate_input(
        self,
        doc1_analysis: Dict[str, Any],
        doc2_analysis: Dict[str, Any],
        dr_number: Optional[str] = None,
        incident_date: Optional[datetime] = None
    ) -> None:
        """Validate input parameters."""
        try:
            # Check required analysis fields
            for doc in [doc1_analysis, doc2_analysis]:
                if not isinstance(doc, dict):
                    raise ValueError("Document analysis must be a dictionary")
                
                required_fields = ["requirements", "temporal_info", "structure"]
                missing_fields = [field for field in required_fields if field not in doc]
                if missing_fields:
                    raise ValueError(f"Missing required fields in document analysis: {missing_fields}")
            
            # Validate DR number format if provided
            if dr_number and not re.match(r'^DR-\d{4}$', dr_number):
                raise ValueError("Invalid DR number format. Must be in format 'DR-XXXX'")
            
            # Validate incident date if provided
            if incident_date and not isinstance(incident_date, datetime):
                raise ValueError("Incident date must be a datetime object")
                
        except Exception as e:
            self._handle_db_error("validating input", e)

    async def _calculate_similarity_scores(
        self,
        doc1_analysis: Dict[str, Any],
        doc2_analysis: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate similarity scores between documents."""
        try:
            # Get text blocks
            doc1_blocks = [block["text"] for block in doc1_analysis.get("text_blocks", [])]
            doc2_blocks = [block["text"] for block in doc2_analysis.get("text_blocks", [])]
            
            # Calculate semantic similarity
            if doc1_blocks and doc2_blocks:
                doc1_embeddings = self.sentence_model.encode(" ".join(doc1_blocks))
                doc2_embeddings = self.sentence_model.encode(" ".join(doc2_blocks))
                semantic_sim = float(cosine_similarity([doc1_embeddings], [doc2_embeddings])[0][0])
            else:
                semantic_sim = 0.0
            
            # Calculate structural similarity
            doc1_structure = doc1_analysis.get("structure", {})
            doc2_structure = doc2_analysis.get("structure", {})
            structural_sim = await self._calculate_structural_similarity(doc1_structure, doc2_structure)
            
            # Calculate requirement similarity
            doc1_reqs = doc1_analysis.get("requirements", [])
            doc2_reqs = doc2_analysis.get("requirements", [])
            req_sim = await self._calculate_requirement_similarity(doc1_reqs, doc2_reqs)
            
            return {
                "semantic": semantic_sim,
                "structural": structural_sim,
                "requirements": req_sim,
                "overall": (semantic_sim + structural_sim + req_sim) / 3
            }
            
        except Exception as e:
            self._handle_db_error("calculating similarity scores", e)

    async def _calculate_structural_similarity(
        self,
        struct1: Dict[str, Any],
        struct2: Dict[str, Any]
    ) -> float:
        """Calculate structural similarity between documents."""
        try:
            # Get section titles
            titles1 = [section["title"] for section in struct1.get("sections", [])]
            titles2 = [section["title"] for section in struct2.get("sections", [])]
            
            if not titles1 or not titles2:
                return 0.0
            
            # Calculate Levenshtein similarity for each pair
            similarities = []
            for t1 in titles1:
                scores = [Levenshtein.ratio(t1, t2) for t2 in titles2]
                similarities.append(max(scores))
            
            # Average the similarities
            return sum(similarities) / len(similarities)
            
        except Exception as e:
            self._handle_db_error("calculating structural similarity", e)

    async def _calculate_requirement_similarity(
        self,
        reqs1: List[Dict[str, Any]],
        reqs2: List[Dict[str, Any]]
    ) -> float:
        """Calculate similarity between requirements."""
        try:
            if not reqs1 or not reqs2:
                return 0.0
            
            # Get requirement texts
            texts1 = [req["text"] for req in reqs1]
            texts2 = [req["text"] for req in reqs2]
            
            # Calculate embeddings
            embeddings1 = self.sentence_model.encode(texts1)
            embeddings2 = self.sentence_model.encode(texts2)
            
            # Calculate similarity matrix
            sim_matrix = cosine_similarity(embeddings1, embeddings2)
            
            # Get best matches for each requirement
            best_matches = np.max(sim_matrix, axis=1)
            
            # Average the similarities
            return float(np.mean(best_matches))
            
        except Exception as e:
            self._handle_db_error("calculating requirement similarity", e)

    async def _calculate_requirement_coverage(
        self,
        reqs1: List[Dict[str, Any]],
        reqs2: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate requirement coverage between two documents."""
        try:
            # Get requirement texts
            texts1 = [req["text"] for req in reqs1]
            texts2 = [req["text"] for req in reqs2]
            
            # Calculate embeddings
            embeddings1 = self.sentence_model.encode(texts1) if texts1 else []
            embeddings2 = self.sentence_model.encode(texts2) if texts2 else []
            
            # Calculate coverage for each document
            coverage = {
                "doc1": await self._calculate_coverage(embeddings2, embeddings1),
                "doc2": await self._calculate_coverage(embeddings1, embeddings2),
                "average": (await self._calculate_coverage(embeddings2, embeddings1) + await self._calculate_coverage(embeddings1, embeddings2)) / 2
            }
            
            return coverage
            
        except Exception as e:
            self._handle_db_error("calculating requirement coverage", e)

    async def _calculate_coverage(
        self,
        source_embeddings: np.ndarray,
        target_embeddings: np.ndarray
    ) -> float:
        """Calculate coverage of source embeddings by target embeddings."""
        try:
            if len(source_embeddings) == 0 or len(target_embeddings) == 0:
                return 0.0
            
            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(source_embeddings, target_embeddings)
            
            # Get best match for each source requirement
            best_matches = np.max(similarity_matrix, axis=1)
            
            # Calculate coverage (percentage of requirements with good matches)
            coverage = np.mean(best_matches >= 0.8)  # Threshold for good match
            
            return float(coverage)
            
        except Exception as e:
            self._handle_db_error("calculating coverage", e)

    async def _validate_temporal_context(
        self,
        comparison_results: Dict[str, Any],
        incident_date: datetime,
        dr_number: Optional[str] = None
    ) -> Dict[str, Any]:
        """Validate temporal context of comparison results."""
        try:
            # Extract temporal information
            temporal_info = comparison_results.get("temporal_info", {})
            doc1_dates = temporal_info.get("doc1", {}).get("temporal_info", {})
            doc2_dates = temporal_info.get("doc2", {}).get("temporal_info", {})
            
            # Analyze temporal alignment
            alignment = {
                "incident_date": incident_date.isoformat(),
                "doc1_alignment": await self._analyze_temporal_alignment(doc1_dates, incident_date),
                "doc2_alignment": await self._analyze_temporal_alignment(doc2_dates, incident_date)
            }
            
            # Add disaster context if available
            if dr_number:
                alignment["disaster_context"] = await self._get_disaster_context(dr_number)
            
            return alignment
            
        except Exception as e:
            self._handle_db_error("validating temporal context", e)

    async def _analyze_temporal_alignment(
        self,
        date_info: Dict[str, Any],
        incident_date: datetime
    ) -> Dict[str, Any]:
        """Analyze temporal alignment of dates with incident date."""
        try:
            alignment = {
                "before_incident": 0,
                "during_incident": 0,
                "after_incident": 0,
                "total_dates": 0,
                "average_distance": 0
            }
            
            dates = []
            for date in date_info.get("validated_dates", []):
                date_obj = datetime.fromisoformat(date["date"])
                days_diff = (date_obj - incident_date).days
                
                if days_diff < 0:
                    alignment["before_incident"] += 1
                elif days_diff == 0:
                    alignment["during_incident"] += 1
                else:
                    alignment["after_incident"] += 1
                
                dates.append(abs(days_diff))
            
            alignment["total_dates"] = len(dates)
            alignment["average_distance"] = sum(dates) / len(dates) if dates else 0
            
            return alignment
            
        except Exception as e:
            self._handle_db_error("analyzing temporal alignment", e)

    async def _get_disaster_context(self, dr_number: str) -> Dict[str, Any]:
        """Get disaster context for temporal validation."""
        try:
            # Query disaster declaration
            async with self.db as session:
                query = (
                    select(DisasterDeclaration)
                    .where(DisasterDeclaration.dr_number == dr_number)
                )
                result = await session.execute(query)
                disaster = result.scalar_one_or_none()
                
                if not disaster:
                    return {"error": f"Disaster DR-{dr_number} not found"}
                
                return {
                    "dr_number": disaster.dr_number,
                    "incident_type": disaster.incident_type,
                    "incident_period": {
                        "start": disaster.incident_period_start.isoformat(),
                        "end": disaster.incident_period_end.isoformat() if disaster.incident_period_end else None
                    },
                    "declaration_date": disaster.declaration_date.isoformat(),
                    "state": disaster.state,
                    "affected_areas": disaster.affected_areas
                }
            
        except Exception as e:
            self._handle_db_error("getting disaster context", e)

    async def _analyze_relevant_appeals(
        self,
        dr_number: str,
        doc1_analysis: Dict[str, Any],
        doc2_analysis: Dict[str, Any],
        incident_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Analyze relevant FEMA appeals for the comparison."""
        try:
            # Get disaster-specific appeals
            appeals = await self.db.execute(
                select(FEMAAppeal)
                .where(FEMAAppeal.dr_number == dr_number)
                .order_by(FEMAAppeal.decision_date.desc())
            )
            appeals = appeals.fetchall()

            # Extract key concepts from documents
            doc1_concepts = await self._extract_key_concepts(doc1_analysis)
            doc2_concepts = await self._extract_key_concepts(doc2_analysis)
            combined_concepts = doc1_concepts + doc2_concepts

            # Analyze each appeal
            appeal_matches = []
            for appeal in appeals:
                appeal_concepts = await self._extract_key_concepts({
                    "text": appeal.content,
                    "requirements": appeal.requirements
                })
                
                relevance_score = await self._calculate_concept_similarity(
                    combined_concepts,
                    appeal_concepts
                )
                
                if relevance_score > 0.5:  # Threshold for appeal relevance
                    appeal_matches.append({
                        "appeal_number": appeal.appeal_number,
                        "decision_date": appeal.decision_date.isoformat(),
                        "decision_outcome": appeal.decision_outcome,
                        "relevance_score": relevance_score,
                        "key_findings": await self._extract_key_findings(appeal),
                        "applicability": await self._assess_appeal_applicability(
                            appeal,
                            doc1_analysis,
                            doc2_analysis,
                            incident_date
                        )
                    })

            return {
                "relevant_appeals": sorted(
                    appeal_matches,
                    key=lambda x: x["relevance_score"],
                    reverse=True
                ),
                "appeal_count": len(appeal_matches),
                "average_relevance": sum(p["relevance_score"] for p in appeal_matches) / len(appeal_matches) if appeal_matches else 0,
                "temporal_coverage": await self._analyze_appeal_coverage(appeal_matches, incident_date)
            }

        except Exception as e:
            self._handle_db_error("analyzing appeals", e)

    async def _calculate_compliance_scores(
        self,
        req_comparison: Dict[str, Any],
        temporal_comparison: Dict[str, Any],
        structure_comparison: Dict[str, Any],
        appeals_analysis: Optional[Dict[str, Any]],
        policy_analysis: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate detailed compliance scores."""
        try:
            # Base compliance from requirements
            requirement_score = await self._calculate_requirement_compliance(req_comparison)
            
            # Temporal compliance
            temporal_score = await self._calculate_temporal_compliance(temporal_comparison)
            
            # Structural compliance
            structural_score = await self._calculate_structural_compliance(structure_comparison)
            
            # Policy compliance if available
            policy_score = await self._calculate_policy_compliance(policy_analysis) if policy_analysis else None
            
            # Appeals impact if available
            appeals_score = await self._calculate_appeals_impact(appeals_analysis) if appeals_analysis else None
            
            # Calculate weighted overall score
            weights = {
                "requirements": 0.35,
                "temporal": 0.20,
                "structural": 0.15,
                "policy": 0.20,
                "appeals": 0.10
            }
            
            scores = {
                "requirement_compliance": requirement_score,
                "temporal_compliance": temporal_score,
                "structural_compliance": structural_score,
                "policy_compliance": policy_score,
                "appeals_impact": appeals_score
            }
            
            # Calculate overall score
            valid_scores = {k: v for k, v in scores.items() if v is not None}
            if valid_scores:
                total_weight = sum(weights[k] for k in valid_scores.keys())
                overall_score = sum(
                    scores[k] * (weights[k] / total_weight)
                    for k in valid_scores.keys()
                )
            else:
                overall_score = None

            return {
                "scores": scores,
                "overall_compliance": overall_score,
                "confidence": await self._calculate_score_confidence(valid_scores),
                "compliance_factors": await self._identify_compliance_factors(scores)
            }

        except Exception as e:
            self._handle_db_error("calculating compliance scores", e)

    async def _calculate_requirement_compliance(self, req_comparison: Dict[str, Any]) -> float:
        """Calculate requirement compliance score."""
        try:
            # Calculate compliance based on matched and unmatched requirements
            matched_count = req_comparison["matched_count"]
            total_requirements = req_comparison["total_requirements1"] + req_comparison["total_requirements2"]
            
            compliance = (matched_count / total_requirements) if total_requirements else 0
            
            return compliance
            
        except Exception as e:
            self._handle_db_error("calculating requirement compliance", e)

    async def _calculate_temporal_compliance(self, temporal_comparison: Dict[str, Any]) -> float:
        """Calculate temporal compliance score."""
        try:
            # Calculate compliance based on common and unique dates
            common_dates = len(temporal_comparison["common_dates"])
            only_in_doc1 = len(temporal_comparison["only_in_doc1"])
            only_in_doc2 = len(temporal_comparison["only_in_doc2"])
            
            compliance = (common_dates / (common_dates + only_in_doc1 + only_in_doc2)) if (common_dates + only_in_doc1 + only_in_doc2) else 0
            
            return compliance
            
        except Exception as e:
            self._handle_db_error("calculating temporal compliance", e)

    async def _calculate_structural_compliance(self, structure_comparison: Dict[str, Any]) -> float:
        """Calculate structural compliance score."""
        try:
            # Calculate compliance based on structure similarity
            compliance = structure_comparison["overall_similarity"]
            
            return compliance
            
        except Exception as e:
            self._handle_db_error("calculating structural compliance", e)

    async def _calculate_policy_compliance(self, policy_analysis: Dict[str, Any]) -> float:
        """Calculate policy compliance score."""
        try:
            # Calculate compliance based on policy applicability
            policy_applicability = policy_analysis["matching_policies"][0]["applicability"]["applicability_score"] if policy_analysis["matching_policies"] else 0
            
            compliance = policy_applicability
            
            return compliance
            
        except Exception as e:
            self._handle_db_error("calculating policy compliance", e)

    async def _calculate_appeals_impact(self, appeals_analysis: Dict[str, Any]) -> float:
        """Calculate appeals impact score."""
        try:
            # Calculate impact based on relevant appeals
            relevant_appeals = appeals_analysis["relevant_appeals"]
            impact = sum(a["relevance_score"] for a in relevant_appeals) / len(relevant_appeals) if relevant_appeals else 0
            
            return impact
            
        except Exception as e:
            self._handle_db_error("calculating appeals impact", e)

    async def _identify_compliance_factors(self, scores: Dict[str, float]) -> List[str]:
        """Identify factors affecting compliance scores."""
        try:
            # Identify factors with low scores
            low_scores = [k for k, v in scores.items() if v < 0.7]
            
            return low_scores
            
        except Exception as e:
            self._handle_db_error("identifying compliance factors", e)

    async def _generate_recommendations(
        self,
        req_comparison: Dict[str, Any],
        temporal_comparison: Dict[str, Any],
        structure_comparison: Dict[str, Any],
        appeals_analysis: Optional[Dict[str, Any]],
        policy_analysis: Optional[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate recommendations based on comparison results."""
        recommendations = []
        
        # Check requirements
        if req_comparison["missing_requirements"]:
            recommendations.append({
                "type": "requirement",
                "severity": "high",
                "description": "Missing required elements",
                "details": req_comparison["missing_requirements"],
                "suggested_action": "Add missing requirements"
            })
            
        # Check temporal alignment
        if temporal_comparison["misaligned_dates"]:
            recommendations.append({
                "type": "temporal",
                "severity": "medium",
                "description": "Date misalignment detected",
                "details": temporal_comparison["misaligned_dates"],
                "suggested_action": "Review and align dates"
            })
            
        # Check structure
        if structure_comparison["similarity_score"] < 0.7:
            recommendations.append({
                "type": "structure",
                "severity": "medium",
                "description": "Document structure needs improvement",
                "details": structure_comparison["differences"],
                "suggested_action": "Restructure document to match template"
            })
            
        # Include appeals-based recommendations
        if appeals_analysis and appeals_analysis.get("relevant_appeals"):
            for appeal in appeals_analysis["relevant_appeals"][:3]:  # Top 3 most relevant
                if appeal["decision_outcome"] in ["denied", "partially_granted"]:
                    recommendations.append({
                        "type": "appeal_precedent",
                        "severity": "high",
                        "description": f"Similar case was {appeal['decision_outcome']}",
                        "details": appeal["key_findings"],
                        "suggested_action": "Review and address similar issues",
                        "reference": appeal["appeal_number"]
                    })
                    
        # Include policy-based recommendations
        if policy_analysis and policy_analysis.get("matching_policies"):
            for policy in policy_analysis["matching_policies"][:3]:  # Top 3 most relevant
                if policy["applicability"]["temporal_validity"]:
                    recommendations.append({
                        "type": "policy_applicability",
                        "severity": "medium",
                        "description": f"Policy {policy['policy_number']} is applicable",
                        "details": policy["applicability"]["requirement_coverage"],
                        "suggested_action": "Review and ensure compliance with policy requirements"
                    })
                    
        return sorted(
            recommendations,
            key=lambda x: {"high": 0, "medium": 1, "low": 2}[x["severity"]]
        )

    async def _extract_metadata(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from document analysis."""
        metadata = {
            "document_type": analysis.get("document_type"),
            "file_name": analysis.get("file_name"),
            "file_size": analysis.get("file_size"),
            "page_count": analysis.get("page_count")
        }
        
        return metadata

    async def _log_comparison_metrics(
        self,
        doc1_path: str,
        doc2_path: str,
        comparison_results: Dict[str, Any]
    ) -> None:
        """Log metrics from document comparison."""
        try:
            metrics = {
                "doc1_path": doc1_path,
                "doc2_path": doc2_path,
                "timestamp": datetime.utcnow().isoformat(),
                "similarity_scores": comparison_results.get("similarity", {}),
                "compliance_scores": comparison_results.get("compliance", {}),
                "missing_requirements_count": len(comparison_results.get("missing_requirements", [])),
                "temporal_analysis": {
                    "conflicts": len(comparison_results.get("temporal_analysis", {}).get("conflicts", [])),
                    "recommendations": len(comparison_results.get("temporal_analysis", {}).get("recommendations", []))
                }
            }
            
            logger.info(f"Document comparison metrics: {metrics}")
            
        except Exception as e:
            self._handle_db_error("logging comparison metrics", e)

    async def _calculate_score_confidence(self, scores: Dict[str, float]) -> float:
        """Calculate confidence level in compliance scores."""
        if not scores:
            return 0.0
            
        # Base confidence on number of available scores
        coverage_factor = len(scores) / 5  # 5 is the total possible scores
        
        # Adjust based on score variance
        score_values = list(scores.values())
        variance = np.var(score_values) if len(score_values) > 1 else 0
        variance_factor = 1 - (variance * 2)  # Higher variance reduces confidence
        
        # Combine factors
        confidence = (coverage_factor * 0.7) + (variance_factor * 0.3)
        
        return max(0.0, min(1.0, confidence))  # Ensure between 0 and 1

    async def _extract_key_concepts(self, analysis: Dict[str, Any]) -> List[str]:
        """Extract key concepts from document analysis."""
        concepts = []
        
        # Extract from requirements
        for req in analysis.get("requirements", []):
            concepts.extend(self._extract_noun_phrases(req["text"]))
            
        # Extract from citations
        for citation in analysis.get("citations", []):
            concepts.extend(self._extract_noun_phrases(citation["text"]))
            
        # Extract from text blocks
        for block in analysis.get("text_blocks", []):
            concepts.extend(self._extract_noun_phrases(block))
            
        return list(set(concepts))  # Remove duplicates

    def _extract_noun_phrases(self, text: str) -> List[str]:
        """Extract noun phrases from text using spaCy."""
        doc = self.nlp(text)
        return [chunk.text.lower() for chunk in doc.noun_chunks]

    async def _calculate_concept_similarity(
        self,
        concepts1: List[str],
        concepts2: List[str]
    ) -> float:
        """Calculate similarity between two sets of concepts."""
        if not concepts1 or not concepts2:
            return 0.0
            
        # Get embeddings for both concept sets
        embeddings1 = self.sentence_model.encode(concepts1)
        embeddings2 = self.sentence_model.encode(concepts2)
        
        # Calculate similarity matrix
        similarity_matrix = cosine_similarity(embeddings1, embeddings2)
        
        # Return average of maximum similarities
        return float(np.mean(np.max(similarity_matrix, axis=1)))

    async def _extract_key_findings(self, appeal: FEMAAppeal) -> List[Dict[str, Any]]:
        """Extract key findings from an appeal."""
        findings = []
        
        # Process appeal content
        doc = self.nlp(appeal.content)
        
        # Look for sentences with key indicators
        key_indicators = [
            "therefore",
            "conclude",
            "find",
            "determine",
            "decision"
        ]
        
        for sent in doc.sents:
            sent_text = sent.text.lower()
            if any(indicator in sent_text for indicator in key_indicators):
                findings.append({
                    "text": sent.text,
                    "type": "conclusion" if "therefore" in sent_text else "finding",
                    "confidence": 0.8 if "therefore" in sent_text else 0.6
                })
                
        return findings

    async def _assess_appeal_applicability(
        self,
        appeal: FEMAAppeal,
        doc1_analysis: Dict[str, Any],
        doc2_analysis: Dict[str, Any],
        incident_date: Optional[datetime]
    ) -> Dict[str, Any]:
        """Assess how an appeal applies to the documents being compared."""
        try:
            # Check temporal applicability
            temporal_valid = True
            if incident_date:
                temporal_valid = (
                    appeal.decision_date <= incident_date
                    and (not appeal.expiration_date or appeal.expiration_date >= incident_date)
                )
            
            # Extract requirements from appeal
            appeal_reqs = await self._extract_key_requirements(appeal)
            
            # Compare against document requirements
            doc1_reqs = doc1_analysis.get("requirements", [])
            doc2_reqs = doc2_analysis.get("requirements", [])
            
            # Calculate requirement coverage
            req_coverage = await self._calculate_requirement_coverage(
                appeal_reqs,
                doc1_reqs,
                doc2_reqs
            )
            
            return {
                "temporal_validity": temporal_valid,
                "requirement_coverage": req_coverage,
                "applicability_score": await self._calculate_applicability_score(
                    temporal_valid,
                    req_coverage
                ),
                "missing_requirements": await self._identify_missing_requirements(
                    appeal_reqs,
                    doc1_reqs,
                    doc2_reqs
                )
            }

        except Exception as e:
            self._handle_db_error("assessing appeal applicability", e)

    async def _calculate_requirement_coverage(
        self,
        source_reqs: List[str],
        doc1_reqs: List[Dict[str, Any]],
        doc2_reqs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate requirement coverage between source and documents."""
        try:
            if not source_reqs:
                return {"doc1": 0.0, "doc2": 0.0}
            
            # Get requirement texts
            doc1_texts = [req["text"] for req in doc1_reqs]
            doc2_texts = [req["text"] for req in doc2_reqs]
            
            # Calculate embeddings
            source_embeddings = self.sentence_model.encode(source_reqs)
            doc1_embeddings = self.sentence_model.encode(doc1_texts) if doc1_texts else []
            doc2_embeddings = self.sentence_model.encode(doc2_texts) if doc2_texts else []
            
            # Calculate coverage for each document
            doc1_coverage = await self._calculate_coverage(source_embeddings, doc1_embeddings)
            doc2_coverage = await self._calculate_coverage(source_embeddings, doc2_embeddings)
            
            return {
                "doc1": doc1_coverage,
                "doc2": doc2_coverage,
                "average": (doc1_coverage + doc2_coverage) / 2
            }
            
        except Exception as e:
            self._handle_db_error("calculating requirement coverage", e)

    async def _calculate_coverage(
        self,
        source_embeddings: np.ndarray,
        target_embeddings: np.ndarray
    ) -> float:
        """Calculate coverage of source embeddings by target embeddings."""
        try:
            if len(source_embeddings) == 0 or len(target_embeddings) == 0:
                return 0.0
            
            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(source_embeddings, target_embeddings)
            
            # Get best match for each source requirement
            best_matches = np.max(similarity_matrix, axis=1)
            
            # Calculate coverage (percentage of requirements with good matches)
            coverage = np.mean(best_matches >= 0.8)  # Threshold for good match
            
            return float(coverage)
            
        except Exception as e:
            self._handle_db_error("calculating coverage", e)

    async def _identify_missing_requirements(
        self,
        policy_reqs: List[str],
        doc1_reqs: List[Dict[str, Any]],
        doc2_reqs: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Identify missing requirements in documents compared to policy."""
        try:
            # Extract requirement texts
            doc1_texts = [req["text"] for req in doc1_reqs]
            doc2_texts = [req["text"] for req in doc2_reqs]
            
            # Get embeddings
            policy_embeddings = self.sentence_model.encode(policy_reqs) if policy_reqs else []
            doc1_embeddings = self.sentence_model.encode(doc1_texts) if doc1_texts else []
            doc2_embeddings = self.sentence_model.encode(doc2_texts) if doc2_texts else []
            
            # Find missing requirements
            missing_reqs = []
            for i, policy_req in enumerate(policy_reqs):
                # Check against doc1
                doc1_scores = cosine_similarity([policy_embeddings[i]], doc1_embeddings)[0]
                doc1_match = max(doc1_scores) if len(doc1_scores) > 0 else 0
                
                # Check against doc2
                doc2_scores = cosine_similarity([policy_embeddings[i]], doc2_embeddings)[0]
                doc2_match = max(doc2_scores) if len(doc2_scores) > 0 else 0
                
                # If requirement is not well-matched in either document
                if doc1_match < 0.7 and doc2_match < 0.7:
                    missing_reqs.append({
                        "requirement": policy_req,
                        "best_match_doc1": doc1_match,
                        "best_match_doc2": doc2_match,
                        "severity": "high" if max(doc1_match, doc2_match) < 0.5 else "medium"
                    })
                    
            return missing_reqs
            
        except Exception as e:
            self._handle_db_error("identifying missing requirements", e)

    async def _calculate_applicability_score(
        self,
        temporal_valid: bool,
        req_coverage: Dict[str, float]
    ) -> float:
        """Calculate policy applicability score."""
        try:
            # Base score on requirement coverage
            coverage_score = (req_coverage["doc1"] + req_coverage["doc2"]) / 2
            
            # Adjust for temporal validity
            if not temporal_valid:
                coverage_score *= 0.5  # Reduce score if not temporally valid
            
            return max(0.0, min(1.0, coverage_score))  # Ensure between 0 and 1
            
        except Exception as e:
            self._handle_db_error("calculating applicability score", e)

    async def _identify_compliance_factors(
        self,
        scores: Dict[str, float]
    ) -> List[Dict[str, Any]]:
        """Identify factors affecting compliance scores."""
        try:
            factors = []
            
            # Define thresholds
            thresholds = {
                "high": 0.8,
                "medium": 0.6,
                "low": 0.4
            }
            
            # Analyze each score component
            for component, score in scores.items():
                if score is None:
                    continue
                    
                # Determine impact level
                impact = "high"
                if score >= thresholds["high"]:
                    impact = "positive"
                elif score >= thresholds["medium"]:
                    impact = "neutral"
                elif score >= thresholds["low"]:
                    impact = "negative"
                else:
                    impact = "critical"
                
                factors.append({
                    "component": component,
                    "score": score,
                    "impact": impact,
                    "description": await self._get_factor_description(component, score, impact)
                })
            
            return sorted(
                factors,
                key=lambda x: {"critical": 0, "negative": 1, "neutral": 2, "positive": 3}[x["impact"]]
            )
            
        except Exception as e:
            self._handle_db_error("identifying compliance factors", e)

    async def _get_factor_description(
        self,
        component: str,
        score: float,
        impact: str
    ) -> str:
        """Get description for a compliance factor."""
        try:
            descriptions = {
                "requirement_compliance": {
                    "positive": "Strong alignment with requirements",
                    "neutral": "Moderate alignment with requirements",
                    "negative": "Weak alignment with requirements",
                    "critical": "Critical requirement gaps"
                },
                "temporal_compliance": {
                    "positive": "Valid temporal alignment",
                    "neutral": "Minor temporal discrepancies",
                    "negative": "Significant temporal issues",
                    "critical": "Critical temporal conflicts"
                },
                "structural_compliance": {
                    "positive": "Well-structured content",
                    "neutral": "Acceptable structure",
                    "negative": "Poor structure",
                    "critical": "Critical structural issues"
                },
                "policy_compliance": {
                    "positive": "Strong policy alignment",
                    "neutral": "Moderate policy alignment",
                    "negative": "Weak policy alignment",
                    "critical": "Critical policy gaps"
                },
                "appeals_impact": {
                    "positive": "Favorable appeals precedent",
                    "neutral": "Mixed appeals impact",
                    "negative": "Unfavorable appeals precedent",
                    "critical": "Critical appeals conflicts"
                }
            }
            
            return descriptions.get(component, {}).get(impact, f"Unknown {impact} impact")
            
        except Exception as e:
            self._handle_db_error("getting factor description", e)
