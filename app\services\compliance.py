from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.review import ComplianceReview, ReviewStatus
from app.models.policy import PolicyVersion
from app.models.disaster import DisasterDeclaration

class ComplianceEngine:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def validate_project_compliance(
        self,
        project_id: int,
        dr_number: str
    ) -> ComplianceReview:
        """Validate project compliance against applicable policies."""
        # Get disaster declaration
        stmt = select(DisasterDeclaration).where(DisasterDeclaration.dr_number == dr_number)
        result = await self.db.execute(stmt)
        disaster = result.scalar_one_or_none()
        
        if not disaster:
            raise ValueError(f"Disaster declaration {dr_number} not found")
        
        # Get applicable policies
        applicable_policies = disaster.applicable_policies
        
        # Create compliance review
        review = ComplianceReview(
            project_id=project_id,
            status=ReviewStatus.IN_PROGRESS,
            findings={},
            violations=[],
            remediation_plan={},
            compliance_score=0.0
        )
        
        total_score = 0
        total_weight = 0
        violations = []
        
        for policy in applicable_policies:
            policy_score, policy_violations = await self._check_policy_compliance(
                project_id,
                policy
            )
            weight = self._calculate_policy_weight(policy)
            total_score += policy_score * weight
            total_weight += weight
            violations.extend(policy_violations)
        
        if total_weight > 0:
            review.compliance_score = total_score / total_weight
        
        review.violations = violations
        review.remediation_plan = await self._generate_remediation_plan(violations)
        review.status = ReviewStatus.COMPLETED
        
        self.db.add(review)
        await self.db.commit()
        await self.db.refresh(review)
        
        return review

    async def check_policy_requirements(self, dr_number: str) -> Dict:
        """Check and summarize policy requirements for a disaster."""
        stmt = select(DisasterDeclaration).where(DisasterDeclaration.dr_number == dr_number)
        result = await self.db.execute(stmt)
        disaster = result.scalar_one_or_none()
        
        if not disaster:
            raise ValueError(f"Disaster declaration {dr_number} not found")
        
        requirements = {
            "mandatory": [],
            "recommended": [],
            "optional": []
        }
        
        for policy in disaster.applicable_policies:
            policy_reqs = self._extract_policy_requirements(policy)
            requirements["mandatory"].extend(policy_reqs.get("mandatory", []))
            requirements["recommended"].extend(policy_reqs.get("recommended", []))
            requirements["optional"].extend(policy_reqs.get("optional", []))
        
        return requirements

    async def generate_compliance_report(
        self,
        project_id: int,
        dr_number: str
    ) -> Dict:
        """Generate a detailed compliance report."""
        stmt = select(ComplianceReview).where(
            ComplianceReview.project_id == project_id
        ).order_by(ComplianceReview.created_at.desc())
        result = await self.db.execute(stmt)
        latest_review = result.scalar_one_or_none()
        
        if not latest_review:
            latest_review = await self.validate_project_compliance(project_id, dr_number)
        
        report = {
            "project_id": project_id,
            "dr_number": dr_number,
            "compliance_score": latest_review.compliance_score,
            "review_date": latest_review.created_at,
            "status": latest_review.status,
            "violations": latest_review.violations,
            "remediation_plan": latest_review.remediation_plan,
            "summary": self._generate_report_summary(latest_review)
        }
        
        return report

    async def track_resolution_status(
        self,
        project_id: int,
        violation_ids: List[str]
    ) -> Dict:
        """Track the resolution status of compliance violations."""
        stmt = select(ComplianceReview).where(
            ComplianceReview.project_id == project_id
        ).order_by(ComplianceReview.created_at.desc())
        result = await self.db.execute(stmt)
        review = result.scalar_one_or_none()
        
        if not review:
            raise ValueError(f"No compliance review found for project {project_id}")
        
        resolution_status = {
            "resolved": [],
            "pending": [],
            "in_progress": []
        }
        
        for violation in review.violations:
            if violation["id"] in violation_ids:
                if violation.get("resolved"):
                    resolution_status["resolved"].append(violation)
                elif violation.get("in_progress"):
                    resolution_status["in_progress"].append(violation)
                else:
                    resolution_status["pending"].append(violation)
        
        return resolution_status

    async def _check_policy_compliance(
        self,
        project_id: int,
        policy: PolicyVersion
    ) -> tuple[float, List[Dict]]:
        """Check compliance with a specific policy version."""
        # Placeholder for actual policy compliance checking logic
        # This would involve parsing policy requirements and checking project data
        score = 1.0  # Perfect compliance
        violations = []
        
        # Simulate finding violations (replace with actual logic)
        if project_id % 2 == 0:  # Just for demonstration
            score = 0.8
            violations.append({
                "id": f"V{project_id}-{policy.id}",
                "policy_number": policy.policy.policy_number,
                "description": "Sample violation",
                "severity": "medium",
                "resolved": False
            })
        
        return score, violations

    def _calculate_policy_weight(self, policy: PolicyVersion) -> float:
        """Calculate the weight of a policy for compliance scoring."""
        # Placeholder for policy weight calculation logic
        # This could be based on policy type, importance, etc.
        return 1.0

    async def _generate_remediation_plan(self, violations: List[Dict]) -> Dict:
        """Generate a plan to address compliance violations."""
        plan = {
            "steps": [],
            "timeline": [],
            "resources": []
        }
        
        for violation in violations:
            plan["steps"].append({
                "violation_id": violation["id"],
                "action": f"Address {violation['description']}",
                "priority": violation["severity"],
                "status": "pending"
            })
        
        return plan

    def _extract_policy_requirements(self, policy: PolicyVersion) -> Dict:
        """Extract requirements from policy content."""
        # Placeholder for requirement extraction logic
        # This would involve NLP or pattern matching to identify requirements
        return {
            "mandatory": [
                {"id": "M1", "description": "Sample mandatory requirement"}
            ],
            "recommended": [
                {"id": "R1", "description": "Sample recommended practice"}
            ],
            "optional": [
                {"id": "O1", "description": "Sample optional measure"}
            ]
        }

    def _generate_report_summary(self, review: ComplianceReview) -> Dict:
        """Generate a summary of the compliance review."""
        return {
            "total_violations": len(review.violations),
            "compliance_level": self._get_compliance_level(review.compliance_score),
            "key_findings": self._summarize_findings(review.findings),
            "priority_actions": self._get_priority_actions(review.violations)
        }

    def _get_compliance_level(self, score: float) -> str:
        """Convert compliance score to a descriptive level."""
        if score >= 0.9:
            return "High"
        elif score >= 0.7:
            return "Moderate"
        else:
            return "Low"

    def _summarize_findings(self, findings: Dict) -> List[str]:
        """Summarize key findings from the review."""
        # Placeholder for findings summarization logic
        return ["Sample finding 1", "Sample finding 2"]

    def _get_priority_actions(self, violations: List[Dict]) -> List[str]:
        """Identify priority actions from violations."""
        priority_actions = []
        for violation in violations:
            if violation["severity"] in ["high", "critical"]:
                priority_actions.append(
                    f"Address {violation['description']} (Violation {violation['id']})"
                )
        return priority_actions
