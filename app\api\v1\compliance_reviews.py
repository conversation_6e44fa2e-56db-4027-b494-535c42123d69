from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.core.database import get_db
from app.models.compliance import ComplianceReview
from app.schemas.compliance import ComplianceReviewCreate, ComplianceReviewResponse
from app.core.security import get_current_active_user
from app.models.user import User
from typing import List

router = APIRouter(prefix="/api/v1/compliance-reviews", tags=["compliance-reviews"])

@router.post("/", response_model=ComplianceReviewResponse, status_code=status.HTTP_201_CREATED)
async def create_compliance_review(review: ComplianceReviewCreate, db: AsyncSession = Depends(get_db), current_user: User = Depends(get_current_active_user)):
    db_review = ComplianceReview(**review.dict(), reviewer_id=current_user.id)
    db.add(db_review)
    await db.commit()
    await db.refresh(db_review)
    return db_review

@router.get("/", response_model=List[ComplianceReviewResponse])
async def get_compliance_reviews(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    skip: int = 0,
    limit: int = 10
):
    query = select(ComplianceReview).where(ComplianceReview.reviewer_id == current_user.id).offset(skip).limit(limit)
    result = await db.execute(query)
    reviews = result.scalars().all()
    return reviews

@router.get("/{review_id}", response_model=ComplianceReviewResponse)
async def get_compliance_review(review_id: int, db: AsyncSession = Depends(get_db), current_user: User = Depends(get_current_active_user)):
    query = select(ComplianceReview).where(ComplianceReview.id == review_id, ComplianceReview.reviewer_id == current_user.id)
    result = await db.execute(query)
    review = result.scalars().first()
    if not review:
        raise HTTPException(status_code=404, detail="Compliance review not found")
    return review