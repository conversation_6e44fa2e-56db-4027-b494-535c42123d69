"""
Command-line tool for interacting with the document version control system.
"""

import argparse
import logging
from pathlib import Path
from typing import Optional

from app.documents.version_control import DocumentVersionControl

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_version(args):
    """Create a new version of a document."""
    version_control = DocumentVersionControl()
    source_path = Path(args.source_path)
    
    if not source_path.exists():
        logger.error(f"Source file not found: {source_path}")
        return
    
    version = version_control.create_version(
        document_id=args.document_id,
        source_path=source_path,
        author=args.author,
        changes=args.changes.split(',') if args.changes else [],
        tags=args.tags.split(',') if args.tags else []
    )
    logger.info(f"Created version {version.version_id}")

def list_versions(args):
    """List all versions of a document."""
    version_control = DocumentVersionControl()
    versions = version_control.list_versions(args.document_id)
    
    if not versions:
        logger.info("No versions found")
        return
    
    logger.info(f"Found {len(versions)} versions:")
    for version in versions:
        logger.info(f"Version {version.version_id}:")
        logger.info(f"  Created at: {version.timestamp}")
        logger.info(f"  Author: {version.author}")
        logger.info(f"  Changes: {version.changes}")
        logger.info(f"  Tags: {version.tags}")

def compare_versions(args):
    """Compare two versions of a document."""
    version_control = DocumentVersionControl()
    comparison = version_control.compare_versions(
        args.document_id,
        args.version1,
        args.version2
    )
    
    logger.info("Version comparison:")
    logger.info(f"  Size difference: {comparison['differences']['size']} bytes")
    logger.info(f"  Changes: {comparison['differences']['changes']}")
    logger.info(f"  Checksum different: {comparison['differences']['checksum_different']}")

def restore_version(args):
    """Restore a specific version of a document."""
    version_control = DocumentVersionControl()
    target_path = Path(args.target_path)
    
    if version_control.restore_version(args.document_id, args.version_id, target_path):
        logger.info(f"Restored version {args.version_id} to {target_path}")
        logger.info(f"Content: {target_path.read_text()}")
    else:
        logger.error(f"Failed to restore version {args.version_id}")

def main():
    parser = argparse.ArgumentParser(description="Document Version Control Tool")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Create version command
    create_parser = subparsers.add_parser("create", help="Create a new version")
    create_parser.add_argument("source_path", help="Path to the source document")
    create_parser.add_argument("--document-id", required=True, help="Document ID")
    create_parser.add_argument("--author", default="user", help="Author name")
    create_parser.add_argument("--changes", help="Comma-separated list of changes")
    create_parser.add_argument("--tags", help="Comma-separated list of tags")
    create_parser.set_defaults(func=create_version)

    # List versions command
    list_parser = subparsers.add_parser("list", help="List document versions")
    list_parser.add_argument("document_id", help="Document ID")
    list_parser.set_defaults(func=list_versions)

    # Compare versions command
    compare_parser = subparsers.add_parser("compare", help="Compare two versions")
    compare_parser.add_argument("document_id", help="Document ID")
    compare_parser.add_argument("version1", help="First version ID")
    compare_parser.add_argument("version2", help="Second version ID")
    compare_parser.set_defaults(func=compare_versions)

    # Restore version command
    restore_parser = subparsers.add_parser("restore", help="Restore a version")
    restore_parser.add_argument("document_id", help="Document ID")
    restore_parser.add_argument("version_id", help="Version ID to restore")
    restore_parser.add_argument("target_path", help="Path to restore the version to")
    restore_parser.set_defaults(func=restore_version)

    args = parser.parse_args()
    if hasattr(args, 'func'):
        args.func(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 