"""
API Key Management System for ComplianceMax.
"""
from datetime import datetime, timedelta
import secrets
from typing import Optional, Dict, List
import hashlib
from fastapi import Request, HTTPException, Depends
from fastapi.security import API<PERSON>eyHeader
from pydantic import BaseModel
import redis
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

# API key header specification
API_KEY_HEADER = APIKeyHeader(name="X-API-Key")

class APIKey(BaseModel):
    """API Key model."""
    key_id: str
    key_hash: str
    owner_id: str
    created_at: datetime
    expires_at: datetime
    scopes: List[str]
    is_active: bool
    rotation_due: datetime

class APIKeyManager:
    """API Key management system."""
    
    def __init__(self):
        """Initialize API Key manager."""
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_API_KEY_DB,
            decode_responses=True
        )
        self.key_prefix = "api_key:"
        self.rotation_window = timedelta(days=30)  # Rotate keys every 30 days
    
    def generate_key(self, owner_id: str, scopes: List[str]) -> Dict[str, str]:
        """Generate new API key."""
        key_id = secrets.token_urlsafe(8)
        api_key = secrets.token_urlsafe(32)
        key_hash = self._hash_key(api_key)
        
        key_data = APIKey(
            key_id=key_id,
            key_hash=key_hash,
            owner_id=owner_id,
            created_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(days=90),
            scopes=scopes,
            is_active=True,
            rotation_due=datetime.utcnow() + self.rotation_window
        )
        
        # Store in Redis
        self.redis_client.hset(
            f"{self.key_prefix}{key_id}",
            mapping=key_data.dict()
        )
        
        return {
            "key_id": key_id,
            "api_key": api_key,
            "expires_at": key_data.expires_at.isoformat()
        }
    
    def validate_key(self, api_key: str) -> Optional[APIKey]:
        """Validate API key and return key data if valid."""
        try:
            key_hash = self._hash_key(api_key)
            key_id = api_key[:11]  # Extract key ID from API key
            
            # Get key data from Redis
            key_data = self.redis_client.hgetall(f"{self.key_prefix}{key_id}")
            if not key_data:
                return None
            
            key = APIKey(**key_data)
            
            # Validate key
            if not key.is_active:
                logger.warning(f"Attempt to use inactive API key: {key_id}")
                return None
            
            if datetime.utcnow() > key.expires_at:
                logger.warning(f"Attempt to use expired API key: {key_id}")
                return None
            
            if key.key_hash != key_hash:
                logger.warning(f"Invalid API key hash: {key_id}")
                return None
            
            # Check if rotation is due
            if datetime.utcnow() > key.rotation_due:
                logger.info(f"API key rotation due: {key_id}")
                self._mark_for_rotation(key)
            
            return key
            
        except Exception as e:
            logger.error(f"API key validation error: {str(e)}", exc_info=True)
            return None
    
    def rotate_key(self, old_key_id: str) -> Optional[Dict[str, str]]:
        """Rotate an existing API key."""
        try:
            key_data = self.redis_client.hgetall(f"{self.key_prefix}{old_key_id}")
            if not key_data:
                return None
            
            old_key = APIKey(**key_data)
            
            # Generate new key
            new_key = self.generate_key(old_key.owner_id, old_key.scopes)
            
            # Deactivate old key after grace period
            self.redis_client.hset(
                f"{self.key_prefix}{old_key_id}",
                "is_active",
                "false"
            )
            
            return new_key
            
        except Exception as e:
            logger.error(f"API key rotation error: {str(e)}", exc_info=True)
            return None
    
    def revoke_key(self, key_id: str) -> bool:
        """Revoke an API key."""
        try:
            self.redis_client.hset(
                f"{self.key_prefix}{key_id}",
                "is_active",
                "false"
            )
            logger.info(f"API key revoked: {key_id}")
            return True
        except Exception as e:
            logger.error(f"API key revocation error: {str(e)}", exc_info=True)
            return False
    
    def _hash_key(self, api_key: str) -> str:
        """Create secure hash of API key."""
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    def _mark_for_rotation(self, key: APIKey) -> None:
        """Mark an API key for rotation."""
        self.redis_client.hset(
            f"{self.key_prefix}{key.key_id}",
            "needs_rotation",
            "true"
        )

async def get_api_key(
    request: Request,
    api_key: str = Depends(API_KEY_HEADER)
) -> APIKey:
    """Dependency for validating API keys."""
    key_manager = APIKeyManager()
    key = key_manager.validate_key(api_key)
    
    if not key:
        raise HTTPException(
            status_code=401,
            detail="Invalid or expired API key"
        )
    
    return key 