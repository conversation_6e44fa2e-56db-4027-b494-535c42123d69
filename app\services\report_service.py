from sqlalchemy.orm import Session
from app.models.report import Report, ReportTemplate, ReportType
from app.schemas.report import ReportCreate, TemplateCreate
from datetime import datetime
import json
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import pandas as pd
import io

class ReportService:
    def __init__(self, db: Session):
        self.db = db

    def create_template(self, template: TemplateCreate) -> ReportTemplate:
        db_template = ReportTemplate(
            name=template.name,
            description=template.description,
            type=template.type,
            template_data=template.template_data,
            is_system=template.is_system
        )
        self.db.add(db_template)
        self.db.commit()
        self.db.refresh(db_template)
        return db_template

    def get_templates(self) -> list[ReportTemplate]:
        return self.db.query(ReportTemplate).all()

    def generate_report(self, report: ReportCreate, user_id: int) -> Report:
        # Create report record
        db_report = Report(
            title=report.title,
            type=report.type,
            parameters=report.parameters,
            created_at=datetime.utcnow(),
            created_by=user_id,
            format=report.format
        )
        
        # Generate report data based on type
        if report.type == ReportType.COMPLIANCE:
            data = self._generate_compliance_report(report.parameters)
        elif report.type == ReportType.COST:
            data = self._generate_cost_report(report.parameters)
        elif report.type == ReportType.RISK:
            data = self._generate_risk_report(report.parameters)
        else:
            data = self._generate_custom_report(report.parameters)
        
        db_report.data = data
        
        # Generate formatted report
        if report.format == "PDF":
            report_content = self._generate_pdf_report(data)
        elif report.format == "EXCEL":
            report_content = self._generate_excel_report(data)
        else:
            report_content = json.dumps(data)
        
        self.db.add(db_report)
        self.db.commit()
        self.db.refresh(db_report)
        return db_report

    def _generate_compliance_report(self, parameters: dict) -> dict:
        # Query compliance data based on parameters
        # This is a placeholder implementation
        return {
            "compliance_status": "Compliant",
            "total_requirements": 10,
            "compliant_requirements": 8,
            "non_compliant_requirements": 2,
            "details": []
        }

    def _generate_cost_report(self, parameters: dict) -> dict:
        # Query cost data based on parameters
        # This is a placeholder implementation
        return {
            "total_costs": 100000,
            "cost_breakdown": {
                "mitigation": 50000,
                "implementation": 30000,
                "maintenance": 20000
            },
            "details": []
        }

    def _generate_risk_report(self, parameters: dict) -> dict:
        # Query risk data based on parameters
        # This is a placeholder implementation
        return {
            "risk_level": "Medium",
            "risk_factors": [
                {"name": "Flood Risk", "level": "High"},
                {"name": "Compliance Risk", "level": "Low"}
            ],
            "details": []
        }

    def _generate_custom_report(self, parameters: dict) -> dict:
        # Generate custom report based on parameters
        return parameters

    def _generate_pdf_report(self, data: dict) -> bytes:
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=letter)
        
        # Add report content
        y = 750
        for key, value in data.items():
            c.drawString(100, y, f"{key}: {value}")
            y -= 20
        
        c.save()
        return buffer.getvalue()

    def _generate_excel_report(self, data: dict) -> bytes:
        buffer = io.BytesIO()
        df = pd.DataFrame.from_dict(data)
        df.to_excel(buffer, index=False)
        return buffer.getvalue()
