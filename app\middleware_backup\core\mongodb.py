import logging
import motor.motor_asyncio
from beanie import init_beanie, Document, PydanticObjectId
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Optional
from datetime import datetime
from app.core.config import settings

logger = logging.getLogger(__name__)

# MongoDB document models
class ComplianceDocument(Document):
    """Document model for storing compliance-related files."""
    id: Optional[PydanticObjectId] = None
    title: str
    document_type: str
    file_path: str
    upload_date: datetime = Field(default_factory=datetime.utcnow)
    last_modified: datetime = Field(default_factory=datetime.utcnow)
    status: str
    metadata: Dict = Field(default_factory=dict)
    associated_applications: List[str] = Field(default_factory=list)

    class Settings:
        name = "compliance_documents"
        indexes = [
            "title",
            "document_type",
            "status",
            "upload_date"
        ]

class FEMAApplication(Document):
    """FEMA application model for tracking disaster assistance applications."""
    id: Optional[PydanticObjectId] = None
    disaster_number: str
    applicant_id: str
    status: str
    submission_date: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    documents: List[str] = Field(default_factory=list)
    compliance_status: str
    notes: Optional[str] = None

    class Settings:
        name = "fema_applications"
        indexes = [
            "disaster_number",
            "applicant_id",
            "status",
            "compliance_status"
        ]

class PolicyRequirement(Document):
    """Policy requirement model for tracking compliance requirements."""
    id: Optional[PydanticObjectId] = None
    policy_id: str
    title: str
    description: str
    requirements: List[str] = Field(default_factory=list)
    effective_date: datetime
    expiration_date: Optional[datetime] = None
    status: str
    source: str
    last_updated: datetime = Field(default_factory=datetime.utcnow)

    class Settings:
        name = "policy_requirements"
        indexes = [
            "policy_id",
            "status",
            "effective_date",
            "expiration_date"
        ]

# Import the PolicyVersion from policy_validator to avoid duplication
from app.services.policy_validator import PolicyVersion

async def init_mongodb():
    """Initialize MongoDB connection with Beanie ODM."""
    try:
        logger.info(f"Connecting to MongoDB at {settings.MONGO_URI}")
        
        # Create Motor client
        client = motor.motor_asyncio.AsyncIOMotorClient(
            settings.MONGO_URI,
            serverSelectionTimeoutMS=5000,  # 5 second timeout for server selection
            connectTimeoutMS=10000,         # 10 second timeout for connection
            socketTimeoutMS=30000,          # 30 second timeout for socket operations
            maxPoolSize=10,                 # Connection pool size
            retryWrites=True                # Enable retry for write operations
        )
        
        # Initialize Beanie with the MongoDB client
        await init_beanie(
            database=client.get_default_database(),
            document_models=[
                FEMAApplication,
                ComplianceDocument,
                PolicyRequirement,
                PolicyVersion
            ]
        )
        
        logger.info("MongoDB connection established successfully")
        
        # Test the connection
        await client.admin.command('ping')
        logger.info("MongoDB server ping successful")
        
        return client
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {str(e)}")
        # Raise for startup failure
        raise
