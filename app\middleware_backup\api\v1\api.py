from fastapi import APIRouter
from app.api.v1 import (
    auth, documents, compliance, flood_maps, fema,
    cbcs, costs, reports, subscriptions, projects
)

api_router = APIRouter()

# Include routers
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(documents.router, prefix="/documents", tags=["documents"])
api_router.include_router(compliance.router, prefix="/compliance", tags=["compliance"])
api_router.include_router(flood_maps.router, prefix="/flood-maps", tags=["flood-maps"])
api_router.include_router(fema.router, prefix="/fema", tags=["fema"])
api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(cbcs.router, prefix="/cbcs", tags=["cbcs"])
api_router.include_router(costs.router, prefix="/costs", tags=["costs"])
api_router.include_router(reports.router, prefix="/reports", tags=["reports"])
api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["subscriptions"])
