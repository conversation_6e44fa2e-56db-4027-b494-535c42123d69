import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Alert, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { getRemediationPlans } from '../services/remediation';

const RemediationPlan: React.FC = () => {
    const [plans, setPlans] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchPlans = async () => {
            try {
                const response = await getRemediationPlans();
                setPlans(response);
            } catch (err: any) {
                setError(err.response?.data?.detail || 'Failed to load remediation plans');
            } finally {
                setLoading(false);
            }
        };
        fetchPlans();
    }, []);

    if (loading) return <CircularProgress aria-label="Loading remediation plans" />;
    if (error) return <Alert severity="error" role="alert">{error}</Alert>;

    return (
        <Box sx={{ p: 3 }} role="main">
            <Typography variant="h4" gutterBottom>
                Remediation Plans
            </Typography>
            {plans.length === 0 ? (
                <Typography>No remediation plans found.</Typography>
            ) : (
                <Table aria-label="Remediation plans table">
                    <TableHead>
                        <TableRow>
                            <TableCell>Plan ID</TableCell>
                            <TableCell>Creation Date</TableCell>
                            <TableCell>Status</TableCell>
                            <TableCell>Priority</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {plans.map((plan) => (
                            <TableRow key={plan.id}>
                                <TableCell>{plan.id}</TableCell>
                                <TableCell>{plan.creation_date}</TableCell>
                                <TableCell>{plan.status}</TableCell>
                                <TableCell>{plan.priority}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            )}
        </Box>
    );
};

export default RemediationPlan;