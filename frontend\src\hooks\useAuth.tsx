import { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import AuthService, { User, LoginCredentials } from '../services/auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadUser = useMemo(() => async () => {
    try {
      const token = AuthService.getToken();
      console.log('[Auth] Initial token:', token);
      if (token) {
        const userData = await AuthService.getCurrentUser();
        console.log('[Auth] Loaded user:', userData);
        setUser(userData);
      }
    } catch (err) {
      console.error('[Auth] Failed to load user:', err);
      AuthService.logout();
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadUser();
  }, [loadUser]);

  const login = async (credentials: LoginCredentials) => {
    try {
      setError(null);
      await AuthService.login(credentials);
      const token = AuthService.getToken();
      console.log('[Auth] Post-login token:', token);
      const userData = await AuthService.getCurrentUser();
      console.log('[Auth] Post-login user:', userData);
      setUser(userData);
    } catch (err) {
      console.error('[Auth] Login error:', err);
      setError('Invalid credentials');
      throw err;
    }
  };

  const logout = () => {
    AuthService.logout();
    setUser(null);
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      setError(null);
      const updatedUser = await AuthService.changePassword(currentPassword, newPassword);
      console.log('[Auth] Password changed successfully');
      setUser(updatedUser);
    } catch (err) {
      console.error('[Auth] Password change error:', err);
      setError('Failed to change password');
      throw err;
    }
  };

  console.log('[Auth] 🎉 HMR TRIUMPH - ComplianceMax Auth System Complete! 🎉');

  const value = useMemo(() => ({
    user,
    loading,
    error,
    login,
    logout,
    changePassword
  }), [user, loading, error]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
