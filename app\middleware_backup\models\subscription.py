from datetime import datetime
from sqlalchemy import String, Float, DateTime, Foreign<PERSON>ey, <PERSON><PERSON>an, Enum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base
import enum

class SubscriptionTier(str, enum.Enum):
    BASIC = "basic"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"

class Subscription(Base):
    __tablename__ = "subscriptions"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    tier: Mapped[SubscriptionTier] = mapped_column(Enum(SubscriptionTier))
    start_date: Mapped[datetime] = mapped_column(DateTime)
    end_date: Mapped[datetime] = mapped_column(DateTime)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    auto_renew: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # Relationships
    user = relationship("User", back_populates="subscriptions")
    billing_records = relationship("Billing", back_populates="subscription")
    
class SubscriptionFeature(Base):
    __tablename__ = "subscription_features"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    tier: Mapped[SubscriptionTier] = mapped_column(Enum(SubscriptionTier))
    feature_name: Mapped[str] = mapped_column(String)
    feature_description: Mapped[str] = mapped_column(String)
    is_enabled: Mapped[bool] = mapped_column(Boolean, default=True)
    
class Billing(Base):
    __tablename__ = "billing"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    subscription_id: Mapped[int] = mapped_column(ForeignKey("subscriptions.id"))
    amount: Mapped[float] = mapped_column(Float)
    billing_date: Mapped[datetime] = mapped_column(DateTime)
    payment_status: Mapped[str] = mapped_column(String)
    payment_method: Mapped[str] = mapped_column(String)
    invoice_number: Mapped[str] = mapped_column(String)
    
    # Relationships
    subscription = relationship("Subscription", back_populates="billing_records")
