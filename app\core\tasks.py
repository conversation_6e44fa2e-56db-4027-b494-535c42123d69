"""
Background task management for ComplianceMax.
"""

import asyncio
from typing import Any, Callable, Dict, List, Optional
from datetime import datetime
import uuid
from fastapi import BackgroundTasks
from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import MetricsCollector

logger = get_logger(__name__)

class TaskStatus:
    """Task status constants."""
    
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskManager:
    """Background task manager."""
    
    def __init__(self):
        """Initialize task manager."""
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.max_retries = 3
        self.retry_delay = 5  # seconds
    
    async def create_task(
        self,
        func: Callable,
        *args,
        task_id: Optional[str] = None,
        description: str = "",
        **kwargs
    ) -> str:
        """Create and register a new task."""
        task_id = task_id or str(uuid.uuid4())
        
        self.tasks[task_id] = {
            "id": task_id,
            "function": func,
            "args": args,
            "kwargs": kwargs,
            "status": TaskStatus.PENDING,
            "description": description,
            "created_at": datetime.utcnow(),
            "started_at": None,
            "completed_at": None,
            "error": None,
            "result": None,
            "retries": 0
        }
        
        return task_id
    
    async def execute_task(self, task_id: str) -> None:
        """Execute a registered task."""
        if task_id not in self.tasks:
            logger.error(f"Task {task_id} not found")
            return
        
        task = self.tasks[task_id]
        task["status"] = TaskStatus.RUNNING
        task["started_at"] = datetime.utcnow()
        
        start_time = time.time()
        success = True
        
        try:
            # Execute task
            result = await task["function"](*task["args"], **task["kwargs"])
            
            # Update task status
            task["status"] = TaskStatus.COMPLETED
            task["result"] = result
            
        except Exception as e:
            success = False
            logger.error(f"Task {task_id} failed: {str(e)}", exc_info=True)
            
            # Handle retries
            if task["retries"] < self.max_retries:
                task["retries"] += 1
                task["status"] = TaskStatus.PENDING
                # Schedule retry
                await asyncio.sleep(self.retry_delay)
                await self.execute_task(task_id)
            else:
                task["status"] = TaskStatus.FAILED
                task["error"] = str(e)
        
        finally:
            # Update task completion time
            task["completed_at"] = datetime.utcnow()
            
            # Track metrics
            duration = time.time() - start_time
            MetricsCollector.track_performance(
                operation="background_task",
                duration=duration,
                success=success
            )
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status and details."""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        return {
            "id": task["id"],
            "status": task["status"],
            "description": task["description"],
            "created_at": task["created_at"],
            "started_at": task["started_at"],
            "completed_at": task["completed_at"],
            "error": task["error"],
            "retries": task["retries"]
        }
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending task."""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        if task["status"] == TaskStatus.PENDING:
            task["status"] = TaskStatus.CANCELLED
            task["completed_at"] = datetime.utcnow()
            return True
        return False
    
    def cleanup_tasks(self, max_age_hours: int = 24) -> None:
        """Clean up old completed tasks."""
        now = datetime.utcnow()
        to_remove = []
        
        for task_id, task in self.tasks.items():
            if task["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                age = (now - task["completed_at"]).total_seconds() / 3600
                if age > max_age_hours:
                    to_remove.append(task_id)
        
        for task_id in to_remove:
            del self.tasks[task_id]

# Create global task manager instance
task_manager = TaskManager()

async def add_background_task(
    background_tasks: BackgroundTasks,
    func: Callable,
    *args,
    description: str = "",
    **kwargs
) -> str:
    """Add a task to FastAPI background tasks."""
    # Create task
    task_id = await task_manager.create_task(
        func,
        *args,
        description=description,
        **kwargs
    )
    
    # Add to background tasks
    background_tasks.add_task(
        task_manager.execute_task,
        task_id
    )
    
    return task_id 