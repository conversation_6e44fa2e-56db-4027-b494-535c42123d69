from sqlalchemy.orm import Session
from app.models.cost import Cost, Budget, CostBenefit, CostType
from app.schemas.cost import CostCreate, BudgetCreate, CostBenefitCreate
from datetime import datetime
from sqlalchemy import func

class CostService:
    def __init__(self, db: Session):
        self.db = db

    def create_cost(self, cost: CostCreate) -> Cost:
        db_cost = Cost(
            project_id=cost.project_id,
            type=cost.type,
            amount=cost.amount,
            description=cost.description,
            date_incurred=cost.date_incurred,
            category=cost.category,
            vendor=cost.vendor,
            invoice_number=cost.invoice_number
        )
        self.db.add(db_cost)
        self.db.commit()
        self.db.refresh(db_cost)
        return db_cost

    def get_project_costs(self, project_id: int) -> list[Cost]:
        return self.db.query(Cost).filter(Cost.project_id == project_id).all()

    def get_project_budget(self, project_id: int) -> Budget:
        return self.db.query(Budget).filter(Budget.project_id == project_id).first()

    def create_budget(self, budget: BudgetCreate) -> Budget:
        db_budget = Budget(
            project_id=budget.project_id,
            fiscal_year=budget.fiscal_year,
            total_amount=budget.total_amount,
            allocated_amount=budget.allocated_amount,
            remaining_amount=budget.total_amount - budget.allocated_amount,
            last_updated=datetime.utcnow()
        )
        self.db.add(db_budget)
        self.db.commit()
        self.db.refresh(db_budget)
        return db_budget

    def create_cost_benefit_analysis(self, analysis: CostBenefitCreate) -> CostBenefit:
        roi = ((analysis.estimated_savings - analysis.implementation_cost) / 
               analysis.implementation_cost) * 100
        
        payback_period = (analysis.implementation_cost / 
                         (analysis.estimated_savings / 12))  # in months
        
        db_analysis = CostBenefit(
            project_id=analysis.project_id,
            benefit_description=analysis.benefit_description,
            estimated_savings=analysis.estimated_savings,
            implementation_cost=analysis.implementation_cost,
            roi_percentage=roi,
            payback_period=payback_period
        )
        self.db.add(db_analysis)
        self.db.commit()
        self.db.refresh(db_analysis)
        return db_analysis

    def get_cost_summary(self, project_id: int) -> dict:
        total_costs = (self.db.query(func.sum(Cost.amount))
                      .filter(Cost.project_id == project_id)
                      .scalar() or 0)
        
        costs_by_type = (self.db.query(Cost.type, func.sum(Cost.amount))
                        .filter(Cost.project_id == project_id)
                        .group_by(Cost.type)
                        .all())
        
        return {
            "total_costs": total_costs,
            "costs_by_type": {str(cost_type): amount for cost_type, amount in costs_by_type}
        }
