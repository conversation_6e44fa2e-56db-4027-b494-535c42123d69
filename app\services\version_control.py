from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, JSON, ForeignKey
from sqlalchemy.orm import Session, relationship
import difflib
import logging
import hashlib
from app.core.celery_app import celery_app
from app.core.cache import RedisCache
from app.core.database import Base

logger = logging.getLogger(__name__)

class DocumentVersion(Base):
    __tablename__ = "document_versions"

    id = Column(Integer, primary_key=True)
    document_id = Column(String, index=True)
    version_number = Column(String)
    content = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String)
    comment = Column(String)
    metadata = Column(JSON)
    hash = Column(String)

    def __repr__(self):
        return f"<DocumentVersion {self.document_id}:{self.version_number}>"

class VersionDiff:
    def __init__(self, from_version: str, to_version: str, changes: List[Dict[str, str]], stats: Dict[str, int]):
        self.from_version = from_version
        self.to_version = to_version
        self.changes = changes
        self.stats = stats

class VersionControlService:
    def __init__(self, db_session: Session):
        self.db = db_session
        self.cache = RedisCache()
        self.cache_ttl = 3600  # 1 hour

    async def create_version(
        self,
        document_id: str,
        content: str,
        user_id: str,
        comment: str,
        metadata: Optional[Dict] = None
    ) -> DocumentVersion:
        """Create a new version of a document."""
        try:
            # Get latest version
            latest = await self.get_latest_version(document_id)
            
            # Generate new version number
            version_number = self._increment_version(
                latest.version_number if latest else "0.0"
            )
            
            # Calculate content hash
            content_hash = self._calculate_hash(content)
            
            # Create new version
            version = DocumentVersion(
                document_id=document_id,
                version_number=version_number,
                content=content,
                created_by=user_id,
                comment=comment,
                metadata=metadata or {},
                hash=content_hash
            )
            
            self.db.add(version)
            await self.db.commit()
            
            # Invalidate cache
            await self._invalidate_cache(document_id)
            
            return version

        except Exception as e:
            logger.error(f"Error creating version: {str(e)}")
            await self.db.rollback()
            raise

    async def get_latest_version(
        self, 
        document_id: str
    ) -> Optional[DocumentVersion]:
        """Get the latest version of a document."""
        try:
            cache_key = f"latest_version:{document_id}"
            
            # Try to get from cache
            cached_version = await self.cache.get(cache_key)
            if cached_version:
                return DocumentVersion(**cached_version)

            # Get from database
            latest = self.db.query(DocumentVersion)\
                .filter(DocumentVersion.document_id == document_id)\
                .order_by(DocumentVersion.created_at.desc())\
                .first()

            if latest:
                # Cache the result
                await self.cache.set(
                    cache_key,
                    {
                        "id": latest.id,
                        "document_id": latest.document_id,
                        "version_number": latest.version_number,
                        "created_at": latest.created_at.isoformat(),
                        "created_by": latest.created_by,
                        "comment": latest.comment,
                        "metadata": latest.metadata,
                        "hash": latest.hash
                    },
                    self.cache_ttl
                )

            return latest

        except Exception as e:
            logger.error(f"Error getting latest version: {str(e)}")
            raise

    async def get_version_history(
        self, 
        document_id: str,
        limit: int = 10
    ) -> List[DocumentVersion]:
        """Get version history for a document."""
        try:
            cache_key = f"version_history:{document_id}:{limit}"
            
            # Try to get from cache
            cached_history = await self.cache.get(cache_key)
            if cached_history:
                return [DocumentVersion(**v) for v in cached_history]

            # Get from database
            versions = self.db.query(DocumentVersion)\
                .filter(DocumentVersion.document_id == document_id)\
                .order_by(DocumentVersion.created_at.desc())\
                .limit(limit)\
                .all()

            if versions:
                # Cache the result
                await self.cache.set(
                    cache_key,
                    [{
                        "id": v.id,
                        "document_id": v.document_id,
                        "version_number": v.version_number,
                        "created_at": v.created_at.isoformat(),
                        "created_by": v.created_by,
                        "comment": v.comment,
                        "metadata": v.metadata,
                        "hash": v.hash
                    } for v in versions],
                    self.cache_ttl
                )

            return versions

        except Exception as e:
            logger.error(f"Error getting version history: {str(e)}")
            raise

    @celery_app.task(name="version_control.compare_versions")
    async def compare_versions(
        self,
        document_id: str,
        from_version: str,
        to_version: str
    ) -> VersionDiff:
        """Compare two versions of a document."""
        try:
            # Get versions
            v1 = self.db.query(DocumentVersion).filter(
                DocumentVersion.document_id == document_id,
                DocumentVersion.version_number == from_version
            ).first()
            
            v2 = self.db.query(DocumentVersion).filter(
                DocumentVersion.document_id == document_id,
                DocumentVersion.version_number == to_version
            ).first()

            if not v1 or not v2:
                raise ValueError("Version not found")

            # Generate diff
            diff = difflib.unified_diff(
                v1.content.splitlines(),
                v2.content.splitlines(),
                fromfile=f'v{from_version}',
                tofile=f'v{to_version}',
                lineterm=''
            )

            # Process diff into structured format
            changes = []
            stats = {"additions": 0, "deletions": 0, "modifications": 0}

            for line in diff:
                if line.startswith('+'):
                    changes.append({"type": "addition", "content": line[1:]})
                    stats["additions"] += 1
                elif line.startswith('-'):
                    changes.append({"type": "deletion", "content": line[1:]})
                    stats["deletions"] += 1
                elif line.startswith('@'):
                    changes.append({"type": "header", "content": line})
                else:
                    changes.append({"type": "context", "content": line})

            return VersionDiff(
                from_version=from_version,
                to_version=to_version,
                changes=changes,
                stats=stats
            )

        except Exception as e:
            logger.error(f"Error comparing versions: {str(e)}")
            raise

    def _increment_version(self, version: str) -> str:
        """Increment version number."""
        major, minor = map(int, version.split('.'))
        return f"{major}.{minor + 1}"

    def _calculate_hash(self, content: str) -> str:
        """Calculate hash of content."""
        return hashlib.sha256(content.encode()).hexdigest()

    async def _invalidate_cache(self, document_id: str):
        """Invalidate cache entries for a document."""
        keys_to_delete = [
            f"latest_version:{document_id}",
            f"version_history:{document_id}:*"
        ]
        for key in keys_to_delete:
            await self.cache.delete(key)
