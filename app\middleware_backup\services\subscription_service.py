from sqlalchemy.orm import Session
from app.models.subscription import Subscription, SubscriptionFeature, Billing, SubscriptionTier
from app.schemas.subscription import SubscriptionCreate, BillingCreate
from datetime import datetime, timedelta
import stripe
from app.core.config import settings

class SubscriptionService:
    def __init__(self, db: Session):
        self.db = db
        stripe.api_key = settings.STRIPE_SECRET_KEY

    def create_subscription(self, subscription: SubscriptionCreate, user_id: int) -> Subscription:
        # Create Stripe subscription
        stripe_subscription = self._create_stripe_subscription(subscription)
        
        # Create local subscription record
        db_subscription = Subscription(
            user_id=user_id,
            tier=subscription.tier,
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow() + timedelta(days=30),
            is_active=True,
            auto_renew=subscription.auto_renew
        )
        self.db.add(db_subscription)
        self.db.commit()
        self.db.refresh(db_subscription)
        return db_subscription

    def get_subscription(self, user_id: int) -> Subscription:
        return self.db.query(Subscription).filter(Subscription.user_id == user_id).first()

    def get_tier_features(self, tier: SubscriptionTier) -> list[SubscriptionFeature]:
        return (self.db.query(SubscriptionFeature)
                .filter(SubscriptionFeature.tier == tier)
                .filter(SubscriptionFeature.is_enabled == True)
                .all())

    def create_billing(self, billing: BillingCreate) -> Billing:
        db_billing = Billing(
            subscription_id=billing.subscription_id,
            amount=billing.amount,
            billing_date=datetime.utcnow(),
            payment_status="pending",
            payment_method=billing.payment_method,
            invoice_number=self._generate_invoice_number()
        )
        self.db.add(db_billing)
        self.db.commit()
        self.db.refresh(db_billing)
        return db_billing

    def process_payment(self, billing_id: int, token: str) -> bool:
        billing = self.db.query(Billing).filter(Billing.id == billing_id).first()
        if not billing:
            return False
            
        try:
            # Process payment through Stripe
            charge = stripe.Charge.create(
                amount=int(billing.amount * 100),  # Convert to cents
                currency="usd",
                source=token,
                description=f"Invoice {billing.invoice_number}"
            )
            
            billing.payment_status = "completed"
            self.db.commit()
            return True
        except stripe.error.StripeError:
            billing.payment_status = "failed"
            self.db.commit()
            return False

    def _create_stripe_subscription(self, subscription: SubscriptionCreate) -> dict:
        # This is a placeholder for actual Stripe subscription creation
        # You would typically create a Stripe Customer and Subscription here
        return {"id": "sub_123", "status": "active"}

    def _generate_invoice_number(self) -> str:
        # Generate a unique invoice number
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        count = self.db.query(Billing).count()
        return f"INV-{timestamp}-{count + 1}"
