"""Compliance review and policy requirement models."""

from datetime import datetime
from typing import List, Optional, Dict
from sqlalchemy import String, DateTime, ForeignKey, Text, JSON, Float, Integer, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base, TimestampMixin
from app.models.fema import ApplicationStatus
from uuid import uuid4
from app.models.enums import ComplianceStatus, RiskLevel, ValidationStatus
from app.models.compliance import PolicyRequirement  # Import the unified model

class ComplianceReview(Base, TimestampMixin):
    """Model for tracking compliance reviews."""
    __tablename__ = "compliance_reviews"

    id: Mapped[str] = mapped_column(String, primary_key=True)
    document_id: Mapped[str] = mapped_column(String, ForeignKey("documents.id"), nullable=False)
    requirement_id: Mapped[str] = mapped_column(String, ForeignKey("policy_requirements.id"), nullable=False)
    reviewer_id: Mapped[int] = mapped_column(Inte<PERSON>, ForeignKey("users.id"), nullable=True)
    status: Mapped[str] = mapped_column(String, nullable=False, default=ValidationStatus.PENDING)
    compliance_status: Mapped[str] = mapped_column(String, nullable=False, default=ComplianceStatus.UNDER_REVIEW)
    risk_level: Mapped[str] = mapped_column(String, nullable=False, default=RiskLevel.MEDIUM)
    findings: Mapped[Dict] = mapped_column(JSON, nullable=False, default=dict)
    score: Mapped[float] = mapped_column(Float, nullable=True)
    review_date: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)
    notes: Mapped[str] = mapped_column(Text, nullable=True)

    # Relationships
    document = relationship("Document", back_populates="compliance_reviews")
    requirement = relationship("PolicyRequirement")
    reviewer = relationship("User", back_populates="reviews")

    def __repr__(self) -> str:
        return f"<ComplianceReview {self.id} ({self.status})>"

class ComplianceReviewV2(Base, TimestampMixin):
    """Enhanced compliance review model with detailed tracking."""
    __tablename__ = "compliance_reviews_v2"

    id: Mapped[str] = mapped_column(String, primary_key=True, default=lambda: str(uuid4()))
    
    # Review metadata
    status: Mapped[ApplicationStatus] = mapped_column(
        SQLEnum(ApplicationStatus), 
        default=ApplicationStatus.PENDING
    )
    review_type: Mapped[str] = mapped_column(String, nullable=False)  # e.g., "initial", "appeal", "amendment"
    confidence_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Document references
    application_id: Mapped[str] = mapped_column(String, ForeignKey("fema_applications.id"))
    document_id: Mapped[str] = mapped_column(String, ForeignKey("compliance_documents.id"))
    policy_version_id: Mapped[str] = mapped_column(String, ForeignKey("policy_versions.id"))
    
    # Review content
    findings: Mapped[List[Dict]] = mapped_column(JSON, default=list)  # List of compliance findings
    requirements_met: Mapped[List[str]] = mapped_column(JSON, default=list)  # List of met requirements
    deficiencies: Mapped[List[Dict]] = mapped_column(JSON, default=list)  # List of deficiencies
    recommendations: Mapped[Optional[str]] = mapped_column(Text)
    
    # Review process
    reviewer_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"))
    review_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationships
    application: Mapped["FEMAApplication"] = relationship("FEMAApplication", back_populates="reviews")
    document: Mapped["ComplianceDocument"] = relationship("ComplianceDocument", back_populates="reviews")
    policy_version: Mapped["PolicyVersion"] = relationship("PolicyVersion")
    reviewer: Mapped["User"] = relationship("User")
    linked_documents: Mapped[List["ReviewDocument"]] = relationship("ReviewDocument", back_populates="review")

    def __repr__(self) -> str:
        return f"<ComplianceReviewV2 {self.id} ({self.status})>"

class ReviewDocument(Base, TimestampMixin):
    """Model for linking additional documents to a review."""
    __tablename__ = "review_documents"
    
    review_id: Mapped[str] = mapped_column(String, ForeignKey("compliance_reviews_v2.id"), primary_key=True)
    document_id: Mapped[str] = mapped_column(String, ForeignKey("compliance_documents.id"), primary_key=True)
    
    # Relationships
    review: Mapped["ComplianceReviewV2"] = relationship("ComplianceReviewV2", back_populates="linked_documents")
    document: Mapped["ComplianceDocument"] = relationship("ComplianceDocument")

class PolicyRequirement(Base, TimestampMixin):
    """Model for defining policy requirements and compliance criteria."""
    __tablename__ = "policy_requirements"

    id: Mapped[str] = mapped_column(String, primary_key=True)
    title: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str] = mapped_column(String, nullable=False)
    category: Mapped[str] = mapped_column(String, nullable=False)
    required_documents: Mapped[List[str]] = mapped_column(JSON)  # List of required document types
    compliance_criteria: Mapped[Dict] = mapped_column(JSON)  # Compliance criteria definition
    
    def __repr__(self) -> str:
        return f"<PolicyRequirement {self.id} ({self.title})>" 