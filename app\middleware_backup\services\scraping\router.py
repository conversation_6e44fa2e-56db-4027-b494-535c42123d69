from fastapi import APIRouter, BackgroundTasks
from .govstar_scraper import GovStarScraper

router = APIRouter(prefix="/scraping", tags=["Scraping"])

@router.post("/govstar")
async def start_govstar_scrape(background_tasks: BackgroundTasks):
    scraper = GovStarScraper()
    background_tasks.add_task(scraper.scrape_page, scraper.base_url)
    return {"message": "Scraping initiated for GovStar.org"}
