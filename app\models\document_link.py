from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, func
from sqlalchemy.orm import relationship

from app.db.base_class import Base

class DocumentLink(Base):
    """Model for linking documents to FEMA applications."""
    __tablename__ = "document_links"

    id = Column(String, primary_key=True, index=True)
    document_id = Column(String, ForeignKey("documents.id"), nullable=False, index=True)
    application_id = Column(String, ForeignKey("applications.id"), nullable=False, index=True)
    category = Column(String, nullable=False, index=True)
    subcategory = Column(String, nullable=True)
    is_primary = Column(Boolean, default=False)
    notes = Column(String, nullable=True)
    validation_status = Column(String, nullable=False, default="pending")
    compliance_status = Column(String, nullable=False, default="pending")
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    # Relationships
    document = relationship("Document", back_populates="links")
    application = relationship("Application", back_populates="document_links")
    reviews = relationship("ComplianceReview", back_populates="document_link", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DocumentLink(id={self.id}, document_id={self.document_id}, category={self.category})>" 