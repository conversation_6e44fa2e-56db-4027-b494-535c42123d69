from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import List
from app.core.deps import get_db
from app.models.policy import PolicyDocument, PolicyVersion
from app.models.disaster import DisasterDeclaration
from app.schemas.policy import PolicyCreate, PolicyResponse, PolicyVersionCreate, PolicyVersionResponse
from app.services.storage import DocumentStorage

router = APIRouter()
storage_service = DocumentStorage()

@router.post("/", response_model=PolicyResponse)
async def create_policy(
    policy: PolicyCreate,
    db: Session = Depends(get_db)
):
    """Create a new policy document"""
    db_policy = PolicyDocument(
        policy_number=policy.policy_number,
        title=policy.title,
        description=policy.description,
        policy_type=policy.policy_type
    )
    db.add(db_policy)
    db.commit()
    db.refresh(db_policy)
    return db_policy

@router.post("/{policy_id}/versions", response_model=PolicyVersionResponse)
async def add_policy_version(
    policy_id: int,
    version: PolicyVersionCreate,
    document: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Add a new version to a policy document"""
    policy = db.query(PolicyDocument).filter_by(id=policy_id).first()
    if not policy:
        raise HTTPException(status_code=404, detail="Policy not found")
    
    # Save the document file
    document_url = await storage_service.save_document(document, f"policy_{policy_id}")
    
    # Create the policy version
    db_version = PolicyVersion(
        policy_id=policy_id,
        version_number=version.version_number,
        content=version.content,
        effective_date=version.effective_date,
        expiration_date=version.expiration_date,
        document_url=document_url
    )
    db.add(db_version)
    db.commit()
    db.refresh(db_version)
    return db_version

@router.get("/{policy_id}/versions", response_model=List[PolicyVersionResponse])
async def get_policy_versions(
    policy_id: int,
    db: Session = Depends(get_db)
):
    """Get all versions of a policy document"""
    policy = db.query(PolicyDocument).filter_by(id=policy_id).first()
    if not policy:
        raise HTTPException(status_code=404, detail="Policy not found")
    return policy.versions

@router.post("/{policy_id}/versions/{version_id}/disasters/{dr_number}")
async def associate_policy_with_disaster(
    policy_id: int,
    version_id: int,
    dr_number: str,
    db: Session = Depends(get_db)
):
    """Associate a policy version with a disaster declaration"""
    version = db.query(PolicyVersion).filter_by(id=version_id, policy_id=policy_id).first()
    if not version:
        raise HTTPException(status_code=404, detail="Policy version not found")
    
    disaster = db.query(DisasterDeclaration).filter_by(dr_number=dr_number).first()
    if not disaster:
        raise HTTPException(status_code=404, detail="Disaster not found")
    
    version.applicable_disasters.append(disaster)
    db.commit()
    return {"message": "Policy version associated with disaster"}
