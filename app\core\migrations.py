"""
Database migration system for ComplianceMax.
"""

import os
from typing import List, Optional
from alembic import command
from alembic.config import Config
from alembic.script import ScriptDirectory
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

class MigrationManager:
    """Database migration manager using Alembic."""
    
    def __init__(self, migrations_dir: str = "migrations"):
        """Initialize migration manager."""
        self.migrations_dir = migrations_dir
        self.alembic_cfg = self._create_config()
    
    def _create_config(self) -> Config:
        """Create Alembic configuration."""
        config = Config()
        config.set_main_option("script_location", self.migrations_dir)
        config.set_main_option("sqlalchemy.url", settings.get_database_url())
        return config
    
    def create_migration(self, message: str) -> str:
        """Create a new migration."""
        try:
            revision = command.revision(
                self.alembic_cfg,
                message=message,
                autogenerate=True
            )
            logger.info(f"Created migration: {revision.revision}")
            return revision.revision
        except Exception as e:
            logger.error(f"Failed to create migration: {str(e)}")
            raise
    
    def upgrade(self, revision: str = "head") -> None:
        """Upgrade database to specified revision."""
        try:
            command.upgrade(self.alembic_cfg, revision)
            logger.info(f"Upgraded database to {revision}")
        except Exception as e:
            logger.error(f"Failed to upgrade database: {str(e)}")
            raise
    
    def downgrade(self, revision: str) -> None:
        """Downgrade database to specified revision."""
        try:
            command.downgrade(self.alembic_cfg, revision)
            logger.info(f"Downgraded database to {revision}")
        except Exception as e:
            logger.error(f"Failed to downgrade database: {str(e)}")
            raise
    
    def get_current_revision(self) -> Optional[str]:
        """Get current database revision."""
        try:
            script = ScriptDirectory.from_config(self.alembic_cfg)
            return script.get_current_head()
        except Exception as e:
            logger.error(f"Failed to get current revision: {str(e)}")
            return None
    
    def get_history(self) -> List[dict]:
        """Get migration history."""
        try:
            script = ScriptDirectory.from_config(self.alembic_cfg)
            history = []
            
            for revision in script.walk_revisions():
                history.append({
                    "revision": revision.revision,
                    "down_revision": revision.down_revision,
                    "message": revision.doc,
                    "created_date": revision.created_date.isoformat()
                })
            
            return history
        except Exception as e:
            logger.error(f"Failed to get migration history: {str(e)}")
            return []
    
    def check_pending_migrations(self) -> List[dict]:
        """Check for pending migrations."""
        try:
            script = ScriptDirectory.from_config(self.alembic_cfg)
            current = self.get_current_revision()
            pending = []
            
            for revision in script.walk_revisions():
                if current and revision.revision == current:
                    break
                pending.append({
                    "revision": revision.revision,
                    "message": revision.doc
                })
            
            return pending
        except Exception as e:
            logger.error(f"Failed to check pending migrations: {str(e)}")
            return []

# Create migration manager instance
migration_manager = MigrationManager()

def init_migrations() -> None:
    """Initialize migration environment."""
    try:
        # Create migrations directory if it doesn't exist
        os.makedirs(migration_manager.migrations_dir, exist_ok=True)
        
        # Initialize Alembic
        command.init(migration_manager.alembic_cfg, migration_manager.migrations_dir)
        logger.info("Initialized migration environment")
    except Exception as e:
        logger.error(f"Failed to initialize migrations: {str(e)}")
        raise

def create_initial_migration() -> None:
    """Create initial migration."""
    try:
        migration_manager.create_migration("Initial migration")
        logger.info("Created initial migration")
    except Exception as e:
        logger.error(f"Failed to create initial migration: {str(e)}")
        raise

def apply_migrations() -> None:
    """Apply all pending migrations."""
    try:
        pending = migration_manager.check_pending_migrations()
        if pending:
            logger.info(f"Found {len(pending)} pending migrations")
            migration_manager.upgrade()
            logger.info("Applied all pending migrations")
        else:
            logger.info("No pending migrations")
    except Exception as e:
        logger.error(f"Failed to apply migrations: {str(e)}")
        raise 