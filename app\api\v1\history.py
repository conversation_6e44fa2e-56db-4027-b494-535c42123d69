from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
from app.core.deps import get_db, get_current_user
from app.services.history import HistoryService
from app.schemas.history import HistoryResponse, AuditTrailResponse
from app.models.document import DocumentHistory
from app.models.policy import PolicyVersion
from app.services.document_analysis import DocumentAnalyzer
from app.services.document_comparison import DocumentComparator
from app.services.compliance_validation import ComplianceValidator
from app.db.mongodb import get_database

router = APIRouter()

@router.get("/review/{review_id}", response_model=List[HistoryResponse])
async def get_review_history(
    review_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get the history of changes for a specific review"""
    history = HistoryService.get_review_history(
        db=db,
        review_id=review_id,
        start_date=start_date,
        end_date=end_date
    )
    return history

@router.get("/user/{user_id}", response_model=List[HistoryResponse])
async def get_user_changes(
    user_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get all changes made by a specific user"""
    if current_user.id != user_id and not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to view other users' history"
        )
    
    history = HistoryService.get_user_changes(
        db=db,
        user_id=user_id,
        start_date=start_date,
        end_date=end_date
    )
    return history

@router.get("/audit/{review_id}", response_model=AuditTrailResponse)
async def get_audit_trail(
    review_id: int,
    include_documents: bool = Query(True, description="Include document changes in the audit trail"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Generate a complete audit trail for a review"""
    audit_trail = HistoryService.generate_audit_trail(
        db=db,
        review_id=review_id,
        include_document_changes=include_documents
    )
    return audit_trail

@router.get("/documents/{document_id}/history", response_model=List[Dict[str, Any]])
async def get_document_history(document_id: str):
    """Get the analysis and compliance history for a document."""
    try:
        db = await get_database()
        history = await DocumentHistory.find({"document_id": document_id}).sort("timestamp", -1).to_list(None)
        
        # Format history entries
        formatted_history = []
        for entry in history:
            formatted_entry = {
                "timestamp": entry.timestamp.isoformat(),
                "action": entry.action,
                "details": entry.details
            }
            
            # Add policy information if available
            if entry.action == "compliance_validation" and "policy_id" in entry.details:
                policy = await PolicyVersion.find_one({"_id": entry.details["policy_id"]})
                if policy:
                    formatted_entry["details"]["policy_info"] = {
                        "number": policy.policy_number,
                        "title": policy.title,
                        "version": policy.version_number,
                        "effective_date": policy.effective_date.isoformat()
                    }
            
            formatted_history.append(formatted_entry)
        
        return formatted_history
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/documents/{document_id}/history")
async def add_history_entry(document_id: str, action: str, details: Dict[str, Any]):
    """Add a new history entry for a document."""
    try:
        db = await get_database()
        history_entry = DocumentHistory(
            document_id=document_id,
            timestamp=datetime.utcnow(),
            action=action,
            details=details
        )
        await history_entry.save()
        return {"status": "success", "message": "History entry added"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{document_id}/analysis-history", response_model=List[Dict[str, Any]])
async def get_analysis_history(document_id: str):
    """Get the analysis history for a document."""
    try:
        db = await get_database()
        history = await DocumentHistory.find({
            "document_id": document_id,
            "action": {"$in": ["document_analysis", "compliance_validation"]}
        }).sort("timestamp", -1).to_list(None)
        
        formatted_history = []
        for entry in history:
            formatted_entry = {
                "timestamp": entry.timestamp.isoformat(),
                "action": entry.action,
                "analysis_type": entry.details.get("analysis_type", "unknown"),
                "scores": entry.details.get("scores", {}),
                "findings_count": len(entry.details.get("findings", [])),
                "status": entry.details.get("status", "unknown")
            }
            formatted_history.append(formatted_entry)
        
        return formatted_history
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{document_id}/compliance-history", response_model=List[Dict[str, Any]])
async def get_compliance_history(document_id: str):
    """Get the compliance validation history for a document."""
    try:
        db = await get_database()
        history = await DocumentHistory.find({
            "document_id": document_id,
            "action": "compliance_validation"
        }).sort("timestamp", -1).to_list(None)
        
        formatted_history = []
        for entry in history:
            formatted_entry = {
                "timestamp": entry.timestamp.isoformat(),
                "dr_number": entry.details.get("dr_number"),
                "overall_score": entry.details.get("scores", {}).get("overall"),
                "deficiencies_count": entry.details.get("deficiency_count", 0),
                "temporal_issues_count": entry.details.get("temporal_issues_count", 0),
                "status": entry.details.get("status"),
                "policy_comparisons": [
                    {
                        "policy_id": pc["policy_id"],
                        "policy_number": pc["policy_number"],
                        "similarity_score": pc["comparison"]["similarity_score"]
                    }
                    for pc in entry.details.get("policy_comparisons", [])
                ]
            }
            formatted_history.append(formatted_entry)
        
        return formatted_history
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{document_id}/comparison-history", response_model=List[Dict[str, Any]])
async def get_comparison_history(document_id: str):
    """Get the document comparison history for a document."""
    try:
        db = await get_database()
        history = await DocumentHistory.find({
            "document_id": document_id,
            "action": "document_comparison"
        }).sort("timestamp", -1).to_list(None)
        
        formatted_history = []
        for entry in history:
            formatted_entry = {
                "timestamp": entry.timestamp.isoformat(),
                "target_document_id": entry.details.get("target_document_id"),
                "similarity_score": entry.details.get("similarity_score"),
                "requirement_matches": len(entry.details.get("requirement_comparison", {}).get("matches", [])),
                "temporal_matches": len(entry.details.get("temporal_comparison", {}).get("common_dates", [])),
                "structure_similarity": entry.details.get("structure_comparison", {}).get("overall_similarity")
            }
            formatted_history.append(formatted_entry)
        
        return formatted_history
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/documents/{document_id}/history")
async def clear_document_history(document_id: str):
    """Clear all history entries for a document."""
    try:
        db = await get_database()
        result = await DocumentHistory.delete_many({"document_id": document_id})
        return {
            "status": "success",
            "message": f"Deleted {result.deleted_count} history entries",
            "deleted_count": result.deleted_count
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{document_id}/latest-analysis")
async def get_latest_analysis(document_id: str):
    """Get the latest analysis result for a document."""
    try:
        db = await get_database()
        latest = await DocumentHistory.find_one({
            "document_id": document_id,
            "action": "document_analysis"
        }, sort=[("timestamp", -1)])
        
        if not latest:
            raise HTTPException(status_code=404, detail="No analysis found for document")
        
        return {
            "timestamp": latest.timestamp.isoformat(),
            "analysis": latest.details
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{document_id}/latest-compliance")
async def get_latest_compliance(document_id: str):
    """Get the latest compliance validation result for a document."""
    try:
        db = await get_database()
        latest = await DocumentHistory.find_one({
            "document_id": document_id,
            "action": "compliance_validation"
        }, sort=[("timestamp", -1)])
        
        if not latest:
            raise HTTPException(status_code=404, detail="No compliance validation found for document")
        
        return {
            "timestamp": latest.timestamp.isoformat(),
            "validation": latest.details
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
