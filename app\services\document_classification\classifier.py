"""
Document Classification Service
Implements document classification using various techniques.
"""

import os
import re
import logging
from typing import Dict, List, Optional
from pathlib import Path
import PyPDF2
import docx
import pandas as pd
from .base import DocumentClassifier

logger = logging.getLogger(__name__)

class DefaultDocumentClassifier(DocumentClassifier):
    """Default implementation of document classification."""
    
    def __init__(self):
        """Initialize the classifier."""
        self.logger = logging.getLogger('DefaultDocumentClassifier')
        self.supported_formats = ['.pdf', '.docx', '.txt', '.csv', '.xlsx']
        
        # Document type patterns
        self.document_patterns = {
            'policy': [
                r'policy\s*#?\s*\d+',
                r'standard\s*operating\s*procedure',
                r'sop\s*#?\s*\d+'
            ],
            'procedure': [
                r'procedure\s*#?\s*\d+',
                r'work\s*instruction',
                r'wi\s*#?\s*\d+'
            ],
            'report': [
                r'audit\s*report',
                r'investigation\s*report',
                r'compliance\s*review'
            ],
            'form': [
                r'form\s*#?\s*\d+',
                r'checklist',
                r'template'
            ]
        }
        
        # Compliance-related keywords
        self.compliance_keywords = [
            'compliance', 'regulation', 'standard', 'requirement',
            'audit', 'inspection', 'certification', 'accreditation',
            'policy', 'procedure', 'guideline', 'framework'
        ]
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported file formats.
        
        Returns:
            List of supported file extensions
        """
        return self.supported_formats
    
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if the document can be processed.
        
        Args:
            document_path: Path to the document
            
        Returns:
            True if document can be processed, False otherwise
        """
        try:
            if not os.path.exists(document_path):
                self.logger.error(f"Document not found: {document_path}")
                return False
                
            file_ext = os.path.splitext(document_path)[1].lower()
            if file_ext not in self.supported_formats:
                self.logger.error(f"Unsupported file format: {file_ext}")
                return False
                
            return True
        except Exception as e:
            self.logger.error(f"Error validating document: {e}")
            return False
    
    def classify(self, document_path: str) -> Dict:
        """
        Classify a document and extract metadata.
        
        Args:
            document_path: Path to the document
            
        Returns:
            Dictionary containing classification results and metadata
        """
        try:
            if not self.validate_document(document_path):
                raise ValueError(f"Invalid document: {document_path}")
            
            # Extract text content
            text_content = self._extract_text(document_path)
            
            # Analyze content
            analysis = self._analyze_content(text_content)
            
            # Determine document type
            doc_type = self._determine_document_type(text_content)
            
            # Check compliance relevance
            compliance_info = self._check_compliance_relevance(text_content)
            
            # Extract keywords
            keywords = self._extract_keywords(text_content)
            
            # Generate summary
            summary = self._generate_summary(text_content)
            
            return {
                'document_type': doc_type,
                'metadata': {
                    'file_name': os.path.basename(document_path),
                    'file_size': os.path.getsize(document_path),
                    'file_extension': os.path.splitext(document_path)[1],
                    'last_modified': os.path.getmtime(document_path)
                },
                'content_analysis': analysis,
                'compliance_info': compliance_info,
                'keywords': keywords,
                'summary': summary
            }
            
        except Exception as e:
            self.logger.error(f"Error classifying document: {e}")
            raise
    
    def _extract_text(self, document_path: str) -> str:
        """
        Extract text from document based on file type.
        
        Args:
            document_path: Path to the document
            
        Returns:
            Extracted text or None if extraction fails
        """
        file_ext = os.path.splitext(document_path)[1].lower()
        
        if file_ext == '.pdf':
            return self._extract_pdf_text(document_path)
        elif file_ext == '.docx':
            return self._extract_docx_text(document_path)
        elif file_ext == '.txt':
            return self._extract_txt_text(document_path)
        elif file_ext in ['.csv', '.xlsx']:
            return self._extract_excel_text(document_path)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
    
    def _extract_pdf_text(self, document_path: str) -> str:
        """Extract text from PDF document."""
        try:
            text = ""
            with open(document_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text
        except Exception as e:
            self.logger.error(f"Error extracting PDF text: {e}")
            raise
    
    def _extract_docx_text(self, document_path: str) -> str:
        """Extract text from DOCX document."""
        try:
            doc = docx.Document(document_path)
            return "\n".join([paragraph.text for paragraph in doc.paragraphs])
        except Exception as e:
            self.logger.error(f"Error extracting DOCX text: {e}")
            raise
    
    def _extract_txt_text(self, document_path: str) -> str:
        """Extract text from TXT document."""
        try:
            with open(document_path, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            self.logger.error(f"Error extracting TXT text: {e}")
            raise
    
    def _extract_excel_text(self, document_path: str) -> str:
        """Extract text from Excel document."""
        try:
            if document_path.endswith('.csv'):
                df = pd.read_csv(document_path)
            else:
                df = pd.read_excel(document_path)
            return df.to_string()
        except Exception as e:
            self.logger.error(f"Error extracting Excel text: {e}")
            raise
    
    def _analyze_content(self, text: str) -> Dict:
        """
        Analyze document content for basic characteristics.
        
        Args:
            text: Document text to analyze
            
        Returns:
            Dictionary of content analysis results
        """
        lines = text.split('\n')
        words = text.split()
        
        return {
            'line_count': len(lines),
            'word_count': len(words),
            'character_count': len(text),
            'average_line_length': sum(len(line) for line in lines) / len(lines) if lines else 0,
            'average_word_length': sum(len(word) for word in words) / len(words) if words else 0
        }
    
    def _determine_document_type(self, text: str) -> str:
        """
        Determine document type based on content.
        
        Args:
            text: Document text to analyze
            
        Returns:
            Determined document type
        """
        text_lower = text.lower()
        
        for doc_type, patterns in self.document_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    return doc_type
        
        return 'unknown'
    
    def _check_compliance_relevance(self, text: str) -> Dict:
        """
        Check if document is compliance-relevant.
        
        Args:
            text: Document text to analyze
            
        Returns:
            True if document is compliance-relevant, False otherwise
        """
        text_lower = text.lower()
        matches = []
        
        for keyword in self.compliance_keywords:
            if keyword in text_lower:
                matches.append(keyword)
        
        return {
            'is_compliance_related': len(matches) > 0,
            'matched_keywords': matches,
            'relevance_score': len(matches) / len(self.compliance_keywords)
        }
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        Extract important keywords from document.
        
        Args:
            text: Document text to analyze
            
        Returns:
            List of extracted keywords
        """
        # Simple implementation - can be enhanced with NLP
        words = text.lower().split()
        word_freq = {}
        
        for word in words:
            if len(word) > 3:  # Ignore short words
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Sort by frequency and return top 10
        return sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
    
    def _generate_summary(self, text: str) -> str:
        """
        Generate a simple summary of the document.
        
        Args:
            text: Document text to summarize
            
        Returns:
            Generated summary
        """
        # Simple implementation - can be enhanced with NLP
        sentences = text.split('.')
        if len(sentences) > 3:
            return '. '.join(sentences[:3]) + '.'
        return text 