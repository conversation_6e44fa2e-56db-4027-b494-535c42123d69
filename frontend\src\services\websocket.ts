import { WS_BASE_URL } from '../config';

export interface DocumentUpdate {
  document_id: string;
  action: 'analysis_complete' | 'compliance_complete' | 'error';
  status: string;
  details?: any;
  timestamp: string;
}

export class WebSocketService {
  private socket: WebSocket | null = null;
  private messageHandlers: Map<string, ((data: any) => void)[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    this.setupReconnection = this.setupReconnection.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
  }

  connect(documentId: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      return;
    }

    this.socket = new WebSocket(`${WS_BASE_URL}/ws/documents/${documentId}`);
    
    this.socket.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    };

    this.socket.onmessage = this.handleMessage;

    this.socket.onclose = () => {
      console.log('WebSocket disconnected');
      this.setupReconnection();
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  private setupReconnection(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        if (this.socket) {
          this.connect(this.getDocumentIdFromUrl(this.socket.url));
        }
      }, delay);
    }
  }

  private getDocumentIdFromUrl(url: string): string {
    const parts = url.split('/');
    return parts[parts.length - 1];
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      const handlers = this.messageHandlers.get(data.action) || [];
      handlers.forEach(handler => handler(data));
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
    }
  }

  subscribe<T>(action: string, handler: (data: T) => void): () => void {
    const handlers = this.messageHandlers.get(action) || [];
    handlers.push(handler);
    this.messageHandlers.set(action, handlers);

    return () => {
      const updatedHandlers = this.messageHandlers.get(action) || [];
      this.messageHandlers.set(
        action,
        updatedHandlers.filter(h => h !== handler)
      );
    };
  }

  subscribeToDocumentUpdates(handler: (update: DocumentUpdate) => void): () => void {
    return this.subscribe<DocumentUpdate>('document_update', handler);
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.messageHandlers.clear();
    this.reconnectAttempts = 0;
  }

  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  async waitForConnection(timeout: number = 5000): Promise<boolean> {
    if (this.isConnected()) {
      return true;
    }

    return new Promise((resolve) => {
      const checkInterval = 100;
      let elapsed = 0;

      const check = () => {
        if (this.isConnected()) {
          resolve(true);
          return;
        }

        elapsed += checkInterval;
        if (elapsed >= timeout) {
          resolve(false);
          return;
        }

        setTimeout(check, checkInterval);
      };

      check();
    });
  }
}

// Create a singleton instance
export const websocketService = new WebSocketService();
