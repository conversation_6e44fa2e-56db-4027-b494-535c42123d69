"""
Base Compliance Rule
Defines the interface for compliance rules.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from datetime import datetime

class Rule(ABC):
    """Abstract base class for compliance rules."""
    
    def __init__(self, 
                 rule_id: str,
                 name: str,
                 description: str,
                 severity: str = "medium",
                 category: str = "general",
                 enabled: bool = True):
        """
        Initialize a compliance rule.
        
        Args:
            rule_id: Unique identifier for the rule
            name: Human-readable name of the rule
            description: Detailed description of the rule
            severity: Severity level (low, medium, high, critical)
            category: Rule category (e.g., security, privacy, operational)
            enabled: Whether the rule is active
        """
        self.rule_id = rule_id
        self.name = name
        self.description = description
        self.severity = severity.lower()
        self.category = category.lower()
        self.enabled = enabled
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    @abstractmethod
    def evaluate(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate a document against this rule.
        
        Args:
            document: Document metadata and content to evaluate
            
        Returns:
            Dictionary containing:
            - passed: Whether the document passed the rule
            - message: Description of the result
            - details: Additional details about the evaluation
            - timestamp: When the evaluation occurred
        """
        pass
    
    @abstractmethod
    def validate(self) -> bool:
        """
        Validate the rule configuration.
        
        Returns:
            True if the rule is valid, False otherwise
        """
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the rule to a dictionary representation.
        
        Returns:
            Dictionary containing rule configuration
        """
        return {
            'rule_id': self.rule_id,
            'name': self.name,
            'description': self.description,
            'severity': self.severity,
            'category': self.category,
            'enabled': self.enabled,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Rule':
        """
        Create a rule instance from a dictionary.
        
        Args:
            data: Dictionary containing rule configuration
            
        Returns:
            Rule instance
        """
        rule = cls(
            rule_id=data['rule_id'],
            name=data['name'],
            description=data['description'],
            severity=data.get('severity', 'medium'),
            category=data.get('category', 'general'),
            enabled=data.get('enabled', True)
        )
        rule.created_at = datetime.fromisoformat(data['created_at'])
        rule.updated_at = datetime.fromisoformat(data['updated_at'])
        return rule 