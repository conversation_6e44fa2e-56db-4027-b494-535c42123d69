from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List

class StandardBase(BaseModel):
    code: str
    title: str
    description: Optional[str] = None
    version: str
    effective_date: datetime

class StandardCreate(StandardBase):
    pass

class StandardResponse(StandardBase):
    id: int
    
    class Config:
        from_attributes = True

class RequirementBase(BaseModel):
    standard_id: int
    code: str
    description: str
    criteria: str

class RequirementCreate(RequirementBase):
    pass

class RequirementResponse(RequirementBase):
    id: int
    
    class Config:
        from_attributes = True

class ReviewBase(BaseModel):
    requirement_id: int
    project_id: int
    status: str
    notes: Optional[str] = None
    evidence: Optional[str] = None

class ReviewCreate(ReviewBase):
    pass

class ReviewResponse(ReviewBase):
    id: int
    review_date: datetime
    reviewer_id: int
    
    class Config:
        from_attributes = True
