import os
import secrets
from typing import Optional, Dict, Any, List
from pydantic import PostgresDsn, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    PROJECT_NAME: str = "ComplianceMax"
    API_V1_STR: str = "/api/v1"
    
    # Security
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    ALGORITHM: str = "HS256"  # JWT signing algorithm
    
    # Rate limiting
    ENABLE_RATE_LIMITING: bool = True
    RATE_LIMIT_DEFAULT_LIMIT: int = 60  # 60 requests
    RATE_LIMIT_DEFAULT_PERIOD: int = 60  # per minute
    RATE_LIMIT_WHITELIST: List[str] = ["127.0.0.1", "::1"]  # localhost IPv4 and IPv6
    RATE_LIMIT_BURST_MULTIPLIER: float = 2.0  # Allow burst up to 2x the limit
    
    # Security headers
    SECURITY_HEADERS: Dict[str, str] = {
        "X-Frame-Options": "SAMEORIGIN",
        "X-XSS-Protection": "1; mode=block",
        "X-Content-Type-Options": "nosniff", 
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'; img-src 'self' data:; script-src 'self'; style-src 'self' 'unsafe-inline';",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    
    # Database
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "compliancemax"
    POSTGRES_PORT: int = 5432
    SQLALCHEMY_DATABASE_URI: Optional[str] = "sqlite:///./compliancemax.db"
    SQLALCHEMY_DATABASE_URL: Optional[str] = "sqlite:///./compliancemax.db"

    @field_validator("SQLALCHEMY_DATABASE_URI")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info):
        if isinstance(v, str):
            return v
            
        # For simplicity, just return a SQLite database URL
        return "sqlite:///./compliancemax.db"
    
    # CORS - Updated for more robust frontend-backend integration
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",  # React default
        "http://localhost:5174",  # Vite dev server
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5174",
        "http://localhost:8000",  # Backend URL if accessing API directly
        "http://127.0.0.1:8000",
    ]
    
    # Method to get CORS origins from environment or default list
    @property
    def CORS_ORIGINS(self) -> List[str]:
        cors_origins_env = os.getenv("CORS_ORIGINS")
        if cors_origins_env:
            return [origin.strip() for origin in cors_origins_env.split(",")]
        return self.BACKEND_CORS_ORIGINS
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    
    # Email settings
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USER: str = ""
    SMTP_PASSWORD: str = ""
    
    # Storage
    STORAGE_BUCKET: str = "compliancemax-storage"
    
    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./compliancemax.db")
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 10
    
    # Redis settings
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    # File upload settings
    UPLOAD_DIR: str = "uploads"
    STORAGE_DIR: str = "storage"
    MAX_CONTENT_LENGTH: int = 16 * 1024 * 1024  # 16 MB
    ALLOWED_EXTENSIONS: list = ["pdf", "doc", "docx", "txt"]
    
    # Session settings
    SESSION_EXPIRE: int = 1800  # 30 minutes
    
    # API settings
    DEBUG: bool = True
    
    # OpenAI settings for document analysis
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    
    # Feature flags
    ENABLE_MULTI_ENGINE_OCR: bool = True

    model_config = SettingsConfigDict(
        env_file=".env", 
        env_file_encoding="utf-8",
        extra="ignore"
    )

settings = Settings()

def get_settings() -> Settings:
    return settings