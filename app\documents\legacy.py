from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.document import Document, ComplianceStatus, RiskLevel, MitigationStatus
from app.models.document_version import DocumentVersion
from app.schemas.document import (
    DocumentCreate, DocumentUpdate, DocumentComplianceUpdate,
    DocumentMitigationUpdate, DocumentDisasterUpdate
)

class DocumentService:
    @staticmethod
    def create_document(db: Session, document: DocumentCreate, user_id: int) -> Document:
        db_document = Document(**document.model_dump(), user_id=user_id)
        db.add(db_document)
        db.commit()
        
        # Create initial version
        version = DocumentVersion(
            document_id=db_document.id,
            version_number=1,
            created_by_id=user_id,
            change_type="CREATE",
            change_summary="Initial document creation",
            **{k: v for k, v in document.model_dump().items() if k != "user_id"}
        )
        db.add(version)
        db.commit()
        db.refresh(db_document)
        return db_document

    @staticmethod
    def create_version(
        db: Session,
        document: Document,
        user_id: int,
        change_type: str,
        change_summary: str,
        updated_fields: Dict[str, Any]
    ) -> DocumentVersion:
        """Create a new version of a document."""
        # Increment document version
        document.current_version += 1
        
        # Create version record
        version = DocumentVersion(
            document_id=document.id,
            version_number=document.current_version,
            created_by_id=user_id,
            change_type=change_type,
            change_summary=change_summary,
            # Copy all fields from current document
            title=document.title,
            description=document.description,
            fema_id=document.fema_id,
            compliance_status=document.compliance_status,
            compliance_percentage=document.compliance_percentage,
            compliance_notes=document.compliance_notes,
            last_compliance_check=document.last_compliance_check,
            due_date=document.due_date,
            risk_level=document.risk_level,
            disaster_number=document.disaster_number,
            disaster_type=document.disaster_type,
            incident_period_start=document.incident_period_start,
            incident_period_end=document.incident_period_end,
            declaration_date=document.declaration_date,
            mitigation_status=document.mitigation_status,
            mitigation_plan_expires=document.mitigation_plan_expires,
            mitigation_funding_amount=document.mitigation_funding_amount,
            requirements=document.requirements,
            last_review_date=document.last_review_date,
            next_review_date=document.next_review_date,
            review_frequency_days=document.review_frequency_days,
            jurisdiction=document.jurisdiction,
            state_code=document.state_code,
            county_code=document.county_code
        )
        
        db.add(version)
        return version

    @staticmethod
    def get_document(db: Session, document_id: int, user_id: int) -> Optional[Document]:
        return db.query(Document).filter(
            and_(Document.id == document_id, Document.user_id == user_id)
        ).first()

    @staticmethod
    def get_document_version(
        db: Session,
        document_id: int,
        version_number: int,
        user_id: int
    ) -> Optional[DocumentVersion]:
        return db.query(DocumentVersion).join(Document).filter(
            and_(
                Document.id == document_id,
                Document.user_id == user_id,
                DocumentVersion.version_number == version_number
            )
        ).first()

    @staticmethod
    def get_document_versions(
        db: Session,
        document_id: int,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[DocumentVersion]:
        return db.query(DocumentVersion).join(Document).filter(
            and_(
                Document.id == document_id,
                Document.user_id == user_id
            )
        ).order_by(DocumentVersion.version_number.desc())\
         .offset(skip).limit(limit).all()

    @staticmethod
    def get_documents(
        db: Session,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        compliance_status: Optional[ComplianceStatus] = None,
        risk_level: Optional[RiskLevel] = None,
        due_before: Optional[datetime] = None
    ) -> List[Document]:
        query = db.query(Document).filter(Document.user_id == user_id)
        
        if compliance_status:
            query = query.filter(Document.compliance_status == compliance_status)
        if risk_level:
            query = query.filter(Document.risk_level == risk_level)
        if due_before:
            query = query.filter(Document.due_date <= due_before)
            
        return query.offset(skip).limit(limit).all()

    @staticmethod
    def update_document(
        db: Session,
        document_id: int,
        user_id: int,
        document_update: DocumentUpdate
    ) -> Optional[Document]:
        db_document = DocumentService.get_document(db, document_id, user_id)
        if db_document:
            update_data = document_update.model_dump(exclude_unset=True)
            
            # Create new version before applying updates
            DocumentService.create_version(
                db=db,
                document=db_document,
                user_id=user_id,
                change_type="UPDATE",
                change_summary="General document update",
                updated_fields=update_data
            )
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_document, field, value)
            
            db.commit()
            db.refresh(db_document)
        return db_document

    @staticmethod
    def update_compliance(
        db: Session,
        document_id: int,
        user_id: int,
        compliance_update: DocumentComplianceUpdate
    ) -> Optional[Document]:
        db_document = DocumentService.get_document(db, document_id, user_id)
        if db_document:
            update_data = compliance_update.model_dump()
            
            # Create new version
            DocumentService.create_version(
                db=db,
                document=db_document,
                user_id=user_id,
                change_type="COMPLIANCE_UPDATE",
                change_summary="Updated compliance information",
                updated_fields=update_data
            )
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_document, field, value)
            db_document.last_compliance_check = datetime.utcnow()
            
            db.commit()
            db.refresh(db_document)
        return db_document

    @staticmethod
    def update_mitigation(
        db: Session,
        document_id: int,
        user_id: int,
        mitigation_update: DocumentMitigationUpdate
    ) -> Optional[Document]:
        db_document = DocumentService.get_document(db, document_id, user_id)
        if db_document:
            update_data = mitigation_update.model_dump()
            
            # Create new version
            DocumentService.create_version(
                db=db,
                document=db_document,
                user_id=user_id,
                change_type="MITIGATION_UPDATE",
                change_summary="Updated mitigation plan",
                updated_fields=update_data
            )
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_document, field, value)
            
            db.commit()
            db.refresh(db_document)
        return db_document

    @staticmethod
    def update_disaster_info(
        db: Session,
        document_id: int,
        user_id: int,
        disaster_update: DocumentDisasterUpdate
    ) -> Optional[Document]:
        db_document = DocumentService.get_document(db, document_id, user_id)
        if db_document:
            update_data = disaster_update.model_dump()
            
            # Create new version
            DocumentService.create_version(
                db=db,
                document=db_document,
                user_id=user_id,
                change_type="DISASTER_UPDATE",
                change_summary="Updated disaster declaration information",
                updated_fields=update_data
            )
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_document, field, value)
            
            db.commit()
            db.refresh(db_document)
        return db_document

    @staticmethod
    def get_documents_due_for_review(
        db: Session,
        user_id: int,
        days_threshold: int = 30
    ) -> List[Document]:
        threshold_date = datetime.utcnow() + timedelta(days=days_threshold)
        return db.query(Document).filter(
            and_(
                Document.user_id == user_id,
                Document.next_review_date <= threshold_date
            )
        ).all()

    @staticmethod
    def get_expiring_mitigation_plans(
        db: Session,
        user_id: int,
        days_threshold: int = 90
    ) -> List[Document]:
        threshold_date = datetime.utcnow() + timedelta(days=days_threshold)
        return db.query(Document).filter(
            and_(
                Document.user_id == user_id,
                Document.mitigation_plan_expires <= threshold_date,
                Document.mitigation_status != MitigationStatus.EXPIRED
            )
        ).all()

    @staticmethod
    def get_document_stats(db: Session, user_id: int) -> Dict:
        total = db.query(Document).filter(Document.user_id == user_id).count()
        
        compliance_stats = {
            status: db.query(Document).filter(
                and_(
                    Document.user_id == user_id,
                    Document.compliance_status == status
                )
            ).count()
            for status in ComplianceStatus
        }
        
        risk_stats = {
            level: db.query(Document).filter(
                and_(
                    Document.user_id == user_id,
                    Document.risk_level == level
                )
            ).count()
            for level in RiskLevel
        }
        
        avg_compliance = db.query(func.avg(Document.compliance_percentage))\
            .filter(Document.user_id == user_id)\
            .scalar() or 0.0
        
        due_review = len(DocumentService.get_documents_due_for_review(db, user_id))
        expiring_plans = len(DocumentService.get_expiring_mitigation_plans(db, user_id))
        
        return {
            "total_documents": total,
            "compliance_stats": compliance_stats,
            "risk_level_stats": risk_stats,
            "average_compliance_percentage": float(avg_compliance),
            "documents_due_review": due_review,
            "expiring_mitigation_plans": expiring_plans
        }
