from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.core.deps import get_db, get_current_active_user
from app.services.cost_service import CostService
from app.schemas.cost import (
    CostCreate, CostResponse,
    BudgetCreate, BudgetResponse,
    CostBenefitCreate, CostBenefitResponse
)
from app.models.user import User

router = APIRouter()

@router.post("/costs/", response_model=CostResponse)
def create_cost(
    cost: CostCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = CostService(db)
    return service.create_cost(cost)

@router.get("/costs/", response_model=List[CostResponse])
def list_costs(
    project_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = CostService(db)
    return service.get_costs(project_id=project_id, skip=skip, limit=limit)

@router.post("/budgets/", response_model=BudgetResponse)
def create_budget(
    budget: BudgetCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = CostService(db)
    return service.create_budget(budget)

@router.get("/budgets/", response_model=List[BudgetResponse])
def list_budgets(
    project_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = CostService(db)
    return service.get_budgets(project_id=project_id, skip=skip, limit=limit)

@router.post("/cost-benefits/", response_model=CostBenefitResponse)
def create_cost_benefit(
    cost_benefit: CostBenefitCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = CostService(db)
    return service.create_cost_benefit(cost_benefit)

@router.get("/cost-benefits/", response_model=List[CostBenefitResponse])
def list_cost_benefits(
    project_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    service = CostService(db)
    return service.get_cost_benefits(project_id=project_id, skip=skip, limit=limit)
