"""Simple test for document version control functionality."""

import os
import shutil
from pathlib import Path
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('version_control_test.log'),
        logging.StreamHandler()
    ]
)

class SimpleVersionControl:
    """Basic version control implementation for testing."""
    
    def __init__(self, base_path: str = "test_storage"):
        self.base_path = Path(base_path)
        self.versions_path = self.base_path / "versions"
        self.versions_path.mkdir(parents=True, exist_ok=True)
    
    def create_version(self, document_id: str, source_path: Path, author: str, changes: list[str]) -> dict:
        """Create a new version of a document."""
        version_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        version_path = self.versions_path / f"{document_id}_{version_id}"
        
        # Copy file to version directory
        shutil.copy2(source_path, version_path)
        
        # Create metadata
        metadata = {
            "version_id": version_id,
            "document_id": document_id,
            "timestamp": datetime.now().isoformat(),
            "author": author,
            "changes": changes,
            "path": str(version_path)
        }
        
        # Save metadata to file
        metadata_path = self.versions_path / f"{document_id}_{version_id}.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, default=str)
        
        logging.info(f"Created version {version_id} for document {document_id}")
        return metadata
    
    def get_version(self, document_id: str, version_id: str) -> Path:
        """Get the file path for a specific version."""
        version_path = self.versions_path / f"{document_id}_{version_id}"
        if not version_path.exists():
            raise FileNotFoundError(f"Version {version_id} not found for document {document_id}")
        return version_path
    
    def get_metadata(self, document_id: str, version_id: str) -> dict:
        """Get metadata for a specific version."""
        metadata_path = self.versions_path / f"{document_id}_{version_id}.json"
        if not metadata_path.exists():
            raise FileNotFoundError(f"Metadata not found for version {version_id}")
        with open(metadata_path, 'r') as f:
            return json.load(f)
    
    def cleanup(self):
        """Clean up test files."""
        if self.base_path.exists():
            shutil.rmtree(self.base_path)

def create_test_document(content: str) -> Path:
    """Create a test document with specified content."""
    doc_path = Path("test_documents") / "test.txt"
    doc_path.parent.mkdir(exist_ok=True)
    doc_path.write_text(content)
    return doc_path

def test_version_control():
    """Test the version control system."""
    try:
        # Initialize version control
        vc = SimpleVersionControl()
        
        # Create test document
        doc_id = "test_doc_001"
        doc_path = create_test_document("Initial version of the document")
        
        # Create first version
        v1_metadata = vc.create_version(
            document_id=doc_id,
            source_path=doc_path,
            author="test_user",
            changes=["Initial document creation"]
        )
        logging.info(f"Created version 1: {v1_metadata['version_id']}")
        
        # Update document and create second version
        doc_path.write_text("Updated version of the document with changes")
        v2_metadata = vc.create_version(
            document_id=doc_id,
            source_path=doc_path,
            author="test_user",
            changes=["Updated content"]
        )
        logging.info(f"Created version 2: {v2_metadata['version_id']}")
        
        # Verify versions exist and get their content
        v1_path = vc.get_version(doc_id, v1_metadata['version_id'])
        v2_path = vc.get_version(doc_id, v2_metadata['version_id'])
        
        logging.info(f"Version 1 content: {v1_path.read_text()}")
        logging.info(f"Version 2 content: {v2_path.read_text()}")
        
        # Compare versions
        v1_meta = vc.get_metadata(doc_id, v1_metadata['version_id'])
        v2_meta = vc.get_metadata(doc_id, v2_metadata['version_id'])
        logging.info(f"Version comparison: {v2_meta['changes']}")
        
        # Clean up
        vc.cleanup()
        shutil.rmtree("test_documents")
        logging.info("Test completed successfully")
        
    except Exception as e:
        logging.error(f"Test failed: {str(e)}")
        raise

if __name__ == "__main__":
    test_version_control() 