from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional, Any, Union, Dict, List, Tuple
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON><PERSON>Bearer
from jose import JW<PERSON>rror, jwt, ExpiredSignatureError
from sqlalchemy.orm import Session
from app.core.config import settings, get_settings
from app.core.database import get_db
from app.models.user import User
from app.core.password import verify_password, validate_password_complexity
import logging
import secrets
import base64
import pyotp
import qrcode
from io import BytesIO
from passlib.context import CryptContext
from app.models.audit_log import AuditLog

logger = logging.getLogger(__name__)

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")

settings = get_settings()

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def generate_totp_secret() -> str:
    """
    Generate a random secret for <PERSON>TP (Time-based One-Time Password).
    
    Returns:
        str: A base32-encoded secret key for TOTP.
    """
    # Generate 20 random bytes (160 bits)
    random_bytes = secrets.token_bytes(20)
    # Convert to base32
    return base64.b32encode(random_bytes).decode('utf-8')

def generate_totp_uri(secret: str, username: str) -> str:
    """
    Generate a TOTP URI for QR code generation.
    
    Args:
        secret (str): The TOTP secret key.
        username (str): The username for the TOTP URI.
    
    Returns:
        str: The TOTP URI.
    """
    totp = pyotp.TOTP(secret)
    return totp.provisioning_uri(
        username,
        issuer_name="ComplianceMax"
    )

def verify_totp(secret: str, token: str) -> bool:
    """
    Verify a TOTP token.
    
    Args:
        secret (str): The TOTP secret key.
        token (str): The token to verify.
    
    Returns:
        bool: True if the token is valid, False otherwise.
    """
    totp = pyotp.TOTP(secret)
    return totp.verify(token)

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """
    Authenticate a user by verifying their username and password.
    
    Args:
        db (Session): The database session.
        username (str): The username to authenticate.
        password (str): The password to verify.
    
    Returns:
        Optional[User]: The authenticated user if successful, None otherwise.
    """
    user = db.query(User).filter(User.username == username).first()
    if not user:
        logger.warning(f"Authentication failed: User {username} not found")
        return None
    if not verify_password(password, user.hashed_password):
        logger.warning(f"Authentication failed: Incorrect password for user {username}")
        return None
    return user

def create_access_token(
    subject: Union[str, Any], expires_delta: timedelta = None
) -> str:
    """Create JWT access token."""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            logger.warning("Token payload does not contain 'sub' field")
            raise credentials_exception
    except ExpiredSignatureError:
        logger.warning("Token has expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except JWTError as e:
        logger.warning(f"Invalid token: {str(e)}")
        raise credentials_exception

    query = db.query(User).filter(User.username == username)
    user = query.first()
    if user is None:
        logger.warning(f"User not found: {username}")
        raise credentials_exception
    return user

def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    if not current_user.is_active:
        logger.warning(f"Inactive user attempted access: {current_user.username}")
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

class SecurityManager:
    """Handles security-related functionality including access control and audit logging."""
    
    def __init__(self, db: Session):
        self.db = db
        
    def validate_access(self, user_id: int, resource_type: str, resource_id: str, action: str) -> bool:
        """
        Validate if a user has access to perform an action on a resource.
        
        Args:
            user_id: The ID of the user
            resource_type: Type of resource (e.g., 'document', 'checklist')
            resource_id: ID of the resource
            action: Action being performed (e.g., 'read', 'write', 'delete')
        """
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                return False
                
            # Admin users have full access
            if user.is_admin:
                return True
                
            # Check user roles and permissions
            if not user.is_active:
                return False
                
            # Implement role-based access control
            allowed_actions = self._get_allowed_actions(user, resource_type)
            has_access = action in allowed_actions
            
            # Log access attempt
            self.audit_log(
                action="access_check",
                user_id=user_id,
                details={
                    'resource_type': resource_type,
                    'resource_id': resource_id,
                    'action': action,
                    'granted': has_access
                }
            )
            
            return has_access
            
        except Exception as e:
            logger.error(f"Error validating access: {str(e)}")
            return False
            
    def _get_allowed_actions(self, user: User, resource_type: str) -> List[str]:
        """Get allowed actions for a user based on their roles."""
        # Define role-based permissions
        role_permissions = {
            'admin': ['read', 'write', 'delete', 'approve'],
            'editor': ['read', 'write'],
            'viewer': ['read']
        }
        
        # Get user's role (implement based on your user model)
        user_role = user.role if hasattr(user, 'role') else 'viewer'
        
        return role_permissions.get(user_role, ['read'])
        
    def audit_log(self, action: str, user_id: int, details: Dict) -> None:
        """
        Log security-relevant actions to the audit log.
        
        Args:
            action: The action being performed
            user_id: The ID of the user performing the action
            details: Additional details about the action
        """
        try:
            log_entry = AuditLog(
                timestamp=datetime.utcnow(),
                user_id=user_id,
                action=action,
                details=details
            )
            
            self.db.add(log_entry)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error creating audit log: {str(e)}")
            self.db.rollback()
            
    def validate_input(self, input_data: Dict, schema: Dict) -> Tuple[bool, Optional[str]]:
        """
        Validate input data against a schema.
        
        Args:
            input_data: The data to validate
            schema: The validation schema
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Implement input validation logic based on schema
            for field, rules in schema.items():
                if field not in input_data and rules.get('required', False):
                    return False, f"Missing required field: {field}"
                    
                if field in input_data:
                    value = input_data[field]
                    
                    # Type validation
                    expected_type = rules.get('type')
                    if expected_type and not isinstance(value, expected_type):
                        return False, f"Invalid type for {field}"
                    
                    # Length validation
                    min_length = rules.get('min_length')
                    max_length = rules.get('max_length')
                    if isinstance(value, (str, list)):
                        if min_length and len(value) < min_length:
                            return False, f"{field} is too short"
                        if max_length and len(value) > max_length:
                            return False, f"{field} is too long"
                    
                    # Pattern validation
                    pattern = rules.get('pattern')
                    if pattern and isinstance(value, str):
                        import re
                        if not re.match(pattern, value):
                            return False, f"Invalid format for {field}"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating input: {str(e)}")
            return False, str(e)
            
    def sanitize_output(self, data: Dict) -> Dict:
        """
        Sanitize output data to prevent sensitive information disclosure.
        
        Args:
            data: The data to sanitize
        
        Returns:
            Sanitized data
        """
        # Define sensitive fields to remove/mask
        sensitive_fields = ['password', 'token', 'secret', 'key']
        
        def _sanitize_value(value):
            if isinstance(value, dict):
                return self.sanitize_output(value)
            elif isinstance(value, list):
                return [_sanitize_value(v) for v in value]
            return value
        
        sanitized = {}
        for key, value in data.items():
            if key.lower() in sensitive_fields:
                sanitized[key] = '******'
            else:
                sanitized[key] = _sanitize_value(value)
                
        return sanitized