import { useState, useEffect } from 'react';
import axios from 'axios';

interface DocumentChange {
  timestamp: string;
  user_id: number;
  document_id: string;
  document_type: string;
  action: string;
}

interface Change {
  timestamp: string;
  user_id: number;
  type: string;
  details: string;
  comment?: string;
  changes?: Record<string, { from: any; to: any }>;
  findings_diff?: {
    added: string[];
    removed: string[];
    modified: string[];
  };
}

interface AuditTrail {
  review_id: number;
  creation_date?: string;
  last_modified?: string;
  changes: Change[];
  document_changes?: DocumentChange[];
}

interface HistoryEntry {
  id: number;
  review_id: number;
  user_id: number;
  change_type: string;
  previous_state?: Record<string, any>;
  new_state: Record<string, any>;
  timestamp: string;
  comment?: string;
}

interface UseHistoryResult {
  history: HistoryEntry[];
  auditTrail: AuditTrail | null;
  loading: boolean;
  error: Error | null;
  fetchHistory: (reviewId: number, startDate?: string, endDate?: string) => Promise<void>;
  fetchAuditTrail: (reviewId: number, includeDocuments?: boolean) => Promise<void>;
}

export const useHistory = (): UseHistoryResult => {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [auditTrail, setAuditTrail] = useState<AuditTrail | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchHistory = async (
    reviewId: number,
    startDate?: string,
    endDate?: string
  ) => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (startDate) params.append('start_date', startDate);
      if (endDate) params.append('end_date', endDate);

      const response = await axios.get(
        `/api/v1/history/review/${reviewId}?${params.toString()}`
      );
      setHistory(response.data);
      setError(null);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAuditTrail = async (
    reviewId: number,
    includeDocuments: boolean = true
  ) => {
    try {
      setLoading(true);
      const response = await axios.get(
        `/api/v1/history/audit/${reviewId}?include_documents=${includeDocuments}`
      );
      setAuditTrail(response.data);
      setError(null);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  return {
    history,
    auditTrail,
    loading,
    error,
    fetchHistory,
    fetchAuditTrail
  };
};
