from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional

class FloodMapBase(BaseModel):
    firm_panel_id: str
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    flood_zone: Optional[str] = None
    map_scale: Optional[int] = None
    firmette_url: Optional[str] = None

class FloodMapCreate(FloodMapBase):
    pass

class FloodMapResponse(FloodMapBase):
    id: int
    effective_date: Optional[datetime] = None
    last_updated: datetime
    user_id: int

    class Config:
        from_attributes = True

class FirmetteRequestCreate(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    address: Optional[str] = None

class FirmetteRequestResponse(BaseModel):
    id: int
    latitude: float
    longitude: float
    address: Optional[str]
    status: str
    created_at: datetime
    completed_at: Optional[datetime]
    flood_map_id: Optional[int]
    user_id: int

    class Config:
        from_attributes = True
