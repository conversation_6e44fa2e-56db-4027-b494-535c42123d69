"""
Test script for document version control functionality.
"""

import logging
import os
from datetime import datetime
from pathlib import Path

from app.documents.version_control import DocumentVersionControl

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('version_control_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_version_control():
    """Test the document version control functionality."""
    try:
        # Initialize version control
        version_control = DocumentVersionControl()
        logger.info("Initialized version control system")

        # Create test document
        doc_id = f"test_doc_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_content = "This is a test document."
        test_file = Path("test_document.txt")
        test_file.write_text(test_content)
        
        logger.info(f"Created test document: {test_file}")

        # Create first version
        version1 = version_control.create_version(
            document_id=doc_id,
            source_path=test_file,
            author="test_user",
            changes=["Initial version"],
            tags=["test"]
        )
        logger.info(f"Created version 1: {version1.version_id}")

        # Create second version
        test_file.write_text("This is an updated test document.")
        version2 = version_control.create_version(
            document_id=doc_id,
            source_path=test_file,
            author="test_user",
            changes=["Updated content"],
            tags=["test", "updated"]
        )
        logger.info(f"Created version 2: {version2.version_id}")

        # List versions
        versions = version_control.list_versions(doc_id)
        logger.info(f"Found {len(versions)} versions:")
        for version in versions:
            logger.info(f"Version {version.version_id}:")
            logger.info(f"  Created at: {version.timestamp}")
            logger.info(f"  Changes: {version.changes}")
            logger.info(f"  Tags: {version.tags}")

        # Compare versions
        comparison = version_control.compare_versions(doc_id, version1.version_id, version2.version_id)
        logger.info("Version comparison:")
        logger.info(f"  Size difference: {comparison['differences']['size']} bytes")
        logger.info(f"  Changes: {comparison['differences']['changes']}")
        logger.info(f"  Checksum different: {comparison['differences']['checksum_different']}")

        # Restore first version
        restore_path = Path("restored_version.txt")
        version_control.restore_version(doc_id, version1.version_id, restore_path)
        logger.info(f"Restored version 1 to: {restore_path}")
        logger.info(f"Restored content: {restore_path.read_text()}")

    except Exception as e:
        logger.error(f"Test failed: {str(e)}", exc_info=True)
        raise
    finally:
        # Cleanup
        if test_file.exists():
            test_file.unlink()
        if restore_path.exists():
            restore_path.unlink()
        logger.info("Cleanup completed")

if __name__ == "__main__":
    test_version_control() 