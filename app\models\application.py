"""Application model for managing FEMA applications."""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base

class Application(Base):
    """Model for FEMA applications."""
    __tablename__ = "applications"

    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    status = Column(String, nullable=False, default="pending")
    application_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

    # Relationships
    document_links = relationship("DocumentLink", back_populates="application", cascade="all, delete-orphan")
    compliance_results = relationship("ComplianceResult", back_populates="application", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Application {self.id} ({self.name})>"