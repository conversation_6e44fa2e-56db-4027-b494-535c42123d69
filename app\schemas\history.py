from pydantic import BaseModel
from datetime import datetime
from typing import Dict, List, Optional
from app.models.history import ChangeType

class HistoryResponse(BaseModel):
    id: int
    review_id: int
    user_id: int
    change_type: ChangeType
    previous_state: Optional[Dict] = None
    new_state: Dict
    timestamp: datetime
    comment: Optional[str] = None

    class Config:
        from_attributes = True

class DocumentChange(BaseModel):
    timestamp: str
    user_id: int
    document_id: str
    document_type: str
    action: str

class Change(BaseModel):
    timestamp: str
    user_id: int
    type: ChangeType
    details: str
    comment: Optional[str] = None
    changes: Optional[Dict] = None
    findings_diff: Optional[Dict] = None

class AuditTrailResponse(BaseModel):
    review_id: int
    creation_date: Optional[str]
    last_modified: Optional[str]
    changes: List[Change]
    document_changes: Optional[List[DocumentChange]]
