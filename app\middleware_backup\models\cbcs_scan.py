from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, JSON, Enum
from sqlalchemy.orm import relationship
from app.models.base import Base, TimestampMixin
from enum import Enum as PyEnum

class ScanStatus(str, PyEnum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class CBCSScan(Base, TimestampMixin):
    __tablename__ = "cbcs_scans"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    status = Column(Enum(ScanStatus), nullable=False, default=ScanStatus.PENDING)
    scan_date = Column(DateTime, nullable=True)
    results = Column(JSON, nullable=True)
    error_message = Column(String, nullable=True)

    document = relationship("Document", back_populates="cbcs_scans") 