"""
Caching system for ComplianceMax.
"""

import json
import pickle
import time
import hashlib
import asyncio
from typing import Any, Optional, Union, Dict, List, Tuple
from functools import wraps
import redis
from redis.connection import ConnectionPool
from app.core.config import settings
from app.core.logging import get_logger
from app.core.metrics import MetricsCollector

logger = get_logger(__name__)

class CacheBackend:
    """Base cache backend."""
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        raise NotImplementedError
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> None:
        """Set value in cache."""
        raise NotImplementedError
    
    async def delete(self, key: str) -> None:
        """Delete value from cache."""
        raise NotImplementedError
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        raise NotImplementedError
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        raise NotImplementedError

class RedisCache(CacheBackend):
    """Redis cache implementation with connection pooling."""
    
    _pool: Optional[ConnectionPool] = None
    
    def __init__(self):
        """Initialize Redis connection pool."""
        if RedisCache._pool is None:
            RedisCache._pool = ConnectionPool(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                db=settings.REDIS_DB,
                ssl=settings.REDIS_SSL,
                socket_timeout=settings.REDIS_TIMEOUT,
                decode_responses=False,  # Keep binary for pickle
                max_connections=settings.REDIS_POOL_SIZE,
                health_check_interval=30
            )
        
        self.redis = redis.Redis(
            connection_pool=RedisCache._pool
        )
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis with error handling."""
        start_time = time.time()
        success = True
        
        try:
            value = self.redis.get(key)
            if value is None:
                return None
            return pickle.loads(value)
        except pickle.UnpicklingError as e:
            success = False
            logger.error(f"Redis unpickling error for key {key}: {str(e)}")
            await self.delete(key)  # Delete corrupted data
            return None
        except redis.RedisError as e:
            success = False
            logger.error(f"Redis get error for key {key}: {str(e)}")
            return None
        except Exception as e:
            success = False
            logger.error(f"Unexpected error in cache get for key {key}: {str(e)}")
            return None
        finally:
            duration = time.time() - start_time
            MetricsCollector.track_storage_operation(
                operation="cache_get",
                backend="redis",
                duration=duration,
                success=success
            )
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> None:
        """Set value in Redis with compression for large values."""
        start_time = time.time()
        success = True
        
        try:
            serialized = pickle.dumps(value)
            if ttl:
                self.redis.setex(key, ttl, serialized)
            else:
                self.redis.set(key, serialized)
        except (pickle.PicklingError, TypeError) as e:
            success = False
            logger.error(f"Redis serialization error for key {key}: {str(e)}")
        except redis.RedisError as e:
            success = False
            logger.error(f"Redis set error for key {key}: {str(e)}")
        except Exception as e:
            success = False
            logger.error(f"Unexpected error in cache set for key {key}: {str(e)}")
        finally:
            duration = time.time() - start_time
            MetricsCollector.track_storage_operation(
                operation="cache_set",
                backend="redis",
                duration=duration,
                success=success
            )
    
    async def delete(self, key: str) -> None:
        """Delete value from Redis."""
        try:
            self.redis.delete(key)
        except redis.RedisError as e:
            logger.error(f"Redis delete error for key {key}: {str(e)}")
            MetricsCollector.track_error("cache", "delete_error")
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis."""
        try:
            return bool(self.redis.exists(key))
        except redis.RedisError as e:
            logger.error(f"Redis exists error for key {key}: {str(e)}")
            MetricsCollector.track_error("cache", "exists_error")
            return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        try:
            self.redis.flushdb()
        except redis.RedisError as e:
            logger.error(f"Redis clear error: {str(e)}")
            MetricsCollector.track_error("cache", "clear_error")

class Cache:
    """Cache manager."""
    
    def __init__(self):
        """Initialize cache manager."""
        self.backend = RedisCache()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        start_time = time.time()
        success = True
        
        try:
            value = await self.backend.get(key)
            return value
        except Exception as e:
            success = False
            logger.error(f"Cache get error: {str(e)}")
            return None
        finally:
            duration = time.time() - start_time
            MetricsCollector.track_storage_operation(
                operation="cache_get",
                backend="redis",
                duration=duration,
                success=success
            )
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> None:
        """Set value in cache."""
        start_time = time.time()
        success = True
        
        try:
            await self.backend.set(key, value, ttl)
        except Exception as e:
            success = False
            logger.error(f"Cache set error: {str(e)}")
        finally:
            duration = time.time() - start_time
            MetricsCollector.track_storage_operation(
                operation="cache_set",
                backend="redis",
                duration=duration,
                success=success
            )
    
    async def delete(self, key: str) -> None:
        """Delete value from cache."""
        await self.backend.delete(key)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        return await self.backend.exists(key)
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        await self.backend.clear()

# Create global cache instance
cache = Cache()

def cached(ttl: Optional[int] = None):
    """Cache decorator."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            key = f"{func.__name__}:{args}:{kwargs}"
            
            # Try to get from cache
            cached_value = await cache.get(key)
            if cached_value is not None:
                return cached_value
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result
            await cache.set(key, result, ttl)
            
            return result
        return wrapper
    return decorator

# Cache helper for document processing
async def get_document_cache(document_id: str) -> Optional[Dict]:
    """Get document processing result from cache if available."""
    if not settings.ENABLE_DOCUMENT_CACHING:
        return None
        
    cache_key = f"doc_processing:{document_id}"
    cache = RedisCache()
    return await cache.get(cache_key)

async def set_document_cache(document_id: str, result: Dict) -> bool:
    """Cache document processing result."""
    if not settings.ENABLE_DOCUMENT_CACHING:
        return False
        
    cache_key = f"doc_processing:{document_id}"
    cache = RedisCache()
    return await cache.set(cache_key, result, settings.DOCUMENT_CACHE_TTL)

class RateLimiter:
    """Advanced in-memory rate limiter with IP whitelisting and dynamic limits."""

    def __init__(self):
        self.requests: Dict[str, List[float]] = {}
        self.blocked_keys: Dict[str, float] = {}  # key -> block expiration time
        self.whitelisted_ips: List[str] = settings.RATE_LIMIT_WHITELIST
        
        # Start background cleanup task
        asyncio.create_task(self._cleanup_old_requests())
    
    async def _cleanup_old_requests(self):
        """Periodically clean up old requests and expired blocks."""
        while True:
            try:
                current = time.time()
                
                # Clean up old requests
                for key in list(self.requests.keys()):
                    self.requests[key] = [t for t in self.requests[key] if current - t < 3600]  # Keep last hour
                    if not self.requests[key]:
                        del self.requests[key]
                
                # Clean up expired blocks
                for key in list(self.blocked_keys.keys()):
                    if current > self.blocked_keys[key]:
                        del self.blocked_keys[key]
                
                logger.debug(f"Rate limiter cleanup: {len(self.requests)} active keys, {len(self.blocked_keys)} blocked keys")
            except Exception as e:
                logger.error(f"Error during rate limiter cleanup: {str(e)}")
            
            # Run cleanup every 5 minutes
            await asyncio.sleep(300)
    
    def _is_whitelisted(self, key: str) -> bool:
        """Check if a key (typically an IP) is whitelisted."""
        # Extract IP from key if it contains IP
        ip = key.split(":")[-1] if ":" in key else key
        return ip in self.whitelisted_ips or settings.DEBUG
    
    def _get_limit_for_endpoint(self, endpoint: str) -> Tuple[int, int]:
        """Get rate limit for a specific endpoint type."""
        endpoint_type = endpoint.split(":")[0] if ":" in endpoint else endpoint
        
        # Default limits
        default_limit = 60  # requests
        default_period = 60  # seconds
        
        # Different limits for different endpoint types
        if endpoint_type == "login" or endpoint_type == "register":
            return 5, 60  # 5 requests per minute for auth endpoints
        elif endpoint_type == "password_reset":
            return 3, 300  # 3 requests per 5 minutes for password reset
        elif "upload" in endpoint_type:
            return 10, 60  # 10 uploads per minute
        elif "download" in endpoint_type or "document" in endpoint_type:
            return 30, 60  # 30 downloads/document requests per minute
        
        return default_limit, default_period
    
    def _generate_key_hash(self, key: str) -> str:
        """Generate a hash of the key to avoid storing sensitive information."""
        # Add salt to prevent rainbow table attacks
        salted_key = f"{key}:{settings.SECRET_KEY[:8]}"
        return hashlib.sha256(salted_key.encode()).hexdigest()[:16]
    
    async def check_rate_limit(
        self, 
        key: str, 
        limit: Optional[int] = None, 
        period: Optional[int] = None,
        endpoint: Optional[str] = None
    ) -> Tuple[bool, int]:
        """Check if rate limit is exceeded.

        Args:
            key: Rate limit key (e.g., IP address or user ID)
            limit: Maximum number of requests (optional)
            period: Time period in seconds (optional)
            endpoint: Type of endpoint being accessed (optional)

        Returns:
            Tuple of (is_allowed, retry_after)
        """
        # Skip rate limiting for whitelisted IPs
        if self._is_whitelisted(key):
            return True, 0
        
        # Check if key is currently blocked
        current = time.time()
        hashed_key = self._generate_key_hash(key)
        
        if hashed_key in self.blocked_keys:
            if current < self.blocked_keys[hashed_key]:
                retry_after = int(self.blocked_keys[hashed_key] - current)
                return False, retry_after
            else:
                # Block expired, remove it
                del self.blocked_keys[hashed_key]
        
        # Get dynamic limits if not provided
        if endpoint and (limit is None or period is None):
            default_limit, default_period = self._get_limit_for_endpoint(endpoint)
            limit = limit or default_limit
            period = period or default_period
        
        # Ensure values are set
        limit = limit or 60  # Default: 60 requests 
        period = period or 60  # Default: per minute
        
        # Initialize request list for this key if it doesn't exist
        if hashed_key not in self.requests:
            self.requests[hashed_key] = []
        
        # Clean up old requests for this key
        self.requests[hashed_key] = [t for t in self.requests[hashed_key] if current - t < period]
        
        # Check if limit is exceeded
        if len(self.requests[hashed_key]) >= limit:
            # Calculate when the oldest request will expire
            oldest_request = self.requests[hashed_key][0]
            retry_after = int(period - (current - oldest_request))
            
            # If this is a repeated violation, block for progressively longer periods
            violation_count = len(self.requests[hashed_key]) - limit
            if violation_count > 5:  # Threshold for blocking
                # Block duration increases with violation count
                block_duration = min(3600, 60 * (2 ** (violation_count - 5)))  # Max 1 hour
                self.blocked_keys[hashed_key] = current + block_duration
                logger.warning(f"Rate limit key {hashed_key} blocked for {block_duration} seconds due to {violation_count} violations")
            
            return False, max(1, retry_after)
        
        # Record the current request
        self.requests[hashed_key].append(current)
        return True, 0
    
    async def reset_limits(self, key: str) -> bool:
        """Reset rate limits for a specific key."""
        try:
            hashed_key = self._generate_key_hash(key)
            if hashed_key in self.requests:
                del self.requests[hashed_key]
            if hashed_key in self.blocked_keys:
                del self.blocked_keys[hashed_key]
            return True
        except Exception as e:
            logger.error(f"Error resetting rate limits for {key}: {str(e)}")
            return False
            
    async def add_to_whitelist(self, ip: str) -> bool:
        """Add an IP to the whitelist."""
        if ip not in self.whitelisted_ips:
            self.whitelisted_ips.append(ip)
            return True
        return False
        
    async def remove_from_whitelist(self, ip: str) -> bool:
        """Remove an IP from the whitelist."""
        if ip in self.whitelisted_ips:
            self.whitelisted_ips.remove(ip)
            return True
        return False