<!DOCTYPE html>
<title></title><noscript>
<meta content="0; URL=https://policies.google.com/terms" http-equiv="refresh"></noscript>
<a href="https://policies.google.com/terms" id="link">https://policies.google.com/terms</a> 
<script nonce="qfk4KSjt5AKMQ0xTp4Dblw">
var url="https://policies.google.com/terms";
try{var curl=window.location.href;var match=curl.match(/\/intl\/([^\/]+)\/policies/);var locale=match&&match[1];var hl;var gl;if(locale){if(locale.indexOf("_")>0){var parts=locale.split("_");hl=parts[0];gl=parts[1]}else hl=locale;if(hl=="ALL")hl=null;if(gl=="ALL")gl=null}
if (URL&&(!hl||!gl)){ var cu=new URL(curl);hl=hl||cu.searchParams.get("hl");gl=gl||cu.searchParams.get("gl");}
if (URL&&curl.indexOf("authuser")!==-1){var cu=new URL(curl);var authuser=parseInt(cu.searchParams.get("authuser"),10);if(!isNaN(authuser))url=url.replace('.com/','.com/u/'+authuser+'/');}
if(!gl){var tld=location.hostname.split(".").pop().toLowerCase();if(tld&&tld.length==2)gl=tld;if(tld=="cn")url=url.replace(".com/",".cn/")}
if(hl&&gl)url+="?hl="+encodeURIComponent(hl)+"&gl="+encodeURIComponent(gl);else if(hl)url+="?hl="+encodeURIComponent(hl);else if(gl)url+="?gl="+encodeURIComponent(gl);var link=document.getElementById("link");if(link){link.innerText=url;link.href=url}}catch(e){}if(window.location.hash)url+=window.location.hash;window.location.href=url;
</script>