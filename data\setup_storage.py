import os
from pathlib import Path
import logging
from datetime import datetime
import shutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('storage_setup.log'),
        logging.StreamHandler()
    ]
)

class StorageSetup:
    def __init__(self, base_path: str = "storage"):
        self.base_path = Path(base_path)
        self.structure = {
            "documents": {
                "policies": {
                    "active": {},
                    "archived": {},
                    "drafts": {}
                },
                "evidence": {
                    "submitted": {},
                    "verified": {},
                    "rejected": {}
                },
                "reports": {
                    "monthly": {},
                    "quarterly": {},
                    "annual": {}
                }
            },
            "backups": {
                "daily": {},
                "weekly": {},
                "monthly": {}
            },
            "temp": {},
            "metadata": {
                "index": {},
                "logs": {}
            }
        }

    def create_directory_structure(self):
        """Create the complete directory structure."""
        try:
            # Create base directory
            self.base_path.mkdir(parents=True, exist_ok=True)
            logging.info(f"Created base directory at {self.base_path}")

            # Create .gitkeep files to maintain empty directories
            def create_structure(base: Path, structure: dict):
                for name, content in structure.items():
                    path = base / name
                    path.mkdir(parents=True, exist_ok=True)
                    (path / ".gitkeep").touch()
                    
                    if isinstance(content, dict):
                        create_structure(path, content)

            create_structure(self.base_path, self.structure)
            logging.info("Created complete directory structure")

            # Create README files in key directories
            self._create_readme_files()
            
            return True
        except Exception as e:
            logging.error(f"Error creating directory structure: {str(e)}")
            return False

    def _create_readme_files(self):
        """Create README files with directory information."""
        readme_content = """# Document Storage Directory

This directory contains {description}

## Structure
{structure}

## Usage Guidelines
1. Follow the naming conventions for files
2. Maintain proper metadata
3. Keep backups up to date
4. Follow retention policies

## Last Updated
{date}
"""

        readme_files = {
            "documents": "all system documents including policies, evidence, and reports",
            "backups": "system backups organized by frequency",
            "temp": "temporary files that should be cleaned up regularly",
            "metadata": "document metadata and system logs"
        }

        for dir_name, description in readme_files.items():
            path = self.base_path / dir_name / "README.md"
            structure = "\n".join([f"- {name}" for name in self.structure[dir_name].keys()])
            content = readme_content.format(
                description=description,
                structure=structure,
                date=datetime.now().strftime("%Y-%m-%d")
            )
            path.write_text(content)

    def setup_permissions(self):
        """Set up basic file permissions."""
        try:
            # Make all directories readable and writable by the owner
            for root, dirs, files in os.walk(self.base_path):
                for d in dirs:
                    os.chmod(Path(root) / d, 0o755)
                for f in files:
                    if f != ".gitkeep":  # Don't change gitkeep files
                        os.chmod(Path(root) / f, 0o644)
            
            logging.info("Set up directory and file permissions")
            return True
        except Exception as e:
            logging.error(f"Error setting permissions: {str(e)}")
            return False

    def create_backup_script(self):
        """Create a basic backup script."""
        backup_script = """#!/usr/bin/env python3
import os
import shutil
from datetime import datetime
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='backup.log'
)

def backup_documents(source_dir, backup_dir):
    try:
        # Create backup directory with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = Path(backup_dir) / f"backup_{timestamp}"
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # Copy files
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                if file.startswith('.'):  # Skip hidden files
                    continue
                    
                src_path = Path(root) / file
                rel_path = src_path.relative_to(source_dir)
                dst_path = backup_path / rel_path
                
                # Create destination directory if needed
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Copy file
                shutil.copy2(src_path, dst_path)
                logging.info(f"Backed up {src_path} to {dst_path}")
        
        logging.info(f"Backup completed successfully to {backup_path}")
        return True
    except Exception as e:
        logging.error(f"Backup failed: {str(e)}")
        return False

if __name__ == "__main__":
    source_dir = "storage/documents"
    backup_dir = "storage/backups/daily"
    backup_documents(source_dir, backup_dir)
"""

        script_path = self.base_path / "backup_script.py"
        script_path.write_text(backup_script)
        os.chmod(script_path, 0o755)
        logging.info("Created backup script")

def main():
    setup = StorageSetup()
    
    # Create directory structure
    if not setup.create_directory_structure():
        logging.error("Failed to create directory structure")
        return
    
    # Set up permissions
    if not setup.setup_permissions():
        logging.error("Failed to set up permissions")
        return
    
    # Create backup script
    setup.create_backup_script()
    
    logging.info("Storage setup completed successfully")

if __name__ == "__main__":
    main() 