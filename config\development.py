"""
Development Configuration Module

Development-specific configuration settings that override base settings.
"""

from .base import *

# Override database configuration for development
DATABASE = {
    'engine': 'sqlite',
    'name': 'compliancemax_dev.db',
    'path': BASE_DIR / 'compliancemax_dev.db'
}

# Enable debug mode
SECURITY['debug'] = True

# Disable SSL verification in development
SECURITY['ssl_verify'] = False

# Increase logging verbosity
LOGGING['loggers']['']['level'] = 'DEBUG'
for handler in LOGGING['handlers'].values():
    handler['level'] = 'DEBUG'

# Enable all features in development
FEATURES = {
    'enable_ai_matching': True,
    'enable_batch_processing': True,
    'enable_advanced_search': True,
    'enable_export': True,
    'enable_notifications': True
}

# Reduce rate limiting for development
API['rate_limit'] = {
    'enabled': False,
    'requests': 1000,
    'window': 60
}

# Use in-memory cache in development
CACHE = {
    'enabled': True,
    'backend': 'memory',
    'ttl': 300,  # 5 minutes
    'max_size': 100
}

# Reduce thresholds for easier testing
POLICY_MATCHER['confidence_threshold'] = 0.5
POLICY_MATCHER['min_similarity_score'] = 0.3

COMPLIANCE_CHECKER['confidence_threshold'] = 0.5
COMPLIANCE_CHECKER['required_match_percentage'] = 0.6

# Disable strict mode for requirement validation in development
REQUIREMENT_VALIDATOR['strict_mode'] = False 