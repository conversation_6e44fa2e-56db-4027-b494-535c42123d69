from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from app.models.compliance import ApplicationStatus, ComplianceStatus

class ApplicationBase(BaseModel):
    disaster_number: str = Field(..., description="FEMA disaster number")
    applicant_id: str = Field(..., description="FEMA applicant ID")
    project_title: str = Field(..., description="Project title")

class ApplicationCreate(ApplicationBase):
    pass

class ApplicationUpdate(BaseModel):
    status: Optional[ApplicationStatus] = None
    compliance_status: Optional[ComplianceStatus] = None

class ApplicationResponse(ApplicationBase):
    id: str
    status: ApplicationStatus
    compliance_status: ComplianceStatus
    submission_date: datetime
    last_updated: datetime
    user_id: int

    class Config:
        from_attributes = True

class PolicyBase(BaseModel):
    title: str = Field(..., description="Policy requirement title")
    description: str = Field(..., description="Policy requirement description")
    category: str = Field(..., description="Policy requirement category")
    required_documents: str = Field(..., description="JSON string of required document types")
    compliance_criteria: str = Field(..., description="JSON string of compliance criteria")

class PolicyCreate(PolicyBase):
    pass

class PolicyUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    required_documents: Optional[str] = None
    compliance_criteria: Optional[str] = None

class PolicyResponse(PolicyBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class DocumentBase(BaseModel):
    document_type: str = Field(..., description="Type of document")
    compliance_status: Optional[ComplianceStatus] = ComplianceStatus.UNDER_REVIEW
    review_notes: Optional[str] = None
    version: Optional[int] = 1
    is_archived: Optional[bool] = False

class DocumentCreate(DocumentBase):
    pass

class DocumentUpdate(BaseModel):
    document_type: Optional[str] = None
    compliance_status: Optional[ComplianceStatus] = None
    review_notes: Optional[str] = None
    version: Optional[int] = None
    is_archived: Optional[bool] = None

class DocumentResponse(DocumentBase):
    id: str
    application_id: str
    file_path: str
    original_filename: str
    file_size: int
    mime_type: str
    upload_date: datetime
    last_accessed: Optional[datetime] = None
    archive_path: Optional[str] = None
    archive_date: Optional[datetime] = None
    checksum: Optional[str] = None

    class Config:
        from_attributes = True
