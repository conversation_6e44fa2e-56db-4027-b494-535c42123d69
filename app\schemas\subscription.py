from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum

class SubscriptionTier(str, Enum):
    BASIC = "basic"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"

class SubscriptionBase(BaseModel):
    user_id: int
    tier: SubscriptionTier
    start_date: datetime
    end_date: datetime
    is_active: bool = True
    auto_renew: bool = True

class SubscriptionCreate(SubscriptionBase):
    pass

class SubscriptionResponse(SubscriptionBase):
    id: int
    
    class Config:
        from_attributes = True

class SubscriptionFeatureBase(BaseModel):
    tier: SubscriptionTier
    feature_name: str
    feature_description: str
    is_enabled: bool = True

class SubscriptionFeatureCreate(SubscriptionFeatureBase):
    pass

class SubscriptionFeatureResponse(SubscriptionFeatureBase):
    id: int
    
    class Config:
        from_attributes = True

class BillingBase(BaseModel):
    subscription_id: int
    amount: float = Field(gt=0)
    billing_date: datetime
    payment_status: str
    payment_method: str
    invoice_number: str

class BillingCreate(BillingBase):
    pass

class BillingResponse(BillingBase):
    id: int
    
    class Config:
        from_attributes = True
