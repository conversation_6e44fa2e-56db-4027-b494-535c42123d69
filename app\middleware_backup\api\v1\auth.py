from datetime import timed<PERSON><PERSON>
import uuid
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>Form
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import (
    authenticate_user,
    create_access_token,
    get_password_hash,
    get_current_active_user,
    validate_password_complexity,
    generate_totp_secret,
    generate_totp_uri,
    verify_totp
)
from app.models.user import User
from app.schemas.token import Token, TOTPVerifyRequest, TOTPSetupResponse, TOTPResponse
from app.schemas.user import UserCreate, UserResponse
from app.core.config import settings
from fastapi import Request

router = APIRouter()

# Store temporary session data for 2FA process
# In production, this should be moved to Redis or another persistent store
active_2fa_sessions = {}

@router.post(
    '/register',
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    responses={
        201: {
            "description": "User successfully created",
            "content": {
                "application/json": {
                    "example": {
                        "username": "testuser",
                        "full_name": "Test User",
                        "id": 1,
                        "is_active": True,
                        "created_at": "2025-03-24T00:14:24.964Z",
                        "updated_at": "2025-03-24T00:14:24.964Z"
                    }
                }
            }
        }
    }
)
async def register_user(request: Request, user: UserCreate, db: Session = Depends(get_db)):
    """Register a new user."""
    try:
        # Check rate limit
        rate_limiter = request.app.state.rate_limiter
        is_allowed, retry_after = await rate_limiter.check_rate_limit(
            key=f"register:{request.client.host}",
            limit=5,
            period=60
        )
        if not is_allowed:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Too many registration attempts. Please try again in {retry_after} seconds."
            )

        # Check if user exists
        db_user = db.query(User).filter(User.username == user.username).first()
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )
            
        # Check if email exists
        email_user = db.query(User).filter(User.email == user.email).first()
        if email_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create new user - add detailed logging
        print(f"Creating user with username: {user.username}, full_name: {user.full_name}, email: {user.email}")
        hashed_password = get_password_hash(user.password)
        print("Password hashed successfully")
        
        db_user = User(
            username=user.username,
            full_name=user.full_name,
            email=user.email,
            hashed_password=hashed_password,
            is_active=True
        )
        print("User model instance created")
        
        db.add(db_user)
        print("User added to session")
        
        db.commit()
        print("Database committed")
        
        db.refresh(db_user)
        print("User refreshed")
        
        return db_user
    except Exception as e:
        # Log the specific error
        print(f"ERROR IN REGISTRATION: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        # Return the error detail to help with debugging
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration error: {str(e)}"
        )

@router.post(
    '/login',
    response_model=Token,
    responses={
        200: {
            "description": "Login successful",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "token_type": "bearer"
                    }
                }
            }
        }
    }
)
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Login and get access token."""
    # Check rate limit
    rate_limiter = request.app.state.rate_limiter
    is_allowed, retry_after = await rate_limiter.check_rate_limit(
        key=f"login:{request.client.host}",
        limit=5,
        period=60
    )
    if not is_allowed:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Too many login attempts. Please try again in {retry_after} seconds."
        )

    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    # Record successful login attempt
    user.record_login_attempt(success=True)
    db.commit()
    
    # Check if user has 2FA enabled
    if user.is_two_factor_enabled:
        # Create temporary session for 2FA verification
        session_id = str(uuid.uuid4())
        active_2fa_sessions[session_id] = {
            "user_id": user.id,
            "username": user.username
        }
        
        # Return token with 2FA flag
        return {
            "access_token": "",  # No access token until 2FA verification
            "token_type": "bearer",
            "requires_second_factor": True,
            "session_id": session_id
        }
    
    # If 2FA is not enabled, proceed with normal token generation
    access_token = create_access_token(data={"sub": user.username})
    return {"access_token": access_token, "token_type": "bearer"}

@router.post('/verify-2fa', response_model=TOTPResponse)
async def verify_2fa(
    request: Request,
    verification_request: TOTPVerifyRequest,
    db: Session = Depends(get_db)
):
    """Verify a TOTP code for two-factor authentication."""
    # Check rate limit
    rate_limiter = request.app.state.rate_limiter
    is_allowed, retry_after = await rate_limiter.check_rate_limit(
        key=f"verify_2fa:{request.client.host}",
        limit=5,
        period=60
    )
    if not is_allowed:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Too many verification attempts. Please try again in {retry_after} seconds."
        )

    # Check if the session exists
    if verification_request.session_id not in active_2fa_sessions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification session"
        )
    
    # Get the user from the session
    session_data = active_2fa_sessions[verification_request.session_id]
    user = db.query(User).filter(User.id == session_data["user_id"]).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Verify the TOTP code
    if not verify_totp(user.totp_secret, verification_request.totp_code):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid verification code"
        )
    
    # Clean up session
    del active_2fa_sessions[verification_request.session_id]
    
    # Generate JWT with 2FA verification flag
    access_token = create_access_token(
        data={
            "sub": user.username,
            "two_factor_verified": True
        }
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }

@router.post('/enable-2fa', response_model=TOTPSetupResponse)
async def enable_2fa(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Enable two-factor authentication for the current user."""
    try:
        # Generate a new TOTP secret
        encrypted_secret = generate_totp_secret()
        
        # Update the user model
        current_user.totp_secret = encrypted_secret
        current_user.is_two_factor_enabled = False  # Will be set to True after verification
        db.commit()
        
        # Generate URI for QR code
        totp_uri = generate_totp_uri(current_user.username, encrypted_secret)
        
        return {
            "secret": encrypted_secret,
            "qr_code_uri": totp_uri
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error enabling 2FA: {str(e)}"
        )

class Verify2FASetupRequest(BaseModel):
    totp_code: str

@router.post('/verify-2fa-setup')
async def verify_2fa_setup(
    verification: Verify2FASetupRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Verify and complete 2FA setup after scanning the QR code."""
    if not current_user.totp_secret:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication not initiated"
        )
    
    # Verify the TOTP code
    if not verify_totp(current_user.totp_secret, verification.totp_code):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid verification code"
        )
    
    # Complete 2FA setup
    current_user.is_two_factor_enabled = True
    db.commit()
    
    return {"detail": "Two-factor authentication successfully enabled"}

@router.post('/disable-2fa')
async def disable_2fa(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Disable two-factor authentication for the current user."""
    if not current_user.is_two_factor_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication not enabled"
        )
    
    # Disable 2FA
    current_user.totp_secret = None
    current_user.is_two_factor_enabled = False
    db.commit()
    
    return {"detail": "Two-factor authentication disabled"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """Get current user info."""
    return current_user

class PasswordChangeRequest(BaseModel):
    """Schema for password change request."""
    current_password: str
    new_password: str

@router.post(
    "/change-password", 
    status_code=status.HTTP_200_OK,
    responses={
        200: {
            "description": "Password successfully changed",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Password updated successfully"
                    }
                }
            }
        },
        400: {
            "description": "Bad request - incorrect password or invalid new password",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Incorrect password"
                    }
                }
            }
        }
    }
)
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Change user password."""
    # Verify current password
    if not current_user.verify_password(password_data.current_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect password"
        )
    
    # Validate new password
    if not validate_password_complexity(password_data.new_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be at least 8 characters and contain uppercase, lowercase, numbers, and special characters"
        )
    
    # Ensure new password is different from current
    if password_data.current_password == password_data.new_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password must be different from current password"
        )
    
    # Update password
    try:
        current_user.hashed_password = get_password_hash(password_data.new_password)
        db.commit()
        return {"detail": "Password updated successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating password: {str(e)}"
        )