from datetime import datetime
from sqlalchemy import Column, String, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import Base, TimestampMixin
from uuid import uuid4

class DisasterDeclaration(Base, TimestampMixin):
    __tablename__ = "disasters"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    dr_number = Column(String, unique=True, index=True, nullable=False)
    incident_type = Column(String, nullable=False)
    incident_period_start = Column(DateTime, nullable=False)
    incident_period_end = Column(DateTime, nullable=True)
    declaration_date = Column(DateTime, nullable=False)
    state = Column(String, nullable=False)
    
    # Store affected areas as JSON
    affected_areas = Column(JSON, default=list)
    
    # Store additional FEMA data
    fema_metadata = Column(JSON, default=dict)
    
    # Relationships
    active_policies = relationship("Policy",
                                secondary="policy_disaster_association",
                                back_populates="applicable_disasters")
    
    projects = relationship("Project", back_populates="disaster")
    
    def __repr__(self):
        return f"<DisasterDeclaration DR-{self.dr_number} ({self.state}))>"





