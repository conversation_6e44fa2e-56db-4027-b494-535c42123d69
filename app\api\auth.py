"""
Authentication and Authorization System
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any
from pydantic import BaseModel
import logging
from pathlib import Path
import json

# Security configuration
SECRET_KEY = "your-secret-key-here"  # In production, use environment variable
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# User model
class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None
    roles: list[str] = []

class UserInDB(User):
    hashed_password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# User database
USERS_FILE = Path("data/users.json")

def get_user(username: str) -> Optional[UserInDB]:
    """Get user from database."""
    try:
        if not USERS_FILE.exists():
            return None
            
        with open(USERS_FILE) as f:
            users = json.load(f)
            
        user_data = users.get(username)
        if user_data:
            return UserInDB(**user_data)
            
    except Exception as e:
        logging.error(f"Error getting user {username}: {e}")
    return None

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash password."""
    return pwd_context.hash(password)

def authenticate_user(username: str, password: str) -> Optional[User]:
    """Authenticate user."""
    user = get_user(username)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    """Get current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current active user."""
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

def check_permission(user: User, required_role: str) -> bool:
    """Check if user has required role."""
    return required_role in user.roles

def require_role(role: str):
    """Decorator to require specific role."""
    def role_checker(user: User = Depends(get_current_active_user)):
        if not check_permission(user, role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        return user
    return role_checker

# Initialize default admin user if no users exist
def initialize_default_admin():
    """Initialize default admin user if no users exist."""
    if not USERS_FILE.exists():
        USERS_FILE.parent.mkdir(parents=True, exist_ok=True)
        admin_user = {
            "admin": {
                "username": "admin",
                "email": "<EMAIL>",
                "full_name": "Administrator",
                "disabled": False,
                "roles": ["admin"],
                "hashed_password": get_password_hash("admin")  # Change this in production!
            }
        }
        with open(USERS_FILE, "w") as f:
            json.dump(admin_user, f, indent=2)
        logging.info("Created default admin user")

# Initialize on import
initialize_default_admin() 