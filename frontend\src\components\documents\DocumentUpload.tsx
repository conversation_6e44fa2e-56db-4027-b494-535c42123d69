import React, { useState, useCallback } from 'react';
import { 
  Box, 
  Button, 
  Typography, 
  CircularProgress, 
  Alert, 
  LinearProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  SelectChangeEvent,
  Paper
} from '@mui/material';
import { api } from '../../services/api';
import { SUPPORTED_FILE_TYPES, MAX_FILE_SIZE } from '../../config';

// Document type enum to match backend
enum DocumentType {
  POLICY = "policy",
  PROCEDURE = "procedure",
  EVIDENCE = "evidence",
  REPORT = "report",
  OTHER = "other"
}

const DocumentUpload: React.FC = () => {
    const [file, setFile] = useState<File | null>(null);
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [documentType, setDocumentType] = useState<DocumentType>(DocumentType.OTHER);
    const [loading, setLoading] = useState(false);
    const [progress, setProgress] = useState(0);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    const validateForm = () => {
        const errors: Record<string, string> = {};
        
        if (!file) {
            errors.file = 'Please select a file to upload';
        } else {
            // Check file size
            if (file.size > MAX_FILE_SIZE) {
                errors.file = `File size exceeds the maximum allowed (${MAX_FILE_SIZE / (1024 * 1024)}MB)`;
            }
            
            // Check file type
            if (!SUPPORTED_FILE_TYPES.includes(file.type)) {
                errors.file = `File type not supported. Allowed types: ${SUPPORTED_FILE_TYPES.map(t => t.split('/')[1]).join(', ')}`;
            }
        }
        
        if (!title.trim()) {
            errors.title = 'Title is required';
        }
        
        if (!documentType) {
            errors.documentType = 'Document type is required';
        }
        
        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files.length > 0) {
            setFile(event.target.files[0]);
            setError(null);
            setSuccess(null);
            setValidationErrors({});
        }
    };

    const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
            setFile(event.dataTransfer.files[0]);
            setError(null);
            setSuccess(null);
            setValidationErrors({});
        }
    }, []);

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
    };

    const handleDocumentTypeChange = (event: SelectChangeEvent<string>) => {
        setDocumentType(event.target.value as DocumentType);
    };

    const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setTitle(event.target.value);
    };

    const handleDescriptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setDescription(event.target.value);
    };

    const resetForm = () => {
        setFile(null);
        setTitle('');
        setDescription('');
        setDocumentType(DocumentType.OTHER);
        setValidationErrors({});
    };

    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }
        
        setLoading(true);
        setProgress(0);
        setError(null);
        
        try {
            const formData = new FormData();
            formData.append('file', file as File);
            formData.append('title', title);
            formData.append('description', description || '');
            formData.append('document_type', documentType);
            
            const response = await api.post('/documents', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                onUploadProgress: (progressEvent) => {
                    if (progressEvent.total) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        setProgress(percentCompleted);
                    }
                }
            });
            
            setSuccess('Document uploaded successfully');
            resetForm();
        } catch (err: any) {
            console.error('Upload error:', err);
            if (err.response?.status === 413) {
                setError('File size is too large');
            } else if (err.response?.status === 415) {
                setError('File type not supported');
            } else if (err.response?.status === 429) {
                setError('Too many uploads. Please try again later.');
            } else {
                setError(err.response?.data?.detail || 'Failed to upload document');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <Paper elevation={3} sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
            <Typography variant="h4" gutterBottom>
                Document Upload
            </Typography>
            
            {error && <Alert severity="error" sx={{ mb: 2 }} role="alert">{error}</Alert>}
            {success && <Alert severity="success" sx={{ mb: 2 }} role="alert">{success}</Alert>}
            
            <Box sx={{ mb: 3 }}>
                <TextField
                    fullWidth
                    label="Document Title"
                    value={title}
                    onChange={handleTitleChange}
                    required
                    error={!!validationErrors.title}
                    helperText={validationErrors.title}
                    disabled={loading}
                    sx={{ mb: 2 }}
                />
                
                <TextField
                    fullWidth
                    label="Description (Optional)"
                    value={description}
                    onChange={handleDescriptionChange}
                    multiline
                    rows={3}
                    disabled={loading}
                    sx={{ mb: 2 }}
                />
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel id="document-type-label">Document Type</InputLabel>
                    <Select
                        labelId="document-type-label"
                        value={documentType}
                        onChange={handleDocumentTypeChange}
                        label="Document Type"
                        required
                        error={!!validationErrors.documentType}
                        disabled={loading}
                    >
                        <MenuItem value={DocumentType.POLICY}>Policy</MenuItem>
                        <MenuItem value={DocumentType.PROCEDURE}>Procedure</MenuItem>
                        <MenuItem value={DocumentType.EVIDENCE}>Evidence</MenuItem>
                        <MenuItem value={DocumentType.REPORT}>Report</MenuItem>
                        <MenuItem value={DocumentType.OTHER}>Other</MenuItem>
                    </Select>
                    {validationErrors.documentType && (
                        <Typography color="error" variant="caption">
                            {validationErrors.documentType}
                        </Typography>
                    )}
                </FormControl>
            </Box>
            
            <Box
                sx={{ 
                    border: '2px dashed #ccc', 
                    borderColor: validationErrors.file ? 'error.main' : '#ccc',
                    borderRadius: 2,
                    p: 3, 
                    textAlign: 'center', 
                    mb: 3,
                    bgcolor: 'background.paper'
                }}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                role="region"
                aria-label="Drag and drop file upload area"
            >
                <Typography variant="body1" gutterBottom>
                    Drag and drop your file here or
                </Typography>
                <Button
                    variant="contained"
                    component="label"
                    sx={{ mt: 1 }}
                    aria-label="Select file to upload"
                    disabled={loading}
                >
                    Select File
                    <input
                        type="file"
                        hidden
                        onChange={handleFileChange}
                        aria-label="File input"
                        accept={SUPPORTED_FILE_TYPES.join(',')}
                    />
                </Button>
                {file && (
                    <Box sx={{ mt: 2 }}>
                        <Typography variant="body2">
                            Selected: {file.name} ({(file.size / (1024 * 1024)).toFixed(2)} MB)
                        </Typography>
                    </Box>
                )}
                {validationErrors.file && (
                    <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                        {validationErrors.file}
                    </Typography>
                )}
            </Box>
            
            {loading && (
                <Box sx={{ width: '100%', mb: 3 }}>
                    <LinearProgress 
                        variant="determinate" 
                        value={progress} 
                        aria-label="Upload progress" 
                    />
                    <Typography variant="body2" align="center" sx={{ mt: 0.5 }}>
                        {progress}% Uploaded
                    </Typography>
                </Box>
            )}
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                    variant="outlined"
                    onClick={resetForm}
                    disabled={loading}
                    aria-label="Reset form"
                >
                    Reset
                </Button>
                <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSubmit}
                    disabled={loading || !file || !title}
                    aria-label="Upload document button"
                    startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                    {loading ? 'Uploading...' : 'Upload Document'}
                </Button>
            </Box>
        </Paper>
    );
};

export default DocumentUpload;