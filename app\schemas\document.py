"""Document schemas."""

from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime
from app.models.enums import ComplianceStatus, RiskLevel, MitigationStatus, DocumentType

class DocumentBase(BaseModel):
    """Base document schema."""
    title: str
    content: Optional[str] = None
    document_type: DocumentType = DocumentType.OTHER
    file_path: str
    original_filename: str
    mime_type: Optional[str] = None
    file_size: Optional[int] = None

class DocumentCreate(DocumentBase):
    """Schema for creating a document."""
    pass

class DocumentUpdate(BaseModel):
    """Schema for updating a document."""
    title: Optional[str] = None
    content: Optional[str] = None
    document_type: Optional[DocumentType] = None
    file_path: Optional[str] = None
    original_filename: Optional[str] = None
    mime_type: Optional[str] = None
    file_size: Optional[int] = None
    is_processed: Optional[bool] = None
    is_archived: Optional[bool] = None
    archive_path: Optional[str] = None
    review_status: Optional[str] = None
    risk_level: Optional[str] = None

class DocumentResponse(DocumentBase):
    """Schema for document response."""
    id: int
    is_processed: bool
    is_archived: bool
    archive_path: Optional[str] = None
    archive_date: Optional[str] = None
    version: int
    parent_version_id: Optional[int] = None
    checksum: Optional[str] = None
    last_reviewed_at: Optional[str] = None
    review_status: Optional[str] = None
    risk_level: Optional[str] = None
    created_at: str
    updated_at: str

    class Config:
        """Pydantic config."""
        from_attributes = True

class DocumentList(BaseModel):
    total: int
    items: List[DocumentResponse]
