"""
PowerPoint Document Classifier
Implements document classification for PowerPoint presentations.
"""

import os
from typing import Dict, Any, List
from pptx import Presentation
from .base import DocumentClassifier

class PowerPointClassifier(DocumentClassifier):
    """Classifier implementation for PowerPoint presentations."""
    
    def __init__(self):
        self.supported_formats = ['.pptx', '.ppt']
    
    def classify(self, document_path: str) -> Dict[str, Any]:
        """
        Classify a PowerPoint presentation and extract metadata.
        
        Args:
            document_path: Path to the PowerPoint file
            
        Returns:
            Dictionary containing:
            - slide_count: Number of slides
            - title: Presentation title
            - author: Presentation author
            - subject: Presentation subject
            - creation_date: Presentation creation date
            - modification_date: Presentation modification date
            - has_notes: Whether any slides have speaker notes
            - has_media: Whether any slides contain media (images, videos)
            - has_animations: Whether any slides have animations
        """
        if not self.validate_document(document_path):
            raise ValueError(f"Invalid PowerPoint document: {document_path}")
            
        prs = Presentation(document_path)
        core_properties = prs.core_properties
        
        # Check for notes, media, and animations
        has_notes = False
        has_media = False
        has_animations = False
        
        for slide in prs.slides:
            # Check for notes
            if slide.notes_slide and slide.notes_slide.notes_text_frame.text.strip():
                has_notes = True
            
            # Check for media
            for shape in slide.shapes:
                if shape.shape_type in [13, 14]:  # 13: Picture, 14: Media
                    has_media = True
                
                # Check for animations
                if hasattr(shape, 'animation') and shape.animation:
                    has_animations = True
            
            if has_notes and has_media and has_animations:
                break
        
        return {
            'slide_count': len(prs.slides),
            'title': core_properties.title,
            'author': core_properties.author,
            'subject': core_properties.subject,
            'creation_date': core_properties.created,
            'modification_date': core_properties.modified,
            'has_notes': has_notes,
            'has_media': has_media,
            'has_animations': has_animations
        }
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats.
        
        Returns:
            List containing ['.pptx', '.ppt']
        """
        return self.supported_formats
    
    def validate_document(self, document_path: str) -> bool:
        """
        Validate if a document is a valid PowerPoint presentation.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            True if document is a valid PowerPoint presentation, False otherwise
        """
        if not os.path.exists(document_path):
            return False
            
        if not any(document_path.lower().endswith(ext) for ext in self.supported_formats):
            return False
            
        try:
            prs = Presentation(document_path)
            return len(prs.slides) > 0
        except:
            return False 