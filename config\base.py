"""
Base Configuration Module

Contains default configuration settings for ComplianceMax.
"""

from pathlib import Path
import os

# Base paths
BASE_DIR = Path(__file__).parent.parent
STORAGE_DIR = BASE_DIR / 'storage'
LOGS_DIR = BASE_DIR / 'logs'

# Create necessary directories
STORAGE_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Document storage configuration
DOCUMENT_STORAGE = {
    'policies': STORAGE_DIR / 'policies',
    'requirements': STORAGE_DIR / 'requirements',
    'uploads': STORAGE_DIR / 'uploads',
    'results': STORAGE_DIR / 'results'
}

# Ensure storage directories exist
for path in DOCUMENT_STORAGE.values():
    path.mkdir(exist_ok=True)

# Database configuration
DATABASE = {
    'engine': 'sqlite',
    'name': 'compliancemax.db',
    'path': BASE_DIR / 'compliancemax.db'
}

# Policy matcher configuration
POLICY_MATCHER = {
    'confidence_threshold': 0.7,
    'min_similarity_score': 0.5,
    'max_results': 100,
    'cache_enabled': True,
    'cache_ttl': 3600  # 1 hour
}

# Document processor configuration
DOCUMENT_PROCESSOR = {
    'max_file_size': 50 * 1024 * 1024,  # 50MB
    'supported_types': {
        'application/pdf': '.pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
        'text/html': '.html',
        'text/plain': '.txt'
    },
    'extract_metadata': True,
    'store_original': True
}

# Compliance checker configuration
COMPLIANCE_CHECKER = {
    'confidence_threshold': 0.7,
    'required_match_percentage': 0.8,
    'store_results': True,
    'generate_report': True
}

# Requirement validator configuration
REQUIREMENT_VALIDATOR = {
    'strict_mode': True,
    'required_fields': {
        'id', 'title', 'description', 'criteria',
        'references', 'version', 'effective_date'
    },
    'min_description_words': 10,
    'validate_references': True
}

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': LOGS_DIR / 'compliancemax.log',
            'formatter': 'standard'
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        }
    },
    'loggers': {
        '': {  # Root logger
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True
        }
    }
}

# Security configuration
SECURITY = {
    'allowed_hosts': ['localhost', '127.0.0.1'],
    'secret_key': os.environ.get('SECRET_KEY', 'default-dev-key-change-in-production'),
    'debug': False,
    'ssl_verify': True,
    'max_upload_size': 50 * 1024 * 1024  # 50MB
}

# API configuration
API = {
    'version': '1.0',
    'prefix': '/api/v1',
    'rate_limit': {
        'enabled': True,
        'requests': 100,
        'window': 60  # 1 minute
    }
}

# Cache configuration
CACHE = {
    'enabled': True,
    'backend': 'memory',
    'ttl': 3600,  # 1 hour
    'max_size': 1000  # Maximum number of items
}

# Feature flags
FEATURES = {
    'enable_ai_matching': True,
    'enable_batch_processing': True,
    'enable_advanced_search': True,
    'enable_export': True,
    'enable_notifications': False
} 