from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field

class DocumentLinkBase(BaseModel):
    """Base schema for document-application links."""
    document_id: str = Field(..., description="ID of the linked document")
    application_id: str = Field(..., description="ID of the application this link belongs to")
    category: str = Field(..., description="Category of the document (e.g., 'Insurance', 'Permits')")
    subcategory: Optional[str] = Field(None, description="Optional subcategory for more specific classification")
    is_primary: bool = Field(False, description="Whether this is the primary document for this category")
    notes: Optional[str] = Field(None, description="Additional notes about this document link")
    validation_status: str = Field("pending", description="Status of document validation")
    compliance_status: str = Field("pending", description="Status of compliance check")

class DocumentLinkCreate(DocumentLinkBase):
    """Schema for creating a new document link."""
    pass

class DocumentLinkUpdate(BaseModel):
    """Schema for updating an existing document link."""
    document_id: Optional[str] = None
    application_id: Optional[str] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    is_primary: Optional[bool] = None
    notes: Optional[str] = None
    validation_status: Optional[str] = None
    compliance_status: Optional[str] = None

class DocumentLink(DocumentLinkBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

# Forward references for related schemas
from app.schemas.document import DocumentResponse
from app.schemas.compliance_review import ComplianceReviewResponse

DocumentLink.model_rebuild() 