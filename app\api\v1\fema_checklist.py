from typing import Dict, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.deps import get_current_active_user, get_db
from app.models.user import User
from app.services.fema_compliance_service import FEMAComplianceService

router = APIRouter()

@router.post("/validate-document/{document_id}")
async def validate_document_compliance(
    document_id: int,
    category: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict:
    """
    Validate a document's compliance against FEMA requirements for a specific category.
    """
    try:
        service = FEMAComplianceService(db)
        results = await service.validate_document_compliance(document_id, category)
        return results
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error validating document compliance: {str(e)}")

@router.get("/report/{category}")
async def get_compliance_report(
    category: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict:
    """
    Generate a compliance report for a specific FEMA category.
    """
    try:
        service = FEMAComplianceService(db)
        report = await service.generate_compliance_report(category)
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating compliance report: {str(e)}") 