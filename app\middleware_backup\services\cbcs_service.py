from sqlalchemy.orm import Session
from app.models.cbcs import Standard, Requirement, ComplianceReview
from app.schemas.cbcs import StandardCreate, RequirementCreate, ReviewCreate
from datetime import datetime

class CBCSService:
    def __init__(self, db: Session):
        self.db = db

    def create_standard(self, standard: StandardCreate) -> Standard:
        db_standard = Standard(
            code=standard.code,
            title=standard.title,
            description=standard.description,
            version=standard.version,
            effective_date=standard.effective_date
        )
        self.db.add(db_standard)
        self.db.commit()
        self.db.refresh(db_standard)
        return db_standard

    def create_requirement(self, requirement: RequirementCreate) -> Requirement:
        db_requirement = Requirement(
            standard_id=requirement.standard_id,
            code=requirement.code,
            description=requirement.description,
            criteria=requirement.criteria
        )
        self.db.add(db_requirement)
        self.db.commit()
        self.db.refresh(db_requirement)
        return db_requirement

    def create_review(self, review: ReviewCreate, reviewer_id: int) -> ComplianceReview:
        db_review = ComplianceReview(
            requirement_id=review.requirement_id,
            project_id=review.project_id,
            status=review.status,
            reviewer_id=reviewer_id,
            notes=review.notes,
            evidence=review.evidence
        )
        self.db.add(db_review)
        self.db.commit()
        self.db.refresh(db_review)
        return db_review

    def get_standard(self, standard_id: int) -> Standard:
        return self.db.query(Standard).filter(Standard.id == standard_id).first()

    def get_requirements(self, standard_id: int) -> list[Requirement]:
        return self.db.query(Requirement).filter(Requirement.standard_id == standard_id).all()

    def get_reviews(self, project_id: int) -> list[ComplianceReview]:
        return self.db.query(ComplianceReview).filter(ComplianceReview.project_id == project_id).all()
