"""Feature engineering system for ML operations."""

from typing import Dict, List, Optional, Any, Union
import numpy as np
from datetime import datetime
import pandas as pd
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.feature_selection import SelectKBest, mutual_info_classif
from sklearn.decomposition import PCA
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class FeatureSet:
    """Feature set metadata and values."""
    feature_names: List[str]
    feature_types: Dict[str, str]
    values: np.ndarray
    metadata: Dict[str, Any]
    timestamp: datetime

class FeatureEngineer:
    """Enhanced feature engineering system."""
    
    def __init__(self):
        self.scalers = {}
        self.encoders = {}
        self.feature_selectors = {}
        self.pca_transformers = {}
        self.feature_history = {}
    
    async def extract_features(
        self,
        raw_data: Dict[str, Any],
        feature_config: Dict[str, Dict[str, Any]]
    ) -> FeatureSet:
        """Extract and engineer features from raw data."""
        try:
            # Extract basic features
            features = await self._extract_basic_features(raw_data, feature_config)
            
            # Generate derived features
            derived = await self._generate_derived_features(features, feature_config)
            
            # Normalize and encode
            processed = await self._normalize_and_encode(derived, feature_config)
            
            # Select important features
            selected = await self._select_features(processed, feature_config)
            
            # Create feature set
            feature_set = FeatureSet(
                feature_names=list(selected.keys()),
                feature_types={k: feature_config[k]['type'] for k in selected},
                values=np.array(list(selected.values())),
                metadata={
                    "source": raw_data.get("source", "unknown"),
                    "timestamp": datetime.utcnow(),
                    "config_version": feature_config.get("version", "1.0")
                },
                timestamp=datetime.utcnow()
            )
            
            # Track feature history
            self._update_feature_history(feature_set)
            
            return feature_set
            
        except Exception as e:
            logger.error(f"Error in feature engineering: {str(e)}")
            raise
    
    async def _extract_basic_features(
        self,
        raw_data: Dict[str, Any],
        config: Dict[str, Dict[str, Any]]
    ) -> Dict[str, float]:
        """Extract basic features from raw data."""
        features = {}
        
        for feature_name, feature_config in config.items():
            if feature_config.get('type') == 'basic':
                extractor = self._get_extractor(feature_config['method'])
                features[feature_name] = extractor(raw_data, feature_config)
        
        return features
    
    async def _generate_derived_features(
        self,
        basic_features: Dict[str, float],
        config: Dict[str, Dict[str, Any]]
    ) -> Dict[str, float]:
        """Generate derived features."""
        features = {**basic_features}  # Start with basic features
        
        for feature_name, feature_config in config.items():
            if feature_config.get('type') == 'derived':
                generator = self._get_generator(feature_config['method'])
                features[feature_name] = generator(basic_features, feature_config)
        
        return features
    
    async def _normalize_and_encode(
        self,
        features: Dict[str, float],
        config: Dict[str, Dict[str, Any]]
    ) -> Dict[str, float]:
        """Normalize and encode features."""
        normalized = {}
        
        for feature_name, value in features.items():
            feature_config = config[feature_name]
            
            if feature_config.get('normalize', False):
                scaler = self._get_scaler(feature_name)
                value = scaler.transform([[value]])[0][0]
            
            if feature_config.get('encode', False):
                encoder = self._get_encoder(feature_name)
                value = encoder.transform([[str(value)]])[0][0]
            
            normalized[feature_name] = value
        
        return normalized
    
    async def _select_features(
        self,
        features: Dict[str, float],
        config: Dict[str, Dict[str, Any]]
    ) -> Dict[str, float]:
        """Select important features."""
        if not config.get('feature_selection', {}).get('enabled', False):
            return features
        
        feature_names = list(features.keys())
        feature_values = np.array(list(features.values())).reshape(1, -1)
        
        # Get or create feature selector
        selector = self._get_feature_selector(
            config['feature_selection'].get('method', 'mutual_info'),
            config['feature_selection'].get('k', len(feature_names))
        )
        
        # Select features
        selected_mask = selector.get_support()
        selected_features = {
            name: features[name]
            for name, selected in zip(feature_names, selected_mask)
            if selected
        }
        
        return selected_features
    
    def _get_extractor(self, method: str):
        """Get feature extraction method."""
        extractors = {
            'direct': lambda data, config: data.get(config['path']),
            'ratio': lambda data, config: data.get(config['numerator']) / data.get(config['denominator']),
            'count': lambda data, config: len(data.get(config['path'], [])),
            'exists': lambda data, config: 1.0 if config['path'] in data else 0.0,
            'timestamp': lambda data, config: datetime.fromisoformat(data.get(config['path'])).timestamp()
        }
        return extractors.get(method)
    
    def _get_generator(self, method: str):
        """Get feature generation method."""
        generators = {
            'sum': lambda features, config: sum(features[f] for f in config['inputs']),
            'product': lambda features, config: np.prod([features[f] for f in config['inputs']]),
            'mean': lambda features, config: np.mean([features[f] for f in config['inputs']]),
            'max': lambda features, config: max(features[f] for f in config['inputs']),
            'min': lambda features, config: min(features[f] for f in config['inputs'])
        }
        return generators.get(method)
    
    def _get_scaler(self, feature_name: str) -> StandardScaler:
        """Get or create scaler for feature."""
        if feature_name not in self.scalers:
            self.scalers[feature_name] = StandardScaler()
        return self.scalers[feature_name]
    
    def _get_encoder(self, feature_name: str) -> OneHotEncoder:
        """Get or create encoder for feature."""
        if feature_name not in self.encoders:
            self.encoders[feature_name] = OneHotEncoder(sparse=False)
        return self.encoders[feature_name]
    
    def _get_feature_selector(self, method: str, k: int) -> SelectKBest:
        """Get or create feature selector."""
        key = f"{method}_{k}"
        if key not in self.feature_selectors:
            if method == 'mutual_info':
                self.feature_selectors[key] = SelectKBest(
                    score_func=mutual_info_classif,
                    k=k
                )
            elif method == 'pca':
                self.feature_selectors[key] = PCA(n_components=k)
        return self.feature_selectors[key]
    
    def _update_feature_history(self, feature_set: FeatureSet):
        """Update feature history."""
        for i, feature_name in enumerate(feature_set.feature_names):
            if feature_name not in self.feature_history:
                self.feature_history[feature_name] = []
            
            self.feature_history[feature_name].append({
                'value': feature_set.values[i],
                'timestamp': feature_set.timestamp,
                'metadata': feature_set.metadata
            })
            
            # Keep last 1000 values
            if len(self.feature_history[feature_name]) > 1000:
                self.feature_history[feature_name] = self.feature_history[feature_name][-1000:]
    
    async def get_feature_stats(self, feature_name: str) -> Dict[str, Any]:
        """Get statistics for a feature."""
        if feature_name not in self.feature_history:
            return {}
        
        values = [h['value'] for h in self.feature_history[feature_name]]
        return {
            'mean': float(np.mean(values)),
            'std': float(np.std(values)),
            'min': float(np.min(values)),
            'max': float(np.max(values)),
            'last_update': self.feature_history[feature_name][-1]['timestamp'],
            'history_size': len(self.feature_history[feature_name])
        }
""" 