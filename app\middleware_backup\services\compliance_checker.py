from typing import List, Dict, Optional
from datetime import datetime
import spacy
from transformers import pipeline
import logging
from sqlalchemy import Column, Integer, String, DateTime, JSON, Enum, Float
from sqlalchemy.orm import Session
import enum
from app.core.celery_app import celery_app
from app.core.cache import RedisCache
from app.core.database import Base

logger = logging.getLogger(__name__)

class ComplianceStatus(str, enum.Enum):
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PARTIAL = "partial"

class ComplianceCheck(Base):
    __tablename__ = "compliance_checks"

    id = Column(Integer, primary_key=True)
    document_id = Column(String, index=True)
    dr_number = Column(String, index=True)
    check_date = Column(DateTime, default=datetime.utcnow)
    findings = Column(JSON)
    overall_status = Column(Enum(ComplianceStatus))
    metadata = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<ComplianceCheck {self.document_id}>"

class ComplianceChecker:
    def __init__(self, db_session: Session):
        self.db = db_session
        self.nlp = spacy.load("en_core_web_lg")
        self.qa_pipeline = pipeline("question-answering")
        self.cache = RedisCache()
        self.similarity_threshold = 0.75
        self.cache_ttl = 3600  # 1 hour

    @celery_app.task(name="compliance.check_document")
    async def check_document(
        self, 
        document_id: str,
        dr_number: str,
        document_text: str,
        requirements: List[Dict]
    ) -> Dict:
        """Check document compliance against requirements."""
        try:
            # Process document with spaCy
            doc = self.nlp(document_text)
            
            findings = []
            overall_status = ComplianceStatus.COMPLIANT

            for requirement in requirements:
                finding = await self._check_requirement(doc, requirement)
                findings.append(finding)
                
                if finding["status"] == ComplianceStatus.NON_COMPLIANT:
                    overall_status = ComplianceStatus.NON_COMPLIANT
                elif finding["status"] == ComplianceStatus.PARTIAL and overall_status != ComplianceStatus.NON_COMPLIANT:
                    overall_status = ComplianceStatus.PARTIAL

            # Create check record
            check = ComplianceCheck(
                document_id=document_id,
                dr_number=dr_number,
                findings=findings,
                overall_status=overall_status,
                metadata={"processor_version": "1.0"}
            )

            self.db.add(check)
            await self.db.commit()
            
            # Invalidate cache
            await self._invalidate_cache(document_id)
            
            return {
                "id": check.id,
                "document_id": check.document_id,
                "dr_number": check.dr_number,
                "check_date": check.check_date.isoformat(),
                "findings": check.findings,
                "overall_status": check.overall_status,
                "metadata": check.metadata
            }

        except Exception as e:
            logger.error(f"Compliance check failed: {str(e)}")
            await self.db.rollback()
            raise

    async def _check_requirement(
        self, 
        doc: spacy.tokens.Doc, 
        requirement: Dict
    ) -> Dict:
        """Check a single requirement against the document."""
        try:
            requirement_text = requirement["text"]
            requirement_doc = self.nlp(requirement_text)
            
            # Find most similar sentences
            similar_sents = []
            max_similarity = 0
            
            for sent in doc.sents:
                similarity = sent.similarity(requirement_doc)
                if similarity > self.similarity_threshold:
                    similar_sents.append(sent.text)
                    max_similarity = max(max_similarity, similarity)

            # Use question-answering to validate specific points
            if requirement.get("validation_type") == "question_answering":
                qa_result = await self._validate_with_qa(
                    doc.text,
                    requirement.get("questions", [])
                )
            else:
                qa_result = None

            # Determine compliance status
            if max_similarity > self.similarity_threshold:
                status = ComplianceStatus.COMPLIANT
                confidence = max_similarity
                details = "Requirement satisfied with high confidence"
            elif similar_sents:
                status = ComplianceStatus.PARTIAL
                confidence = max_similarity
                details = "Requirement partially satisfied"
            else:
                status = ComplianceStatus.NON_COMPLIANT
                confidence = 1 - max_similarity
                details = "Requirement not found in document"

            return {
                "requirement_id": requirement["id"],
                "status": status,
                "confidence": confidence,
                "details": details,
                "relevant_text": similar_sents if similar_sents else None,
                "qa_results": qa_result
            }

        except Exception as e:
            logger.error(f"Requirement check failed: {str(e)}")
            raise

    async def _validate_with_qa(
        self, 
        context: str, 
        questions: List[str]
    ) -> List[Dict]:
        """Validate requirements using question-answering."""
        try:
            results = []
            for question in questions:
                qa_result = self.qa_pipeline(
                    question=question,
                    context=context
                )
                results.append({
                    "question": question,
                    "answer": qa_result["answer"],
                    "confidence": qa_result["score"]
                })
            return results

        except Exception as e:
            logger.error(f"QA validation failed: {str(e)}")
            raise

    async def get_compliance_history(
        self, 
        document_id: str,
        limit: int = 10
    ) -> List[Dict]:
        """Get compliance check history for a document."""
        try:
            cache_key = f"compliance_history:{document_id}"
            
            # Try to get from cache
            cached_result = await self.cache.get(cache_key)
            if cached_result:
                return cached_result

            # Get from database
            checks = self.db.query(ComplianceCheck)\
                .filter(ComplianceCheck.document_id == document_id)\
                .order_by(ComplianceCheck.check_date.desc())\
                .limit(limit)\
                .all()

            if checks:
                # Format results
                results = [{
                    "id": check.id,
                    "document_id": check.document_id,
                    "dr_number": check.dr_number,
                    "check_date": check.check_date.isoformat(),
                    "findings": check.findings,
                    "overall_status": check.overall_status,
                    "metadata": check.metadata
                } for check in checks]

                # Cache the result
                await self.cache.set(cache_key, results, self.cache_ttl)
                
                return results

            return []

        except Exception as e:
            logger.error(f"Error fetching compliance history: {str(e)}")
            raise

    async def get_check_by_id(
        self,
        check_id: int
    ) -> Optional[Dict]:
        """Get a specific compliance check by ID."""
        try:
            cache_key = f"compliance_check:{check_id}"
            
            # Try to get from cache
            cached_result = await self.cache.get(cache_key)
            if cached_result:
                return cached_result

            # Get from database
            check = self.db.query(ComplianceCheck).get(check_id)

            if check:
                result = {
                    "id": check.id,
                    "document_id": check.document_id,
                    "dr_number": check.dr_number,
                    "check_date": check.check_date.isoformat(),
                    "findings": check.findings,
                    "overall_status": check.overall_status,
                    "metadata": check.metadata
                }

                # Cache the result
                await self.cache.set(cache_key, result, self.cache_ttl)
                
                return result

            return None

        except Exception as e:
            logger.error(f"Error fetching compliance check: {str(e)}")
            raise

    async def _invalidate_cache(self, document_id: str):
        """Invalidate cache entries for a document."""
        keys_to_delete = [
            f"compliance_history:{document_id}",
            "compliance_check:*"
        ]
        for key in keys_to_delete:
            await self.cache.delete(key)
