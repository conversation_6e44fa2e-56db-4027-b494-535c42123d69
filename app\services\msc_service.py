import requests
from circuitbreaker import circuit
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class MSCService:
    @circuit(failure_threshold=5, recovery_timeout=60)
    def get_firmette(self, latitude: float, longitude: float) -> dict:
        try:
            response = requests.get(
                f"{settings.FEMA_MSC_API}/firmette",
                params={"lat": latitude, "lon": longitude},
                timeout=10
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to get Firmette: {str(e)}")
            raise

    @circuit(failure_threshold=5, recovery_timeout=60)
    def get_flood_zone(self, latitude: float, longitude: float) -> dict:
        try:
            response = requests.get(
                f"{settings.FEMA_MSC_API}/flood-zone",
                params={"lat": latitude, "lon": longitude},
                timeout=10
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to get flood zone: {str(e)}")
            raise

    def validate_coordinates(self, latitude: float, longitude: float) -> bool:
        if not (-90 <= latitude <= 90) or not (-180 <= longitude <= 180):
            raise ValueError("Invalid coordinates")
        return True

    def process_api_response(self, response: dict) -> dict:
        return {
            "flood_zone": response.get("floodZone"),
            "base_flood_elevation": response.get("baseFloodElevation"),
            "firmette_url": response.get("firmetteUrl")
        }

    def handle_rate_limit(self, response: requests.Response) -> bool:
        if response.status_code == 429:
            logger.warning("Rate limit exceeded for FEMA MSC API")
            return False
        return True

    def parse_flood_zone_data(self, data: dict) -> dict:
        return {
            "flood_zone": data.get("floodZone"),
            "effective_date": data.get("effectiveDate")
        }