from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import List
from app.core.deps import get_db
from app.services.fema_api import FEMAAPIService
from app.services.storage import DocumentStorage
from app.models.disaster import DisasterDeclaration
from app.models.policy import PolicyVersion
from app.schemas.disaster import DisasterResponse, DisasterCreate
from app.schemas.policy import PolicyResponse

router = APIRouter()
fema_service = FEMAAPIService()
storage_service = DocumentStorage()

@router.get("/{dr_number}", response_model=DisasterResponse)
async def get_disaster(
    dr_number: str,
    db: Session = Depends(get_db)
):
    """Get disaster declaration details"""
    disaster = await fema_service.sync_disaster_data(dr_number, db)
    return disaster

@router.get("/{dr_number}/policies", response_model=List[PolicyResponse])
async def get_disaster_policies(
    dr_number: str,
    db: Session = Depends(get_db)
):
    """Get all policies applicable to a disaster"""
    disaster = db.query(DisasterDeclaration).filter_by(dr_number=dr_number).first()
    if not disaster:
        raise HTTPException(status_code=404, detail="Disaster not found")
    return disaster.applicable_policies

@router.post("/{dr_number}/documents")
async def upload_disaster_document(
    dr_number: str,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload a document for a disaster"""
    disaster = db.query(DisasterDeclaration).filter_by(dr_number=dr_number).first()
    if not disaster:
        raise HTTPException(status_code=404, detail="Disaster not found")
    
    filepath = await storage_service.save_document(file, dr_number)
    return {"filepath": filepath}

@router.get("/{dr_number}/documents")
async def list_disaster_documents(
    dr_number: str,
    db: Session = Depends(get_db)
):
    """List all documents for a disaster"""
    disaster = db.query(DisasterDeclaration).filter_by(dr_number=dr_number).first()
    if not disaster:
        raise HTTPException(status_code=404, detail="Disaster not found")
    
    documents = await storage_service.list_documents(dr_number)
    return {"documents": documents}
