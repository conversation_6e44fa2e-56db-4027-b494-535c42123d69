from typing import List, Dict, Optional, Any
from datetime import datetime
import asyncio
import logging
import easyocr
import pytesseract
from paddleocr import PaddleOCR
import pdf2image
import spacy
from transformers import pipeline
from sentence_transformers import SentenceTransformer
import numpy as np
from pathlib import Path
import cv2
import re
from app.core.config import settings

logger = logging.getLogger(__name__)

class DocumentAnalyzer:
    def __init__(self):
        self.ocr_engine = settings.OCR_ENGINE
        self.enable_gpu = settings.ENABLE_GPU
        
        # Initialize OCR engines
        if self.ocr_engine == "easyocr":
            self.reader = easyocr.Reader(['en'], gpu=self.enable_gpu)
        elif self.ocr_engine == "paddleocr":
            self.reader = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=self.enable_gpu)
        
        # Initialize NLP models
        self.nlp = spacy.load("en_core_web_sm")
        self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.qa_pipeline = pipeline("question-answering", model="distilbert-base-cased-distilled-squad")
        
        # Compile regex patterns
        self.date_pattern = re.compile(r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b')
        self.requirement_patterns = [
            re.compile(r'\bmust\s+', re.I),
            re.compile(r'\bshall\s+', re.I),
            re.compile(r'\brequired\s+to\s+', re.I),
            re.compile(r'\brequirements?\s+include\s+', re.I)
        ]
    
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """Process a document through the complete analysis pipeline."""
        try:
            # Convert PDF to images
            if file_path.lower().endswith('.pdf'):
                images = await self._pdf_to_images(file_path)
            else:
                images = [cv2.imread(file_path)]
            
            # Extract text using OCR
            text_blocks = await self._extract_text(images)
            
            # Extract metadata
            metadata = await self._extract_metadata(text_blocks, file_path)
            
            # Extract requirements
            requirements = await self._extract_requirements(text_blocks)
            
            # Extract temporal information
            temporal_info = await self._extract_temporal_info(text_blocks)
            
            # Detect tables
            tables = await self._detect_tables(images)
            
            # Analyze document structure
            structure = await self._analyze_structure(text_blocks)
            
            return {
                "metadata": metadata,
                "requirements": requirements,
                "temporal_info": temporal_info,
                "tables": tables,
                "structure": structure,
                "text_blocks": text_blocks
            }
            
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {str(e)}")
            raise
    
    async def _pdf_to_images(self, pdf_path: str) -> List[np.ndarray]:
        """Convert PDF pages to images."""
        try:
            return pdf2image.convert_from_path(pdf_path)
        except Exception as e:
            logger.error(f"Error converting PDF to images: {str(e)}")
            raise
    
    async def _extract_text(self, images: List[np.ndarray]) -> List[Dict[str, Any]]:
        """Extract text from images using the configured OCR engine."""
        text_blocks = []
        
        try:
            for page_num, image in enumerate(images, 1):
                if self.ocr_engine == "easyocr":
                    results = self.reader.readtext(image)
                    for box, text, conf in results:
                        text_blocks.append({
                            "page": page_num,
                            "text": text,
                            "confidence": conf,
                            "bbox": box,
                            "type": "text"
                        })
                        
                elif self.ocr_engine == "paddleocr":
                    result = self.reader.ocr(image)
                    for line in result:
                        box, (text, conf) = line
                        text_blocks.append({
                            "page": page_num,
                            "text": text,
                            "confidence": conf,
                            "bbox": box,
                            "type": "text"
                        })
                        
                else:  # Default to Tesseract
                    text = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
                    for i in range(len(text['text'])):
                        if text['conf'][i] > 0:  # Filter out low confidence results
                            text_blocks.append({
                                "page": page_num,
                                "text": text['text'][i],
                                "confidence": text['conf'][i] / 100,
                                "bbox": (text['left'][i], text['top'][i], 
                                        text['left'][i] + text['width'][i], 
                                        text['top'][i] + text['height'][i]),
                                "type": "text"
                            })
            
            return text_blocks
            
        except Exception as e:
            logger.error(f"Error extracting text: {str(e)}")
            raise
    
    async def _extract_metadata(self, text_blocks: List[Dict], file_path: str) -> Dict[str, Any]:
        """Extract metadata from the document."""
        try:
            # Get basic file metadata
            path = Path(file_path)
            metadata = {
                "filename": path.name,
                "file_type": path.suffix.lower(),
                "file_size": path.stat().st_size,
                "created": datetime.fromtimestamp(path.stat().st_ctime),
                "modified": datetime.fromtimestamp(path.stat().st_mtime)
            }
            
            # Extract document title (usually in the first few blocks)
            title_candidates = [block["text"] for block in text_blocks[:5] 
                              if len(block["text"]) > 10 and block["confidence"] > 0.8]
            if title_candidates:
                metadata["title"] = title_candidates[0]
            
            # Extract dates
            dates = []
            for block in text_blocks:
                dates.extend(self.date_pattern.findall(block["text"]))
            if dates:
                metadata["dates"] = dates
            
            # Extract organizations using spaCy
            text = " ".join(block["text"] for block in text_blocks)
            doc = self.nlp(text)
            orgs = [ent.text for ent in doc.ents if ent.label_ == "ORG"]
            if orgs:
                metadata["organizations"] = list(set(orgs))
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata: {str(e)}")
            raise
    
    async def _extract_requirements(self, text_blocks: List[Dict]) -> List[Dict[str, Any]]:
        """Extract requirements from text blocks."""
        requirements = []
        
        try:
            for block in text_blocks:
                text = block["text"]
                
                # Check for requirement patterns
                if any(pattern.search(text) for pattern in self.requirement_patterns):
                    # Use spaCy for better sentence segmentation
                    doc = self.nlp(text)
                    for sent in doc.sents:
                        if any(pattern.search(sent.text) for pattern in self.requirement_patterns):
                            requirements.append({
                                "text": sent.text,
                                "page": block["page"],
                                "confidence": block["confidence"],
                                "type": "requirement",
                                "source_block": block
                            })
            
            return requirements
            
        except Exception as e:
            logger.error(f"Error extracting requirements: {str(e)}")
            raise
    
    async def _extract_temporal_info(self, text_blocks: List[Dict]) -> List[Dict[str, Any]]:
        """Extract temporal information from text blocks."""
        temporal_info = []
        
        try:
            for block in text_blocks:
                dates = self.date_pattern.findall(block["text"])
                if dates:
                    # Use spaCy to get the context around dates
                    doc = self.nlp(block["text"])
                    for date in dates:
                        # Find the sentence containing the date
                        for sent in doc.sents:
                            if date in sent.text:
                                temporal_info.append({
                                    "date": date,
                                    "context": sent.text,
                                    "page": block["page"],
                                    "confidence": block["confidence"],
                                    "type": "temporal"
                                })
            
            return temporal_info
            
        except Exception as e:
            logger.error(f"Error extracting temporal information: {str(e)}")
            raise
    
    async def _detect_tables(self, images: List[np.ndarray]) -> List[Dict[str, Any]]:
        """Detect and extract tables from document images."""
        tables = []
        
        try:
            for page_num, image in enumerate(images, 1):
                # Convert to grayscale
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                
                # Apply adaptive thresholding
                thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                            cv2.THRESH_BINARY_INV, 11, 2)
                
                # Detect horizontal and vertical lines
                horizontal = np.copy(thresh)
                vertical = np.copy(thresh)
                
                # Specify size on horizontal axis
                cols = horizontal.shape[1]
                horizontal_size = cols // 30
                horizontalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
                horizontal = cv2.erode(horizontal, horizontalStructure)
                horizontal = cv2.dilate(horizontal, horizontalStructure)
                
                # Specify size on vertical axis
                rows = vertical.shape[0]
                vertical_size = rows // 30
                verticalStructure = cv2.getStructuringElement(cv2.MORPH_RECT, (1, vertical_size))
                vertical = cv2.erode(vertical, verticalStructure)
                vertical = cv2.dilate(vertical, verticalStructure)
                
                # Combine horizontal and vertical lines
                table_mask = cv2.add(horizontal, vertical)
                
                # Find contours
                contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # Filter and process potential tables
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    if w > 100 and h > 100:  # Filter small boxes
                        table_region = image[y:y+h, x:x+w]
                        
                        # Extract text from table region
                        if self.ocr_engine == "easyocr":
                            table_text = self.reader.readtext(table_region)
                        elif self.ocr_engine == "paddleocr":
                            table_text = self.reader.ocr(table_region)
                        else:
                            table_text = pytesseract.image_to_string(table_region)
                        
                        tables.append({
                            "page": page_num,
                            "bbox": (x, y, x+w, y+h),
                            "text": table_text,
                            "type": "table"
                        })
            
            return tables
            
        except Exception as e:
            logger.error(f"Error detecting tables: {str(e)}")
            raise
    
    async def _analyze_structure(self, text_blocks: List[Dict]) -> Dict[str, Any]:
        """Analyze the document structure."""
        try:
            structure = {
                "sections": [],
                "headers": [],
                "paragraphs": [],
                "lists": []
            }
            
            current_section = None
            current_list = []
            
            for block in text_blocks:
                text = block["text"]
                
                # Detect headers (all caps, numbered, etc.)
                if text.isupper() or re.match(r'^\d+\.?\s+[A-Z]', text):
                    if current_section:
                        structure["sections"].append(current_section)
                    current_section = {
                        "header": text,
                        "content": [],
                        "page": block["page"]
                    }
                    structure["headers"].append({
                        "text": text,
                        "page": block["page"],
                        "level": 1 if text.isupper() else 2
                    })
                
                # Detect list items
                elif re.match(r'^\s*[\u2022\u2023\u25E6\u2043\u2219]\s+|^\s*\d+\.\s+|^\s*[a-z]\)\s+', text):
                    current_list.append({
                        "text": text,
                        "page": block["page"]
                    })
                
                # Regular paragraph
                else:
                    if current_list:
                        structure["lists"].append(current_list)
                        current_list = []
                    
                    if len(text.strip()) > 50:  # Minimum length for paragraphs
                        structure["paragraphs"].append({
                            "text": text,
                            "page": block["page"]
                        })
                    
                    if current_section:
                        current_section["content"].append(text)
            
            # Add final section if exists
            if current_section:
                structure["sections"].append(current_section)
            
            # Add final list if exists
            if current_list:
                structure["lists"].append(current_list)
            
            return structure
            
        except Exception as e:
            logger.error(f"Error analyzing document structure: {str(e)}")
            raise
