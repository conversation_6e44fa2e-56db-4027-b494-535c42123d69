from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List
from app.models.document import ComplianceStatus, RiskLevel, MitigationStatus, DocumentType

class DocumentBase(BaseModel):
    filename: str
    file_path: str
    mime_type: Optional[str] = None
    status: str = "pending"
    description: Optional[str] = None
    compliance_status: Optional[ComplianceStatus] = ComplianceStatus.UNDER_REVIEW
    risk_level: Optional[RiskLevel] = RiskLevel.LOW
    mitigation_status: Optional[MitigationStatus] = MitigationStatus.NOT_STARTED
    document_type: DocumentType = DocumentType.OTHER
    review_notes: Optional[str] = None
    review_date: Optional[datetime] = None

class DocumentCreate(DocumentBase):
    pass

class DocumentUpdate(DocumentBase):
    filename: Optional[str] = None
    file_path: Optional[str] = None
    mime_type: Optional[str] = None
    status: Optional[str] = None
    description: Optional[str] = None
    compliance_status: Optional[ComplianceStatus] = None
    risk_level: Optional[RiskLevel] = None
    mitigation_status: Optional[MitigationStatus] = None
    document_type: Optional[DocumentType] = None
    review_notes: Optional[str] = None
    review_date: Optional[datetime] = None

class DocumentResponse(DocumentBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class DocumentList(BaseModel):
    total: int
    items: List[DocumentResponse]
