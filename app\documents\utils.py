"""
Helper functions for document compliance checking and validation.
"""
from datetime import datetime, timedelta
import re
from typing import List, Dict, Tuple, Optional

from .models import ComplianceStatus, Document, RiskLevel


def calculate_temporal_compliance(
    last_review_date: datetime,
    review_frequency_days: int,
    grace_period_days: int = 30
) -> Tuple[bool, int]:
    """
    Calculate compliance status based on review dates and frequency.
    
    Args:
        last_review_date: Date of last document review
        review_frequency_days: Required frequency of reviews in days
        grace_period_days: Additional days allowed before non-compliance
        
    Returns:
        Tuple of (is_compliant: bool, days_until_review: int)
    """
    today = datetime.now()
    next_review_date = last_review_date + timedelta(days=review_frequency_days)
    grace_period_end = next_review_date + timedelta(days=grace_period_days)
    
    days_until_review = (next_review_date - today).days
    is_compliant = today <= grace_period_end
    
    return is_compliant, days_until_review


def assess_risk_level(
    compliance_scores: Dict[str, float],
    high_risk_threshold: float = 0.7,
    medium_risk_threshold: float = 0.85
) -> str:
    """
    Determine overall risk level based on compliance scores.
    
    Args:
        compliance_scores: Dictionary of compliance category scores (0-1)
        high_risk_threshold: Score threshold for high risk classification
        medium_risk_threshold: Score threshold for medium risk classification
        
    Returns:
        Risk level classification ('high', 'medium', or 'low')
    """
    avg_score = sum(compliance_scores.values()) / len(compliance_scores)
    min_score = min(compliance_scores.values())
    
    if min_score < high_risk_threshold:
        return 'high'
    elif avg_score < medium_risk_threshold:
        return 'medium'
    return 'low'


def validate_document_sections(
    content: str,
    required_sections: List[str]
) -> List[str]:
    """
    Check for presence of required sections in document content.
    
    Args:
        content: Document text content
        required_sections: List of section names that must be present
        
    Returns:
        List of missing section names
    """
    missing_sections = []
    for section in required_sections:
        if not re.search(rf"\b{re.escape(section)}\b", content, re.IGNORECASE):
            missing_sections.append(section)
    return missing_sections


def extract_references(content: str) -> List[str]:
    """
    Extract reference strings from document content.
    
    Args:
        content: Document text content
        
    Returns:
        List of unique reference strings found
    """
    # Match common reference patterns (e.g., "Ref: ABC-123", "[REF:ABC-123]")
    patterns = [
        r'Ref:\s*([A-Z0-9-]+)',
        r'\[REF:([^\]]+)\]',
        r'Reference ID:\s*([A-Z0-9-]+)'
    ]
    
    references = []
    for pattern in patterns:
        matches = re.finditer(pattern, content, re.IGNORECASE)
        references.extend(match.group(1) for match in matches)
    
    return list(set(references))


def generate_compliance_summary(
    document_id: str,
    metadata: Dict,
    compliance_status: bool,
    risk_level: str,
    issues: Optional[List[str]] = None
) -> str:
    """
    Generate human-readable summary of compliance results.
    
    Args:
        document_id: Unique identifier for the document
        metadata: Document metadata dictionary
        compliance_status: Overall compliance status
        risk_level: Assessed risk level
        issues: List of compliance issues found
        
    Returns:
        Formatted summary string
    """
    summary = [
        f"Compliance Summary for Document {document_id}",
        "-" * 50,
        f"Title: {metadata.get('title', 'N/A')}",
        f"Last Review: {metadata.get('last_review', 'N/A')}",
        f"Status: {'Compliant' if compliance_status else 'Non-Compliant'}",
        f"Risk Level: {risk_level.title()}"
    ]
    
    if issues:
        summary.extend([
            "\nIdentified Issues:",
            *[f"- {issue}" for issue in issues]
        ])
    
    return "\n".join(summary) 