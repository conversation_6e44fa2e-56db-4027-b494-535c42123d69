from datetime import datetime
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.disaster import DisasterDeclaration
from app.models.policy import PolicyVersion

class IncidentPeriodService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def validate_dates(self, start_date: datetime, end_date: datetime) -> bool:
        """Validate incident period dates."""
        if not start_date or not end_date:
            return False
        
        if start_date > end_date:
            return False
            
        if end_date > datetime.now():
            return False
            
        return True

    async def get_applicable_policies(self, dr_number: str) -> List[PolicyVersion]:
        """Get policies applicable to a disaster declaration."""
        stmt = select(DisasterDeclaration).where(DisasterDeclaration.dr_number == dr_number)
        result = await self.db.execute(stmt)
        disaster = result.scalar_one_or_none()
        
        if not disaster:
            return []
            
        return disaster.applicable_policies

    async def validate_policy_applicability(self, policy_id: int, incident_date: datetime) -> bool:
        """Check if a policy version is applicable for a given date."""
        stmt = select(PolicyVersion).where(PolicyVersion.id == policy_id)
        result = await self.db.execute(stmt)
        policy_version = result.scalar_one_or_none()
        
        if not policy_version:
            return False
            
        # Check if the incident date falls within the policy's effective period
        if incident_date < policy_version.effective_date:
            return False
            
        if policy_version.expiration_date and incident_date > policy_version.expiration_date:
            return False
            
        return True

    async def get_active_policies_for_period(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> List[PolicyVersion]:
        """Get all policies active during a given period."""
        stmt = select(PolicyVersion).where(
            PolicyVersion.effective_date <= end_date,
            (PolicyVersion.expiration_date.is_(None) | (PolicyVersion.expiration_date >= start_date))
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_policy_history(
        self,
        dr_number: str,
        policy_id: int
    ) -> List[PolicyVersion]:
        """Get historical versions of a policy applicable to a disaster."""
        stmt = select(PolicyVersion).where(
            PolicyVersion.id == policy_id,
            PolicyVersion.applicable_disasters.any(dr_number=dr_number)
        ).order_by(PolicyVersion.effective_date.desc())
        result = await self.db.execute(stmt)
        return result.scalars().all()
