"""API endpoints for FEMA applications and compliance reviews."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session

from app.api import deps
from app.services.compliance_review_service import ComplianceReviewService
from app.schemas.fema_application import (
    FEMAApplication,
    FEMAApplicationCreate,
    FEMAApplicationUpdate,
    ComplianceDocument,
    ComplianceDocumentCreate,
    ComplianceReview,
    ComplianceReviewCreate,
    ComplianceReviewUpdate,
    ReviewDocumentLink
)
from app.core.config import get_settings

settings = get_settings()
router = APIRouter()

@router.post("/applications/", response_model=FEMAApplication)
async def create_application(
    *,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user),
    application_in: FEMAApplicationCreate
) -> FEMAApplication:
    """Create a new FEMA application."""
    service = ComplianceReviewService(db)
    application = await service.create_application(
        disaster_number=application_in.disaster_number,
        applicant_id=application_in.applicant_id,
        project_title=application_in.project_title,
        user_id=current_user.id
    )
    return application

@router.get("/applications/", response_model=List[FEMAApplication])
async def list_applications(
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 100
) -> List[FEMAApplication]:
    """List all FEMA applications."""
    service = ComplianceReviewService(db)
    applications = await service.get_applications(skip=skip, limit=limit)
    return applications

@router.get("/applications/{application_id}", response_model=FEMAApplication)
async def get_application(
    application_id: str,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user)
) -> FEMAApplication:
    """Get a specific FEMA application."""
    service = ComplianceReviewService(db)
    application = await service.get_application(application_id)
    if not application:
        raise HTTPException(status_code=404, detail="Application not found")
    return application

@router.put("/applications/{application_id}", response_model=FEMAApplication)
async def update_application(
    *,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user),
    application_id: str,
    application_in: FEMAApplicationUpdate
) -> FEMAApplication:
    """Update a FEMA application."""
    service = ComplianceReviewService(db)
    application = await service.update_application(
        application_id=application_id,
        update_data=application_in.dict(exclude_unset=True)
    )
    if not application:
        raise HTTPException(status_code=404, detail="Application not found")
    return application

@router.post("/applications/{application_id}/documents/", response_model=ComplianceDocument)
async def upload_document(
    *,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user),
    application_id: str,
    document_in: ComplianceDocumentCreate,
    file: UploadFile = File(...)
) -> ComplianceDocument:
    """Upload a compliance document for an application."""
    service = ComplianceReviewService(db)
    document = await service.upload_compliance_document(
        application_id=application_id,
        document_type=document_in.document_type,
        file_path="",  # Will be set by service
        original_filename=file.filename,
        file_size=0,  # Will be set by service
        mime_type=file.content_type
    )
    return document

@router.post("/applications/{application_id}/reviews/", response_model=ComplianceReview)
async def create_review(
    *,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user),
    application_id: str,
    review_in: ComplianceReviewCreate
) -> ComplianceReview:
    """Create a new compliance review for an application."""
    service = ComplianceReviewService(db)
    review = await service.create_compliance_review(
        application_id=application_id,
        document_id=review_in.document_id,
        policy_version_id=review_in.policy_version_id,
        review_type=review_in.review_type,
        reviewer_id=current_user.id
    )
    return review

@router.put("/reviews/{review_id}", response_model=ComplianceReview)
async def update_review(
    *,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user),
    review_id: str,
    review_in: ComplianceReviewUpdate
) -> ComplianceReview:
    """Update a compliance review."""
    service = ComplianceReviewService(db)
    review = await service.update_review_status(
        review_id=review_id,
        status=review_in.status,
        findings=review_in.findings,
        requirements_met=review_in.requirements_met,
        deficiencies=review_in.deficiencies,
        recommendations=review_in.recommendations
    )
    return review

@router.get("/applications/{application_id}/reviews/", response_model=List[ComplianceReview])
async def list_application_reviews(
    application_id: str,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user)
) -> List[ComplianceReview]:
    """List all reviews for an application."""
    service = ComplianceReviewService(db)
    reviews = await service.get_application_reviews(application_id)
    return reviews

@router.post("/reviews/{review_id}/documents/", response_model=ReviewDocumentLink)
async def link_supporting_document(
    *,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_user),
    review_id: str,
    document_id: str
) -> ReviewDocumentLink:
    """Link a supporting document to a review."""
    service = ComplianceReviewService(db)
    link = await service.link_supporting_document(
        review_id=review_id,
        document_id=document_id
    )
    return link 