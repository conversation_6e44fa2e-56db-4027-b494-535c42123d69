from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

class ReportType(str, Enum):
    COMPLIANCE = "compliance"
    COST = "cost"
    RISK = "risk"
    PROGRESS = "progress"
    CUSTOM = "custom"

class ReportBase(BaseModel):
    title: str
    type: ReportType
    parameters: Dict[str, Any]
    format: str  # PDF, EXCEL, JSON

class ReportCreate(ReportBase):
    pass

class ReportResponse(ReportBase):
    id: int
    created_at: datetime
    created_by: int
    data: Dict[str, Any]
    
    class Config:
        from_attributes = True

class ReportTemplateBase(BaseModel):
    name: str
    description: str
    type: ReportType
    template_data: Dict[str, Any]
    is_system: bool = False

class ReportTemplateCreate(ReportTemplateBase):
    pass

class ReportTemplateResponse(ReportTemplateBase):
    id: int
    
    class Config:
        from_attributes = True
