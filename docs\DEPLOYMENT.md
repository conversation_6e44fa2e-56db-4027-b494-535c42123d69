# ComplianceMax Deployment Guide

## System Requirements

### Hardware Requirements
- CPU: 4+ cores recommended
- RAM: 8GB minimum, 16GB recommended
- Storage: 50GB minimum for application and data

### Software Requirements
- Python 3.11+
- Redis 5.0+
- PostgreSQL 13+
- Node.js 18+ (for frontend)
- Docker (optional, for containerized deployment)

## Installation

### 1. Environment Setup

```bash
# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# OR
.venv\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Setup

```bash
# Create database
createdb compliancemax

# Run migrations
alembic upgrade head

# Verify database
python -m scripts.verify_db
```

### 3. Redis Setup

```bash
# Install Redis (Ubuntu/Debian)
sudo apt-get install redis-server

# Start Redis service
sudo systemctl start redis

# Verify Redis connection
redis-cli ping
```

### 4. Environment Configuration

Create `.env` file in project root:

```ini
# Application
DEBUG=false
SECRET_KEY=your-secure-secret-key
ALLOWED_HOSTS=your-domain.com,api.your-domain.com

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/compliancemax

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_API_KEY_DB=1

# Security
CORS_ORIGINS=https://your-domain.com
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true
```

### 5. Security Setup

```bash
# Generate secret key
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Create initial admin API key
python scripts/create_admin_key.py

# Configure SSL/TLS certificates
sudo certbot --nginx -d your-domain.com
```

## Production Deployment

### Option 1: Traditional Deployment

1. **Setup Nginx:**

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

2. **Setup Systemd Service:**

```ini
[Unit]
Description=ComplianceMax Application
After=network.target

[Service]
User=compliancemax
Group=compliancemax
WorkingDirectory=/opt/compliancemax
Environment="PATH=/opt/compliancemax/.venv/bin"
ExecStart=/opt/compliancemax/.venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

### Option 2: Docker Deployment

1. **Build Docker Image:**

```bash
docker build -t compliancemax .
```

2. **Run with Docker Compose:**

```yaml
version: '3.8'
services:
  app:
    image: compliancemax
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - db
      - redis

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: compliancemax
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - pgdata:/var/lib/postgresql/data

  redis:
    image: redis:6
    volumes:
      - redisdata:/data

volumes:
  pgdata:
  redisdata:
```

## Monitoring Setup

1. **Configure Prometheus:**

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'compliancemax'
    static_configs:
      - targets: ['localhost:8000']
```

2. **Setup Grafana:**
- Import provided dashboards from `monitoring/dashboards`
- Configure alerting rules
- Set up notification channels

## Security Checklist

- [ ] Generate strong secret key
- [ ] Configure SSL/TLS certificates
- [ ] Set secure cookie settings
- [ ] Configure CORS properly
- [ ] Set up API key rotation
- [ ] Configure rate limiting
- [ ] Enable security headers
- [ ] Set up backup system
- [ ] Configure logging
- [ ] Set up monitoring

## Backup and Recovery

```bash
# Database backup
pg_dump compliancemax > backup.sql

# Redis backup
redis-cli save

# Application state backup
tar -czf app_state.tar.gz /opt/compliancemax/storage

# Restore database
psql compliancemax < backup.sql
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues:**
```bash
# Check database status
sudo systemctl status postgresql

# Verify connection
psql -U user -d compliancemax -h localhost
```

2. **Redis Connection Issues:**
```bash
# Check Redis status
sudo systemctl status redis

# Clear Redis cache
redis-cli flushall
```

3. **Application Errors:**
```bash
# Check logs
tail -f /var/log/compliancemax/app.log

# Verify permissions
sudo chown -R compliancemax:compliancemax /opt/compliancemax
```

## Maintenance

### Regular Tasks

1. **Database Maintenance:**
```bash
# Vacuum database
vacuumdb -z -d compliancemax

# Analyze tables
psql -d compliancemax -c "ANALYZE VERBOSE"
```

2. **Log Rotation:**
```bash
# Configure logrotate
sudo nano /etc/logrotate.d/compliancemax
```

3. **SSL Certificate Renewal:**
```bash
# Auto-renew certificates
sudo certbot renew
```

### Monitoring

1. **Check System Health:**
```bash
# Application health
curl https://your-domain.com/health

# Monitor resources
htop
```

2. **View Logs:**
```bash
# Application logs
tail -f /var/log/compliancemax/app.log

# Nginx access logs
tail -f /var/log/nginx/access.log
```

## Support

For additional support:
- Documentation: `/docs`
- Issue Tracker: GitHub Issues
- Email: <EMAIL> 