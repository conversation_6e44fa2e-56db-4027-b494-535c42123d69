export enum DocumentType {
    POLICY = "policy",
    MEMO = "memo",
    FIELD_GUIDE = "field_guide",
    APPLICANT_UPLOAD = "applicant_upload"
}

export enum ProcessingStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    COMPLETED = "completed",
    FAILED = "failed"
}

export interface ExtractedData {
    text: string;
    metadata: Record<string, any>;
    confidence_score: number;
    page_number: number;
}

export interface ProcessedDocument {
    id: string;
    filename: string;
    document_type: DocumentType;
    upload_date: string;
    status: ProcessingStatus;
    file_path: string;
    extracted_data: ExtractedData[];
    error_message?: string;
    processed_date?: string;
    dr_number?: string;
    uploader_id: string;
    mime_type: string;
    file_size: number;
}
