from datetime import datetime, timedelta
import uuid
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi_limiter.depends import RateLimiter
from app.core.database import get_db
from app.core.security import (
    authenticate_user, create_access_token, get_password_hash,
    get_current_active_user, validate_password_complexity,
    generate_totp_secret, generate_totp_uri, verify_totp
)
from app.core.redis import RedisSessionManager
from app.models.user import User
from app.schemas.token import Token, TOTPVerifyRequest, TOTPSetupResponse, TOTPResponse
from app.schemas.user import UserCreate, UserResponse
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix='/api/v1/auth', tags=['auth'])

@router.post('/register', response_model=UserResponse, status_code=status.HTTP_201_CREATED,
             dependencies=[Depends(RateLimiter(times=5, seconds=60))])
async def register_user(user: UserCreate, db: AsyncSession = Depends(get_db)):
    try:
        logger.info(f"Received register request for username: {user.username}")
        db_user = await db.execute(select(User).where(User.username == user.username))
        db_user = db_user.scalars().first()
        if db_user:
            logger.warning(f"Username {user.username} already registered")
            raise HTTPException(status_code=400, detail="Username already registered")
        logger.info("Validating password complexity")
        if not validate_password_complexity(user.password):
            logger.warning("Password complexity validation failed")
            raise HTTPException(status_code=400, detail="Password complexity insufficient")
        logger.info("Hashing password")
        hashed_password = get_password_hash(user.password)
        logger.info("Creating new user in database")
        db_user = User(username=user.username, full_name=user.full_name, hashed_password=hashed_password, is_active=True)
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)
        logger.info(f"User {user.username} registered successfully")
        return db_user
    except Exception as e:
        logger.error(f"Error in register_user: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post('/login', response_model=Token, dependencies=[Depends(RateLimiter(times=5, seconds=60))])
async def login(request: Request, form_data: OAuth2PasswordRequestForm = Depends(), db: AsyncSession = Depends(get_db)):
    try:
        user = await authenticate_user(db, form_data.username, form_data.password)
        if not user:
            raise HTTPException(status_code=401, detail="Incorrect credentials")
        if not user.is_active:
            raise HTTPException(status_code=400, detail="Inactive account")
        if user.is_two_factor_enabled:
            session_id = str(uuid.uuid4())
            await RedisSessionManager.set_session(None, {"user_id": user.id, "username": user.username}, max_age=300)
            return {"access_token": "", "token_type": "bearer", "requires_second_factor": True, "session_id": session_id}
        access_token = create_access_token(data={"sub": user.username})
        response = {"access_token": access_token, "token_type": "bearer"}
        response = JSONResponse(content=response)
        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            secure=True,
            samesite="strict",
            max_age=settings.SESSION_EXPIRE
        )
        return response
    except Exception as e:
        logger.error(f"Error in login: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post('/verify-2fa', response_model=TOTPResponse, dependencies=[Depends(RateLimiter(times=5, seconds=60))])
async def verify_2fa(request: Request, verification_request: TOTPVerifyRequest, db: AsyncSession = Depends(get_db)):
    try:
        session_data = await RedisSessionManager.get_session(Request({"type": "http", "cookies": {"session_id": verification_request.session_id}}))
        if not session_data:
            raise HTTPException(status_code=400, detail="Invalid session")
        user = await db.execute(select(User).where(User.id == session_data["user_id"]))
        user = user.scalars().first()
        if not user or not verify_totp(user.totp_secret, verification_request.totp_code):
            raise HTTPException(status_code=401, detail="Invalid 2FA code")
        await RedisSessionManager.delete_session(Request({"type": "http", "cookies": {"session_id": verification_request.session_id}}), None)
        access_token = create_access_token(data={"sub": user.username, "two_factor_verified": True})
        response = {"access_token": access_token, "token_type": "bearer"}
        response = JSONResponse(content=response)
        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            secure=True,
            samesite="strict",
            max_age=settings.SESSION_EXPIRE
        )
        return response
    except Exception as e:
        logger.error(f"Error in verify_2fa: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post('/enable-2fa', response_model=TOTPSetupResponse, dependencies=[Depends(RateLimiter(times=5, seconds=60))])
async def enable_2fa(current_user: User = Depends(get_current_active_user), db: AsyncSession = Depends(get_db)):
    try:
        encrypted_secret = generate_totp_secret()
        current_user.totp_secret = encrypted_secret
        current_user.is_two_factor_enabled = False
        db.add(current_user)
        await db.commit()
        totp_uri = generate_totp_uri(current_user.username, encrypted_secret)
        return {"secret": encrypted_secret, "qr_code_uri": totp_uri}
    except Exception as e:
        logger.error(f"Error in enable_2fa: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post('/verify-2fa-setup', dependencies=[Depends(RateLimiter(times=5, seconds=60))])
async def verify_2fa_setup(verification: TOTPVerifyRequest, current_user: User = Depends(get_current_active_user), db: AsyncSession = Depends(get_db)):
    try:
        if not current_user.totp_secret or not verify_totp(current_user.totp_secret, verification.totp_code):
            raise HTTPException(status_code=401, detail="Invalid 2FA code")
        current_user.is_two_factor_enabled = True
        db.add(current_user)
        await db.commit()
        return {"detail": "2FA enabled"}
    except Exception as e:
        logger.error(f"Error in verify_2fa_setup: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post('/disable-2fa', dependencies=[Depends(RateLimiter(times=5, seconds=60))])
async def disable_2fa(current_user: User = Depends(get_current_active_user), db: AsyncSession = Depends(get_db)):
    try:
        if not current_user.is_two_factor_enabled:
            raise HTTPException(status_code=400, detail="2FA not enabled")
        current_user.totp_secret = None
        current_user.is_two_factor_enabled = False
        db.add(current_user)
        await db.commit()
        return {"detail": "2FA disabled"}
    except Exception as e:
        logger.error(f"Error in disable_2fa: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    return current_user

class PasswordChangeRequest(BaseModel):
    current_password: str
    new_password: str

@router.post("/change-password", status_code=status.HTTP_200_OK)
async def change_password(request: PasswordChangeRequest, current_user: User = Depends(get_current_active_user), db: AsyncSession = Depends(get_db)):
    try:
        if not verify_password(request.current_password, current_user.hashed_password):
            raise HTTPException(status_code=400, detail="Incorrect current password")
        if not validate_password_complexity(request.new_password):
            raise HTTPException(status_code=400, detail="New password does not meet complexity requirements")
        current_user.hashed_password = get_password_hash(request.new_password)
        db.add(current_user)
        await db.commit()
        return {"detail": "Password changed successfully"}
    except Exception as e:
        logger.error(f"Error in change_password: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")