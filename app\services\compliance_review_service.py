"""Service for handling enhanced compliance reviews."""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from uuid import uuid4

from app.models.fema import (
    FEMAApplication,
    ComplianceDocument,
    ApplicationStatus,
    ComplianceStatus
)
from app.models.compliance_review import (
    ComplianceReviewV2,
    ReviewDocument,
    PolicyRequirement
)
from app.services.fema_compliance_service import FEMAComplianceService
from app.core.config import get_settings

settings = get_settings()

class ComplianceReviewService:
    def __init__(self, db: Session):
        self.db = db
        self.compliance_service = FEMAComplianceService(db)

    async def create_application(
        self,
        disaster_number: str,
        applicant_id: str,
        project_title: str,
        user_id: int
    ) -> FEMAApplication:
        """Create a new FEMA application."""
        application = FEMAApplication(
            id=str(uuid4()),
            disaster_number=disaster_number,
            applicant_id=applicant_id,
            project_title=project_title,
            user_id=user_id
        )
        self.db.add(application)
        self.db.commit()
        self.db.refresh(application)
        return application

    async def upload_compliance_document(
        self,
        application_id: str,
        document_type: str,
        file_path: str,
        original_filename: str,
        file_size: Optional[int] = None,
        mime_type: Optional[str] = None
    ) -> ComplianceDocument:
        """Upload a new compliance document for an application."""
        document = ComplianceDocument(
            id=str(uuid4()),
            application_id=application_id,
            document_type=document_type,
            file_path=file_path,
            original_filename=original_filename,
            file_size=file_size,
            mime_type=mime_type,
            upload_date=datetime.utcnow()
        )
        self.db.add(document)
        self.db.commit()
        self.db.refresh(document)
        return document

    async def create_compliance_review(
        self,
        application_id: str,
        document_id: str,
        policy_version_id: str,
        review_type: str,
        reviewer_id: int
    ) -> ComplianceReviewV2:
        """Create a new compliance review."""
        review = ComplianceReviewV2(
            id=str(uuid4()),
            application_id=application_id,
            document_id=document_id,
            policy_version_id=policy_version_id,
            review_type=review_type,
            reviewer_id=reviewer_id,
            findings=[],
            requirements_met=[],
            deficiencies=[]
        )
        self.db.add(review)
        self.db.commit()
        self.db.refresh(review)
        return review

    async def update_review_status(
        self,
        review_id: str,
        status: ApplicationStatus,
        findings: Optional[List[Dict]] = None,
        requirements_met: Optional[List[str]] = None,
        deficiencies: Optional[List[Dict]] = None,
        recommendations: Optional[str] = None
    ) -> ComplianceReviewV2:
        """Update the status and findings of a compliance review."""
        review = self.db.query(ComplianceReviewV2).filter(
            ComplianceReviewV2.id == review_id
        ).first()
        
        if not review:
            raise ValueError(f"Review {review_id} not found")

        review.status = status
        if findings is not None:
            review.findings = findings
        if requirements_met is not None:
            review.requirements_met = requirements_met
        if deficiencies is not None:
            review.deficiencies = deficiencies
        if recommendations is not None:
            review.recommendations = recommendations

        # Update application status based on review
        self._update_application_status(review)

        self.db.commit()
        self.db.refresh(review)
        return review

    def _update_application_status(self, review: ComplianceReviewV2) -> None:
        """Update application status based on review findings."""
        application = review.application
        
        # Calculate compliance based on requirements met vs deficiencies
        total_requirements = len(review.requirements_met) + len(review.deficiencies)
        if total_requirements == 0:
            return

        compliance_ratio = len(review.requirements_met) / total_requirements
        
        if compliance_ratio >= 0.9:
            application.compliance_status = ComplianceStatus.COMPLIANT
        elif compliance_ratio >= 0.7:
            application.compliance_status = ComplianceStatus.REQUIRES_REVIEW
        else:
            application.compliance_status = ComplianceStatus.NON_COMPLIANT

        # Update application status based on review status
        if review.status == ApplicationStatus.APPROVED:
            application.status = ApplicationStatus.APPROVED
        elif review.status == ApplicationStatus.REJECTED:
            application.status = ApplicationStatus.REJECTED
        elif review.status == ApplicationStatus.NEEDS_INFO:
            application.status = ApplicationStatus.NEEDS_INFO
        else:
            application.status = ApplicationStatus.IN_REVIEW

        self.db.commit()

    async def link_supporting_document(
        self,
        review_id: str,
        document_id: str
    ) -> ReviewDocument:
        """Link a supporting document to a review."""
        link = ReviewDocument(
            review_id=review_id,
            document_id=document_id
        )
        self.db.add(link)
        self.db.commit()
        self.db.refresh(link)
        return link

    async def get_application_reviews(
        self,
        application_id: str
    ) -> List[Dict[str, Any]]:
        """Get all reviews for an application with their details."""
        reviews = self.db.query(ComplianceReviewV2).filter(
            ComplianceReviewV2.application_id == application_id
        ).all()

        return [{
            "id": review.id,
            "status": review.status,
            "review_type": review.review_type,
            "confidence_score": review.confidence_score,
            "findings": review.findings,
            "requirements_met": review.requirements_met,
            "deficiencies": review.deficiencies,
            "recommendations": review.recommendations,
            "review_date": review.review_date,
            "reviewer": review.reviewer.username if review.reviewer else None,
            "document": review.document.original_filename if review.document else None
        } for review in reviews] 