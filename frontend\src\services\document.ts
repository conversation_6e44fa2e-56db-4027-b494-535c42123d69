import axios from 'axios';
import { API_BASE_URL } from '../config';

export interface ValidationResult {
  timestamp: string;
  validation: {
    dr_number: string;
    scores: {
      overall: number;
      requirements: number;
      temporal: number;
      structure: number;
    };
    deficiencies: Array<{
      type: string;
      description: string;
      severity: string;
      policy_reference: string;
    }>;
    temporal_issues: Array<{
      type: string;
      description: string;
      date_range: {
        start: string;
        end: string;
      };
    }>;
    policy_matches: Array<{
      policy_id: string;
      policy_number: string;
      title: string;
      match_score: number;
      requirements_met: number;
      requirements_total: number;
    }>;
  };
}

export interface ComplianceHistory {
  timestamp: string;
  dr_number: string;
  overall_score: number;
  deficiencies_count: number;
  temporal_issues_count: number;
  status: string;
  policy_comparisons: Array<{
    policy_id: string;
    policy_number: string;
    similarity_score: number;
  }>;
}

export class DocumentService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/api/v1`;
  }

  async uploadDocument(file: File, drNumber: string): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('dr_number', drNumber);

    const response = await axios.post(`${this.baseUrl}/documents/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.document_id;
  }

  async analyzeDocument(documentId: string): Promise<any> {
    const response = await axios.post(`${this.baseUrl}/documents/${documentId}/analyze`);
    return response.data;
  }

  async validateCompliance(documentId: string, drNumber: string): Promise<ValidationResult> {
    const response = await axios.post(
      `${this.baseUrl}/documents/${documentId}/validate-compliance`,
      { dr_number: drNumber }
    );
    return response.data;
  }

  async getComplianceHistory(documentId: string): Promise<ComplianceHistory[]> {
    const response = await axios.get(
      `${this.baseUrl}/documents/${documentId}/compliance-history`
    );
    return response.data;
  }

  async getLatestCompliance(documentId: string): Promise<ValidationResult> {
    const response = await axios.get(
      `${this.baseUrl}/documents/${documentId}/latest-compliance`
    );
    return response.data;
  }

  async compareDocuments(sourceDocId: string, targetDocId: string): Promise<any> {
    const response = await axios.post(
      `${this.baseUrl}/documents/compare`,
      {
        source_document_id: sourceDocId,
        target_document_id: targetDocId,
      }
    );
    return response.data;
  }

  async getDocumentMetadata(documentId: string): Promise<any> {
    const response = await axios.get(`${this.baseUrl}/documents/${documentId}`);
    return response.data;
  }

  async deleteDocument(documentId: string): Promise<void> {
    await axios.delete(`${this.baseUrl}/documents/${documentId}`);
  }

  async getAnalysisHistory(documentId: string): Promise<any> {
    const response = await axios.get(
      `${this.baseUrl}/documents/${documentId}/analysis-history`
    );
    return response.data;
  }

  async getComparisonHistory(documentId: string): Promise<any> {
    const response = await axios.get(
      `${this.baseUrl}/documents/${documentId}/comparison-history`
    );
    return response.data;
  }

  async addHistoryEntry(documentId: string, action: string, details: any): Promise<void> {
    await axios.post(`${this.baseUrl}/documents/${documentId}/history`, {
      action,
      details,
    });
  }

  async clearHistory(documentId: string): Promise<void> {
    await axios.delete(`${this.baseUrl}/documents/${documentId}/history`);
  }
}
