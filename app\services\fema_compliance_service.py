from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from datetime import datetime
import re
from dateutil import parser
import logging

from app.models.document import Document
from app.models.checklist import ChecklistItem
from app.services.document_service import DocumentService
from app.core.config import get_settings
from app.schemas.fema_checklist import (
    ComplianceStatus,
    RequirementStatus,
    ComplianceCheckResult,
    ComplianceValidationResponse
)

logger = logging.getLogger(__name__)
settings = get_settings()

class FEMAComplianceService:
    def __init__(self, db: Session):
        self.db = db
        self.document_service = DocumentService(db)

    def _validate_dates(self, content: str, date_format: Optional[str] = None) -> bool:
        """
        Validate dates in document content.
        Supports multiple date formats and checks for valid date ranges.
        """
        try:
            # Common FEMA date patterns
            date_patterns = [
                r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
                r'\d{2}-\d{2}-\d{4}',  # MM-DD-YYYY
                r'\w+ \d{1,2},? \d{4}', # Month DD, YYYY
                r'\d{4}-\d{2}-\d{2}'    # YYYY-MM-DD (ISO)
            ]

            if date_format:
                date_patterns.insert(0, date_format)  # Prioritize specified format

            found_dates = []
            for pattern in date_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    try:
                        date_str = match.group()
                        parsed_date = parser.parse(date_str)
                        found_dates.append(parsed_date)
                    except (ValueError, parser.ParserError):
                        continue

            if not found_dates:
                return False

            # Validate date ranges (within reasonable FEMA timeframes)
            current_date = datetime.now()
            for date in found_dates:
                # Dates shouldn't be more than 10 years in the past or 2 years in the future
                years_diff = (current_date.year - date.year)
                if years_diff > 10 or years_diff < -2:
                    logger.warning(f"Date {date} outside reasonable range")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating dates: {str(e)}")
            return False

    def _validate_amounts(self, content: str) -> bool:
        """
        Validate monetary amounts in document content.
        Checks for valid currency formats and reasonable ranges.
        """
        try:
            # Currency patterns
            amount_patterns = [
                r'\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?',  # $X,XXX.XX
                r'\d{1,3}(?:,\d{3})*(?:\.\d{2})? dollars?',  # X,XXX.XX dollars
                r'\$\d+(?:\.\d{2})?'  # $XXX.XX
            ]

            found_amounts = []
            for pattern in amount_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    amount_str = match.group()
                    # Clean up the amount string
                    amount_str = amount_str.replace('$', '').replace(',', '').replace(' dollars', '')
                    try:
                        amount = float(amount_str)
                        found_amounts.append(amount)
                    except ValueError:
                        continue

            if not found_amounts:
                return False

            # Validate amount ranges (based on typical FEMA project thresholds)
            for amount in found_amounts:
                # Check if amounts are within reasonable FEMA project ranges
                if amount < 0 or amount > 1_000_000_000:  # $1B upper limit
                    logger.warning(f"Amount ${amount:,.2f} outside reasonable range")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating amounts: {str(e)}")
            return False

    def _check_supporting_documents(self, document_id: int, required_types: List[str]) -> bool:
        """
        Check for required supporting documents.
        Verifies that all required document types are present and valid.
        """
        try:
            if not required_types:
                return True

            # Get all related documents
            related_docs = self.db.query(Document).filter(
                Document.id != document_id,  # Exclude the current document
                Document.is_processed == True,  # Only consider processed documents
                Document.is_archived == False  # Exclude archived documents
            ).all()

            # Track which required types have been found
            found_types = set()

            for doc in related_docs:
                # Check document type/category
                if doc.mime_type in required_types:
                    found_types.add(doc.mime_type)
                
                # Check filename patterns
                for req_type in required_types:
                    if req_type.lower() in doc.original_filename.lower():
                        found_types.add(req_type)

            # All required types must be found
            return len(found_types) >= len(required_types)

        except Exception as e:
            logger.error(f"Error checking supporting documents: {str(e)}")
            return False

    async def validate_document_compliance(
        self, 
        document_id: int,
        category: str
    ) -> ComplianceValidationResponse:
        """
        Validates a document's compliance against FEMA requirements for a specific category.
        Includes advanced validation with confidence scoring and risk assessment.
        """
        document = self.document_service.get_document(document_id)
        if not document:
            raise ValueError(f"Document with id {document_id} not found")

        checklist_items = self.db.query(ChecklistItem).filter(
            ChecklistItem.category == category
        ).all()

        requirements_met = []
        requirements_missing = []
        total_score = 0.0
        check_results = []

        for item in checklist_items:
            result = await self._check_requirement_compliance(document, item)
            check_results.append(result)
            
            if result.is_compliant:
                requirements_met.append(result.requirement)
                total_score += result.confidence_score
            else:
                requirements_missing.append(result.requirement)

        avg_score = total_score / len(checklist_items) if checklist_items else 0
        risk_level = self._calculate_risk_level(avg_score, check_results)
        
        return ComplianceValidationResponse(
            document_id=document_id,
            category=category,
            requirements_met=requirements_met,
            requirements_missing=requirements_missing,
            overall_status=self._determine_overall_status(requirements_missing, avg_score),
            validation_date=datetime.utcnow(),
            score=avg_score,
            risk_level=risk_level
        )

    async def _check_requirement_compliance(
        self,
        document: Document,
        requirement: ChecklistItem
    ) -> ComplianceCheckResult:
        """
        Enhanced compliance check that includes:
        - Document content analysis
        - Required fields verification
        - Date validation
        - Amount verification
        - Supporting documentation checks
        """
        evidence = []
        risk_factors = []
        confidence_score = 0.0

        # Document content analysis
        if document.content:
            # Check for required keywords/phrases
            if requirement.required_keywords:
                keyword_matches = self._analyze_keywords(document.content, requirement.required_keywords)
                confidence_score += keyword_matches * 0.4
                if keyword_matches < 0.5:
                    risk_factors.append("Low keyword match rate")

            # Validate dates if required
            if requirement.requires_dates:
                valid_dates = self._validate_dates(document.content, requirement.date_format)
                confidence_score += 0.3 if valid_dates else 0
                if not valid_dates:
                    risk_factors.append("Missing or invalid dates")

            # Check monetary amounts if required
            if requirement.requires_amounts:
                valid_amounts = self._validate_amounts(document.content)
                confidence_score += 0.3 if valid_amounts else 0
                if not valid_amounts:
                    risk_factors.append("Missing or invalid monetary amounts")

        # Supporting documentation check
        if requirement.requires_supporting_docs:
            has_support = self._check_supporting_documents(document.id, requirement.supporting_doc_types)
            confidence_score += 0.2 if has_support else 0
            if not has_support:
                risk_factors.append("Missing supporting documentation")

        is_compliant = confidence_score >= requirement.confidence_threshold and not risk_factors

        return ComplianceCheckResult(
            requirement=requirement.requirement,
            is_compliant=is_compliant,
            confidence_score=confidence_score,
            evidence=", ".join(evidence) if evidence else None,
            risk_factors=risk_factors if risk_factors else None
        )

    def _analyze_keywords(self, content: str, keywords: List[str]) -> float:
        """Analyze keyword matches in document content."""
        if not content or not keywords:
            return 0.0
        
        matches = sum(1 for keyword in keywords if keyword.lower() in content.lower())
        return matches / len(keywords)

    def _calculate_risk_level(self, score: float, check_results: List[ComplianceCheckResult]) -> str:
        """Calculate risk level based on compliance score and check results."""
        if score >= 0.9:
            return "LOW"
        elif score >= 0.7:
            return "MEDIUM"
        return "HIGH"

    def _determine_overall_status(self, missing_requirements: List[str], score: float) -> ComplianceStatus:
        """Determine overall compliance status."""
        if not missing_requirements and score >= 0.9:
            return ComplianceStatus.COMPLIANT
        elif score >= 0.7:
            return ComplianceStatus.REQUIRES_REVIEW
        return ComplianceStatus.NON_COMPLIANT

    async def generate_compliance_report(
        self,
        category: str
    ) -> Dict:
        """
        Generates a comprehensive compliance report for a specific category.
        """
        checklist_items = self.db.query(ChecklistItem).filter(
            ChecklistItem.category == category
        ).all()

        report = {
            "category": category,
            "total_requirements": len(checklist_items),
            "completed_requirements": 0,
            "missing_requirements": [],
            "completion_percentage": 0.0,
            "status": ComplianceStatus.PENDING
        }

        for item in checklist_items:
            if item.status == RequirementStatus.COMPLETE:
                report["completed_requirements"] += 1
            else:
                report["missing_requirements"].append({
                    "requirement": item.requirement,
                    "notes": item.notes
                })

        report["completion_percentage"] = (
            report["completed_requirements"] / report["total_requirements"]
        ) * 100 if report["total_requirements"] > 0 else 0

        if report["completion_percentage"] == 100:
            report["status"] = ComplianceStatus.COMPLIANT
        elif report["completion_percentage"] >= 70:
            report["status"] = ComplianceStatus.REQUIRES_REVIEW
        else:
            report["status"] = ComplianceStatus.NON_COMPLIANT

        return report 