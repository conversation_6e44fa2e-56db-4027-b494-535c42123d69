"""
Requirement Validator Module

Validates compliance requirements against defined standards and rules.
"""

import logging
from typing import Dict, List, Optional, Set
from pathlib import Path
import json
from dataclasses import dataclass
from datetime import datetime
import re

@dataclass
class ValidationResult:
    """Represents the result of a requirement validation."""
    requirement_id: str
    is_valid: bool
    issues: List[str]
    suggestions: List[str]
    timestamp: datetime

class RequirementValidator:
    """Validates requirements against defined rules and standards."""
    
    def __init__(self):
        """Initialize the requirement validator."""
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # Common validation rules
        self.rules = {
            'completeness': [
                self._check_has_description,
                self._check_has_criteria,
                self._check_has_references
            ],
            'clarity': [
                self._check_ambiguous_terms,
                self._check_measurable_criteria,
                self._check_clear_actions
            ],
            'consistency': [
                self._check_terminology_consistency,
                self._check_reference_validity,
                self._check_version_consistency
            ]
        }
        
        # Common ambiguous terms to flag
        self.ambiguous_terms = {
            'appropriate', 'adequate', 'reasonable', 'sufficient',
            'necessary', 'proper', 'suitable', 'satisfactory',
            'timely', 'effective', 'efficient', 'quality'
        }
        
        # Required fields for requirements
        self.required_fields = {
            'id', 'title', 'description', 'criteria',
            'references', 'version', 'effective_date'
        }

    def _setup_logging(self):
        """Configure logging for the requirement validator."""
        handler = logging.FileHandler('requirement_validator.log')
        handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def validate_requirement(self, requirement: Dict) -> ValidationResult:
        """
        Validate a single requirement against all rules.
        
        Args:
            requirement: Dictionary containing requirement data
            
        Returns:
            ValidationResult object with validation results
        """
        issues = []
        suggestions = []
        
        # Check required fields
        field_issues = self._check_required_fields(requirement)
        issues.extend(field_issues)
        
        # Run completeness checks
        for check in self.rules['completeness']:
            result = check(requirement)
            if result:
                issues.extend(result.get('issues', []))
                suggestions.extend(result.get('suggestions', []))
        
        # Run clarity checks
        for check in self.rules['clarity']:
            result = check(requirement)
            if result:
                issues.extend(result.get('issues', []))
                suggestions.extend(result.get('suggestions', []))
        
        # Run consistency checks
        for check in self.rules['consistency']:
            result = check(requirement)
            if result:
                issues.extend(result.get('issues', []))
                suggestions.extend(result.get('suggestions', []))
        
        return ValidationResult(
            requirement_id=requirement.get('id', 'unknown'),
            is_valid=len(issues) == 0,
            issues=issues,
            suggestions=suggestions,
            timestamp=datetime.now()
        )

    def validate_requirements_file(self, requirements_file: Path) -> Dict[str, ValidationResult]:
        """
        Validate all requirements in a file.
        
        Args:
            requirements_file: Path to the JSON file containing requirements
            
        Returns:
            Dictionary mapping requirement IDs to their validation results
        """
        try:
            with open(requirements_file, 'r', encoding='utf-8') as f:
                requirements = json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading requirements file: {str(e)}")
            raise
            
        results = {}
        for req_id, req_data in requirements.items():
            results[req_id] = self.validate_requirement(req_data)
            
        return results

    def _check_required_fields(self, requirement: Dict) -> List[str]:
        """Check if all required fields are present."""
        issues = []
        missing_fields = self.required_fields - set(requirement.keys())
        
        if missing_fields:
            issues.append(f"Missing required fields: {', '.join(missing_fields)}")
            
        return issues

    def _check_has_description(self, requirement: Dict) -> Dict:
        """Check if requirement has a clear description."""
        issues = []
        suggestions = []
        
        description = requirement.get('description', '')
        
        if not description:
            issues.append("Missing description")
        elif len(description.split()) < 10:
            issues.append("Description is too brief")
            suggestions.append("Expand description to provide more detail")
            
        return {'issues': issues, 'suggestions': suggestions}

    def _check_has_criteria(self, requirement: Dict) -> Dict:
        """Check if requirement has measurable criteria."""
        issues = []
        suggestions = []
        
        criteria = requirement.get('criteria', [])
        
        if not criteria:
            issues.append("Missing compliance criteria")
            suggestions.append("Add specific, measurable criteria for compliance")
        else:
            for criterion in criteria:
                if not self._is_measurable(criterion):
                    issues.append(f"Criterion not measurable: {criterion}")
                    suggestions.append(f"Make criterion measurable: {criterion}")
                    
        return {'issues': issues, 'suggestions': suggestions}

    def _check_has_references(self, requirement: Dict) -> Dict:
        """Check if requirement has valid references."""
        issues = []
        suggestions = []
        
        references = requirement.get('references', [])
        
        if not references:
            issues.append("Missing references")
            suggestions.append("Add relevant policy or standard references")
        else:
            for ref in references:
                if not self._is_valid_reference(ref):
                    issues.append(f"Invalid reference format: {ref}")
                    suggestions.append("Use standard reference format: [document] Section X.Y")
                    
        return {'issues': issues, 'suggestions': suggestions}

    def _check_ambiguous_terms(self, requirement: Dict) -> Dict:
        """Check for ambiguous terms in requirement text."""
        issues = []
        suggestions = []
        
        description = requirement.get('description', '').lower()
        found_terms = set()
        
        for term in self.ambiguous_terms:
            if term in description:
                found_terms.add(term)
                
        if found_terms:
            issues.append(f"Contains ambiguous terms: {', '.join(found_terms)}")
            suggestions.append("Replace ambiguous terms with specific, measurable criteria")
            
        return {'issues': issues, 'suggestions': suggestions}

    def _check_measurable_criteria(self, requirement: Dict) -> Dict:
        """Check if criteria are measurable."""
        issues = []
        suggestions = []
        
        criteria = requirement.get('criteria', [])
        
        for criterion in criteria:
            if not any(indicator in criterion.lower() for indicator in 
                      ['=', '>', '<', 'equal', 'less', 'more', 'minimum', 'maximum', 
                       'at least', 'no more than', 'between']):
                issues.append(f"Criterion may not be measurable: {criterion}")
                suggestions.append(f"Add specific metrics or measurements to criterion")
                
        return {'issues': issues, 'suggestions': suggestions}

    def _check_clear_actions(self, requirement: Dict) -> Dict:
        """Check if requirement specifies clear actions."""
        issues = []
        suggestions = []
        
        description = requirement.get('description', '').lower()
        
        if not any(action in description for action in 
                  ['must', 'shall', 'will', 'required to', 'needs to']):
            issues.append("No clear action verbs found")
            suggestions.append("Add clear action verbs (must, shall, will) to specify requirements")
            
        return {'issues': issues, 'suggestions': suggestions}

    def _check_terminology_consistency(self, requirement: Dict) -> Dict:
        """Check for consistent terminology use."""
        issues = []
        suggestions = []
        
        # Check for mixed terminology
        text = f"{requirement.get('description', '')} {' '.join(requirement.get('criteria', []))}"
        
        # Example: Check for mixing of similar terms
        term_groups = [
            {'policy', 'standard', 'requirement', 'rule'},
            {'must', 'shall', 'will', 'should'},
            {'verify', 'validate', 'check', 'ensure'}
        ]
        
        for group in term_groups:
            used_terms = {term for term in group if term in text.lower()}
            if len(used_terms) > 1:
                issues.append(f"Mixed terminology used: {', '.join(used_terms)}")
                suggestions.append(f"Standardize terminology: use one term consistently")
                
        return {'issues': issues, 'suggestions': suggestions}

    def _check_reference_validity(self, requirement: Dict) -> Dict:
        """Check if references are valid and properly formatted."""
        issues = []
        suggestions = []
        
        references = requirement.get('references', [])
        
        for ref in references:
            if not self._is_valid_reference(ref):
                issues.append(f"Invalid reference format: {ref}")
                suggestions.append("Use format: [document] Section X.Y")
                
        return {'issues': issues, 'suggestions': suggestions}

    def _check_version_consistency(self, requirement: Dict) -> Dict:
        """Check version and date consistency."""
        issues = []
        suggestions = []
        
        version = requirement.get('version')
        effective_date = requirement.get('effective_date')
        
        if not version or not effective_date:
            issues.append("Missing version or effective date")
            suggestions.append("Add version number and effective date")
        else:
            try:
                datetime.strptime(effective_date, '%Y-%m-%d')
            except ValueError:
                issues.append(f"Invalid date format: {effective_date}")
                suggestions.append("Use YYYY-MM-DD format for dates")
                
        return {'issues': issues, 'suggestions': suggestions}

    def _is_measurable(self, text: str) -> bool:
        """Check if text contains measurable criteria."""
        measurable_indicators = [
            r'\d+',  # Numbers
            r'(?:=|>|<|>=|<=)',  # Comparison operators
            r'\b(?:equal|less|more|minimum|maximum|at least|no more than|between)\b'  # Words
        ]
        
        return any(re.search(pattern, text, re.IGNORECASE) for pattern in measurable_indicators)

    def _is_valid_reference(self, reference: str) -> bool:
        """Check if reference follows the standard format."""
        # Example format: [document] Section X.Y
        reference_pattern = r'^\[[\w\s-]+\]\s+(?:Section|§)\s+[\d\.]+$'
        return bool(re.match(reference_pattern, reference)) 