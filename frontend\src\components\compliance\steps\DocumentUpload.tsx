import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  IconButton,
  LinearProgress,
  useTheme,
} from '@mui/material';
import {
  CloudUpload,
  Delete,
  CheckCircle,
  Description,
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';

interface DocumentUploadProps {
  documents: any[];
  onUpdate: (documents: any[]) => void;
}

interface FileWithProgress extends File {
  id: string;
  progress: number;
  status: 'uploading' | 'complete' | 'error';
  preview?: string;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  documents,
  onUpdate,
}) => {
  const theme = useTheme();
  const [files, setFiles] = React.useState<FileWithProgress[]>(documents || []);

  const onDrop = React.useCallback(
    (acceptedFiles: File[]) => {
      const newFiles = acceptedFiles.map((file) =>
        Object.assign(file, {
          id: Math.random().toString(36).substr(2, 9),
          progress: 0,
          status: 'uploading' as const,
        })
      );

      setFiles((prev) => [...prev, ...newFiles]);
      onUpdate([...files, ...newFiles]);

      // Simulate upload progress
      newFiles.forEach((file) => {
        let progress = 0;
        const interval = setInterval(() => {
          progress += 10;
          if (progress > 100) {
            clearInterval(interval);
            setFiles((prev) =>
              prev.map((f) =>
                f.id === file.id
                  ? { ...f, progress: 100, status: 'complete' as const }
                  : f
              )
            );
          } else {
            setFiles((prev) =>
              prev.map((f) =>
                f.id === file.id ? { ...f, progress } : f
              )
            );
          }
        }, 500);
      });
    },
    [files, onUpdate]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
        '.docx',
      ],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
    },
  });

  const removeFile = (fileId: string) => {
    setFiles((prev) => prev.filter((f) => f.id !== fileId));
    onUpdate(files.filter((f) => f.id !== fileId));
  };

  return (
    <Box>
      <Typography
        variant="h6"
        sx={{ mb: 3, color: 'rgba(255, 255, 255, 0.9)' }}
      >
        Upload Supporting Documents
      </Typography>

      <Box
        {...getRootProps()}
        sx={{
          p: 4,
          mb: 3,
          border: '2px dashed rgba(255, 255, 255, 0.2)',
          borderRadius: theme.shape.borderRadius,
          textAlign: 'center',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            borderColor: theme.palette.primary.main,
            background: 'rgba(255, 255, 255, 0.05)',
          },
        }}
      >
        <input {...getInputProps()} />
        <CloudUpload
          sx={{
            fontSize: 48,
            mb: 2,
            color: isDragActive
              ? theme.palette.primary.main
              : 'rgba(255, 255, 255, 0.7)',
          }}
        />
        <Typography
          variant="h6"
          sx={{ mb: 1, color: 'rgba(255, 255, 255, 0.9)' }}
        >
          {isDragActive
            ? 'Drop the files here'
            : 'Drag & drop files here, or click to select'}
        </Typography>
        <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
          Supports PDF, DOC, DOCX, JPG, and PNG files
        </Typography>
      </Box>

      <Grid container spacing={2}>
        {files.map((file) => (
          <Grid item xs={12} key={file.id}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
              }}
            >
              <Description sx={{ mr: 2, color: 'rgba(255, 255, 255, 0.7)' }} />
              <Box sx={{ flexGrow: 1 }}>
                <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                  {file.name}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={file.progress}
                  sx={{
                    mt: 1,
                    height: 4,
                    borderRadius: 2,
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor:
                        file.status === 'complete'
                          ? theme.palette.success.main
                          : theme.palette.primary.main,
                    },
                  }}
                />
              </Box>
              {file.status === 'complete' && (
                <CheckCircle
                  sx={{ mx: 1, color: theme.palette.success.main }}
                />
              )}
              <IconButton
                onClick={() => removeFile(file.id)}
                sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
              >
                <Delete />
              </IconButton>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default DocumentUpload;
