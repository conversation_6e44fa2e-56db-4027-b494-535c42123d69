"""
Document Processor Module

Handles all document processing operations with a unified interface.
"""

import os
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path
import docx
import PyPDF2
import pandas as pd
from bs4 import BeautifulSoup
import magic
import hashlib

class DocumentProcessor:
    """Unified document processing system for all supported file types."""
    
    SUPPORTED_TYPES = {
        'application/pdf': '.pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
        'text/html': '.html',
        'text/plain': '.txt'
    }

    def __init__(self, storage_path: Union[str, Path]):
        """
        Initialize the document processor.
        
        Args:
            storage_path: Base path for document storage
        """
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        self._setup_logging()

    def _setup_logging(self):
        """Configure logging for the document processor."""
        handler = logging.FileHandler('document_processor.log')
        handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def process_document(self, file_path: Union[str, Path]) -> Dict:
        """
        Process a document and extract its content and metadata.
        
        Args:
            file_path: Path to the document to process
            
        Returns:
            Dict containing document content and metadata
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Document not found: {file_path}")
            
        mime_type = magic.from_file(str(file_path), mime=True)
        
        if mime_type not in self.SUPPORTED_TYPES:
            raise ValueError(f"Unsupported file type: {mime_type}")
            
        # Calculate document hash
        file_hash = self._calculate_hash(file_path)
        
        # Extract content based on file type
        content = self._extract_content(file_path, mime_type)
        
        # Generate metadata
        metadata = self._generate_metadata(file_path, mime_type, file_hash)
        
        return {
            'content': content,
            'metadata': metadata
        }

    def _calculate_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file."""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()

    def _extract_content(self, file_path: Path, mime_type: str) -> str:
        """Extract text content from document based on mime type."""
        try:
            if mime_type == 'application/pdf':
                return self._extract_pdf_content(file_path)
            elif mime_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return self._extract_docx_content(file_path)
            elif mime_type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                return self._extract_xlsx_content(file_path)
            elif mime_type == 'text/html':
                return self._extract_html_content(file_path)
            elif mime_type == 'text/plain':
                return self._extract_text_content(file_path)
        except Exception as e:
            self.logger.error(f"Error extracting content from {file_path}: {str(e)}")
            raise

    def _extract_pdf_content(self, file_path: Path) -> str:
        """Extract text from PDF files."""
        content = []
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                content.append(page.extract_text())
        return '\n'.join(content)

    def _extract_docx_content(self, file_path: Path) -> str:
        """Extract text from DOCX files."""
        doc = docx.Document(file_path)
        return '\n'.join(paragraph.text for paragraph in doc.paragraphs)

    def _extract_xlsx_content(self, file_path: Path) -> str:
        """Extract text from XLSX files."""
        df = pd.read_excel(file_path)
        return df.to_string()

    def _extract_html_content(self, file_path: Path) -> str:
        """Extract text from HTML files."""
        with open(file_path, 'r', encoding='utf-8') as file:
            soup = BeautifulSoup(file, 'html.parser')
            return soup.get_text()

    def _extract_text_content(self, file_path: Path) -> str:
        """Extract text from plain text files."""
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()

    def _generate_metadata(self, file_path: Path, mime_type: str, file_hash: str) -> Dict:
        """Generate metadata for the document."""
        stats = file_path.stat()
        return {
            'filename': file_path.name,
            'mime_type': mime_type,
            'size': stats.st_size,
            'created': stats.st_ctime,
            'modified': stats.st_mtime,
            'hash': file_hash,
            'extension': self.SUPPORTED_TYPES[mime_type]
        }

    def store_document(self, file_path: Union[str, Path], category: str) -> Path:
        """
        Store a document in the appropriate category directory.
        
        Args:
            file_path: Path to the document to store
            category: Document category (e.g., 'policies', 'requirements')
            
        Returns:
            Path where the document was stored
        """
        file_path = Path(file_path)
        category_path = self.storage_path / category
        category_path.mkdir(exist_ok=True)
        
        # Process the document to validate it
        doc_info = self.process_document(file_path)
        
        # Create a filename with the hash to ensure uniqueness
        new_filename = f"{doc_info['metadata']['hash']}{doc_info['metadata']['extension']}"
        new_path = category_path / new_filename
        
        # Copy the file to storage
        with open(file_path, 'rb') as src, open(new_path, 'wb') as dst:
            dst.write(src.read())
            
        self.logger.info(f"Stored document {file_path} as {new_path}")
        return new_path 