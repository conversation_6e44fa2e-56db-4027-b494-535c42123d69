import axios from 'axios';

interface EHPReview {
  id: string;
  project_id: string;
  project_name: string;
  review_date: string;
  reviewer: string;
  status: string;
  comments: string;
  created_at: string;
  updated_at: string;
}

const API_URL = 'http://localhost:8000';

export const getEHPReviews = async (): Promise<EHPReview[]> => {
  try {
    const response = await axios.get(`${API_URL}/api/v1/ehp/reviews`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching EHP reviews:', error);
    return [];
  }
};

export const getEHPReviewById = async (id: string): Promise<EHPReview | null> => {
  try {
    const response = await axios.get(`${API_URL}/api/v1/ehp/reviews/${id}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching EHP review ${id}:`, error);
    return null;
  }
}; 