# This service account aggregates reader permissions for the revisions in a given cluster
# Should be used for remote secret creation.
apiVersion: v1
kind: ServiceAccount
  {{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
  {{- range .Values.global.imagePullSecrets }}
  - name: {{ . }}
    {{- end }}
    {{- end }}
metadata:
  name: istio-reader-service-account
  namespace: {{ .Values.global.istioNamespace }}
  labels:
    app: istio-reader
    release: {{ .Release.Name }}
