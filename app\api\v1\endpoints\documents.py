"""
Document processing and compliance analysis endpoints.
"""

from typing import List, Dict, Any
from fastapi import APIRouter, UploadFile, File, Depends, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import aiofiles
import os
from pathlib import Path
import uuid
import shutil

from app.core.config import settings
from app.core.document_processor import DocumentProcessor
from app.core.compliance_analyzer import ComplianceAnalyzer
from app.core.logging import get_logger
from app.core.metrics import track_performance
from app.core.exceptions import DocumentProcessingError
from app.core.circuit_breaker import circuit_breaker

router = APIRouter()
logger = get_logger(__name__)

# Initialize processors
document_processor = DocumentProcessor()
compliance_analyzer = ComplianceAnalyzer()

class ComplianceRequirement(BaseModel):
    """Compliance requirement model."""
    id: str
    description: str
    required_entities: List[str] = []
    required_sections: List[str] = []
    requires_date: bool = False

class AnalysisRequest(BaseModel):
    """Document analysis request model."""
    document_id: str
    requirements: List[ComplianceRequirement]

class AnalysisResponse(BaseModel):
    """Document analysis response model."""
    document_id: str
    compliance_status: str
    findings: List[Dict[str, Any]]
    metadata: Dict[str, Any]

async def save_upload_file(upload_file: UploadFile) -> str:
    """Save uploaded file and return file path."""
    # Create upload directory if it doesn't exist
    upload_dir = Path("uploads")
    upload_dir.mkdir(exist_ok=True)
    
    # Generate unique filename
    file_extension = os.path.splitext(upload_file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = upload_dir / unique_filename
    
    # Save file
    try:
        async with aiofiles.open(file_path, 'wb') as f:
            content = await upload_file.read()
            await f.write(content)
        return str(file_path)
    except Exception as e:
        logger.error(f"File save failed: {str(e)}", exc_info=True)
        raise DocumentProcessingError(
            message="Failed to save uploaded file",
            details={"filename": upload_file.filename}
        )

async def cleanup_file(file_path: str) -> None:
    """Clean up temporary file."""
    try:
        os.remove(file_path)
    except Exception as e:
        logger.warning(f"File cleanup failed: {str(e)}")

@router.post("/upload", response_model=Dict[str, Any])
@circuit_breaker("document_upload")
@track_performance("document_upload")
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...)
) -> Dict[str, Any]:
    """
    Upload and process a document.
    
    Args:
        file: Document file to upload
        background_tasks: FastAPI background tasks
        
    Returns:
        Processed document content and metadata
    """
    try:
        # Validate file size
        if file.size > settings.MAX_UPLOAD_SIZE:
            raise DocumentProcessingError(
                message="File size exceeds maximum limit",
                details={"max_size": settings.MAX_UPLOAD_SIZE}
            )
        
        # Save file
        file_path = await save_upload_file(file)
        
        try:
            # Process document
            result = await document_processor.process_document(file_path)
            
            # Schedule cleanup
            background_tasks.add_task(cleanup_file, file_path)
            
            return {
                "document_id": str(uuid.uuid4()),
                "filename": file.filename,
                "content": result
            }
            
        except Exception as e:
            # Clean up on error
            await cleanup_file(file_path)
            raise
            
    except DocumentProcessingError as e:
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Document upload failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Document upload failed",
                "error": str(e)
            }
        )

@router.post("/analyze", response_model=AnalysisResponse)
@circuit_breaker("compliance_analysis")
@track_performance("compliance_analysis")
async def analyze_document(
    request: AnalysisRequest
) -> Dict[str, Any]:
    """
    Analyze document for compliance.
    
    Args:
        request: Analysis request containing document ID and requirements
        
    Returns:
        Compliance analysis results
    """
    try:
        # Get document content (in production, this would come from storage)
        document_content = {
            "text": "Sample document content",
            "metadata": {"type": "test"}
        }
        
        # Analyze document
        analysis_result = await compliance_analyzer.analyze_document(
            document_content=document_content,
            requirements=[req.dict() for req in request.requirements]
        )
        
        return {
            "document_id": request.document_id,
            "compliance_status": analysis_result["compliance_status"],
            "findings": analysis_result["findings"],
            "metadata": analysis_result["metadata"]
        }
        
    except DocumentProcessingError as e:
        raise HTTPException(
            status_code=e.status_code,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Document analysis failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Document analysis failed",
                "error": str(e)
            }
        )

@router.get("/status/{document_id}")
async def get_document_status(document_id: str) -> Dict[str, Any]:
    """
    Get document processing status.
    
    Args:
        document_id: Document identifier
        
    Returns:
        Document processing status and metadata
    """
    try:
        # In production, this would check a database or queue
        return {
            "document_id": document_id,
            "status": "completed",
            "metadata": {}
        }
    except Exception as e:
        logger.error(f"Status check failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Failed to get document status",
                "error": str(e)
            }
        ) 