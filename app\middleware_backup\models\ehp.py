from datetime import datetime
from typing import List, Optional
from sqlalchemy import String, DateTime, ForeignKey, Enum, JSO<PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from app.core.database import Base
import enum

class EHPReviewStatus(str, enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    APPROVED = "approved"
    DENIED = "denied"
    NEEDS_INFO = "needs_info"

class EHPReviewType(str, enum.Enum):
    STANDARD = "standard"
    EXPEDITED = "expedited"
    PROGRAMMATIC = "programmatic"

class EHPReviewV2(Base):
    __tablename__ = "ehp_reviews_v2"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.id"))
    reviewer_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    review_type: Mapped[EHPReviewType] = mapped_column(Enum(EHPReviewType))
    status: Mapped[EHPReviewStatus] = mapped_column(Enum(EHPReviewStatus), default=EHPReviewStatus.PENDING)
    environmental_impacts: Mapped[List[dict]] = mapped_column(JSON, default=list)
    historical_considerations: Mapped[List[dict]] = mapped_column(JSON, default=list)
    required_permits: Mapped[List[dict]] = mapped_column(JSON, default=list)
    mitigation_measures: Mapped[List[dict]] = mapped_column(JSON, default=list)
    review_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    completed_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    notes: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, onupdate=func.now())
    
    # Relationships
    project: Mapped["Project"] = relationship("Project", back_populates="ehp_reviews_v2")
    reviewer: Mapped["User"] = relationship("User")
    documents: Mapped[List["Document"]] = relationship("Document", secondary="ehp_review_documents_v2")

class EHPReviewDocumentV2(Base):
    __tablename__ = "ehp_review_documents_v2"
    
    review_id: Mapped[int] = mapped_column(ForeignKey("ehp_reviews_v2.id"), primary_key=True)
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"), primary_key=True)
    document_type: Mapped[str] = mapped_column(String)
    added_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    
    # Relationships
    review: Mapped["EHPReviewV2"] = relationship("EHPReviewV2")
    document: Mapped["Document"] = relationship("Document")
