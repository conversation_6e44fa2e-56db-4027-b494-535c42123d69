from typing import List, Dict, Optional, Any, Union
from fastapi import UploadFile, HTTPException
from datetime import datetime
import asyncio
import aiohttp
import os
import hashlib
import re
from dataclasses import dataclass
from enum import Enum
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import schedule
from bs4 import BeautifulSoup
import pytesseract
from sqlalchemy.orm import Session
import numpy as np
from transformers import pipeline
from datetime import datetime, timedelta

from app.core.config import settings
from app.models.document import Document, PolicyDocument, ApplicantDocument
from app.services.ocr_processor import EnhancedOCRProcessor
from app.models.policy_version import PolicyVersion
from app.services.fema_api import FEMAAPIService
from app.schemas.document import DocumentCreate, DocumentUpdate
from app.services.storage_service import StorageService

class ProcessingStatus(Enum):
    QUEUED = "queued"
    PROCESSING = "processing"
    ANALYZING = "analyzing"
    COMPLIANCE_CHECK = "compliance_check"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class AnalysisResult:
    document_id: str
    requirements: List[Dict]
    temporal_info: Dict
    sections: Dict
    cross_references: List[Dict]
    compliance_status: Dict
    confidence_score: float
    processing_history: List[Dict]

class DocumentAnalyzer:
    def __init__(self):
        self.nlp = pipeline("ner")
        self.ocr_processor = EnhancedOCRProcessor()
        
    async def extract_requirements(self, text: str) -> List[Dict]:
        """Extract policy requirements using NLP."""
        requirements = []
        requirement_patterns = [
            r"must\s+\w+",
            r"shall\s+\w+",
            r"required\s+to\s+\w+",
            r"requirements?\s+include\s+",
            r"mandatory\s+\w+",
            r"necessary\s+to\s+\w+"
        ]
        
        sentences = text.split('.')
        for sentence in sentences:
            if any(re.search(pattern, sentence, re.IGNORECASE) for pattern in requirement_patterns):
                requirements.append({
                    "text": sentence.strip(),
                    "type": self._classify_requirement(sentence),
                    "confidence": self._calculate_confidence(sentence)
                })
        
        return requirements

    async def analyze_temporal_info(self, text: str) -> Dict:
        """Extract and analyze temporal information."""
        date_patterns = [
            r"\d{1,2}/\d{1,2}/\d{2,4}",
            r"\d{4}-\d{2}-\d{2}",
            r"(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}"
        ]
        
        temporal_info = {
            "dates": [],
            "periods": [],
            "deadlines": []
        }
        
        for pattern in date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                date_str = match.group()
                context = text[max(0, match.start()-50):min(len(text), match.end()+50)]
                
                date_info = {
                    "date": date_str,
                    "context": context.strip(),
                    "type": self._classify_date_type(context)
                }
                
                if "deadline" in context.lower() or "due" in context.lower():
                    temporal_info["deadlines"].append(date_info)
                elif "period" in context.lower():
                    temporal_info["periods"].append(date_info)
                else:
                    temporal_info["dates"].append(date_info)
        
        return temporal_info

    async def classify_sections(self, text: str) -> Dict:
        """Classify document sections."""
        sections = {}
        current_section = None
        
        section_patterns = [
            r"^(?:Section|SECTION)\s+\d+",
            r"^(?:Article|ARTICLE)\s+\d+",
            r"^\d+\.\d+\s+[A-Z]",
            r"^[A-Z][A-Z\s]+:"
        ]
        
        lines = text.split('\n')
        for line in lines:
            for pattern in section_patterns:
                if re.match(pattern, line):
                    current_section = line.strip()
                    sections[current_section] = {
                        "content": [],
                        "type": self._classify_section_type(line),
                        "importance": self._calculate_section_importance(line)
                    }
                    break
            
            if current_section and not any(re.match(p, line) for p in section_patterns):
                sections[current_section]["content"].append(line.strip())
        
        return sections

    async def detect_cross_references(self, text: str) -> List[Dict]:
        """Detect cross-references to other documents."""
        cross_references = []
        reference_patterns = [
            r"(?:see|refer to|as per|according to|as defined in)\s+(?:section|policy|document)\s+[\w\-\.]+",
            r"(?:Policy|Document|Form)\s+#?\s*[\w\-\.]+",
            r"\b\d{2}\s*CFR\s*\d+(?:\.\d+)*\b"
        ]
        
        for pattern in reference_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                reference = match.group()
                context = text[max(0, match.start()-50):min(len(text), match.end()+50)]
                
                cross_references.append({
                    "reference": reference,
                    "context": context.strip(),
                    "type": self._classify_reference_type(reference),
                    "confidence": self._calculate_confidence(context)
                })
        
        return cross_references

    def _classify_requirement(self, text: str) -> str:
        """Classify the type of requirement."""
        if "must" in text.lower():
            return "mandatory"
        elif "should" in text.lower():
            return "recommended"
        elif "may" in text.lower():
            return "optional"
        return "undefined"

    def _classify_date_type(self, context: str) -> str:
        """Classify the type of date based on context."""
        context_lower = context.lower()
        if any(word in context_lower for word in ["deadline", "due", "by"]):
            return "deadline"
        elif any(word in context_lower for word in ["start", "begin", "from"]):
            return "start_date"
        elif any(word in context_lower for word in ["end", "until", "through"]):
            return "end_date"
        return "reference_date"

    def _classify_section_type(self, text: str) -> str:
        """Classify the type of section."""
        text_lower = text.lower()
        if any(word in text_lower for word in ["requirement", "must", "shall"]):
            return "requirements"
        elif any(word in text_lower for word in ["procedure", "process", "step"]):
            return "procedure"
        elif any(word in text_lower for word in ["definition", "term"]):
            return "definitions"
        return "general"

    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence score for extracted information."""
        # Implement confidence calculation based on multiple factors
        factors = {
            "length": min(1.0, len(text.split()) / 20),  # Longer context = higher confidence
            "clarity": 0.8 if text.strip().endswith('.') else 0.6,  # Complete sentences
            "structure": 0.9 if any(char.isupper() for char in text[:1]) else 0.7  # Proper capitalization
        }
        return np.mean(list(factors.values()))

class ComplianceChecker:
    def __init__(self):
        self.analyzer = DocumentAnalyzer()
        
    async def check_compliance(self, document: Dict, policy_requirements: List[Dict]) -> Dict:
        """Check document compliance against policy requirements."""
        compliance_results = {
            "status": "pending",
            "requirements_met": [],
            "requirements_missing": [],
            "warnings": [],
            "score": 0.0
        }
        
        # Extract document requirements
        doc_requirements = await self.analyzer.extract_requirements(document["text"])
        
        # Match requirements
        for policy_req in policy_requirements:
            requirement_status = await self._check_requirement(policy_req, doc_requirements)
            if requirement_status["met"]:
                compliance_results["requirements_met"].append(requirement_status)
            else:
                compliance_results["requirements_missing"].append(requirement_status)
        
        # Calculate compliance score
        total_reqs = len(policy_requirements)
        met_reqs = len(compliance_results["requirements_met"])
        compliance_results["score"] = (met_reqs / total_reqs) if total_reqs > 0 else 0.0
        
        # Set overall status
        if compliance_results["score"] >= 0.95:
            compliance_results["status"] = "compliant"
        elif compliance_results["score"] >= 0.8:
            compliance_results["status"] = "partially_compliant"
        else:
            compliance_results["status"] = "non_compliant"
        
        return compliance_results

    async def _check_requirement(self, policy_req: Dict, doc_requirements: List[Dict]) -> Dict:
        """Check if a specific requirement is met."""
        requirement_status = {
            "requirement": policy_req,
            "met": False,
            "confidence": 0.0,
            "matching_text": None
        }
        
        for doc_req in doc_requirements:
            similarity = self._calculate_similarity(policy_req["text"], doc_req["text"])
            if similarity > 0.8:  # High similarity threshold
                requirement_status["met"] = True
                requirement_status["confidence"] = similarity
                requirement_status["matching_text"] = doc_req["text"]
                break
        
        return requirement_status

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate text similarity score."""
        # Implement text similarity calculation
        # This is a simplified version - in practice, use more sophisticated NLP
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0

class DocumentProcessor:
    def __init__(self):
        self.analyzer = DocumentAnalyzer()
        self.compliance_checker = ComplianceChecker()
        self.status_tracker = {}
        self.processing_queue = PriorityQueue()
        
    async def process_document(self, document_id: str, content: str, priority: int = 1) -> AnalysisResult:
        """Process a document through the complete pipeline."""
        try:
            # Update status
            self._update_status(document_id, ProcessingStatus.PROCESSING)
            
            # Extract text if needed
            if isinstance(content, bytes):
                content = await self._extract_text(content)
            
            # Analyze document
            self._update_status(document_id, ProcessingStatus.ANALYZING)
            requirements = await self.analyzer.extract_requirements(content)
            temporal_info = await self.analyzer.analyze_temporal_info(content)
            sections = await self.analyzer.classify_sections(content)
            cross_references = await self.analyzer.detect_cross_references(content)
            
            # Check compliance
            self._update_status(document_id, ProcessingStatus.COMPLIANCE_CHECK)
            policy_requirements = await self._get_policy_requirements(document_id)
            compliance_status = await self.compliance_checker.check_compliance(
                {"text": content}, policy_requirements
            )
            
            # Calculate overall confidence
            confidence_score = np.mean([
                req["confidence"] for req in requirements if "confidence" in req
            ])
            
            # Create result
            result = AnalysisResult(
                document_id=document_id,
                requirements=requirements,
                temporal_info=temporal_info,
                sections=sections,
                cross_references=cross_references,
                compliance_status=compliance_status,
                confidence_score=confidence_score,
                processing_history=self.status_tracker.get(document_id, [])
            )
            
            self._update_status(document_id, ProcessingStatus.COMPLETED)
            return result
            
        except Exception as e:
            self._update_status(document_id, ProcessingStatus.ERROR, str(e))
            raise
    
    def _update_status(self, document_id: str, status: ProcessingStatus, message: str = None):
        """Update document processing status."""
        if document_id not in self.status_tracker:
            self.status_tracker[document_id] = []
            
        self.status_tracker[document_id].append({
            "status": status,
            "timestamp": datetime.now(),
            "message": message
        })
    
    async def _extract_text(self, content: bytes) -> str:
        """Extract text from document content."""
        # Implement text extraction based on document type
        # This is a placeholder - implement actual extraction logic
        return content.decode('utf-8')
    
    async def _get_policy_requirements(self, document_id: str) -> List[Dict]:
        """Get policy requirements for compliance checking."""
        # Implement policy requirement retrieval
        # This is a placeholder - implement actual requirement retrieval
        return []

class DocumentService:
    def __init__(self, db: Session, storage: StorageService):
        self.db = db
        self.storage = storage
        # Configure Tesseract path for Windows
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        
        # Initialize components
        self.processor = DocumentProcessor()
        self.analyzer = DocumentAnalyzer()
        self.compliance_checker = ComplianceChecker()
    
    async def process_document(
        self, 
        file: UploadFile,
        document_data: DocumentCreate,
        priority: int = 1
    ) -> Document:
        """Process a document through the complete pipeline."""
        try:
            # Save document
            document = await self.create_document(file, document_data)
            
            # Process document
            content = await self.storage.get_file_content(document.file_path)
            result = await self.processor.process_document(
                document_id=str(document.id),
                content=content,
                priority=priority
            )
            
            # Update document with analysis results
            document.analysis_results = result.__dict__
            self.db.commit()
            
            return document
            
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error processing document: {str(e)}"
            )
    
    async def get_document_status(self, document_id: str) -> Dict:
        """Get document processing status and results."""
        status_history = self.processor.status_tracker.get(document_id, [])
        if not status_history:
            raise HTTPException(status_code=404, detail="Document not found")
            
        current_status = status_history[-1]["status"]
        return {
            "document_id": document_id,
            "current_status": current_status,
            "history": status_history,
            "last_updated": status_history[-1]["timestamp"]
        }
    
    async def check_document_compliance(
        self, 
        document_id: str,
        policy_requirements: List[Dict] = None
    ) -> Dict:
        """Check document compliance against policy requirements."""
        document = self.db.query(Document).filter(Document.id == document_id).first()
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
            
        content = await self.storage.get_file_content(document.file_path)
        if isinstance(content, bytes):
            content = content.decode('utf-8')
            
        if not policy_requirements:
            policy_requirements = await self._get_policy_requirements(document)
            
        compliance_results = await self.compliance_checker.check_compliance(
            {"text": content},
            policy_requirements
        )
        
        # Update document compliance status
        document.compliance_status = compliance_results
        self.db.commit()
        
        return compliance_results

    async def create_document(
        self, 
        file: UploadFile, 
        document_data: DocumentCreate, 
        owner_id: int
    ) -> Document:
        """Create a new document with uploaded file."""
        try:
            # Create document record
            db_document = Document(
                title=document_data.title,
                description=document_data.description,
                filename=file.filename,
                content_type=file.content_type,
                owner_id=owner_id
            )
            self.db.add(db_document)
            self.db.flush()  # Get the id without committing

            # Save file
            file_path, checksum, file_size = await self.storage.save_file(
                file=file,
                document_id=str(db_document.id)
            )

            # Update document with file info
            db_document.file_path = file_path
            db_document.checksum = checksum
            db_document.file_size = file_size
            
            self.db.commit()
            self.db.refresh(db_document)
            
            logger.info(f"Document created successfully: {db_document.id}")
            return db_document

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating document: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating document"
            )

    async def get_document(self, document_id: int, user_id: int) -> Document:
        """Get a document by ID."""
        document = self.db.query(Document).filter(
            Document.id == document_id,
            Document.owner_id == user_id
        ).first()
        
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        return document

    async def get_user_documents(
        self, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Document]:
        """Get all documents owned by a user."""
        return self.db.query(Document)\
            .filter(Document.owner_id == user_id)\
            .offset(skip)\
            .limit(limit)\
            .all()

    async def update_document(
        self, 
        document_id: int, 
        document_data: DocumentUpdate, 
        user_id: int
    ) -> Document:
        """Update document metadata."""
        document = await self.get_document(document_id, user_id)
        
        for field, value in document_data.dict(exclude_unset=True).items():
            setattr(document, field, value)
        
        document.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(document)
        
        logger.info(f"Document updated successfully: {document_id}")
        return document

    async def delete_document(self, document_id: int, user_id: int) -> bool:
        """Delete a document and its file."""
        document = await self.get_document(document_id, user_id)
        
        try:
            # Delete file first
            if document.file_path:
                await self.storage.delete_file(document.file_path)
            
            # Delete document record
            self.db.delete(document)
            self.db.commit()
            
            logger.info(f"Document deleted successfully: {document_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting document {document_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting document"
            )

    async def verify_document(self, document_id: int, user_id: int) -> bool:
        """Verify document file integrity."""
        document = await self.get_document(document_id, user_id)
        return await self.storage.verify_file(document.file_path, document.checksum)

    async def process_document(
        self,
        filename: str,
        file_path: str,
        document_type: DocumentType,
        dr_number: Optional[str],
        uploader_id: str,
        mime_type: str
    ) -> ProcessedDocument:
        """Process a document through OCR."""
        try:
            # Create document record
            document = ProcessedDocument(
                filename=filename,
                document_type=document_type,
                upload_date=datetime.utcnow(),
                file_path=file_path,
                dr_number=dr_number,
                uploader_id=uploader_id,
                mime_type=mime_type,
                file_size=os.path.getsize(file_path),
                status=ProcessingStatus.PROCESSING
            )
            await document.save()
            
            extracted_data: List[ExtractedData] = []
            
            if mime_type == 'application/pdf':
                # Convert PDF to images
                images = convert_from_path(file_path)
                for i, image in enumerate(images):
                    text = self._process_image(image)
                    extracted_data.append(
                        ExtractedData(
                            text=text,
                            metadata={"page": i + 1},
                            confidence_score=self._calculate_confidence(text),
                            page_number=i + 1
                        )
                    )
            else:
                # Process single image
                image = Image.open(file_path)
                text = self._process_image(image)
                extracted_data.append(
                    ExtractedData(
                        text=text,
                        metadata={"page": 1},
                        confidence_score=self._calculate_confidence(text),
                        page_number=1
                    )
                )
            
            document.status = ProcessingStatus.COMPLETED
            document.processed_date = datetime.utcnow()
            document.extracted_data = extracted_data
            await document.save()
            
            return document
            
        except Exception as e:
            if document:
                document.status = ProcessingStatus.FAILED
                document.error_message = str(e)
                await document.save()
            raise
    
    def _process_image(self, image: Image.Image) -> str:
        """Process a single image through OCR."""
        return pytesseract.image_to_string(image)
    
    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence score based on text quality."""
        if not text:
            return 0.0
        words = text.split()
        if not words:
            return 0.0
        # Simple heuristic: longer words tend to be more reliable
        avg_word_length = sum(len(word) for word in words) / len(words)
        return min(1.0, avg_word_length / 10.0)
    
    async def get_processed_documents(self, dr_number: Optional[str] = None) -> List[ProcessedDocument]:
        """Get list of processed documents."""
        query = {}
        if dr_number:
            query["dr_number"] = dr_number
        return await ProcessedDocument.find(query).to_list()
    
    async def get_document_by_id(self, document_id: str) -> Optional[ProcessedDocument]:
        """Get a processed document by ID."""
        return await ProcessedDocument.get(document_id)

    async def start_integration_layer(self):
        await self.integration_layer.start()
