<!DOCTYPE html>
<html itemscope itemtype="https://schema.org/WebPage" class="no-js no-ie" lang="en" dir="ltr">
                    <script nonce="ybHWTf_2bjronyNWExNH1Q">
  function r(p){try{window.stop();}catch(exception){document.execCommand('Stop');}window.location.replace(p+window.location.search)}var ua=navigator.userAgent;if(ua.match(".*NT 5\.[12].*")!==null){ r("/chrome/fallback/") }if(ua.indexOf("MSIE")>=0||ua.indexOf("Trident/7.0") > 0){ r("/chrome/fallback/") }if(((v = new RegExp("Version\\/(\\d+)").exec(ua)) !== null ? parseInt(v[1]) <= 13 : !/Chrome|Chromium|CriOS/.test(ua) && /Safari/.test(ua))){ r("/chrome/fallback/") }</script>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="content-language" content="en-us">
    <meta name="apple-itunes-app" content="app-id=535886823, affiliate-data=ct=global-chrome-web-smart-app-banner&pt=9008">
                    <link rel="preconnect" href="https://www.google-analytics.com" >
                    <link rel="preconnect" href="https://www.googletagmanager.com" >
                    <link rel="preconnect" href="https://www.youtube.com" >
                    <link rel="preconnect" href="https://s.ytimg.com" >
                    <link rel="preconnect" href="https://tools.google.com" >
                    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
                    <meta http-equiv="X-UA-Compatible" content="IE=edge">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <meta name="description" content="Chrome is the official web browser from Google, built to be fast, secure, and customizable. Download now and make it yours.">
                    <title>Google Chrome - The Fast & Secure Web Browser Built to be Yours</title><!--[if IE]>
                    <link rel="shortcut icon" href="/chrome/static/images/favicons/favicon.ico"><![endif]-->
                    <link rel="icon" type="image/png" sizes="16x16" href="/chrome/static/images/favicons/favicon-16x16.png">
                    <link rel="icon" type="image/png" sizes="32x32" href="/chrome/static/images/favicons/favicon-32x32.png">
                    <link rel="shortcut icon" type="image/png" sizes="32x32" href="/chrome/static/images/favicons/favicon-32x32.png">
                    <link rel="icon" type="image/png" sizes="96x96" href="/chrome/static/images/favicons/favicon-96x96.png">
                    <link rel="icon" type="image/png" sizes="192x192" href="/chrome/static/images/favicons/android-icon-192x192.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="57x57" href="/chrome/static/images/favicons/apple-icon-57x57.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="60x60" href="/chrome/static/images/favicons/apple-icon-60x60.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="72x72" href="/chrome/static/images/favicons/apple-icon-72x72.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="76x76" href="/chrome/static/images/favicons/apple-icon-76x76.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="114x114" href="/chrome/static/images/favicons/apple-icon-114x114.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="120x120" href="/chrome/static/images/favicons/apple-icon-120x120.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="144x144" href="/chrome/static/images/favicons/apple-icon-144x144.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="152x152" href="/chrome/static/images/favicons/apple-icon-152x152.png">
                    <link rel="apple-touch-icon" type="image/png" sizes="180x180" href="/chrome/static/images/favicons/apple-icon-180x180.png">
                    <link rel="manifest" href="/chrome/static/images/favicons/manifest.json">
                    <meta name="msapplication-config" content="/chrome/static/images/favicons/browserconfig.xml" />
                    <link href="https://www.google.com/chrome/" rel="canonical">
                    <meta name="twitter:card" content="summary_large_image">
                    <meta name="twitter:title" content="Google Chrome - The Fast & Secure Web Browser Built to be Yours">
                    <meta name="twitter:description" content="Chrome is the official web browser from Google, built to be fast, secure, and customizable. Download now and make it yours.">
                    <meta name="twitter:image" content="https://www.google.com/chrome/static/images/homepage/homepage-v2.png">
                    <meta name="twitter:url" content="https://www.google.com/chrome/">
                    <meta property="og:type" content="website">
                    <meta property="og:title" content="Google Chrome - The Fast & Secure Web Browser Built to be Yours">
                    <meta property="og:description" content="Chrome is the official web browser from Google, built to be fast, secure, and customizable. Download now and make it yours.">
                    <meta property="og:url" content="https://www.google.com/chrome/">
                    <meta property="og:image" content="https://www.google.com/chrome/static/images/homepage/homepage-v2.png">
                    <meta property="og:locale" content="en_US">
                    <link rel="preload" fetchpriority="high" as="image" href="https://www.google.com/chrome/static/images/dev-components/home-poster-2x.webp" nonce="ybHWTf_2bjronyNWExNH1Q">
                    <script nonce="ybHWTf_2bjronyNWExNH1Q">
  if (window.trustedTypes && window.trustedTypes.createPolicy) {
    ttp = trustedTypes.createPolicy('default', {
      createScriptURL: (string) => {
        const url = new URL(string, document.baseURI);
        if(url.origin = window.location.origin || url.origin === "www.googletagmanager.com" || url.origin === "www.google-analytics.com") return url.href
        throw new TypeError('createScriptURL: invalid URL')
      }
    });
  }
</script>
    <script nonce="ybHWTf_2bjronyNWExNH1Q">
      var isEdge = (window.navigator && window.navigator.userAgent && window.navigator.userAgent.indexOf('Edg/') !== -1) ||
        Boolean(window.external && window.external['getHostEnvironmentValue']);
      var osMode = 'no api';
      if (isEdge) {
        var hasApi = window.external && window.external.getHostEnvironmentValue;
        if (hasApi) {
          osMode = window.external.getHostEnvironmentValue('os-mode');
          if (osMode && osMode.match) {
            osMode = osMode.match(/\d/);
            osMode = osMode[0] || null;
          }
        }
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
          edgeSModeApi: osMode
        });
        if (window.localStorage) {
          window.localStorage.setItem('osmode', osMode);
          if (osMode && osMode !== 'no api') {
            const storedSMode = window.localStorage.getItem('smode');
            if (osMode === '0') {
              if (storedSMode && storedSMode === '2') {
                window.dataLayer.push({
                  formerSMode: true
                });
                window.localStorage.setItem('smode', '0');
              }
            } else if (osMode === '2') {
              if (!storedSMode) {
                window.localStorage.setItem('smode', osMode);
              }
            }
          }
        }
      }
    </script>
    <script nonce="ybHWTf_2bjronyNWExNH1Q">
      var agent = window.navigator.userAgent.toLowerCase();
      var edgeType = "";
      switch (true) {
        case agent.indexOf("edge")> -1:
          edgeType = "Legacy Edge";
          break;
        case agent.indexOf("edg")> -1:
          edgeType = "Chromium Edge";
          break;
      }
      if (edgeType != "") {
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
          edgeVersion: edgeType
        });
      }
    </script><!-- Google Tag Manager -->
                    <script nonce="ybHWTf_2bjronyNWExNH1Q">
  window.dataLayer = window.dataLayer || []
  // Initialize event
  window.dataLayer.push({
    event: "dataLayer_initialized",
    page_name: "Google Chrome - The Fast & Secure Web Browser Built to be Yours",
    locale: "en_US",
      diversion_experience: "Conversion - Desktop",
    experiments: undefined});
</script><!-- Google Tag Manager -->
                    <script nonce="ybHWTf_2bjronyNWExNH1Q">
  window.dataLayer = window.dataLayer || [];
  function glueCookieNotificationBarLoaded() {
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    ((typeof ttp === "undefined") ? ('https://www.googletagmanager.com/gtm.js?id='+i+dl) :
    ttp.createScriptURL('https://www.googletagmanager.com/gtm.js?id='+i+dl) );
    f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer',
    'GTM-PZ6TRJB');
  }
</script><!-- End Google Tag Manager -->
                    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Product+Sans&family=Google+Sans+Display:ital@0;1&family=Google+Sans_old:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,700&family=Google+Sans+Text:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,700&display=swap" as="style" nonce="ybHWTf_2bjronyNWExNH1Q">
                    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Product+Sans&family=Google+Sans+Display:ital@0;1&family=Google+Sans_old:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,700&family=Google+Sans+Text:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,700&display=swap" nonce="ybHWTf_2bjronyNWExNH1Q">
                    <style>
/*! normalize-scss | MIT/GPLv2 License | bit.ly/normalize-scss */html{line-height:1.15;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,footer,header,nav,section{display:block}h1{font-size:2em;margin:.67em 0}figcaption,figure{display:block}figure{margin:1em 40px}hr{-webkit-box-sizing:content-box;box-sizing:content-box;height:0;overflow:visible}main{display:block}pre{font-family:monospace,monospace;font-size:1em}a{background-color:rgba(0,0,0,0);-webkit-text-decoration-skip:objects}abbr[title]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b,strong{font-weight:inherit}b,strong{font-weight:bolder}code,kbd,samp{font-family:monospace,monospace;font-size:1em}dfn{font-style:italic}mark{background-color:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-0.25em}sup{top:-0.5em}audio,video{display:inline-block}audio:not([controls]){display:none;height:0}img{border-style:none}svg:not(:root){overflow:hidden}button,input,optgroup,select,textarea{font-family:sans-serif;font-size:100%;line-height:1.15;margin:0}button{overflow:visible}button,select{text-transform:none}button,html [type=button],[type=reset],[type=submit]{-webkit-appearance:button}button::-moz-focus-inner,[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring,[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring{outline:1px dotted ButtonText}input{overflow:visible}[type=checkbox],[type=radio]{-webkit-box-sizing:border-box;box-sizing:border-box;padding:0}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}[type=search]::-webkit-search-cancel-button,[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}fieldset{padding:.35em .75em .625em}legend{-webkit-box-sizing:border-box;box-sizing:border-box;display:table;max-width:100%;padding:0;color:inherit;white-space:normal}progress{display:inline-block;vertical-align:baseline}textarea{overflow:auto}details{display:block}summary{display:list-item}menu{display:block}canvas{display:inline-block}template{display:none}[hidden]{display:none}.chr-header-v3__nav-list{list-style:none;padding:0}body{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}body{word-break:break-word;word-wrap:break-word}.chr-modal,.chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-links,.chr-download-button+.chr-simplified-download .platform{display:none}.chr-modal.show,.chr-modal .show,.chr-download-button+.chr-simplified-download .show{display:block !important}.chr-background__yellow{background-color:#fde293}.dark-theme .chr-background__yellow{background-color:#fde293}.chr-background__blue{background-color:#1a73e8}.dark-theme .chr-background__blue{background-color:#1a73e8}.chr-background__red{background-color:#f6aea9}.dark-theme .chr-background__red{background-color:#f6aea9}.chr-background__dark{background-color:#202124}.dark-theme .chr-background__dark{background-color:#202124}.chr-background__white{background-color:#fff}.dark-theme .chr-background__white{background-color:#fff}.chr-background__dark-blue{background-color:#174ea6}.dark-theme .chr-background__dark-blue{background-color:#174ea6}.chr-background__dark-grey{background-color:#80868b}.dark-theme .chr-background__dark-grey{background-color:#80868b}.chr-background__dark-green{background-color:#188038}.dark-theme .chr-background__dark-green{background-color:#188038}.chr-background__dark-yellow{background-color:#fbbc04}.dark-theme .chr-background__dark-yellow{background-color:#fbbc04}.chr-background__light-blue-01{background-color:#e8f0fe}.dark-theme .chr-background__light-blue-01{background-color:#e8f0fe}.chr-background__light-blue-02{background-color:#d2e3fc}.dark-theme .chr-background__light-blue-02{background-color:#d2e3fc}.chr-background__light-green-01{background-color:#e6f4ea}.dark-theme .chr-background__light-green-01{background-color:#e6f4ea}.chr-background__light-green-02{background-color:#ceead6}.dark-theme .chr-background__light-green-02{background-color:#ceead6}.chr-background__light-grey{background-color:#f8f9fa}.dark-theme .chr-background__light-grey{background-color:#f8f9fa}.chr-background__light-red{background-color:#fce8e6}.dark-theme .chr-background__light-red{background-color:#fce8e6}.chr-background__light-yellow{background-color:#fef7e0}.dark-theme .chr-background__light-yellow{background-color:#fef7e0}.chr-background__ai-main-10{background-color:#e8effd}.dark-theme .chr-background__ai-main-10{background-color:#e8effd}.chr-background__ai-analog-variant-30{background-color:#eef9fe}.dark-theme .chr-background__ai-analog-variant-30{background-color:#eef9fe}.chr-background__ai-analog-variant-50{background-color:#e7f8fe}.dark-theme .chr-background__ai-analog-variant-50{background-color:#e7f8fe}.chr-background__ai-analog-30{background-color:#e0f6fe}.dark-theme .chr-background__ai-analog-30{background-color:#e0f6fe}.chr-background__ai-muted-10{background-color:#f0f5fd}.dark-theme .chr-background__ai-muted-10{background-color:#f0f5fd}.chr-background__ai-main-10-gradient{background:radial-gradient(82.52% 85.35% at 2.9% 100%, #b2caff 0%, #e8effd 100%)}.dark-theme .chr-background__ai-main-10-gradient{background:radial-gradient(82.52% 85.35% at 2.9% 100%, #b2caff 0%, #e8effd 100%)}.chr-background__ai-analog-variant-50-gradient{background:radial-gradient(81.92% 81.92% at 50% 100%, #cbf5ff 0%, #e7f8fe 100%)}.dark-theme .chr-background__ai-analog-variant-50-gradient{background:radial-gradient(81.92% 81.92% at 50% 100%, #cbf5ff 0%, #e7f8fe 100%)}.chr-background__ai-analog-10-gradient{background:linear-gradient(125deg, rgba(91, 140, 255, 0.4) 0.38%, #d7f6ff 52%)}.dark-theme .chr-background__ai-analog-10-gradient{background:linear-gradient(125deg, rgba(91, 140, 255, 0.4) 0.38%, #d7f6ff 52%)}.chr-background__ai-analog-30-gradient{background:radial-gradient(100% 100% at 50% 100%, #caf1fe 0%, #e0f6fe 71.01%)}.dark-theme .chr-background__ai-analog-30-gradient{background:radial-gradient(100% 100% at 50% 100%, #caf1fe 0%, #e0f6fe 71.01%)}.chr-background__ai-muted-10-gradient{background:linear-gradient(205deg, #f0f5fd 49.95%, #c9daff 109.5%)}.dark-theme .chr-background__ai-muted-10-gradient{background:linear-gradient(205deg, #f0f5fd 49.95%, #c9daff 109.5%)}.chr-background__ai-gradient-dark-pink{background:linear-gradient(98deg, rgba(112, 201, 224, 0.4) 1.72%, rgba(23, 78, 166, 0.4) 51.2%, rgba(246, 140, 233, 0.4) 91.55%),#185abc}.dark-theme .chr-background__ai-gradient-dark-pink{background:linear-gradient(98deg, rgba(112, 201, 224, 0.4) 1.72%, rgba(23, 78, 166, 0.4) 51.2%, rgba(246, 140, 233, 0.4) 91.55%),#185abc}.chr-background__ai-gradient-four{background:linear-gradient(98.41deg, rgba(214, 228, 253, 0.4) 1.72%, rgba(23, 78, 166, 0) 51.2%, rgba(204, 228, 253, 0.4) 91.55%),#185abc}.dark-theme .chr-background__ai-gradient-four{background:linear-gradient(98.41deg, rgba(214, 228, 253, 0.4) 1.72%, rgba(23, 78, 166, 0) 51.2%, rgba(204, 228, 253, 0.4) 91.55%),#185abc}.chr-background__ai-gradient-five{background:linear-gradient(111.39deg, #a6b1fa 12.6%, rgba(111, 171, 249, 0.66) 68%)}.dark-theme .chr-background__ai-gradient-five{background:linear-gradient(111.39deg, #a6b1fa 12.6%, rgba(111, 171, 249, 0.66) 68%)}.chr-background__ai-blue-soft{background-color:#f0f7fe}.dark-theme .chr-background__ai-blue-soft{background-color:#f0f7fe}.chr-background__ai-purple-25{background-color:#eaedfd}.dark-theme .chr-background__ai-purple-25{background-color:#eaedfd}.chr-background__ai-blue-muted-light{background-color:#def}.dark-theme .chr-background__ai-blue-muted-light{background-color:#def}.chr-background__ai-bright-blue-10{background-color:#edf1fe}.dark-theme .chr-background__ai-bright-blue-10{background-color:#edf1fe}.chr-background__ai-blue-gemini{background-color:#4b6de6}.dark-theme .chr-background__ai-blue-gemini{background-color:#4b6de6}.chr-background__light-background-card{background-color:#edf2fa}.dark-theme .chr-background__light-background-card{background-color:#edf2fa}.chr-background__blue-900{background-color:#174ea6}.dark-theme .chr-background__blue-900{background-color:#174ea6}.chr-background__yellow-300{background-color:#fdd663}.dark-theme .chr-background__yellow-300{background-color:#fdd663}.chr-background__green-300{background-color:#81c995}.dark-theme .chr-background__green-300{background-color:#81c995}.chr-background__blue-300{background-color:#8ab4f8}.dark-theme .chr-background__blue-300{background-color:#8ab4f8}.chr-text-wrap--balance{text-wrap:balance}.chr-text-wrap--pretty{text-wrap:pretty}@-webkit-keyframes card-fade-up{from{opacity:0;-webkit-transform:translateY(-80px);transform:translateY(-80px)}to{opacity:1;-webkit-transform:translateY(-120px);transform:translateY(-120px)}}@keyframes card-fade-up{from{opacity:0;-webkit-transform:translateY(-80px);transform:translateY(-80px)}to{opacity:1;-webkit-transform:translateY(-120px);transform:translateY(-120px)}}@-webkit-keyframes fade-in-upward{from{opacity:0;-webkit-transform:translateY(50px);transform:translateY(50px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fade-in-upward{from{opacity:0;-webkit-transform:translateY(50px);transform:translateY(50px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes fade-in{from{opacity:0}to{opacity:1}}@keyframes fade-in{from{opacity:0}to{opacity:1}}@-webkit-keyframes fade-out{from{opacity:1}to{opacity:0}}@keyframes fade-out{from{opacity:1}to{opacity:0}}@-webkit-keyframes fade-up{from{opacity:0;-webkit-transform:translateY(40px);transform:translateY(40px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fade-up{from{opacity:0;-webkit-transform:translateY(40px);transform:translateY(40px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes fade-up-100{from{opacity:0;-webkit-transform:translateY(100px);transform:translateY(100px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fade-up-100{from{opacity:0;-webkit-transform:translateY(100px);transform:translateY(100px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes fade-out-down{from{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}to{opacity:0;-webkit-transform:translateY(-40px);transform:translateY(-40px)}}@keyframes fade-out-down{from{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}to{opacity:0;-webkit-transform:translateY(-40px);transform:translateY(-40px)}}@-webkit-keyframes fade-out-upwards{from{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}to{opacity:0;-webkit-transform:translateY(-80px);transform:translateY(-80px)}}@keyframes fade-out-upwards{from{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}to{opacity:0;-webkit-transform:translateY(-80px);transform:translateY(-80px)}}@-webkit-keyframes fade-in-downwards{from{opacity:0;-webkit-transform:translateY(-80px);transform:translateY(-80px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fade-in-downwards{from{opacity:0;-webkit-transform:translateY(-80px);transform:translateY(-80px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes fade-in-upwards{from{opacity:0;-webkit-transform:translateY(80px);transform:translateY(80px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fade-in-upwards{from{opacity:0;-webkit-transform:translateY(80px);transform:translateY(80px)}to{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes fade-out-downwards{from{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}to{opacity:0;-webkit-transform:translateY(80px);transform:translateY(80px)}}@keyframes fade-out-downwards{from{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}to{opacity:0;-webkit-transform:translateY(80px);transform:translateY(80px)}}@-webkit-keyframes expand-width{0%{left:0%;width:0%}10%{left:0%;width:100%}100%{left:100%;width:100%}}@keyframes expand-width{0%{left:0%;width:0%}10%{left:0%;width:100%}100%{left:100%;width:100%}}@-webkit-keyframes expand-width-rtl{0%{right:0%;width:0%}10%{right:0%;width:100%}100%{right:100%;width:100%}}@keyframes expand-width-rtl{0%{right:0%;width:0%}10%{right:0%;width:100%}100%{right:100%;width:100%}}@-webkit-keyframes hero-up-phone{from{bottom:-100%}to{bottom:-110px;visibility:visible}}@keyframes hero-up-phone{from{bottom:-100%}to{bottom:-110px;visibility:visible}}@-webkit-keyframes hero-up-tablet{from{bottom:-100%}to{bottom:-120px;visibility:visible}}@keyframes hero-up-tablet{from{bottom:-100%}to{bottom:-120px;visibility:visible}}@-webkit-keyframes hero-up-blue-circle{from{bottom:-100%}to{bottom:-319px;visibility:visible}}@keyframes hero-up-blue-circle{from{bottom:-100%}to{bottom:-319px;visibility:visible}}@-webkit-keyframes hero-down-phone{from{bottom:100%}to{bottom:220px;visibility:visible}}@keyframes hero-down-phone{from{bottom:100%}to{bottom:220px;visibility:visible}}@-webkit-keyframes hero-left-rectangle{from{left:40vw}to{left:0;visibility:visible}}@keyframes hero-left-rectangle{from{left:40vw}to{left:0;visibility:visible}}@-webkit-keyframes hero-right-rectangle{from{right:40vw}to{right:0;visibility:visible}}@keyframes hero-right-rectangle{from{right:40vw}to{right:0;visibility:visible}}@-webkit-keyframes hero-expand-width{from{visibility:hidden;width:0}to{visibility:visible}}@keyframes hero-expand-width{from{visibility:hidden;width:0}to{visibility:visible}}@-webkit-keyframes typing{from{width:0}to{width:100%}}@keyframes typing{from{width:0}to{width:100%}}@-webkit-keyframes hero-scale-up-border{from{border:0;height:0;visibility:hidden;width:0}to{height:100%;visibility:visible}}@keyframes hero-scale-up-border{from{border:0;height:0;visibility:hidden;width:0}to{height:100%;visibility:visible}}@-webkit-keyframes hero-slide-from-bottom{from{-webkit-transform:translateY(120%);transform:translateY(120%)}to{bottom:-24px;visibility:visible}}@keyframes hero-slide-from-bottom{from{-webkit-transform:translateY(120%);transform:translateY(120%)}to{bottom:-24px;visibility:visible}}@-webkit-keyframes blink-caret{from,to{border-color:rgba(0,0,0,0)}50%{border-color:#202124}}@keyframes blink-caret{from,to{border-color:rgba(0,0,0,0)}50%{border-color:#202124}}@-webkit-keyframes fade-in-upwards-and-expand{0%{opacity:0;-webkit-transform:translateY(80px);transform:translateY(80px);width:96px}50%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0);width:96px}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0);width:100%}}@keyframes fade-in-upwards-and-expand{0%{opacity:0;-webkit-transform:translateY(80px);transform:translateY(80px);width:96px}50%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0);width:96px}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0);width:100%}}@-webkit-keyframes assistant-shadows{0%{height:0;top:-40px}5%{height:45px;top:-50px}10%{height:45px;top:-50px}15%{height:35px;top:-40px}80%{height:35px;top:-40px}100%{height:0}}@keyframes assistant-shadows{0%{height:0;top:-40px}5%{height:45px;top:-50px}10%{height:45px;top:-50px}15%{height:35px;top:-40px}80%{height:35px;top:-40px}100%{height:0}}@-webkit-keyframes assistant-colors{0%{width:25%}50%{width:50%}100%{width:25%}}@keyframes assistant-colors{0%{width:25%}50%{width:50%}100%{width:25%}}@-webkit-keyframes curtain{from{top:0%}to{top:-100%}}@keyframes curtain{from{top:0%}to{top:-100%}}@-webkit-keyframes right-enter{from{-webkit-transform:translateX(110%);transform:translateX(110%)}to{-webkit-transform:translateX(0%);transform:translateX(0%)}}@keyframes right-enter{from{-webkit-transform:translateX(110%);transform:translateX(110%)}to{-webkit-transform:translateX(0%);transform:translateX(0%)}}@-webkit-keyframes right-enter-rotation{from{-webkit-transform:translateX(110%) rotateZ(10deg);transform:translateX(110%) rotateZ(10deg)}to{-webkit-transform:translateX(0%) rotateZ(-5.5deg);transform:translateX(0%) rotateZ(-5.5deg)}}@keyframes right-enter-rotation{from{-webkit-transform:translateX(110%) rotateZ(10deg);transform:translateX(110%) rotateZ(10deg)}to{-webkit-transform:translateX(0%) rotateZ(-5.5deg);transform:translateX(0%) rotateZ(-5.5deg)}}@-webkit-keyframes move-left{from{left:100vw}to{left:0;visibility:visible}}@keyframes move-left{from{left:100vw}to{left:0;visibility:visible}}@-webkit-keyframes move-right{from{right:100vw}to{right:0;visibility:visible}}@keyframes move-right{from{right:100vw}to{right:0;visibility:visible}}@-webkit-keyframes up-enter{from{bottom:-100%}to{bottom:0;visibility:visible}}@keyframes up-enter{from{bottom:-100%}to{bottom:0;visibility:visible}}@-webkit-keyframes small-up{from{-webkit-transform:translateY(60px);transform:translateY(60px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes small-up{from{-webkit-transform:translateY(60px);transform:translateY(60px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes up-diagonal{from{-webkit-transform:translate(-1000px, 1000px);transform:translate(-1000px, 1000px);visibility:hidden}to{-webkit-transform:translate(0, 0);transform:translate(0, 0);visibility:visible}}@keyframes up-diagonal{from{-webkit-transform:translate(-1000px, 1000px);transform:translate(-1000px, 1000px);visibility:hidden}to{-webkit-transform:translate(0, 0);transform:translate(0, 0);visibility:visible}}@-webkit-keyframes down-diagonal{from{-webkit-transform:translate(1000px, -1000px);transform:translate(1000px, -1000px);visibility:hidden}to{-webkit-transform:translate(0, 0);transform:translate(0, 0);visibility:visible}}@keyframes down-diagonal{from{-webkit-transform:translate(1000px, -1000px);transform:translate(1000px, -1000px);visibility:hidden}to{-webkit-transform:translate(0, 0);transform:translate(0, 0);visibility:visible}}@-webkit-keyframes left-diagonal{from{-webkit-transform:translate(1000px, 1000px);transform:translate(1000px, 1000px);visibility:hidden}to{-webkit-transform:translate(0, 0);transform:translate(0, 0);visibility:visible}}@keyframes left-diagonal{from{-webkit-transform:translate(1000px, 1000px);transform:translate(1000px, 1000px);visibility:hidden}to{-webkit-transform:translate(0, 0);transform:translate(0, 0);visibility:visible}}@-webkit-keyframes right-diagonal{from{-webkit-transform:translate(-1000px, -1000px);transform:translate(-1000px, -1000px);visibility:hidden}to{-webkit-transform:translate(0, 0);transform:translate(0, 0);visibility:visible}}@keyframes right-diagonal{from{-webkit-transform:translate(-1000px, -1000px);transform:translate(-1000px, -1000px);visibility:hidden}to{-webkit-transform:translate(0, 0);transform:translate(0, 0);visibility:visible}}@-webkit-keyframes scale-up{from{height:0;width:0}to{height:100%;visibility:visible;width:100%}}@keyframes scale-up{from{height:0;width:0}to{height:100%;visibility:visible;width:100%}}@-webkit-keyframes rotate-left{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}50%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}100%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes rotate-left{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}50%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}100%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@-webkit-keyframes rotate-right{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}50%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes rotate-right{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}50%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@-webkit-keyframes full-rotate-left{from{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes full-rotate-left{from{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.chr-button,.chr-download-button{-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-radius:24px;cursor:pointer;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;font-family:"Google Sans",arial,sans-serif;font-weight:500;gap:8px;height:auto;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;padding:12px 24px;white-space:nowrap}.chr-button:disabled,.chr-button--disabled,.chr-download-button:disabled{background-color:#f1f3f4;color:#80868b;cursor:default}.dark-theme .chr-button:disabled,.dark-theme .chr-download-button:disabled,.dark-theme .chr-button--disabled{background-color:#f1f3f4}.dark-theme .chr-button:disabled,.dark-theme .chr-download-button:disabled,.dark-theme .chr-button--disabled{color:#80868b}.chr-button:disabled:focus,.chr-download-button:disabled:focus,.chr-button:disabled:hover,.chr-download-button:disabled:hover,.chr-button:disabled:active,.chr-download-button:disabled:active,.chr-button--disabled:focus,.chr-button--disabled:hover,.chr-button--disabled:active{background-color:#f1f3f4;color:#80868b;outline-offset:-2px}.dark-theme .chr-button:disabled:focus,.dark-theme .chr-download-button:disabled:focus,.dark-theme .chr-button:disabled:hover,.dark-theme .chr-download-button:disabled:hover,.dark-theme .chr-button:disabled:active,.dark-theme .chr-download-button:disabled:active,.dark-theme .chr-button--disabled:focus,.dark-theme .chr-button--disabled:hover,.dark-theme .chr-button--disabled:active{background-color:#f1f3f4}.dark-theme .chr-button:disabled:focus,.dark-theme .chr-download-button:disabled:focus,.dark-theme .chr-button:disabled:hover,.dark-theme .chr-download-button:disabled:hover,.dark-theme .chr-button:disabled:active,.dark-theme .chr-download-button:disabled:active,.dark-theme .chr-button--disabled:focus,.dark-theme .chr-button--disabled:hover,.dark-theme .chr-button--disabled:active{color:#80868b}.chr-button:disabled .chr-button__icon,.chr-download-button:disabled .chr-button__icon,.chr-button--disabled .chr-button__icon{fill:#80868b}.dark-theme .chr-button:disabled .chr-button__icon,.dark-theme .chr-download-button:disabled .chr-button__icon,.dark-theme .chr-button--disabled .chr-button__icon{fill:#80868b}.whats-new .chr-button,.whats-new .chr-download-button{background-color:#0b57d0;color:#fff}.dark-theme .whats-new .chr-button,.dark-theme .whats-new .chr-download-button{background-color:#a8c7fa}.dark-theme .whats-new .chr-button,.dark-theme .whats-new .chr-download-button{color:#062e6f}.whats-new.dark-theme .chr-button,.whats-new.dark-theme .chr-download-button{background-color:#a8c7fa;color:#062e6f}.chr-button__icon{height:20px;width:20px}.chr-button--primary,.chr-link--button-primary,.chr-download-button--jumplink,.chr-download-button--mobile-drawer,.chr-download-button--hero,.chr-download-button--header,.chr-download-button--dropdown{background-color:#1a73e8;color:#fff}.dark-theme .chr-button--primary,.dark-theme .chr-link--button-primary,.dark-theme .chr-download-button--jumplink,.dark-theme .chr-download-button--mobile-drawer,.dark-theme .chr-download-button--hero,.dark-theme .chr-download-button--header,.dark-theme .chr-download-button--dropdown{background-color:#1a73e8}.dark-theme .chr-button--primary,.dark-theme .chr-link--button-primary,.dark-theme .chr-download-button--jumplink,.dark-theme .chr-download-button--mobile-drawer,.dark-theme .chr-download-button--hero,.dark-theme .chr-download-button--header,.dark-theme .chr-download-button--dropdown{color:#fff}.chr-button--primary:hover,.chr-link--button-primary:hover,.chr-download-button--jumplink:hover,.chr-download-button--mobile-drawer:hover,.chr-download-button--hero:hover,.chr-download-button--header:hover,.chr-download-button--dropdown:hover{background-color:#185abc}.dark-theme .chr-button--primary:hover,.dark-theme .chr-link--button-primary:hover,.dark-theme .chr-download-button--jumplink:hover,.dark-theme .chr-download-button--mobile-drawer:hover,.dark-theme .chr-download-button--hero:hover,.dark-theme .chr-download-button--header:hover,.dark-theme .chr-download-button--dropdown:hover{background-color:#a8c7fa}.chr-button--primary:focus,.chr-link--button-primary:focus,.chr-download-button--jumplink:focus,.chr-download-button--mobile-drawer:focus,.chr-download-button--hero:focus,.chr-download-button--header:focus,.chr-download-button--dropdown:focus{outline:unset}.chr-button--primary:focus-visible,.chr-link--button-primary:focus-visible,.chr-download-button--jumplink:focus-visible,.chr-download-button--mobile-drawer:focus-visible,.chr-download-button--hero:focus-visible,.chr-download-button--header:focus-visible,.chr-download-button--dropdown:focus-visible{background-color:#185abc;outline:2px auto #1967d2;outline-offset:-2px}.dark-theme .chr-button--primary:focus-visible,.dark-theme .chr-link--button-primary:focus-visible,.dark-theme .chr-download-button--jumplink:focus-visible,.dark-theme .chr-download-button--mobile-drawer:focus-visible,.dark-theme .chr-download-button--hero:focus-visible,.dark-theme .chr-download-button--header:focus-visible,.dark-theme .chr-download-button--dropdown:focus-visible{background-color:#a8c7fa}.dark-theme .chr-button--primary:focus-visible,.dark-theme .chr-link--button-primary:focus-visible,.dark-theme .chr-download-button--jumplink:focus-visible,.dark-theme .chr-download-button--mobile-drawer:focus-visible,.dark-theme .chr-download-button--hero:focus-visible,.dark-theme .chr-download-button--header:focus-visible,.dark-theme .chr-download-button--dropdown:focus-visible{outline:2px auto #1967d2}.chr-button--primary:active,.chr-link--button-primary:active,.chr-download-button--jumplink:active,.chr-download-button--mobile-drawer:active,.chr-download-button--hero:active,.chr-download-button--header:active,.chr-download-button--dropdown:active{background-color:#174ea6}.dark-theme .chr-button--primary:active,.dark-theme .chr-link--button-primary:active,.dark-theme .chr-download-button--jumplink:active,.dark-theme .chr-download-button--mobile-drawer:active,.dark-theme .chr-download-button--hero:active,.dark-theme .chr-download-button--header:active,.dark-theme .chr-download-button--dropdown:active{background-color:#174ea6}.chr-button--primary .chr-button__icon,.chr-link--button-primary .chr-button__icon,.chr-download-button--jumplink .chr-button__icon,.chr-download-button--mobile-drawer .chr-button__icon,.chr-download-button--hero .chr-button__icon,.chr-download-button--header .chr-button__icon,.chr-download-button--dropdown .chr-button__icon{fill:#fff}.dark-theme .chr-button--primary .chr-button__icon,.dark-theme .chr-link--button-primary .chr-button__icon,.dark-theme .chr-download-button--jumplink .chr-button__icon,.dark-theme .chr-download-button--mobile-drawer .chr-button__icon,.dark-theme .chr-download-button--hero .chr-button__icon,.dark-theme .chr-download-button--header .chr-button__icon,.dark-theme .chr-download-button--dropdown .chr-button__icon{fill:#fff}.chr-button--inverted,.chr-download-button--inverted{background-color:#fff;color:#1967d2}.dark-theme .chr-button--inverted,.dark-theme .chr-download-button--inverted{background-color:#fff}.dark-theme .chr-button--inverted,.dark-theme .chr-download-button--inverted{color:#1967d2}.chr-button--inverted:hover,.chr-download-button--inverted:hover{background-color:#e8f0fe}.dark-theme .chr-button--inverted:hover,.dark-theme .chr-download-button--inverted:hover{background-color:#e8f0fe}.chr-button--inverted:focus,.chr-download-button--inverted:focus{outline:unset}.chr-button--inverted:focus-visible,.chr-download-button--inverted:focus-visible{background-color:#e8f0fe;outline:2px auto #1967d2;outline-offset:-2px}.dark-theme .chr-button--inverted:focus-visible,.dark-theme .chr-download-button--inverted:focus-visible{background-color:#e8f0fe}.dark-theme .chr-button--inverted:focus-visible,.dark-theme .chr-download-button--inverted:focus-visible{outline:2px auto #1967d2}.chr-button--inverted:active,.chr-download-button--inverted:active{background-color:#d2e3fc}.dark-theme .chr-button--inverted:active,.dark-theme .chr-download-button--inverted:active{background-color:#d2e3fc}.chr-button--inverted .chr-button__icon,.chr-download-button--inverted .chr-button__icon{fill:#1967d2}.dark-theme .chr-button--inverted .chr-button__icon,.dark-theme .chr-download-button--inverted .chr-button__icon{fill:#1967d2}.chr-button--secondary,.chr-link--button-secondary,.chr-download-button--secondary{background-color:#e8f0fe;color:#1967d2}.dark-theme .chr-button--secondary,.dark-theme .chr-link--button-secondary,.dark-theme .chr-download-button--secondary{background-color:#e8f0fe}.dark-theme .chr-button--secondary,.dark-theme .chr-link--button-secondary,.dark-theme .chr-download-button--secondary{color:#1967d2}.chr-button--secondary:hover,.chr-link--button-secondary:hover,.chr-download-button--secondary:hover{background-color:#d2e3fc}.dark-theme .chr-button--secondary:hover,.dark-theme .chr-link--button-secondary:hover,.dark-theme .chr-download-button--secondary:hover{background-color:#d2e3fc}.chr-button--secondary:focus,.chr-link--button-secondary:focus,.chr-download-button--secondary:focus{overflow:unset}.chr-button--secondary:focus-visible,.chr-link--button-secondary:focus-visible,.chr-download-button--secondary:focus-visible{background-color:#d2e3fc;outline:2px auto #1967d2;outline-offset:-2px}.dark-theme .chr-button--secondary:focus-visible,.dark-theme .chr-link--button-secondary:focus-visible,.dark-theme .chr-download-button--secondary:focus-visible{background-color:#d2e3fc}.dark-theme .chr-button--secondary:focus-visible,.dark-theme .chr-link--button-secondary:focus-visible,.dark-theme .chr-download-button--secondary:focus-visible{outline:2px auto #1967d2}.chr-button--secondary:active,.chr-link--button-secondary:active,.chr-download-button--secondary:active{background-color:#aecbfa}.dark-theme .chr-button--secondary:active,.dark-theme .chr-link--button-secondary:active,.dark-theme .chr-download-button--secondary:active{background-color:#aecbfa}.chr-button--secondary .chr-button__icon,.chr-link--button-secondary .chr-button__icon,.chr-download-button--secondary .chr-button__icon{fill:#1967d2}.dark-theme .chr-button--secondary .chr-button__icon,.dark-theme .chr-link--button-secondary .chr-button__icon,.dark-theme .chr-download-button--secondary .chr-button__icon{fill:#1967d2}.chr-button--link{color:#1967d2;border-radius:0;display:inline-block;font-family:"Google Sans",arial,sans-serif;font-weight:500;padding:12px 0}.dark-theme .chr-button--link{color:#1967d2}.chr-button--link.chr-cta-small{padding:12px 0}.chr-button--link.chr-cta-large,.chr-button--link.chr-download-button--mobile-drawer,.chr-button--link.chr-download-button--hero{padding:12px 0}.chr-button--link.chr-button--large,.chr-button--link.chr-download-button--hero{border-radius:0}.chr-button--link.chr-button--small{border-radius:0}.chr-button--link:hover{color:#185abc}.dark-theme .chr-button--link:hover{color:#a8c7fa}.chr-button--link:focus{outline:unset}.chr-button--link:focus-visible{outline:2px auto #1967d2;outline-offset:-2px}.dark-theme .chr-button--link:focus-visible{outline:2px auto #1967d2}.chr-button--link:disabled,.chr-button--link.chr-button--disabled{color:#80868b;background:none}.dark-theme .chr-button--link:disabled,.dark-theme .chr-button--link.chr-button--disabled{color:#80868b}.chr-button--link:disabled:hover,.chr-button--link.chr-button--disabled:hover{background:none}.chr-button--link:disabled:focus,.chr-button--link.chr-button--disabled:focus{outline:none}.chr-button--link:disabled:focus-visible,.chr-button--link.chr-button--disabled:focus-visible{outline:none}.chr-button--link .chr-button__icon{fill:#1967d2}.dark-theme .chr-button--link .chr-button__icon{fill:#1967d2}.chr-button--small{gap:4px;padding:12px 16px}.chr-button--small .chr-button__icon{height:16px;width:16px}.chr-button--large,.chr-download-button--hero{border-radius:32px;gap:12px;padding:20px 32px}.chr-button--large .chr-button__icon,.chr-download-button--hero .chr-button__icon{height:24px;width:24px}.chr-button--reversed{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}.whats-new .chr-button,.whats-new .chr-download-button{background-color:#0b57d0;color:#fff}.dark-theme .whats-new .chr-button,.dark-theme .whats-new .chr-download-button{background-color:#a8c7fa}.dark-theme .whats-new .chr-button,.dark-theme .whats-new .chr-download-button{color:#062e6f}.whats-new .chr-button:hover,.whats-new .chr-download-button:hover{background-color:#2368d4}.dark-theme .whats-new .chr-button:hover,.dark-theme .whats-new .chr-download-button:hover{background-color:#a0bded}.whats-new .chr-button:focus,.whats-new .chr-download-button:focus{background-color:#0b57d0;outline:2px auto #1967d2;outline-offset:-2px}.dark-theme .whats-new .chr-button:focus,.dark-theme .whats-new .chr-download-button:focus{background-color:#a8c7fa}.dark-theme .whats-new .chr-button:focus,.dark-theme .whats-new .chr-download-button:focus{outline:2px auto #1967d2}.whats-new .chr-button:active,.whats-new .chr-download-button:active{background-color:#2368d4}.dark-theme .whats-new .chr-button:active,.dark-theme .whats-new .chr-download-button:active{background-color:#a0bded}.chr-action-icon{-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-radius:50%;cursor:pointer;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-transform:scale(1);transform:scale(1);-webkit-transition:-webkit-transform .3s ease-out;transition:-webkit-transform .3s ease-out;transition:transform .3s ease-out;transition:transform .3s ease-out, -webkit-transform .3s ease-out}.chr-action-icon:focus-visible{outline:none}.chr-action-icon__icon{-webkit-transition:-webkit-transform .3s ease-out;transition:-webkit-transform .3s ease-out;transition:transform .3s ease-out;transition:transform .3s ease-out, -webkit-transform .3s ease-out}.chr-action-icon--regular{height:56px;width:56px}.chr-action-icon--regular .chr-action-icon__icon{height:32px;width:32px}.chr-action-icon--small{height:48px;width:48px}.chr-action-icon--small .chr-action-icon__icon{height:28px;width:28px}.chr-action-icon--extra-small{height:36px;padding:0;width:36px}.chr-action-icon--extra-small .chr-action-icon__icon{height:16px;width:16px}.chr-action-icon--large{font-size:1rem;line-height:1.5rem;letter-spacing:0rem;color:#1967d2;font-family:"Google Sans",arial,sans-serif;font-weight:500}.dark-theme .chr-action-icon--large{color:#1967d2}.chr-action-icon--primary{background-color:#1a73e8}.dark-theme .chr-action-icon--primary{background-color:#1a73e8}.chr-action-icon--primary .chr-action-icon__icon{fill:#fff}.dark-theme .chr-action-icon--primary .chr-action-icon__icon{fill:#fff}.chr-action-icon--primary:hover,.chr-action-icon--primary:focus{background-color:#185abc}.dark-theme .chr-action-icon--primary:hover,.dark-theme .chr-action-icon--primary:focus{background-color:#a8c7fa}.chr-action-icon--primary:focus-visible{background-color:#185abc;border:2px solid #1967d2}.dark-theme .chr-action-icon--primary:focus-visible{background-color:#a8c7fa}.dark-theme .chr-action-icon--primary:focus-visible{border:2px solid #1967d2}.chr-action-icon--primary:active{background-color:#174ea6;border:none}.dark-theme .chr-action-icon--primary:active{background-color:#174ea6}.chr-action-icon--primary:disabled{background-color:#f1f3f4}.dark-theme .chr-action-icon--primary:disabled{background-color:#f1f3f4}.chr-action-icon--primary:disabled .chr-action-icon__icon{fill:#80868b}.dark-theme .chr-action-icon--primary:disabled .chr-action-icon__icon{fill:#80868b}.chr-action-icon--secondary{background-color:#e8f0fe}.dark-theme .chr-action-icon--secondary{background-color:#e8f0fe}.chr-action-icon--secondary .chr-action-icon__icon{fill:#1967d2}.dark-theme .chr-action-icon--secondary .chr-action-icon__icon{fill:#1967d2}.chr-action-icon--secondary:hover,.chr-action-icon--secondary:focus{background-color:#d2e3fc}.dark-theme .chr-action-icon--secondary:hover,.dark-theme .chr-action-icon--secondary:focus{background-color:#d2e3fc}.chr-action-icon--secondary:focus-visible{background-color:#d2e3fc;border:2px solid #1967d2}.dark-theme .chr-action-icon--secondary:focus-visible{background-color:#d2e3fc}.dark-theme .chr-action-icon--secondary:focus-visible{border:2px solid #1967d2}.chr-action-icon--secondary:active{background-color:#aecbfa;border:none}.dark-theme .chr-action-icon--secondary:active{background-color:#aecbfa}.chr-action-icon--secondary:disabled{background-color:#f1f3f4}.dark-theme .chr-action-icon--secondary:disabled{background-color:#f1f3f4}.chr-action-icon--secondary:disabled .chr-action-icon__icon{fill:#80868b}.dark-theme .chr-action-icon--secondary:disabled .chr-action-icon__icon{fill:#80868b}.chr-action-icon--tertiary{background-color:#fff;border:1px solid #dadce0}.dark-theme .chr-action-icon--tertiary{background-color:#fff}.dark-theme .chr-action-icon--tertiary{border:1px solid #dadce0}.chr-action-icon--tertiary .chr-action-icon__icon{fill:#202124}.dark-theme .chr-action-icon--tertiary .chr-action-icon__icon{fill:#202124}.chr-action-icon--tertiary:hover,.chr-action-icon--tertiary:focus{background-color:#d2e3fc;border:none}.dark-theme .chr-action-icon--tertiary:hover,.dark-theme .chr-action-icon--tertiary:focus{background-color:#d2e3fc}.chr-action-icon--tertiary:focus-visible{background-color:#d2e3fc;border:2px solid #1967d2}.dark-theme .chr-action-icon--tertiary:focus-visible{background-color:#d2e3fc}.dark-theme .chr-action-icon--tertiary:focus-visible{border:2px solid #1967d2}.chr-action-icon--tertiary:active{background-color:#aecbfa;border:none}.dark-theme .chr-action-icon--tertiary:active{background-color:#aecbfa}.chr-action-icon--tertiary:disabled{background-color:#f1f3f4}.dark-theme .chr-action-icon--tertiary:disabled{background-color:#f1f3f4}.chr-action-icon--tertiary:disabled .chr-action-icon__icon{fill:#80868b}.dark-theme .chr-action-icon--tertiary:disabled .chr-action-icon__icon{fill:#80868b}.chr-action-icon--whats-new{background-color:#0b57d0;color:#fff}.dark-theme .chr-action-icon--whats-new{background-color:#a8c7fa}.dark-theme .chr-action-icon--whats-new{color:#062e6f}.chr-action-icon--whats-new:hover{background-color:#185abc}.dark-theme .chr-action-icon--whats-new:hover{background-color:#a8c7fa}.chr-action-icon--whats-new:focus{background-color:#185abc;outline:2px auto #1967d2;-webkit-transform:scale(1.2);transform:scale(1.2)}.dark-theme .chr-action-icon--whats-new:focus{background-color:#a8c7fa}.dark-theme .chr-action-icon--whats-new:focus{outline:2px auto #1967d2}.chr-action-icon--whats-new:active{background-color:#2368d4}.dark-theme .chr-action-icon--whats-new:active{background-color:#a0bded}.chr-action-icon--whats-new .chr-action-icon__icon{fill:#fff}.dark-theme .chr-action-icon--whats-new .chr-action-icon__icon{fill:#062e6f}.chr-action-icon--light{background-color:#1a73e8}.dark-theme .chr-action-icon--light{background-color:#1a73e8}.chr-action-icon--light .chr-action-icon__icon{fill:#fff}.dark-theme .chr-action-icon--light .chr-action-icon__icon{fill:#fff}.chr-action-icon--light:hover,.chr-action-icon--light:focus{background-color:#185abc;-webkit-transform:scale(1.142);transform:scale(1.142)}.dark-theme .chr-action-icon--light:hover,.dark-theme .chr-action-icon--light:focus{background-color:#a8c7fa}.chr-action-icon--light:focus-visible{background-color:#185abc;border:2px solid #1967d2}.dark-theme .chr-action-icon--light:focus-visible{background-color:#a8c7fa}.dark-theme .chr-action-icon--light:focus-visible{border:2px solid #1967d2}.chr-action-icon--light:active{background-color:#174ea6;border:none}.dark-theme .chr-action-icon--light:active{background-color:#174ea6}.chr-action-icon--light.chr-action-icon.active{background-color:#80868b;fill:#fff}.dark-theme .chr-action-icon--light.chr-action-icon.active{background-color:#80868b}.dark-theme .chr-action-icon--light.chr-action-icon.active{fill:#fff}.chr-action-icon--light.chr-action-icon.active .chr-action-icon__icon{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.chr-action-icon--dark{background-color:#fff}.dark-theme .chr-action-icon--dark{background-color:#fff}.chr-action-icon--dark .chr-action-icon__icon{fill:#1967d2}.dark-theme .chr-action-icon--dark .chr-action-icon__icon{fill:#1967d2}.chr-action-icon--dark:hover,.chr-action-icon--dark:focus{background-color:#d2e3fc;-webkit-transform:scale(1.142);transform:scale(1.142)}.dark-theme .chr-action-icon--dark:hover,.dark-theme .chr-action-icon--dark:focus{background-color:#d2e3fc}.chr-action-icon--dark:focus-visible{background-color:#d2e3fc;border:2px solid #1967d2}.dark-theme .chr-action-icon--dark:focus-visible{background-color:#d2e3fc}.dark-theme .chr-action-icon--dark:focus-visible{border:2px solid #1967d2}.chr-action-icon--dark:active{background-color:#d2e3fc;border:none}.dark-theme .chr-action-icon--dark:active{background-color:#d2e3fc}.chr-action-icon--dark:disabled{background-color:#f1f3f4}.dark-theme .chr-action-icon--dark:disabled{background-color:#f1f3f4}.chr-action-icon--dark:disabled .chr-action-icon__icon{fill:#80868b}.dark-theme .chr-action-icon--dark:disabled .chr-action-icon__icon{fill:#80868b}.chr-action-icon--dark.chr-action-icon.active{background-color:#aecbfa;fill:#fff}.dark-theme .chr-action-icon--dark.chr-action-icon.active{background-color:#aecbfa}.dark-theme .chr-action-icon--dark.chr-action-icon.active{fill:#fff}.chr-action-icon--dark.chr-action-icon.active .chr-action-icon__icon{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.chr-action-icon--inline .chr-action-icon__icon{fill:#202124}.dark-theme .chr-action-icon--inline .chr-action-icon__icon{fill:#202124}.whats-new .chr-action-icon--inline{background-color:#e8f0fe}.dark-theme .whats-new .chr-action-icon--inline{background-color:#e8f0fe}.whats-new .chr-action-icon--inline .chr-action-icon__icon{fill:#1967d2}.dark-theme .whats-new .chr-action-icon--inline .chr-action-icon__icon{fill:#1967d2}.whats-new.dark-theme .chr-action-icon--inline{background-color:#aecbfa}.dark-theme .whats-new.dark-theme .chr-action-icon--inline{background-color:#aecbfa}.chr-download-button{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}.chr-download-button.privacy-download{white-space:inherit}[lang=de] .chr-download-button.privacy-download{padding:20px 10px}@media only screen and (min-width: 600px){[lang=de] .chr-download-button.privacy-download{padding:20px 32px}}[lang=ko] .chr-download-button.privacy-download{padding:20px 10px}@media only screen and (min-width: 600px){[lang=ko] .chr-download-button.privacy-download{padding:20px 32px}}.chr-download-button.privacy-download .chr-button__icon{height:48px;width:48px}@media only screen and (min-width: 600px){.chr-download-button.privacy-download .chr-button__icon{height:24px;width:24px}}[lang=nl] .chr-download-button.home-jumplink-download{font-size:14px;padding:12px 8px}@media only screen and (min-width: 600px){[lang=nl] .chr-download-button.home-jumplink-download{font-size:16px;padding:12px 24px}}[lang=pt] .chr-download-button.home-jumplink-download{padding:12px 8px;white-space:normal}@media only screen and (min-width: 600px){[lang=pt] .chr-download-button.home-jumplink-download{padding:12px 24px;white-space:inherit}}[lang=nl] .chr-download-button.home-download-now{padding:20px 16px}@media only screen and (min-width: 600px){[lang=nl] .chr-download-button.home-download-now{padding:20px 32px}}[lang=pt] .chr-download-button.home-download-now{padding:20px 16px}@media only screen and (min-width: 600px){[lang=pt] .chr-download-button.home-download-now{padding:20px 32px}}[lang=pt] .chr-download-button.home-download-now{font-size:16px}@media only screen and (min-width: 600px){[lang=pt] .chr-download-button.home-download-now{font-size:18px}}[lang=pt] .chr-download-button.home-download-hero{padding:20px 16px}@media only screen and (min-width: 600px){[lang=pt] .chr-download-button.home-download-hero{padding:20px 32px}}.chr-download-button--dropdown{font-family:"Google Sans",arial,sans-serif;font-size:.75rem;padding:12px 16px}.chr-download-button--canary{background-color:#fbbc04;color:#202124}.dark-theme .chr-download-button--canary{background-color:#fbbc04}.dark-theme .chr-download-button--canary{color:#202124}.chr-download-button--canary:focus,.chr-download-button--canary:active,.chr-download-button--canary:hover{background-color:#fbbc04;color:#202124}.dark-theme .chr-download-button--canary:focus,.dark-theme .chr-download-button--canary:active,.dark-theme .chr-download-button--canary:hover{background-color:#fbbc04}.dark-theme .chr-download-button--canary:focus,.dark-theme .chr-download-button--canary:active,.dark-theme .chr-download-button--canary:hover{color:#202124}.chr-download-button--mobile{width:100%}.chr-download-button--header{padding:12px 24px}.chr-download-button--mobile-drawer{width:100%}.chr-download-button--reversed{-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row}.chr-download-button--jumplink{padding:12px 24px}@media only screen and (min-width: 1024px){.chr-download-button--jumplink{padding:8px 20px}}.chr-download-button--inline{border-radius:0}.chr-download-button--inline .chr-download-button__icon{display:none}.chr-download-button__label--mobile{display:block;visibility:visible}@media only screen and (min-width: 1024px){.chr-download-button__label--mobile{display:none;visibility:hidden}}.chr-download-button__label--desktop{display:none;visibility:hidden}@media only screen and (min-width: 1024px){.chr-download-button__label--desktop{display:block;visibility:visible}}.chr-download-button+.chr-simplified-download{text-align:left}[dir=rtl] .chr-download-button+.chr-simplified-download{text-align:right}.chr-download-button+.chr-simplified-download .platform{margin:0 0 var(--spacer-03)}[dir=rtl] .chr-download-button+.chr-simplified-download .chr-checkbox__input{margin:0 0 0 16px}.chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-links{margin:16px auto 0;text-align:center}.chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-links a{border:none;color:#1967d2}.dark-theme .chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-links a{color:#8ab4f8}.chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-opt{font-family:"Google Sans",arial,sans-serif;margin:0 auto 10px}.chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-opt a{color:#1967d2}.dark-theme .chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-opt a{color:#8ab4f8}.chr-download-button+.chr-simplified-download .chr-platform-list{text-align:center}.chr-download-button+.chr-simplified-download .hero-chrome-update-cta{margin:var(--spacer-03) 0 14px}.chr-download-button+.chr-simplified-download .hero-chrome-update-cta .chr-link{padding:14px 0}@media only screen and (min-width: 1024px){.chr-download-button+.chr-simplified-download .hero-chrome-update-cta .chr-link{padding:inherit}}@media only screen and (min-width: 1024px){.chr-download-button+.chr-simplified-download .hero-chrome-update-cta{margin-bottom:24px}}.chr-download-button+.chr-simplified-download.chr-homepage-hero__simplified-opt{width:419px}.chr-download-button+.chr-simplified-download.chr-homepage-hero__simplified-links{width:419px}[lang=es] .chr-download-button+.chr-simplified-download{max-width:582px}[lang=fr] .chr-download-button+.chr-simplified-download{max-width:582px}[lang=it] .chr-download-button+.chr-simplified-download{max-width:582px}[lang=lv] .chr-download-button+.chr-simplified-download{max-width:582px}[lang=ms] .chr-download-button+.chr-simplified-download{max-width:582px}[lang=ro] .chr-download-button+.chr-simplified-download{max-width:582px}[lang=sl] .chr-download-button+.chr-simplified-download{max-width:582px}[lang=uk] .chr-download-button+.chr-simplified-download{max-width:582px}[lang=ca] .chr-download-button+.chr-simplified-download{max-width:602px}[lang=bg] .chr-download-button+.chr-simplified-download{max-width:602px}[lang=fil] .chr-download-button+.chr-simplified-download{max-width:602px}[lang=el] .chr-download-button+.chr-simplified-download{max-width:648px}[lang=ru] .chr-download-button+.chr-simplified-download{max-width:648px}.chr-simplified-download-wrapper{margin:0 auto;max-width:516px;text-align:center}.chr-simplified-download-wrapper--channel{max-width:560px}@media only screen and (min-width: 0)and (max-width: 599px){.chr-simplified-download-wrapper--channel .chr-download-button{font-family:"Google Sans Text",arial,sans-serif;font-size:1rem;font-weight:500;letter-spacing:0;line-height:1.5rem;width:320px}}.chr-simplified-download-wrapper>.chr-download-button{margin:0 auto var(--spacer-01)}.environment{display:none !important}.environment--active{display:block !important}.chr-checkbox{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}.chr-checkbox__input{accent-color:#1a73e8;margin-right:16px;-webkit-transform:scale(1.5);transform:scale(1.5)}.dark-theme .chr-checkbox__input{accent-color:#1a73e8}[dir=rtl] .chr-checkbox__input{margin-left:16px;margin-right:0}.chr-checkbox--grid{-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start;display:-ms-grid;display:grid;grid-auto-rows:auto;-ms-grid-columns:min-content 1fr;grid-template-columns:-webkit-min-content 1fr;grid-template-columns:min-content 1fr}.chr-radio__input{accent-color:#1a73e8}.dark-theme .chr-radio__input{accent-color:#1a73e8}.chr-grid-default-parent{margin:auto;max-width:1440px;padding:0 28px}@media only screen and (min-width: 600px){.chr-grid-default-parent{margin:auto;max-width:1440px;padding:0 28px;padding:0 40px}}@media only screen and (min-width: 1024px){.chr-grid-default-parent{margin:auto;max-width:1440px;padding:0 28px;padding:0 72px}}@media only screen and (min-width: 1440px){.chr-grid-default-parent{margin:auto;max-width:1440px;padding:0 28px;padding:0 74px}}.chr-grid-default-parent--no-overflow-x{overflow-x:hidden}@media only screen and (min-width: 600px){.chr-grid-default-parent--no-overflow-x{overflow-x:visible}}.chr-grid-default{display:-ms-grid;display:grid;grid-column-gap:28px;-ms-grid-columns:(1fr)[4];grid-template-columns:repeat(4, 1fr)}@media only screen and (min-width: 600px){.chr-grid-default{display:-ms-grid;display:grid;grid-column-gap:28px;-ms-grid-columns:(1fr)[4];grid-template-columns:repeat(4, 1fr);grid-column-gap:40px;-ms-grid-columns:(1fr)[12];grid-template-columns:repeat(12, 1fr)}}@media only screen and (min-width: 1024px){.chr-grid-default{display:-ms-grid;display:grid;grid-column-gap:28px;-ms-grid-columns:(1fr)[4];grid-template-columns:repeat(4, 1fr);grid-column-gap:48px;-ms-grid-columns:(1fr)[12];grid-template-columns:repeat(12, 1fr)}}@media only screen and (min-width: 1440px){.chr-grid-default{display:-ms-grid;display:grid;grid-column-gap:28px;-ms-grid-columns:(1fr)[4];grid-template-columns:repeat(4, 1fr);grid-column-gap:64px;-ms-grid-columns:(1fr)[12];grid-template-columns:repeat(12, 1fr)}}.chr-grid-default--centered{-webkit-box-align:center;-ms-flex-align:center;align-items:center}.chr-grid-default-child{padding-right:28px}@media only screen and (min-width: 600px){.chr-grid-default-child{padding-right:28px;padding-right:40px}}@media only screen and (min-width: 1024px){.chr-grid-default-child{padding-right:28px;padding-right:48px}}@media only screen and (min-width: 1440px){.chr-grid-default-child{padding-right:28px;padding-right:64px}}@supports(grid-column-gap: 28px){.chr-grid-default-child{padding-right:0 !important}}.chr-icon{display:inline-block;fill:currentColor;-webkit-transition:-webkit-transform .2s ease;transition:-webkit-transform .2s ease;transition:transform .2s ease;transition:transform .2s ease, -webkit-transform .2s ease;vertical-align:middle}.chr-icon--18{height:18px;width:18px}.chr-icon--24{height:24px;width:24px}.chr-icon--28{height:28px;width:28px}.chr-icon--32{height:32px;width:32px}.chr-icon--40{height:40px;width:40px}.chr-icon--72{height:72px;width:72px}.chr-icon--link{height:16px;width:16px}.chr-image-icon{display:inline-block;-webkit-transition:-webkit-transform .2s ease;transition:-webkit-transform .2s ease;transition:transform .2s ease;transition:transform .2s ease, -webkit-transform .2s ease;vertical-align:middle}@media(hover: hover)and (pointer: fine){.handler-media:hover,.handler-media:focus{background-color:#d2e3fc;border-color:rgba(0,0,0,0)}.dark-theme .handler-media:hover,.dark-theme .handler-media:focus{background-color:#d2e3fc}}.handler-media:active,.handler-media:focus{background-color:#aecbfa;border-color:rgba(0,0,0,0)}.dark-theme .handler-media:active,.dark-theme .handler-media:focus{background-color:#aecbfa}.handler-media:focus-visible{border-color:#1967d2;background-color:#d2e3fc;border-width:2px;outline:0}.dark-theme .handler-media:focus-visible{border-color:#1967d2}.dark-theme .handler-media:focus-visible{background-color:#d2e3fc}.handler-media__container{position:relative}.handler-media__icon{color:#202124}.dark-theme .handler-media__icon{color:#202124}.handler-media__text{font-size:0.75rem;line-height:1.125rem;letter-spacing:0.009375rem;color:#202124;font-family:"Google Sans Text",arial,sans-serif;font-weight:400;text-wrap:nowrap}.dark-theme .handler-media__text{color:#202124}.handler-media--button-circle{-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-radius:50%;cursor:pointer;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}@media only screen and (min-width: 1024px){.handler-media--button-circle:hover::after,.handler-media--button-circle:focus::after,.handler-media--button-circle:active::after{opacity:1}}.handler-media--button-circle .handler-media__label{display:-webkit-box;display:-ms-flexbox;display:flex}.handler-media--button-circle .handler-media__text{display:none}.handler-media--button-circle::after{background-color:#d2e3fc;font-size:0.75rem;line-height:1.125rem;letter-spacing:0.009375rem;color:#202124;border-radius:0.25rem;content:attr(aria-label);font-family:"Google Sans Text",arial,sans-serif;font-weight:400;left:0;opacity:0;overflow:hidden;padding:0.125rem 0.5rem;pointer-events:none;position:absolute;text-wrap:nowrap;-webkit-transition:opacity .3s ease-in-out;transition:opacity .3s ease-in-out;white-space:nowrap}.dark-theme .handler-media--button-circle::after{background-color:#d2e3fc}.dark-theme .handler-media--button-circle::after{color:#202124}.handler-media--button-text{border-radius:24px;cursor:pointer;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;padding:4px 12px 4px 8px;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.handler-media--button-text .handler-media__container,.handler-media--button-text .handler-media__label{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;gap:4px}.handler-media--sm{border:1px solid rgba(0,0,0,0)}.handler-media--sm .handler-media__icon{height:1rem;width:1rem}.handler-media--sm.handler-media--button-circle{height:2rem;padding:var(--spacer-01);width:2rem}.handler-media--sm.handler-media--button-circle::after{top:3rem}.handler-media--md{border:2px solid rgba(0,0,0,0)}.handler-media--md .handler-media__icon{height:2rem;width:2rem}.handler-media--md.handler-media--button-circle{height:4rem;padding:var(--spacer-01);width:4rem}.handler-media--md.handler-media--button-circle::after{top:4.75rem}.handler-media--border{border-color:#dadce0}.dark-theme .handler-media--border{border-color:#dadce0}.handler-media__label[aria-hidden=true]{display:none}.dark-theme.whats-new .handler-media{background-color:#004a77;border-color:rgba(0,0,0,0)}.dark-theme.whats-new .handler-media__icon,.dark-theme.whats-new .handler-media__text,.dark-theme.whats-new .handler-media::after{color:#c2e7ff}.dark-theme.whats-new .handler-media::after{background-color:#004a77}@media(hover: hover)and (pointer: fine){.dark-theme.whats-new .handler-media:hover,.dark-theme.whats-new .handler-media:focus{background-color:#195c84}}.dark-theme.whats-new .handler-media:active,.dark-theme.whats-new .handler-media:focus{background-color:#195c84}.dark-theme.whats-new .handler-media:focus-visible{background-color:#004a77;border-color:#a8c7fa;border-width:2px;outline:0}.chr-icon-text{text-wrap:nowrap}.chr-icon-text--icon{height:var(--icon_height);margin:var(--icon_margin)}.chr-link{font-size:1rem;line-height:1.5rem;letter-spacing:0rem;display:inline-block;font-family:"Google Sans",arial,sans-serif;font-weight:500;padding:12px 0}.chr-link .chr-link__icon{fill:currentColor;height:16px;margin-left:6px;-webkit-transition:-webkit-transform .1s linear;transition:-webkit-transform .1s linear;transition:transform .1s linear;transition:transform .1s linear, -webkit-transform .1s linear;vertical-align:middle;width:16px}.chr-link .chr-link__icon--reversed{margin-left:unset}.chr-link--campaign{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;gap:6px;white-space:nowrap}.chr-link-icon{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.chr-link--external:hover .chr-link__icon,.chr-link--external:focus .chr-link__icon{-webkit-transform:translate(4px, -4px);transform:translate(4px, -4px)}.chr-link--jumplink{color:#5f6368;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-radius:50px;display:-webkit-box;display:-ms-flexbox;display:flex;height:48px;padding:12px 20px}.dark-theme .chr-link--jumplink{color:#5f6368}@media only screen and (min-width: 1024px){.chr-link--jumplink{height:40px;padding:8px 20px}}.chr-link--jumplink:hover{background-color:#f8f9fa}.dark-theme .chr-link--jumplink:hover{background-color:#f8f9fa}.chr-link--jumplink:focus{outline:unset}.chr-link--jumplink:focus-visible{background-color:#f8f9fa;outline:2px auto #1967d2}.dark-theme .chr-link--jumplink:focus-visible{background-color:#f8f9fa}.dark-theme .chr-link--jumplink:focus-visible{outline:2px auto #1967d2}.chr-link--jumplink:active{background-color:#f1f3f4}.dark-theme .chr-link--jumplink:active{background-color:#f1f3f4}.chr-link--jumplink.chr-link--active{background-color:#f1f3f4;color:#202124}.dark-theme .chr-link--jumplink.chr-link--active{background-color:#f1f3f4}.dark-theme .chr-link--jumplink.chr-link--active{color:#202124}.chr-link--small{font-size:0.75rem;line-height:1.25rem;letter-spacing:0.009375rem;font-family:"Google Sans",arial,sans-serif;font-weight:500}.chr-link--small .chr-link__icon{height:12px;width:12px}.chr-link--large{font-size:1.125rem;line-height:1.5rem;letter-spacing:0rem;color:#1967d2;font-family:"Google Sans",arial,sans-serif;font-weight:500}.dark-theme .chr-link--large{color:#1967d2}.chr-link--large .chr-link__icon{height:20px;width:20px}.chr-link--primary{color:#1967d2}.dark-theme .chr-link--primary{color:#1967d2}.chr-link--primary:hover{color:#185abc}.dark-theme .chr-link--primary:hover{color:#a8c7fa}.chr-link--primary:focus{outline:unset}.chr-link--primary:focus-visible{outline:2px auto #1967d2;outline-offset:-2px}.dark-theme .chr-link--primary:focus-visible{outline:2px auto #1967d2}.chr-link--primary .chr-link__icon{fill:#1967d2}.dark-theme .chr-link--primary .chr-link__icon{fill:#1967d2}.whats-new .chr-link--primary .chr-link__icon{fill:#0b57d0}.dark-theme .whats-new .chr-link--primary .chr-link__icon{fill:#a8c7fa}.dark-theme.whats-new .chr-link--primary .chr-link__icon{fill:#a8c7fa}.whats-new .chr-link--primary{color:#0b57d0}.dark-theme .whats-new .chr-link--primary{color:#a8c7fa}.dark-theme.whats-new .chr-link--primary{color:#a8c7fa}.chr-link--inverted{color:#fff}.dark-theme .chr-link--inverted{color:#fff}.chr-link--inverted .chr-link__icon{fill:#fff}.dark-theme .chr-link--inverted .chr-link__icon{fill:#fff}.chr-link--on-black{color:#8ab4f8}.dark-theme .chr-link--on-black{color:#8ab4f8}.chr-link--on-black:focus{outline:unset}.chr-link--on-black:focus-visible{outline:2px auto #1967d2;outline-offset:-2px}.dark-theme .chr-link--on-black:focus-visible{outline:2px auto #1967d2}.chr-link--on-black .chr-link__icon{fill:#8ab4f8}.dark-theme .chr-link--on-black .chr-link__icon{fill:#8ab4f8}.chr-link--inline,.chr-download-button--inline{color:#1967d2;font-family:inherit;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;padding:0}.dark-theme .chr-link--inline,.dark-theme .chr-download-button--inline{color:#1967d2}.chr-link--inline:hover,.chr-download-button--inline:hover{-webkit-text-decoration-color:#1967d2;text-decoration-color:#1967d2;text-decoration:underline}.dark-theme .chr-link--inline:hover,.dark-theme .chr-download-button--inline:hover{-webkit-text-decoration-color:#1967d2;text-decoration-color:#1967d2}.chr-link--inline:focus,.chr-download-button--inline:focus{outline:unset}.chr-link--inline:focus-visible,.chr-download-button--inline:focus-visible{-webkit-text-decoration-color:#1967d2;text-decoration-color:#1967d2;outline:2px auto #1967d2;outline-offset:-2px;text-decoration:underline}.dark-theme .chr-link--inline:focus-visible,.dark-theme .chr-download-button--inline:focus-visible{-webkit-text-decoration-color:#1967d2;text-decoration-color:#1967d2}.dark-theme .chr-link--inline:focus-visible,.dark-theme .chr-download-button--inline:focus-visible{outline:2px auto #1967d2}.chr-link--scroll-down{-ms-flex-line-pack:center;align-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}.chr-link--scroll-down .chr-link__icon{margin-left:0;margin-top:var(--spacer-01)}.chr-link--button,.chr-link--button-secondary,.chr-link--button-primary,.chr-link--button-wn{border-radius:24px;padding-inline:24px}.chr-link--button-wn{background-color:#d3e3fd;color:#041e49;display:block;margin:0 auto 80px;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.chr-link--button-wn .chr-link__icon{-webkit-transform:translateX(4px) rotate(45deg);transform:translateX(4px) rotate(45deg)}[dir=rtl] .chr-link--button-wn .chr-link__icon{-webkit-transform:translateX(-4px) rotate(-135deg);transform:translateX(-4px) rotate(-135deg)}.chr-link--button-wn:hover .chr-link__icon,.chr-link--button-wn:focus .chr-link__icon{-webkit-transform:translateX(8px) rotate(45deg);transform:translateX(8px) rotate(45deg);-webkit-transition:200ms linear;transition:200ms linear}[dir=rtl] .chr-link--button-wn:hover .chr-link__icon,[dir=rtl] .chr-link--button-wn:focus .chr-link__icon{-webkit-transform:translateX(-8px) rotate(-135deg);transform:translateX(-8px) rotate(-135deg)}.chr-link--underline{text-decoration:underline}.chr-link--underline .chr-link-icon{text-decoration:underline}.chr-link--button-primary{color:#fff}.dark-theme .chr-link--button-primary{color:#fff}.chr-link--bold{font-weight:700}*,*::before,*::after{-webkit-box-sizing:border-box;box-sizing:border-box}html:has(>body.whats-new.dark-theme){scrollbar-color:#80868b #3c4043}a{text-decoration:none}div:focus,a:focus,input:focus,select:focus{outline:2px auto #1967d2}.dark-theme div:focus,.dark-theme a:focus,.dark-theme input:focus,.dark-theme select:focus{outline:2px auto #1967d2}body{background-color:#fff;font-family:"Google Sans",arial,sans-serif;margin:0;overflow-x:hidden;padding:0}.dark-theme body{background-color:#fff}body.is-showing-drawer{overflow-y:hidden}body.whats-new{background-color:#f8fafd}.dark-theme body.whats-new{background-color:#28292a}body.whats-new.dark-theme{background-color:#28292a}button{background:none;border:none}button,input,optgroup,select,textarea{font-family:inherit}h1,h2,h3,h4,h5,h6,p{margin:0}li{list-style:none}ul,li{margin:0;padding:0}template{display:none}.chr-main{opacity:0;-webkit-transition:opacity .5s ease-in-out .15s;transition:opacity .5s ease-in-out .15s}.svg-assets{display:none}.svg .svg-fallback{display:none}.default-fallback{display:none}.hide>img{display:none}.content-contain{overflow:hidden}h1,h2,h3,h4,h5,h6,p{margin:0}.headline,.chr-headline-6,.chr-headline-5,.chr-headline-4,.chr-headline-3,.chr-headline-2,.chr-headline-1,.chr-headline-0{color:#202124;font-family:"Google Sans",arial,sans-serif;font-weight:400}.dark-theme .headline,.dark-theme .chr-headline-6,.dark-theme .chr-headline-5,.dark-theme .chr-headline-4,.dark-theme .chr-headline-3,.dark-theme .chr-headline-2,.dark-theme .chr-headline-1,.dark-theme .chr-headline-0{color:#202124}.whats-new .headline,.whats-new .chr-headline-6,.whats-new .chr-headline-5,.whats-new .chr-headline-4,.whats-new .chr-headline-3,.whats-new .chr-headline-2,.whats-new .chr-headline-1,.whats-new .chr-headline-0{color:#1f1f1f}.dark-theme .whats-new .headline,.dark-theme .whats-new .chr-headline-6,.dark-theme .whats-new .chr-headline-5,.dark-theme .whats-new .chr-headline-4,.dark-theme .whats-new .chr-headline-3,.dark-theme .whats-new .chr-headline-2,.dark-theme .whats-new .chr-headline-1,.dark-theme .whats-new .chr-headline-0{color:#e3e3e3}.whats-new.dark-theme .headline,.whats-new.dark-theme .chr-headline-6,.whats-new.dark-theme .chr-headline-5,.whats-new.dark-theme .chr-headline-4,.whats-new.dark-theme .chr-headline-3,.whats-new.dark-theme .chr-headline-2,.whats-new.dark-theme .chr-headline-1,.whats-new.dark-theme .chr-headline-0{color:#e3e3e3}.headline.animation,.animation.chr-headline-6,.animation.chr-headline-5,.animation.chr-headline-4,.animation.chr-headline-3,.animation.chr-headline-2,.animation.chr-headline-1,.animation.chr-headline-0{opacity:0}.headline.animation.animated,.animation.animated.chr-headline-6,.animation.animated.chr-headline-5,.animation.animated.chr-headline-4,.animation.animated.chr-headline-3,.animation.animated.chr-headline-2,.animation.animated.chr-headline-1,.animation.animated.chr-headline-0{-webkit-animation:var(--animation-name) var(--animation-duration) var(--animation-timing);animation:var(--animation-name) var(--animation-duration) var(--animation-timing);-webkit-animation-fill-mode:var(--animation-fill-mode);animation-fill-mode:var(--animation-fill-mode)}.chr-headline-0{font-size:3rem;line-height:3.25rem;letter-spacing:-0.125rem;font-family:"Google Sans",arial,sans-serif;font-weight:700}@media only screen and (min-width: 600px){.chr-headline-0{font-size:4rem;line-height:4.5rem;letter-spacing:-0.15625rem}}@media only screen and (min-width: 1024px){.chr-headline-0{font-size:4.5rem;line-height:5.25rem;letter-spacing:-0.21875rem}}.chr-headline-1{font-size:2.5rem;line-height:3rem;letter-spacing:-0.09375rem;font-family:"Google Sans",arial,sans-serif;font-weight:700}@media only screen and (min-width: 600px){.chr-headline-1{font-size:3rem;line-height:3.5rem;letter-spacing:-0.078125rem}}@media only screen and (min-width: 1024px){.chr-headline-1{font-size:3.75rem;line-height:4.5rem;letter-spacing:-0.078125rem}}.chr-headline-2{font-size:2.25rem;line-height:2.75rem;letter-spacing:-0.046875rem;font-family:"Google Sans",arial,sans-serif;font-weight:700}@media only screen and (min-width: 600px){.chr-headline-2{font-size:2.5rem;line-height:3rem;letter-spacing:-0.046875rem}}@media only screen and (min-width: 1024px){.chr-headline-2{font-size:3rem;line-height:3.5rem;letter-spacing:-0.0625rem}}.chr-headline-3{font-size:1.75rem;line-height:2.25rem;letter-spacing:-0.03125rem;font-family:"Google Sans",arial,sans-serif;font-weight:700}@media only screen and (min-width: 600px){.chr-headline-3{font-size:2rem;line-height:2.5rem;letter-spacing:-0.03125rem}}@media only screen and (min-width: 1024px){.chr-headline-3{font-size:2.25rem;line-height:2.75rem;letter-spacing:-0.046875rem}}.chr-headline-4{font-size:1.5rem;line-height:2rem;letter-spacing:-0.03125rem;font-family:"Google Sans",arial,sans-serif;font-weight:700}@media only screen and (min-width: 600px){.chr-headline-4{font-size:1.5rem;line-height:2rem;letter-spacing:-0.03125rem}}@media only screen and (min-width: 1024px){.chr-headline-4{font-size:1.75rem;line-height:2.25rem;letter-spacing:-0.03125rem}}.chr-headline-5{font-size:1.25rem;line-height:1.75rem;letter-spacing:-0.015625rem;font-family:"Google Sans",arial,sans-serif;font-weight:700}@media only screen and (min-width: 600px){.chr-headline-5{font-size:1.25rem;line-height:1.75rem;letter-spacing:-0.015625rem}}@media only screen and (min-width: 1024px){.chr-headline-5{font-size:1.25rem;line-height:1.75rem;letter-spacing:-0.015625rem}}.chr-headline-6{font-size:1rem;line-height:1.5rem;letter-spacing:-0.00625rem;font-family:"Google Sans",arial,sans-serif;font-weight:500}@media only screen and (min-width: 600px){.chr-headline-6{font-size:1rem;line-height:1.5rem;letter-spacing:-0.00625rem}}@media only screen and (min-width: 1024px){.chr-headline-6{font-size:1rem;line-height:1.5rem;letter-spacing:-0.00625rem}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-headline-0--sm{font-size:3rem;line-height:3.25rem;letter-spacing:-0.125rem;font-weight:700}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-headline-0--md{font-size:4rem;line-height:4.5rem;letter-spacing:-0.15625rem;font-weight:700}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-headline-0--lg{font-size:4.5rem;line-height:5.25rem;letter-spacing:-0.21875rem;font-weight:700}}@media only screen and (min-width: 1439px){.chr-responsive-headline-0--xl{font-size:4.5rem;line-height:5.25rem;letter-spacing:-0.21875rem;font-weight:700}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-headline-1--sm{font-size:2.5rem;line-height:3rem;letter-spacing:-0.09375rem;font-weight:700}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-headline-1--md{font-size:3rem;line-height:3.5rem;letter-spacing:-0.078125rem;font-weight:700}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-headline-1--lg{font-size:3.75rem;line-height:4.5rem;letter-spacing:-0.078125rem;font-weight:700}}@media only screen and (min-width: 1439px){.chr-responsive-headline-1--xl{font-size:3.75rem;line-height:4.5rem;letter-spacing:-0.078125rem;font-weight:700}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-headline-2--sm{font-size:2.25rem;line-height:2.75rem;letter-spacing:-0.046875rem;font-weight:700}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-headline-2--md{font-size:2.5rem;line-height:3rem;letter-spacing:-0.046875rem;font-weight:700}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-headline-2--lg{font-size:3rem;line-height:3.5rem;letter-spacing:-0.0625rem;font-weight:700}}@media only screen and (min-width: 1439px){.chr-responsive-headline-2--xl{font-size:3rem;line-height:3.5rem;letter-spacing:-0.0625rem;font-weight:700}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-headline-3--sm{font-size:1.75rem;line-height:2.25rem;letter-spacing:-0.03125rem;font-weight:700}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-headline-3--md{font-size:2rem;line-height:2.5rem;letter-spacing:-0.03125rem;font-weight:700}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-headline-3--lg{font-size:2.25rem;line-height:2.75rem;letter-spacing:-0.046875rem;font-weight:700}}@media only screen and (min-width: 1439px){.chr-responsive-headline-3--xl{font-size:2.25rem;line-height:2.75rem;letter-spacing:-0.046875rem;font-weight:700}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-headline-4--sm{font-size:1.5rem;line-height:2rem;letter-spacing:-0.03125rem;font-weight:700}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-headline-4--md{font-size:1.5rem;line-height:2rem;letter-spacing:-0.03125rem;font-weight:700}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-headline-4--lg{font-size:1.75rem;line-height:2.25rem;letter-spacing:-0.03125rem;font-weight:700}}@media only screen and (min-width: 1439px){.chr-responsive-headline-4--xl{font-size:1.75rem;line-height:2.25rem;letter-spacing:-0.03125rem;font-weight:700}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-headline-5--sm{font-size:1.25rem;line-height:1.75rem;letter-spacing:-0.015625rem;font-weight:700}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-headline-5--md{font-size:1.25rem;line-height:1.75rem;letter-spacing:-0.015625rem;font-weight:700}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-headline-5--lg{font-size:1.25rem;line-height:1.75rem;letter-spacing:-0.015625rem;font-weight:700}}@media only screen and (min-width: 1439px){.chr-responsive-headline-5--xl{font-size:1.25rem;line-height:1.75rem;letter-spacing:-0.015625rem;font-weight:700}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-headline-6--sm{font-size:1rem;line-height:1.5rem;letter-spacing:-0.00625rem;font-weight:500}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-headline-6--md{font-size:1rem;line-height:1.5rem;letter-spacing:-0.00625rem;font-weight:500}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-headline-6--lg{font-size:1rem;line-height:1.5rem;letter-spacing:-0.00625rem;font-weight:500}}@media only screen and (min-width: 1439px){.chr-responsive-headline-6--xl{font-size:1rem;line-height:1.5rem;letter-spacing:-0.00625rem;font-weight:500}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-link-small--sm{font-size:0.75rem;line-height:1.25rem;letter-spacing:0.009375rem;font-weight:500}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-link-small--md{font-size:0.75rem;line-height:1.25rem;letter-spacing:0.009375rem;font-weight:500}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-link-small--lg{font-size:0.75rem;line-height:1.25rem;letter-spacing:0.009375rem;font-weight:500}}@media only screen and (min-width: 1439px){.chr-responsive-link-small--xl{font-size:0.75rem;line-height:1.25rem;letter-spacing:0.009375rem;font-weight:500}}@media only screen and (min-width: 0)and (max-width: 599px){.chr-responsive-link-large--sm{font-size:1.125rem;line-height:1.5rem;letter-spacing:0rem;font-weight:500}}@media only screen and (min-width: 600px)and (max-width: 1023px){.chr-responsive-link-large--md{font-size:1.125rem;line-height:1.5rem;letter-spacing:0rem;font-weight:500}}@media only screen and (min-width: 1024px)and (max-width: 1439px){.chr-responsive-link-large--lg{font-size:1.125rem;line-height:1.5rem;letter-spacing:0rem;font-weight:500}}@media only screen and (min-width: 1439px){.chr-responsive-link-large--xl{font-size:1.125rem;line-height:1.5rem;letter-spacing:0rem;font-weight:500}}.chr-copy{font-size:1rem;line-height:1.5rem;letter-spacing:0rem;color:#5f6368;font-family:"Google Sans Text",arial,sans-serif;font-weight:400}.dark-theme .chr-copy{color:#5f6368}.whats-new .chr-copy{color:#474747}.dark-theme .whats-new .chr-copy{color:#c7c7c7}.whats-new.dark-theme .chr-copy{color:#c7c7c7}.chr-copy-xl{font-size:1.125rem;line-height:1.75rem;letter-spacing:0rem;color:#5f6368;font-family:"Google Sans Text",arial,sans-serif}.dark-theme .chr-copy-xl{color:#5f6368}.chr-link-small{font-size:0.75rem;line-height:1.25rem;letter-spacing:0.009375rem;color:#1967d2;font-family:"Google Sans",arial,sans-serif;font-weight:500}.dark-theme .chr-link-small{color:#1967d2}.chr-link-large{font-size:1.125rem;line-height:1.5rem;letter-spacing:0rem;color:#1967d2;font-family:"Google Sans",arial,sans-serif;font-weight:500}.dark-theme .chr-link-large{color:#1967d2}.chr-cta,.chr-download-button--header,.chr-download-button--jumplink{font-size:1rem;line-height:1.5rem;letter-spacing:0rem;font-family:"Google Sans Text",arial,sans-serif;font-weight:500}.chr-cta-small{font-size:0.75rem;line-height:1.25rem;letter-spacing:0.009375rem;font-family:"Google Sans Text",arial,sans-serif;font-weight:500}.chr-cta-large,.chr-download-button--hero,.chr-download-button--mobile-drawer{font-size:1.125rem;line-height:1.5rem;letter-spacing:0rem;font-family:"Google Sans",arial,sans-serif;font-weight:500}.chr-eyebrow{font-size:0.875rem;line-height:1.5rem;letter-spacing:0.03125rem;color:#202124;font-family:"Google Sans",arial,sans-serif;font-weight:500;text-transform:uppercase}.dark-theme .chr-eyebrow{color:#202124}.chr-eyebrow-xl{font-size:1.125rem;line-height:1.75rem;letter-spacing:0.015625rem;color:#202124;font-family:"Google Sans",arial,sans-serif;font-weight:700;text-transform:uppercase}.dark-theme .chr-eyebrow-xl{color:#202124}.chr-product{font-size:1.375rem;line-height:1.75rem;letter-spacing:0rem;color:#202124;font-family:"Product Sans",arial,sans-serif;font-weight:400}.dark-theme .chr-product{color:#202124}.chr-caption{font-size:0.75rem;line-height:1.125rem;letter-spacing:0.009375rem;color:#5f6368;font-family:"Google Sans Text",arial,sans-serif;font-weight:400}.dark-theme .chr-caption{color:#5f6368}.chr-pill-xl{font-size:4.5rem;line-height:5.25rem;letter-spacing:-0.09375rem;color:#202124;font-family:"Google Sans",arial,sans-serif;font-weight:500}.dark-theme .chr-pill-xl{color:#202124}.chr-pill-lg{font-size:4rem;line-height:4.5rem;letter-spacing:-0.078125rem;color:#202124;font-family:"Google Sans",arial,sans-serif;font-weight:500}.dark-theme .chr-pill-lg{color:#202124}.chr-pill{font-size:3.25rem;line-height:4.5rem;letter-spacing:-0.078125rem;color:#202124;font-family:"Google Sans",arial,sans-serif;font-weight:500}.dark-theme .chr-pill{color:#202124}.chr-pill-md{font-size:2.5rem;line-height:3rem;letter-spacing:-0.078125rem;color:#202124;font-family:"Google Sans",arial,sans-serif;font-weight:500;text-align:center}.dark-theme .chr-pill-md{color:#202124}.chr-pill-sm{font-size:2rem;line-height:2.5rem;letter-spacing:-0.046875rem;color:#202124;font-family:"Google Sans",arial,sans-serif;font-weight:500;text-align:center}.dark-theme .chr-pill-sm{color:#202124}.chr-text-statement{font-size:1.5rem;line-height:2.5rem;font-family:"Google Sans",arial,sans-serif;font-weight:400;text-align:center}@media only screen and (min-width: 600px){.chr-text-statement{font-size:1.875rem;line-height:3.25rem}}@media only screen and (min-width: 1024px){.chr-text-statement{font-size:2.5rem;line-height:4rem}}.chr-heading-pills{font-size:2.25rem;line-height:2.75rem;letter-spacing:-0.046875rem;font-family:"Google Sans",arial,sans-serif;font-weight:700;text-align:center}@media only screen and (min-width: 600px){.chr-heading-pills{font-size:3rem;line-height:3.5rem;letter-spacing:-0.0625rem}}@media only screen and (min-width: 1024px){.chr-heading-pills{font-size:3.75rem;line-height:4.5rem;letter-spacing:-0.078125rem}}.nowrap{white-space:nowrap}.chr-copy-nowrap{text-wrap:nowrap}@media only screen and (min-width: 0)and (max-width: 600px){.chr-copy-nowrap--sm{text-wrap:nowrap}}@media only screen and (min-width: 601px)and (max-width: 1024px){.chr-copy-nowrap--md{text-wrap:nowrap}}@media only screen and (min-width: 1025px)and (max-width: 1440px){.chr-copy-nowrap--lg{text-wrap:nowrap}}@media only screen and (min-width: 1441px){.chr-copy-nowrap--xl{text-wrap:nowrap}}:root{--spacer-01: 8px;--spacer-02: 16px;--spacer-03: 24px;--spacer-04: 32px;--spacer-05: 24px;--spacer-06: 40px;--spacer-07: 64px;--spacer-08: 80px;--spacer-09: 120px}@media only screen and (min-width: 1024px){:root{--spacer-05: 40px;--spacer-06: 64px;--spacer-07: 80px;--spacer-08: 120px;--spacer-09: 160px}}.spacer-01{padding:var(--spacer-01) 0}.spacer-01--top{padding-top:var(--spacer-01)}.spacer-01--bottom{padding-bottom:var(--spacer-01)}.spacer-01-margin{margin:var(--spacer-01) 0}.spacer-01-margin--top{margin-top:var(--spacer-01)}.spacer-01-margin--bottom{margin-bottom:var(--spacer-01)}.spacer-02{padding:var(--spacer-02) 0}.spacer-02--top{padding-top:var(--spacer-02)}.spacer-02--bottom{padding-bottom:var(--spacer-02)}.spacer-02-margin{margin:var(--spacer-02) 0}.spacer-02-margin--top{margin-top:var(--spacer-02)}.spacer-02-margin--bottom{margin-bottom:var(--spacer-02)}.spacer-03{padding:var(--spacer-03) 0}.spacer-03--top{padding-top:var(--spacer-03)}.spacer-03--bottom{padding-bottom:var(--spacer-03)}.spacer-03-margin{margin:var(--spacer-03) 0}.spacer-03-margin--top{margin-top:var(--spacer-03)}.spacer-03-margin--bottom{margin-bottom:var(--spacer-03)}.spacer-04{padding:var(--spacer-04) 0}.spacer-04--top{padding-top:var(--spacer-04)}.spacer-04--bottom{padding-bottom:var(--spacer-04)}.spacer-04-margin{margin:var(--spacer-04) 0}.spacer-04-margin--top{margin-top:var(--spacer-04)}.spacer-04-margin--bottom{margin-bottom:var(--spacer-04)}.spacer-05{padding:var(--spacer-05) 0}.spacer-05--top{padding-top:var(--spacer-05)}.spacer-05--bottom{padding-bottom:var(--spacer-05)}.spacer-05-margin{margin:var(--spacer-05) 0}.spacer-05-margin--top{margin-top:var(--spacer-05)}.spacer-05-margin--bottom{margin-bottom:var(--spacer-05)}.spacer-06{padding:var(--spacer-06) 0}.spacer-06--top{padding-top:var(--spacer-06)}.spacer-06--bottom{padding-bottom:var(--spacer-06)}.spacer-06-margin{margin:var(--spacer-06) 0}.spacer-06-margin--top{margin-top:var(--spacer-06)}.spacer-06-margin--bottom{margin-bottom:var(--spacer-06)}.spacer-07{padding:var(--spacer-07) 0}.spacer-07--top{padding-top:var(--spacer-07)}.spacer-07--bottom{padding-bottom:var(--spacer-07)}.spacer-07-margin{margin:var(--spacer-07) 0}.spacer-07-margin--top{margin-top:var(--spacer-07)}.spacer-07-margin--bottom{margin-bottom:var(--spacer-07)}.spacer-08{padding:var(--spacer-08) 0}.spacer-08--top{padding-top:var(--spacer-08)}.spacer-08--bottom{padding-bottom:var(--spacer-08)}.spacer-08-margin{margin:var(--spacer-08) 0}.spacer-08-margin--top{margin-top:var(--spacer-08)}.spacer-08-margin--bottom{margin-bottom:var(--spacer-08)}.spacer-09{padding:var(--spacer-09) 0}.spacer-09--top{padding-top:var(--spacer-09)}.spacer-09--bottom{padding-bottom:var(--spacer-09)}.spacer-09-margin{margin:var(--spacer-09) 0}.spacer-09-margin--top{margin-top:var(--spacer-09)}.spacer-09-margin--bottom{margin-bottom:var(--spacer-09)}.shadow-ui{-webkit-box-shadow:0 4px 15px 0 rgba(32,33,36,.05);box-shadow:0 4px 15px 0 rgba(32,33,36,.05)}@media only screen and (min-width: 1024px){.shadow-ui{-webkit-box-shadow:0 8px 20px 0 rgba(32,33,36,.1);box-shadow:0 8px 20px 0 rgba(32,33,36,.1)}}.shadow-elevation-1{-webkit-box-shadow:0 2px 8px 0 rgba(32,33,36,.08);box-shadow:0 2px 8px 0 rgba(32,33,36,.08)}.shadow-elevation-2{-webkit-box-shadow:0 1px 2px rgba(32,33,36,.15),0 1px 8px rgba(32,33,36,.08);box-shadow:0 1px 2px rgba(32,33,36,.15),0 1px 8px rgba(32,33,36,.08)}.chr-card-adaptive{border-radius:1.5rem;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;overflow:hidden;position:relative;width:100%}.chr-card-adaptive__decoration{bottom:-0.375rem;position:absolute;width:100%}.chr-card-adaptive__background{height:100%;left:0;position:absolute;top:0;width:100%;z-index:-1}.chr-card-adaptive__content-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;width:100%}.chr-card-adaptive__badge{margin-bottom:var(--spacer-01)}.chr-card-adaptive__heading{margin-top:1rem;text-wrap:balance}.chr-card-adaptive__body{margin-top:1rem}@media only screen and (min-width: 1024px){.chr-card-adaptive__body{font-size:1.125rem;line-height:1.75rem;letter-spacing:0rem}}.chr-card-adaptive__link{margin-top:0.5rem}@media only screen and (min-width: 1024px){.chr-card-adaptive__link{font-size:1.125rem;line-height:1.5rem;max-width:unset}}.chr-card-adaptive__link.spacer-fix{margin-top:2rem}.chr-card-adaptive__link:focus{outline:unset}.chr-card-adaptive__link:focus-visible{outline:2px auto #1967d2}.dark-theme .chr-card-adaptive__link:focus-visible{outline:2px auto #1967d2}.chr-card-adaptive__no-aspect-ratio{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;min-height:240px}.chr-card-adaptive--border{border:1px solid #dadce0}.dark-theme .chr-card-adaptive--border{border:1px solid #dadce0}@media only screen and (max-width: 1023px){.chr-card-adaptive--border-md{border:1px solid #dadce0}.dark-theme .chr-card-adaptive--border-md{border:1px solid #dadce0}}@media only screen and (min-width: 1024px){.chr-card-adaptive--border-lg{border:1px solid #dadce0}.dark-theme .chr-card-adaptive--border-lg{border:1px solid #dadce0}}@media only screen and (min-width: 1440px){.chr-card-adaptive--border-lg{border:1px solid #dadce0}.dark-theme .chr-card-adaptive--border-lg{border:1px solid #dadce0}}.chr-card-adaptive--border-top-radius{border-top-left-radius:1.5rem;border-top-right-radius:1.5rem}.chr-card-adaptive--border-bottom-radius{border-bottom-left-radius:1.5rem;border-bottom-right-radius:1.5rem}.chr-card-adaptive__media{display:-webkit-box;display:-ms-flexbox;display:flex;overflow:hidden;position:relative;width:100%}.chr-card-adaptive__lottie,.chr-card-adaptive__video{height:100%;position:absolute;width:100%}.chr-card-adaptive--scale .chr-card-adaptive__image,.chr-card-adaptive--scale .chr-card-adaptive__image-2,.chr-card-adaptive--scale .chr-card-adaptive__lottie,.chr-card-adaptive--scale .chr-card-adaptive__video{-webkit-transform:scale(var(--scale_start));transform:scale(var(--scale_start));-webkit-transition:-webkit-transform .3s ease-out;transition:-webkit-transform .3s ease-out;transition:transform .3s ease-out;transition:transform .3s ease-out, -webkit-transform .3s ease-out}.chr-card-adaptive--scale:hover .chr-card-adaptive__image,.chr-card-adaptive--scale:hover .chr-card-adaptive__image-2,.chr-card-adaptive--scale:hover .chr-card-adaptive__lottie,.chr-card-adaptive--scale:hover .chr-card-adaptive__video,.chr-card-adaptive--scale:focus .chr-card-adaptive__image,.chr-card-adaptive--scale:focus .chr-card-adaptive__image-2,.chr-card-adaptive--scale:focus .chr-card-adaptive__lottie,.chr-card-adaptive--scale:focus .chr-card-adaptive__video{-webkit-transform:scale(var(--scale_end));transform:scale(var(--scale_end))}.chr-card-adaptive--padding-sm .chr-card-adaptive__content-wrapper{padding:2rem}.chr-card-adaptive--padding-md .chr-card-adaptive__content-wrapper{padding:var(--spacer-05) 2rem}@media only screen and (min-width: 1024px){.chr-card-adaptive--padding-md .chr-card-adaptive__content-wrapper{padding:var(--spacer-05) var(--spacer-06)}}.chr-card-adaptive--media-cover .chr-card-adaptive__media img,.chr-card-adaptive--media-cover .chr-card-adaptive__media video{height:100%;-o-object-fit:cover;object-fit:cover;position:absolute;width:100%}.chr-card-adaptive--media-contain .chr-card-adaptive__media img,.chr-card-adaptive--media-contain .chr-card-adaptive__media video{-o-object-fit:contain;object-fit:contain;width:100%}@media only screen and (min-width: 1440px){.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media img,.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media .chr-steps-image-all{width:100%}}.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media img,.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media .chr-steps-image-chrome{height:100%;width:unset}@media only screen and (min-width: 600px){.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media img,.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media .chr-steps-image-chrome{height:unset}}.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media img,.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media .chr-steps-image-safari,.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media .chr-steps-image-firefox,.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media .chr-emphasis--firefox,.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media .chr-emphasis--safari{height:unset;width:unset}.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media img,.ty-experiment .chr-card-adaptive--media-contain .chr-card-adaptive__media .chr-steps-image-edge{-o-object-fit:cover;object-fit:cover;width:100%}@media only screen and (min-width: 1024px){.chr-card-adaptive--media-contained .chr-card-adaptive__media{padding-top:var(--spacer-06)}.chr-card-adaptive--media-contained .chr-card-adaptive__media img,.chr-card-adaptive--media-contained .chr-card-adaptive__media video,.chr-card-adaptive--media-contained .chr-card-adaptive__media .chr-video-player-youtube{height:-webkit-min-content;height:-moz-min-content;height:min-content;max-width:calc((min(100vw, 1440px) - 144px - 528px) / 12 * 10 + 432px)}.chr-card-adaptive--media-contained .chr-card-adaptive__media .chr-video-v2__holder{-ms-flex-line-pack:center;align-content:center;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;width:100%}.chr-card-adaptive--media-contained .chr-card-adaptive__media video{border-radius:20px;height:100%;-o-object-fit:cover;object-fit:cover;position:unset;width:100%}}@media only screen and (min-width: 1440px){.chr-card-adaptive--media-contained .chr-card-adaptive__media img,.chr-card-adaptive--media-contained .chr-card-adaptive__media video,.chr-card-adaptive--media-contained .chr-card-adaptive__media .chr-video-player-youtube{max-width:calc((min(100vw, 1440px) - 148px - 704px) / 12 * 10 + 576px)}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape .chr-card-adaptive__content-wrapper{-webkit-column-gap:var(--spacer-05);-moz-column-gap:var(--spacer-05);column-gap:var(--spacer-05);display:-ms-grid;display:grid;-ms-grid-columns:var(--text-wrap-width) 1fr;grid-template-columns:var(--text-wrap-width) 1fr;-ms-grid-rows:auto 1fr;grid-template-rows:auto 1fr;padding:var(--spacer-05) var(--spacer-06) var(--spacer-05) var(--spacer-06);row-gap:var(--spacer-02)}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape .chr-card-adaptive__heading{grid-column:span 1;-ms-grid-row:2;grid-row:2;margin-top:0}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape .chr-card-adaptive__body-wrapper{-ms-grid-column:2;grid-column:2;-ms-grid-row:2;grid-row:2;max-width:560px;place-self:start}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape .chr-card-adaptive__body{margin-top:unset}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape .chr-card-adaptive__media{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;min-height:328px}}@media only screen and (min-width: 1440px){.chr-card-adaptive--landscape .chr-card-adaptive__media{min-height:340px}}.chr-card-adaptive--landscape-padding .chr-card-adaptive__content-wrapper{padding:var(--spacer-03) var(--spacer-04) 2.5rem var(--spacer-04)}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-padding .chr-card-adaptive__content-wrapper{-webkit-column-gap:3rem;-moz-column-gap:3rem;column-gap:3rem;display:-ms-grid;display:grid;-ms-grid-columns:var(--text-wrap-width) 1fr;grid-template-columns:var(--text-wrap-width) 1fr;-ms-grid-rows:auto 1fr;grid-template-rows:auto 1fr;padding:var(--spacer-03) var(--spacer-06) var(--spacer-06) var(--spacer-06);row-gap:var(--spacer-02)}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-padding .chr-card-adaptive__heading{grid-column:span 1;-ms-grid-row:2;grid-row:2;margin-top:0}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-padding .chr-card-adaptive__body-wrapper{-ms-grid-column:2;grid-column:2;-ms-grid-row:2;grid-row:2;max-width:560px;place-self:start}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-padding .chr-card-adaptive__body{margin-top:unset}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-padding .chr-card-adaptive__media{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;min-height:328px}}@media only screen and (min-width: 1440px){.chr-card-adaptive--landscape-padding .chr-card-adaptive__media{min-height:340px}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-flex .chr-card-adaptive__content-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-flow:row nowrap;flex-flow:row nowrap;gap:2.5rem;padding:var(--spacer-05) var(--spacer-06) var(--spacer-05) var(--spacer-06)}}.chr-card-adaptive--landscape-flex .chr-card-adaptive__upper-wrapper{-webkit-box-flex:1;-ms-flex:1;flex:1}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-flex .chr-card-adaptive__upper-wrapper{-ms-flex-preferred-size:var(--text-wrap-width);flex-basis:var(--text-wrap-width);max-width:var(--text-wrap-width)}}.chr-card-adaptive--landscape-flex .chr-card-adaptive__body-wrapper{-webkit-box-flex:2;-ms-flex:2;flex:2}@media only screen and (min-width: 1440px){.chr-card-adaptive--landscape-flex .chr-card-adaptive__body-wrapper{max-width:480px}}@media only screen and (max-width: 1023px){.chr-card-adaptive--landscape-flex .chr-card-adaptive__badge{background:linear-gradient(111.39deg, #a6b1fa 12.6%, rgba(111, 171, 249, 0.66) 68%);border:unset}.dark-theme .chr-card-adaptive--landscape-flex .chr-card-adaptive__badge{background:linear-gradient(111.39deg, #a6b1fa 12.6%, rgba(111, 171, 249, 0.66) 68%)}}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-flex .chr-card-adaptive__heading{margin-top:0}}.chr-card-adaptive--landscape-flex .chr-card-adaptive__body{font-size:1rem;line-height:1.5rem}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-flex .chr-card-adaptive__body{margin-top:unset}}.chr-card-adaptive--landscape-flex .chr-card-adaptive__link{font-size:1rem;line-height:1.5rem}@media only screen and (min-width: 1024px){.chr-card-adaptive--landscape-flex .chr-card-adaptive__media{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;min-height:328px}}@media only screen and (min-width: 1440px){.chr-card-adaptive--landscape-flex .chr-card-adaptive__media{min-height:340px}}.chr-card-adaptive--standard .chr-card-adaptive__body-wrapper{-ms-flex-line-pack:start;align-content:flex-start;-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}.chr-card-adaptive--vertical-qr .chr-card-adaptive__content-wrapper{padding:var(--spacer-05) 2rem 0 2rem}@media only screen and (min-width: 1024px){.chr-card-adaptive--vertical,.chr-card-adaptive--vertical-qr{-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row}}@media only screen and (min-width: 1024px){.chr-card-adaptive--vertical .chr-card-adaptive__content-wrapper,.chr-card-adaptive--vertical-qr .chr-card-adaptive__content-wrapper{max-width:var(--text-wrap-width);padding:var(--spacer-05) var(--spacer-06) 0 var(--spacer-06)}}.chr-card-adaptive--vertical .chr-card-adaptive__body-wrapper,.chr-card-adaptive--vertical-qr .chr-card-adaptive__body-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1}@media only screen and (min-width: 1024px){.chr-card-adaptive--vertical .chr-card-adaptive__body-wrapper,.chr-card-adaptive--vertical-qr .chr-card-adaptive__body-wrapper{margin-bottom:2.25rem}}@media only screen and (min-width: 1024px){.chr-card-adaptive--vertical-padding{-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row}}@media only screen and (min-width: 1024px){.chr-card-adaptive--vertical-padding .chr-card-adaptive__content-wrapper{max-width:var(--text-wrap-width);padding:var(--spacer-05) var(--spacer-06) 0 var(--spacer-06)}}.chr-card-adaptive--vertical-padding .chr-card-adaptive__body-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1}@media only screen and (min-width: 1024px){.chr-card-adaptive--vertical-padding .chr-card-adaptive__body-wrapper{margin-bottom:2.25rem}}.chr-card-adaptive--vertical-padding .chr-card-adaptive__body{font-size:1rem;line-height:1.5rem}.chr-card-adaptive--vertical-padding .chr-card-adaptive__link{font-size:1rem;line-height:1.5rem}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel{border:none;border-radius:unset;gap:var(--spacer-02)}}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel .chr-card-adaptive__content-wrapper{-webkit-box-ordinal-group:2;-ms-flex-order:1;order:1;padding:0}}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel .chr-card-adaptive__media{border-radius:var(--spacer-03)}}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel .chr-card-adaptive__background{background-color:unset}}.chr-card-adaptive--static-carousel .chr-card-adaptive__heading{margin-top:var(--spacer-01)}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel .chr-card-adaptive__heading{color:#202124}.dark-theme .chr-card-adaptive--static-carousel .chr-card-adaptive__heading{color:#202124}}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel .chr-card-adaptive__body{color:#5f6368}.dark-theme .chr-card-adaptive--static-carousel .chr-card-adaptive__body{color:#5f6368}}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel .chr-card-adaptive__link{color:#1967d2}.dark-theme .chr-card-adaptive--static-carousel .chr-card-adaptive__link{color:#1967d2}}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel .chr-card-adaptive__eyebrow{color:#5f6368}.dark-theme .chr-card-adaptive--static-carousel .chr-card-adaptive__eyebrow{color:#5f6368}}@media only screen and (max-width: 1023px){.chr-card-adaptive--static-carousel .chr-link__icon{fill:#1967d2}.dark-theme .chr-card-adaptive--static-carousel .chr-link__icon{fill:#1967d2}}.chr-card-adaptive--unset-min-height-media .chr-card-adaptive__media{min-height:unset}.chr-card-adaptive--overflow-visible{overflow:visible}.chr-card-adaptive--overflow-visible .chr-card-adaptive__background{border-radius:1.5rem}.chr-card-adaptive--overflow-visible.chr-card-adaptive--media-top .chr-card-adaptive__media{border-radius:1.5rem 1.5rem 0 0}.chr-card-adaptive--overflow-visible.chr-card-adaptive--media-bottom .chr-card-adaptive__media{border-radius:0 0 1.5rem 1.5rem}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper{margin:unset;text-align:unset}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-download-button{margin:var(--spacer-03) auto var(--spacer-01)}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-download-button--hero{font-size:1rem;line-height:1.5rem;letter-spacing:0rem;gap:var(--spacer-01);padding:0.75rem var(--spacer-03)}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-download-button--hero .chr-button__icon{height:1.125rem;width:1.125rem}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-simplified-download .chr-platform-list{text-align:unset}@media only screen and (min-width: 600px){.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-simplified-download .chr-platform-list{-webkit-transform:translateX(40px);transform:translateX(40px)}}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-simplified-download .chr-platform-list .os .platform{margin:unset}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-simplified-download .chr-platform-list .hero-chrome-update-cta{display:none}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-opt{margin:var(--spacer-01) auto var(--spacer-01)}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-download-button+.chr-simplified-download .chr-simplified-download__simplified-opt .chr-checkbox>.chr-checkbox__label.chr-caption{vertical-align:middle}.chr-card-adaptive--use-download-button .chr-card-adaptive__content-wrapper .chr-simplified-download-wrapper .chr-simplified-download__simplified-links{margin:var(--spacer-01) auto 0;text-align:unset}.chr-card-adaptive--media-shadow-thank-you-steps .chr-card-adaptive__media::after{-webkit-box-shadow:0 -4px 24px 0 rgba(0,0,0,.08) inset;box-shadow:0 -4px 24px 0 rgba(0,0,0,.08) inset;content:"";height:100%;left:0;pointer-events:none;position:absolute;top:0;width:100%}.chr-card-adaptive.animation{opacity:0}.chr-card-adaptive.animation.animated{-webkit-animation:var(--animation-name) var(--animation-duration) var(--animation-timing);animation:var(--animation-name) var(--animation-duration) var(--animation-timing);-webkit-animation-fill-mode:var(--animation-fill-mode);animation-fill-mode:var(--animation-fill-mode)}.chr-jumplinks-v2{display:none;left:0;pointer-events:none;position:fixed;top:0;-webkit-transition:-webkit-transform .4s ease-in;transition:-webkit-transform .4s ease-in;transition:transform .4s ease-in;transition:transform .4s ease-in, -webkit-transform .4s ease-in;width:100%;will-change:transform;z-index:50}.chr-jumplinks-v2--sticky{position:initial;top:84px;will-change:top}@media only screen and (min-width: 1024px){.chr-jumplinks-v2{display:block}}.chr-jumplinks-v2__list{background-color:#fff;border-radius:50px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;margin:0 auto;padding:8px;pointer-events:all;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.dark-theme .chr-jumplinks-v2__list{background-color:#fff}.chr-jumplinks-v2__list-item{-ms-flex-negative:0;flex-shrink:0;padding:0 1px}.chr-jumplinks-v2__download-button{margin-left:8px}.chr-jumplinks-v2-wrapper{display:none;visibility:hidden;position:relative}@media only screen and (min-width: 1024px){.chr-jumplinks-v2-wrapper{display:block;visibility:visible}}.chr-jumplinks-v2-wrapper__jumplinks{height:3.5rem;position:absolute;top:0;width:100vw}.chr-jumplinks-v2-wrapper__dispatch{height:3.5rem;width:100vw}.below-download .chr-jumplinks-v2:not(.chr-jumplinks-v2--sticky){-webkit-transform:translateY(24px);transform:translateY(24px);-webkit-transition:-webkit-transform .4s ease-in;transition:-webkit-transform .4s ease-in;transition:transform .4s ease-in;transition:transform .4s ease-in, -webkit-transform .4s ease-in}.below-download.scroll-up .chr-jumplinks-v2:not(.chr-jumplinks-v2--sticky){-webkit-transform:translateY(84px);transform:translateY(84px)}.below-download .chr-jumplinks-v2--sticky{position:fixed;top:24px;-webkit-transition:top .4s ease-in;transition:top .4s ease-in}.below-download.scroll-up .chr-jumplinks-v2--sticky{top:84px}.fix-jumplinks .chr-jumplinks-v2--sticky{position:fixed;top:84px}body::after{content:"phone";display:none}@media only screen and (min-width: 600px)and (max-width: 1023px){body::after{content:"tablet"}}@media only screen and (min-width: 1024px)and (max-width: 1439px){body::after{content:"desktop"}}@media only screen and (min-width: 1440px){body::after{content:"large-desktop"}}.chr-module-placeholder{height:100vh}.chr-accordion-timed__panel>img,.chr-accordion-timed__panel>video{display:block;height:1px;width:1px}.chr-jumplinks-mobile{--translate-show: 0;--translate-hide: 100%;background-color:#fff;display:-webkit-box;display:-ms-flexbox;display:flex;visibility:visible;-webkit-box-align:center;-ms-flex-align:center;align-items:center;bottom:0;-webkit-box-shadow:0 1px 2px rgba(32,33,36,.15),0 1px 8px rgba(32,33,36,.08);box-shadow:0 1px 2px rgba(32,33,36,.15),0 1px 8px rgba(32,33,36,.08);display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;padding:8px;position:fixed;-webkit-transform:translateY(var(--translate-show));transform:translateY(var(--translate-show));-webkit-transition:-webkit-transform .4s ease-in-out;transition:-webkit-transform .4s ease-in-out;transition:transform .4s ease-in-out;transition:transform .4s ease-in-out, -webkit-transform .4s ease-in-out;width:100vw;will-change:transform;z-index:50}.dark-theme .chr-jumplinks-mobile{background-color:#fff}@media only screen and (min-width: 1024px){.chr-jumplinks-mobile{display:none;visibility:hidden}}.chr-jumplinks-mobile--active .chr-jumplinks-mobile__menu-list{display:-webkit-box;display:-ms-flexbox;display:flex}.chr-jumplinks-mobile--active .chr-jumplinks-mobile__button .chr-button__icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.chr-jumplinks-mobile--hide-on-download{-webkit-transform:translateY(var(--translate-hide));transform:translateY(var(--translate-hide))}.chr-jumplinks-mobile__menu-root{display:-webkit-box;display:-ms-flexbox;display:flex;position:relative;width:256px}.chr-jumplinks-mobile__button{height:48px;padding:12px 20px;width:100%}.chr-jumplinks-mobile__download-button{height:48px}.chr-jumplinks-mobile__menu-list{background-color:#fff;border-radius:16px;bottom:100%;-webkit-box-shadow:0 1px 2px rgba(32,33,36,.15),0 1px 8px rgba(32,33,36,.08);box-shadow:0 1px 2px rgba(32,33,36,.15),0 1px 8px rgba(32,33,36,.08);display:none;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;left:0;padding:8px;position:absolute;width:256px}.dark-theme .chr-jumplinks-mobile__menu-list{background-color:#fff}.chr-jumplinks-mobile--download-button{-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.chr-jumplinks-mobile--download-button .chr-jumplinks-mobile__menu-root{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}@media only screen and (min-width: 1024px){.chr-jumplinks-mobile--mobile-experience{display:-webkit-box;display:-ms-flexbox;display:flex;visibility:visible}}.below-download .chr-jumplinks-mobile--hide-on-download{-webkit-transform:translateY(var(--translate-show));transform:translateY(var(--translate-show))}.chr-download-browser{min-height:100vh}@media only screen and (min-width: 1024px){.chr-download-browser{min-height:800px}}.chr-reversible--blue{min-height:690px}@media only screen and (min-width: 600px){.chr-reversible--blue{min-height:840px}}@media only screen and (min-width: 1024px){.chr-reversible--blue{min-height:536px}}@media only screen and (min-width: 1440px){.chr-reversible--blue{min-height:640px}}.chr-reversible--green{min-height:710px}@media only screen and (min-width: 600px){.chr-reversible--green{min-height:1000px}}@media only screen and (min-width: 1024px){.chr-reversible--green{min-height:536px}}@media only screen and (min-width: 1440px){.chr-reversible--green{min-height:640px}}@media only screen and (min-width: 600px){.chr-reversible__grid{overflow:hidden}}.chr-reversible__text{display:none}.chr-slider{min-height:780px}@media only screen and (min-width: 600px){.chr-slider{min-height:827px}}@media only screen and (min-width: 1024px){.chr-slider{min-height:765px}}.chr-non-chrome-fast{width:100%}.chr-non-chrome-fast__wrapper{-ms-grid-rows:var(--spacer-05) auto var(--spacer-05) [heading] auto var(--spacer-05);grid-template-rows:var(--spacer-05) auto var(--spacer-05) [heading] auto var(--spacer-05)}@media only screen and (min-width: 1024px){.chr-non-chrome-fast__wrapper{-ms-grid-rows:var(--spacer-07) [heading] auto var(--spacer-05) var(--spacer-05) [media] auto var(--spacer-07);grid-template-rows:var(--spacer-07) [heading] auto var(--spacer-05) var(--spacer-05) [media] auto var(--spacer-07)}}.chr-non-chrome-fast__heading{grid-column:1/-1;grid-row:heading;text-align:center}@media only screen and (min-width: 600px){.chr-non-chrome-fast__heading{-ms-grid-column:3;-ms-grid-column-span:8;grid-column:3/span 8}}@media only screen and (min-width: 1024px){.chr-non-chrome-fast__heading{-ms-grid-column:3;-ms-grid-column-span:8;grid-column:3/span 8}}.chr-non-chrome-fast__media-wrapper{display:none}@media only screen and (min-width: 1024px){.chr-non-chrome-fast__media-wrapper{aspect-ratio:auto 1080/624;border-radius:24px;display:block;grid-column:1/-1;grid-row:media;overflow:hidden;width:100%}.chr-non-chrome-fast__media-wrapper img,.chr-non-chrome-fast__media-wrapper video{height:100%;width:100%}}.chr-fifty-fifty{min-height:1160px}@media only screen and (min-width: 600px){.chr-fifty-fifty{min-height:1330px}}@media only screen and (min-width: 1024px){.chr-fifty-fifty{min-height:695px}}@media only screen and (min-width: 1440px){.chr-fifty-fifty{min-height:730px}}.chr-fifty-fifty__half-1,.chr-fifty-fifty__half-2{display:none}.chr-non-chrome-hero{display:-webkit-box;display:-ms-flexbox;display:flex;min-height:100vh;min-height:100svh;padding-top:64px;position:relative}@media only screen and (min-width: 600px){.chr-non-chrome-hero{display:block;height:auto;min-height:initial}}.chr-non-chrome-hero__parent{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;height:100%;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}@media only screen and (min-width: 0)and (max-width: 600px){.chr-non-chrome-hero__parent{margin-bottom:0;margin-top:0}}@media only screen and (min-width: 1024px){.chr-non-chrome-hero__parent{display:block;height:auto}}.chr-non-chrome-hero__media-wrapper{display:none}@media only screen and (min-width: 600px){.chr-non-chrome-hero__media-wrapper{display:block;-ms-grid-column:2;-ms-grid-column-span:10;grid-column:2/span 10;grid-row:image}.chr-non-chrome-hero__media-wrapper img{height:auto;width:100%}}@media only screen and (min-width: 1024px){.chr-non-chrome-hero__media-wrapper{display:none}}.chr-non-chrome-hero__link{display:block;grid-column:1/-1;grid-row:link;text-align:center}@media only screen and (min-width: 1024px){.chr-non-chrome-hero__link{display:none}}.chr-non-chrome-hero__wrapper{-ms-grid-rows:var(--spacer-07) [logo] auto var(--spacer-02) [heading] auto var(--spacer-08) [download] auto var(--spacer-01) [link];grid-template-rows:var(--spacer-07) [logo] auto var(--spacer-02) [heading] auto var(--spacer-08) [download] auto var(--spacer-01) [link]}@media only screen and (min-height: 0)and (max-height: 760px)and (min-width: 0)and (max-width: 600px){.chr-non-chrome-hero__wrapper{-ms-grid-rows:var(--spacer-05) [logo] auto var(--spacer-01) [heading] auto var(--spacer-05) [download] auto var(--spacer-01) [link];grid-template-rows:var(--spacer-05) [logo] auto var(--spacer-01) [heading] auto var(--spacer-05) [download] auto var(--spacer-01) [link]}}@media only screen and (min-width: 600px){.chr-non-chrome-hero__wrapper{-ms-grid-rows:var(--spacer-07) [logo] auto var(--spacer-02) [heading] auto var(--spacer-08) [download] auto var(--spacer-01) [link] auto var(--spacer-07) [image] auto var(--spacer-06);grid-template-rows:var(--spacer-07) [logo] auto var(--spacer-02) [heading] auto var(--spacer-08) [download] auto var(--spacer-01) [link] auto var(--spacer-07) [image] auto var(--spacer-06)}[lang=ja] .chr-non-chrome-hero__wrapper{-ms-grid-rows:var(--spacer-07) [logo] auto var(--spacer-02) [heading] auto var(--spacer-08) [download] auto var(--spacer-06);grid-template-rows:var(--spacer-07) [logo] auto var(--spacer-02) [heading] auto var(--spacer-08) [download] auto var(--spacer-06)}}@media only screen and (min-width: 1024px){.chr-non-chrome-hero__wrapper{-ms-grid-rows:var(--spacer-05) [logo] auto var(--spacer-01) [heading] auto var(--spacer-03) [download] auto var(--spacer-05);grid-template-rows:var(--spacer-05) [logo] auto var(--spacer-01) [heading] auto var(--spacer-03) [download] auto var(--spacer-05)}[lang=ja] .chr-non-chrome-hero__wrapper{-ms-grid-rows:var(--spacer-05) [logo] auto var(--spacer-01) [heading] auto var(--spacer-03) [download] auto var(--spacer-05);grid-template-rows:var(--spacer-05) [logo] auto var(--spacer-01) [heading] auto var(--spacer-03) [download] auto var(--spacer-05)}}.chr-non-chrome-hero__logo{grid-column:1/-1;grid-row:logo}.chr-non-chrome-hero__logo img{display:block;height:64px;margin:auto;width:64px}@media only screen and (min-height: 0)and (max-height: 760px)and (min-width: 0)and (max-width: 600px){.chr-non-chrome-hero__logo img{height:48px;width:48px}}.chr-non-chrome-hero__heading{grid-column:1/-1;grid-row:heading;margin:0 auto;max-width:290px;min-width:initial;position:unset;text-align:center}.chr-non-chrome-hero__heading--pause-button-variant{position:relative}@media only screen and (min-width: 320px)and (max-width: 324px){.chr-non-chrome-hero__heading{min-width:268px}}@media only screen and (min-width: 600px){.chr-non-chrome-hero__heading{max-width:380px;min-width:initial}}@media only screen and (min-width: 1024px){.chr-non-chrome-hero__heading{margin:0;max-width:initial}}@media only screen and (min-width: 1024px){[lang=es] .chr-non-chrome-hero__heading{font-size:4rem;line-height:5.25rem;letter-spacing:-0.21875rem}}@media only screen and (min-width: 1440px){[lang=es] .chr-non-chrome-hero__heading{font-size:4.5rem;line-height:5.25rem;letter-spacing:-0.21875rem}}@media only screen and (min-width: 1024px){[lang=id] .chr-non-chrome-hero__heading{font-size:3.875rem;line-height:5.25rem;letter-spacing:-0.21875rem}}@media only screen and (min-width: 1440px){[lang=id] .chr-non-chrome-hero__heading{font-size:4.5rem;line-height:5.25rem;letter-spacing:-0.21875rem}}[lang=ko] .chr-non-chrome-hero__heading{line-height:4rem}@media only screen and (min-width: 600px){[lang=ko] .chr-non-chrome-hero__heading{line-height:4.5rem}}@media only screen and (min-width: 1024px){[lang=ko] .chr-non-chrome-hero__heading{line-height:5.25rem}}@media only screen and (min-width: 1024px){[lang=nb] .chr-non-chrome-hero__heading{font-size:3.75rem;line-height:5.25rem;letter-spacing:-0.21875rem}}@media only screen and (min-width: 1440px){[lang=nb] .chr-non-chrome-hero__heading{font-size:4.5rem;line-height:5.625rem;letter-spacing:-0.21875rem}}@media only screen and (min-width: 1024px){[lang=pl] .chr-non-chrome-hero__heading{font-size:3.625rem;line-height:5.25rem;letter-spacing:-0.21875rem}}@media only screen and (min-width: 1440px){[lang=pl] .chr-non-chrome-hero__heading{font-size:4.5rem;line-height:5.25rem;letter-spacing:-0.21875rem}}[lang=zh] .chr-non-chrome-hero__heading{line-height:4rem}@media only screen and (min-width: 600px){[lang=zh] .chr-non-chrome-hero__heading{line-height:5.25rem}}[lang=cs] .chr-non-chrome-hero__heading{line-height:3.25rem}@media only screen and (min-width: 600px){[lang=cs] .chr-non-chrome-hero__heading{line-height:4.5rem}}@media only screen and (min-width: 1024px){[lang=cs] .chr-non-chrome-hero__heading{line-height:5.5rem}}[lang=el] .chr-non-chrome-hero__heading{line-height:3.25rem}@media only screen and (min-width: 600px){[lang=el] .chr-non-chrome-hero__heading{line-height:4.5rem}}@media only screen and (min-width: 1024px){[lang=el] .chr-non-chrome-hero__heading{line-height:5.5rem}}[lang=fil] .chr-non-chrome-hero__heading{line-height:3.25rem}@media only screen and (min-width: 600px){[lang=fil] .chr-non-chrome-hero__heading{line-height:4.5rem}}@media only screen and (min-width: 1024px){[lang=fil] .chr-non-chrome-hero__heading{line-height:5.5rem}}[lang=hu] .chr-non-chrome-hero__heading{line-height:3.25rem}@media only screen and (min-width: 600px){[lang=hu] .chr-non-chrome-hero__heading{line-height:4.5rem}}@media only screen and (min-width: 1024px){[lang=hu] .chr-non-chrome-hero__heading{line-height:5.5rem}}[lang=sr] .chr-non-chrome-hero__heading{line-height:3.25rem}@media only screen and (min-width: 600px){[lang=sr] .chr-non-chrome-hero__heading{line-height:4.5rem}}@media only screen and (min-width: 1024px){[lang=sr] .chr-non-chrome-hero__heading{line-height:5.5rem}}[lang=vi] .chr-non-chrome-hero__heading{line-height:3.25rem}@media only screen and (min-width: 600px){[lang=vi] .chr-non-chrome-hero__heading{line-height:4.5rem}}@media only screen and (min-width: 1024px){[lang=vi] .chr-non-chrome-hero__heading{line-height:5.5rem}}[lang=ja] .l10n[data-locale=ja_ALL] .chr-non-chrome-hero__heading{line-height:3.875rem}@media only screen and (min-width: 600px){[lang=ja] .l10n[data-locale=ja_ALL] .chr-non-chrome-hero__heading{line-height:4.5rem}}@media only screen and (min-width: 1024px){[lang=ja] .l10n[data-locale=ja_ALL] .chr-non-chrome-hero__heading{line-height:5.625rem}}.chr-non-chrome-hero__heading-text{margin:0 auto;max-width:12ch;text-align:center}.chr-non-chrome-hero__download{grid-column:1/-1;grid-row:download}@media only screen and (min-width: 600px){.chr-non-chrome-hero__download{-ms-grid-column:3;-ms-grid-column-span:8;grid-column:3/span 8}}.chr-non-chrome-hero__download .chr-simplified-download-wrapper .chr-download-button{margin-bottom:0}.chr-non-chrome-hero__download .chr-platform-list.hero-platforms{margin-top:8px}@media only screen and (min-width: 600px){.chr-non-chrome-hero--mobile-experience .chr-non-chrome-hero__media-wrapper{display:block}}.chr-non-chrome-hero--mobile-experience .chr-non-chrome-hero__link{display:block}@media only screen and (min-width: 1024px){.chr-non-chrome-hero--mobile-experience .chr-non-chrome-hero__wrapper{-ms-grid-rows:var(--spacer-07) [logo] auto var(--spacer-02) [heading] auto var(--spacer-08) [download] auto var(--spacer-01) [link] auto var(--spacer-07) [image] auto var(--spacer-06);grid-template-rows:var(--spacer-07) [logo] auto var(--spacer-02) [heading] auto var(--spacer-08) [download] auto var(--spacer-01) [link] auto var(--spacer-07) [image] auto var(--spacer-06)}}.chr-header-v3__drawer-nav-li-link,.chr-header-v3__drawer-nav-li .chr-link,.chr-header-v3__drawer-nav-li .chr-cta__button,.chr-header-v3__nav-li .chr-link,.chr-header-v3__nav-li .chr-cta__button,.chr-header-v3__nav-li-link,.chr-header-v3__nav-li-sublink{cursor:pointer;font-family:"Google Sans",arial,sans-serif;font-weight:500;max-width:100%}.chr-header-v3{font-size:1rem;line-height:1.5rem;left:0;position:fixed;right:0;top:0;-webkit-transition:-webkit-transform .4s ease-in;transition:-webkit-transform .4s ease-in;transition:transform .4s ease-in;transition:transform .4s ease-in, -webkit-transform .4s ease-in;z-index:100}.chr-header-v3 .cta-container{height:62px;position:fixed;top:64px;visibility:hidden;width:100%;z-index:-1}@media only screen and (min-width: 560px){.chr-header-v3 .cta-container{-webkit-box-shadow:none;box-shadow:none;right:0;top:0;width:auto;z-index:1}}@media only screen and (min-width: 0)and (max-width: 600px){.chr-header-v3 .cta-container .chr-download-button{width:100%}}.chr-header-v3 .chr-cta__button{border-radius:48px;height:100%;width:100%}[dir=rtl] .chr-header-v3 .chr-cta__button{direction:rtl}.chr-header-v3 .chr-link--nav{color:#5f6368}.dark-theme .chr-header-v3 .chr-link--nav{color:#5f6368}[dir=rtl] .chr-header-v3{direction:ltr}.chr-header-v3__hamburger{display:table;height:100%;width:64px}[dir=rtl] .chr-header-v3__hamburger{float:right}@media only screen and (min-width: 1024px){.chr-header-v3__hamburger{display:none}}.chr-header-v3__hamburger-wrapper{display:table-cell;height:100%;padding:12px;vertical-align:middle}.chr-header-v3__hamburger-button{-webkit-appearance:button;-moz-appearance:button;appearance:button;background:none;border-radius:50%;color:inherit;display:block;font:inherit;height:100%;overflow:visible;padding:0;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:100%}.chr-header-v3__hamburger-icon{height:24px;margin:0 auto;width:24px}.chr-header-v3__hamburger-icon.chr-header-v3__hamburger-icon--standard{fill:#202124;display:block}.dark-theme .chr-header-v3__hamburger-icon.chr-header-v3__hamburger-icon--standard{fill:#202124}.chr-header-v3--reversed .chr-header-v3__hamburger-icon.chr-header-v3__hamburger-icon--standard{display:none}.chr-header-v3__hamburger-icon.chr-header-v3__hamburger-icon--reversed{display:none}.chr-header-v3--reversed .chr-header-v3__hamburger-icon.chr-header-v3__hamburger-icon--reversed{display:block}.chr-header-v3__wrapper{background-color:#fff;-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;height:64px;position:relative}.dark-theme .chr-header-v3__wrapper{background-color:#fff}[dir=rtl] .chr-header-v3__wrapper{direction:ltr}@media only screen and (min-width: 560px){.chr-header-v3__wrapper{z-index:-1}}.chr-header-v3__lockup{margin-left:0;position:relative}@media only screen and (min-width: 1024px){.chr-header-v3__lockup{margin-left:12px}}.no-nav .chr-header-v3__lockup{margin-left:12px}[dir=rtl] .chr-header-v3__lockup{direction:ltr;margin-left:24px;margin-right:0}.chr-header-v3__logo{height:100%}.chr-header-v3__logo-link{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;height:48px;padding:0 8px;font-size:1.375rem;line-height:2rem}[dir=rtl] .chr-header-v3__logo-link{direction:ltr}.chr-header-v3__logo-icon{height:36px;min-height:36px;width:134px}@media screen and (-ms-high-contrast: active),(-ms-high-contrast: none){.chr-header-v3__logo-icon{margin-top:1px}}.chr-header-v3__logo-icon--privacy{height:28px;width:198px}@media screen and (-ms-high-contrast: active),(-ms-high-contrast: none){.chr-header-v3__logo-icon--privacy{margin-top:1px}}.chr-header-v3__jump-to-content{display:none}.chr-header-v3__nav{display:none}@media only screen and (min-width: 1024px){.chr-header-v3__nav{display:block;height:100%;margin-left:40px;max-width:100%}}.chr-header-v3__nav-list{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;margin:0}@media only screen and (min-width: 600px){.chr-header-v3__nav-list{height:100%;overflow:unset}}.chr-header-v3__nav-sublist{border:1px solid;display:none;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;left:0;padding:6px 0;position:absolute;top:54px}.dark-theme .chr-header-v3__nav-sublist{border:1px solid}.chr-header-v3__nav-sublist .chr-header-v3__nav-li{height:46px;margin-bottom:6px;padding:0 8px}.chr-header-v3__nav-li{height:46px}.chr-header-v3__nav-li.environment{display:none;visibility:hidden}.chr-header-v3__nav-li.environment--active{display:inherit;visibility:visible}@media only screen and (min-width: 600px){.chr-header-v3__nav-li{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;height:64px;min-width:-webkit-max-content;min-width:-moz-max-content;min-width:max-content;position:relative}}.chr-header-v3__nav-li .chr-link,.chr-header-v3__nav-li .chr-cta__button,.chr-header-v3__nav-li-link,.chr-header-v3__nav-li-sublink{-webkit-box-align:center;-ms-flex-align:center;align-items:center;cursor:pointer;display:-webkit-box;display:-ms-flexbox;display:flex;height:46px;padding:0 12px;width:100%}.chr-header-v3__nav-li .chr-cta__button{-webkit-transition:none;transition:none}.chr-header-v3__nav-li .chr-cta__button .chr-icon{margin-left:4px;margin-top:2px;-webkit-transform:none;transform:none}.chr-header-v3__nav-li-icon,.chr-header-v3__nav-li .chr-icon--link{margin-left:4px}[dir=rtl] .chr-header-v3__nav-li{direction:rtl}.chr-header-v3__drawer{-webkit-transform:translate3d(-100%, 0, 0);transform:translate3d(-100%, 0, 0)}@media only screen and (min-width: 1024px){.chr-header-v3__drawer{display:none}}.chr-header-v3__drawer .chr-cta__button{display:none;-webkit-transition:none;transition:none}.chr-header-v3__drawer .chr-cta__button.show{display:inline-block}.chr-header-v3__drawer .chr-cta__button .chr-icon{-webkit-transform:rotateX(0deg);transform:rotateX(0deg);-webkit-transition:-webkit-transform .5s;transition:-webkit-transform .5s;transition:transform .5s;transition:transform .5s, -webkit-transform .5s}.cta--hidden .cta-container{top:0 !important}@media only screen and (min-width: 0)and (max-width: 599px){.scroll-down .chr-header-v3--hide-on-scroll-up-mobile{-webkit-transform:translateY(-100%);transform:translateY(-100%);visibility:visible}}@media only screen and (min-width: 600px)and (max-width: 1023px){.scroll-down .chr-header-v3--hide-on-scroll-up-tablet{-webkit-transform:translateY(-100%);transform:translateY(-100%);visibility:visible}}.scroll-up .chr-header-v3{-webkit-transform:unset;transform:unset}@media only screen and (min-width: 1024px){.below-download.scroll-down .chr-header-v3--hide-on-scroll-up-desktop{-webkit-transform:translateY(-100%);transform:translateY(-100%);visibility:visible}}.below-download.scroll-up .chr-header-v3 .cta-container{visibility:visible}.below-download.scroll-up .chr-header-v3 .cta-container--not-on-mobile{display:none;visibility:hidden}@media only screen and (min-width: 1024px){.below-download.scroll-up .chr-header-v3 .cta-container--not-on-mobile{display:block;visibility:visible}}.below-download.scroll-up .chr-header-v3 .cta-container--only-on-mobile{display:block;visibility:visible}@media only screen and (min-width: 1024px){.below-download.scroll-up .chr-header-v3 .cta-container--only-on-mobile{display:none;visibility:hidden}}.below-download.scroll-down.no-hide .chr-header-v3 .cta-container{visibility:visible}.below-download.scroll-down.no-hide .chr-header-v3 .cta-container--not-on-mobile{display:none;visibility:hidden}@media only screen and (min-width: 1024px){.below-download.scroll-down.no-hide .chr-header-v3 .cta-container--not-on-mobile{display:block;visibility:visible}}.below-download.scroll-down.no-hide .chr-header-v3 .cta-container--only-on-mobile{display:block;visibility:visible}@media only screen and (min-width: 1024px){.below-download.scroll-down.no-hide .chr-header-v3 .cta-container--only-on-mobile{display:none;visibility:hidden}}.no-hero-download .chr-header-v3 .cta-container{visibility:visible}.no-hero-download .chr-header-v3 .cta-container--not-on-mobile{display:none;visibility:hidden}@media only screen and (min-width: 1024px){.no-hero-download .chr-header-v3 .cta-container--not-on-mobile{display:block;visibility:visible}}.no-hero-download .chr-header-v3 .cta-container--only-on-mobile{display:block;visibility:visible}@media only screen and (min-width: 1024px){.no-hero-download .chr-header-v3 .cta-container--only-on-mobile{display:none;visibility:hidden}}body:not(.is-showing-drawer).ios{-webkit-overflow-scrolling:touch}body,html{overscroll-behavior-y:none}.chr-cookie-banner{display:none}.chr-mosaic{position:relative;width:100%;z-index:1}@media only screen and (min-width: 1024px){.chr-mosaic{padding-bottom:16px}}.chr-mosaic__mobile-image{display:block;height:auto;min-height:100%;-o-object-fit:cover;object-fit:cover;width:100%}.chr-mosaic__wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;-ms-flex-wrap:nowrap;flex-wrap:nowrap;gap:24px;will-change:transform}@media only screen and (min-width: 1024px){.chr-mosaic__wrapper{gap:32px}}.is-loaded .chr-mosaic__wrapper{-webkit-transition:-webkit-transform .2s linear;transition:-webkit-transform .2s linear;transition:transform .2s linear;transition:transform .2s linear, -webkit-transform .2s linear}.is-loaded.is-resizing .chr-mosaic__wrapper{-webkit-transition:none;transition:none}.chr-mosaic__item{-webkit-transition:-webkit-transform .3s ease-out;transition:-webkit-transform .3s ease-out;transition:transform .3s ease-out;transition:transform .3s ease-out, -webkit-transform .3s ease-out;will-change:transform;z-index:2}.is-resizing .chr-mosaic__item{-webkit-transition:none;transition:none}.chr-mosaic__item:nth-of-type(1){-webkit-box-ordinal-group:2;-ms-flex-order:1;order:1}.chr-mosaic__item:nth-of-type(2){-webkit-box-ordinal-group:5;-ms-flex-order:4;order:4}.chr-mosaic__item:nth-of-type(3){-webkit-box-ordinal-group:4;-ms-flex-order:3;order:3}.chr-mosaic__item:nth-of-type(4){-webkit-box-ordinal-group:3;-ms-flex-order:2;order:2}.chr-mosaic__item:nth-of-type(5){-webkit-box-ordinal-group:6;-ms-flex-order:5;order:5}.is-fading .chr-mosaic__item:nth-child(1) .chr-mosaic__image-container{-webkit-animation-name:mobile-from-bottom-left;animation-name:mobile-from-bottom-left}.is-fading .chr-mosaic__item:nth-child(4) .chr-mosaic__image-container{-webkit-animation-name:animate-in-bottom;animation-name:animate-in-bottom}.is-fading .chr-mosaic__item:nth-child(3) .chr-mosaic__image-container{-webkit-animation-name:mobile-from-bottom-right;animation-name:mobile-from-bottom-right}@media only screen and (min-width: 1024px){.is-loaded .chr-mosaic__item:nth-child(1) .chr-mosaic__image-container{-webkit-animation-name:animate-in-right;animation-name:animate-in-right}.is-loaded .chr-mosaic__item:nth-child(2) .chr-mosaic__image-container{-webkit-animation-name:animate-in-bottom-right;animation-name:animate-in-bottom-right}.is-loaded .chr-mosaic__item:nth-child(3) .chr-mosaic__image-container{-webkit-animation-name:animate-in-bottom;animation-name:animate-in-bottom}.is-loaded .chr-mosaic__item:nth-child(4) .chr-mosaic__image-container{-webkit-animation-name:animate-in-left;animation-name:animate-in-left}.is-loaded .chr-mosaic__item:nth-child(5) .chr-mosaic__image-container{-webkit-animation-name:animate-in-bottom-left;animation-name:animate-in-bottom-left}.chr-mosaic__item:nth-of-type(1){-webkit-transform:translate(25.5%, -22%);transform:translate(25.5%, -22%)}.chr-mosaic__item:nth-of-type(2){opacity:0;-webkit-box-ordinal-group:3;-ms-flex-order:2;order:2;-webkit-transform:translate(0, 50%);transform:translate(0, 50%);z-index:1}.chr-mosaic__item:nth-of-type(3){-webkit-transform:translate(0, 8%) scale(1.15);transform:translate(0, 8%) scale(1.15)}.chr-mosaic__item:nth-of-type(4){-webkit-box-ordinal-group:5;-ms-flex-order:4;order:4;-webkit-transform:translate(44%, -26%) scale(1.25);transform:translate(44%, -26%) scale(1.25)}.chr-mosaic__item:nth-of-type(5){-webkit-transform:translate(23%, 10%);transform:translate(23%, 10%)}}@media only screen and (min-width: 1440px){.chr-mosaic__item:nth-of-type(1){-webkit-transform:translate(23%, -22%);transform:translate(23%, -22%)}.chr-mosaic__item:nth-of-type(4){-webkit-transform:translate(52%, -26%) scale(1.25);transform:translate(52%, -26%) scale(1.25)}}.chr-mosaic__image-container{-webkit-animation-duration:.85s;animation-duration:.85s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out;opacity:0;-webkit-transition:opacity .85s ease-in-out;transition:opacity .85s ease-in-out;will-change:transform,opacity}.is-loaded .chr-mosaic__image-container{opacity:1}.chr-mosaic__image-container--border{border:.6px solid #dadce0;-webkit-box-shadow:0 4.8px 12px 0 rgba(32,33,37,.1);box-shadow:0 4.8px 12px 0 rgba(32,33,37,.1);overflow:hidden;border-radius:20px;border-radius:min(max(12px, 1.3888888889vw), 20px);border-radius:clamp(12px,1.3888888889vw,20px)}.chr-mosaic__image-container img{display:block;height:400px}@media only screen and (min-width: 1024px){.chr-mosaic__image-container img{height:444px;height:min(max(282px, 41.40625vw), 444px);height:clamp(282px,41.40625vw,444px)}}@media only screen and (min-width: 1440px){.chr-mosaic__image-container img{height:424px}}@-webkit-keyframes animate-in-right{from{-webkit-transform:translate(-120px, 40px);transform:translate(-120px, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@keyframes animate-in-right{from{-webkit-transform:translate(-120px, 40px);transform:translate(-120px, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@-webkit-keyframes animate-in-bottom-right{from{-webkit-transform:translate(-20px, 60px);transform:translate(-20px, 60px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@keyframes animate-in-bottom-right{from{-webkit-transform:translate(-20px, 60px);transform:translate(-20px, 60px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@-webkit-keyframes animate-in-bottom{from{-webkit-transform:translate(0, 40px);transform:translate(0, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@keyframes animate-in-bottom{from{-webkit-transform:translate(0, 40px);transform:translate(0, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@-webkit-keyframes animate-in-left{from{-webkit-transform:translate(120px, 40px);transform:translate(120px, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@keyframes animate-in-left{from{-webkit-transform:translate(120px, 40px);transform:translate(120px, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@-webkit-keyframes animate-in-bottom-left{from{-webkit-transform:translate(160px, -40px);transform:translate(160px, -40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@keyframes animate-in-bottom-left{from{-webkit-transform:translate(160px, -40px);transform:translate(160px, -40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@-webkit-keyframes mobile-from-bottom-left{from{-webkit-transform:translate(-60px, 40px);transform:translate(-60px, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@keyframes mobile-from-bottom-left{from{-webkit-transform:translate(-60px, 40px);transform:translate(-60px, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@-webkit-keyframes mobile-from-bottom-right{from{-webkit-transform:translate(60px, 40px);transform:translate(60px, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}@keyframes mobile-from-bottom-right{from{-webkit-transform:translate(60px, 40px);transform:translate(60px, 40px)}to{-webkit-transform:translate(0, 0);transform:translate(0, 0)}}.chr-footer{display:none}</style>
                    <link id="js-css" rel="preload" href="/chrome/static/css/pages/home/<USER>" as="style" nonce="ybHWTf_2bjronyNWExNH1Q"> <!--[if lte IE 9]>
                    <link rel="stylesheet" href="/chrome/static/css/fallback.min.css"><![endif]-->
                    <script type="text/javascript" nonce="ybHWTf_2bjronyNWExNH1Q">
  var isChrome = navigator.userAgent.match(/Chrom(?:e|ium)\/([0-9\.]+)/);
  if (isChrome && parseInt(isChrome[1]) < 57) {
    var fallback = document.createElement('link');
    fallback.rel = 'stylesheet';
    fallback.href = '/chrome/static/css/fallback.min.css';
    document.head.appendChild(fallback);
  }
</script>
<script type="text/javascript" nonce="ybHWTf_2bjronyNWExNH1Q">
  window.onload = function() {
    css = document.getElementById('js-css');
    css.onload = null;
    css.rel = 'stylesheet';
  };
</script>
<noscript><link rel="preconnect" href="/chrome/static/css/pages/home/<USER>">
</noscript>
<script nonce="ybHWTf_2bjronyNWExNH1Q">
  /*! loadCSS rel=preload polyfill. [c]2017 Filament Group, Inc. MIT License */
  !function(n){"use strict";n.loadCSS||(n.loadCSS=function(){});var o=loadCSS.relpreload={};if(o.support=function(){var e;try{e=n.document.createElement("link").relList.supports("preload")}catch(t){e=!1}return function(){return e}}(),o.bindMediaToggle=function(t){var e=t.media||"all";function a(){t.addEventListener?t.removeEventListener("load",a):t.attachEvent&&t.detachEvent("onload",a),t.setAttribute("onload",null),t.media=e}t.addEventListener?t.addEventListener("load",a):t.attachEvent&&t.attachEvent("onload",a),setTimeout(function(){t.rel="stylesheet",t.media="only x"}),setTimeout(a,3e3)},o.poly=function(){if(!o.support())for(var t=n.document.getElementsByTagName("link"),e=0;e<t.length;e++){var a=t[e];"preload"!==a.rel||"style"!==a.getAttribute("as")||a.getAttribute("data-loadcss")||(a.setAttribute("data-loadcss",!0),o.bindMediaToggle(a))}},!o.support()){o.poly();var t=n.setInterval(o.poly,500);n.addEventListener?n.addEventListener("load",function(){o.poly(),n.clearInterval(t)}):n.attachEvent&&n.attachEvent("onload",function(){o.poly(),n.clearInterval(t)})}"undefined"!=typeof exports?exports.loadCSS=loadCSS:n.loadCSS=loadCSS}("undefined"!=typeof global?global:this);
</script><link href="https://www.gstatic.com/glue/cookienotificationbar/cookienotificationbar.min.css" rel="stylesheet" nonce="ybHWTf_2bjronyNWExNH1Q">
<script defer src="https://www.gstatic.com/glue/cookienotificationbar/cookienotificationbar.min.js"
  data-glue-cookie-notification-bar-category="2A"
  data-glue-cookie-notification-bar-language="en"
  data-glue-cookie-notification-bar-site-id="google.com/chrome"
  data-glue-cookie-notification-bar-autoload="false" nonce="ybHWTf_2bjronyNWExNH1Q">
</script>
  </head>
  <body data-comp="EnvironmentDetect" data-channel="stable" data-locale="en_us" data-region="en_US"  class=" l10n" data-ga-type="ga" ><!-- Google Tag Manager (noscript) -->
    <noscript>
  <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PZ6TRJB"
          height="0" width="0" style="display:none;visibility:hidden"></iframe>
</noscript><!-- End Google Tag Manager (noscript) --><div class="chr-header-v3   chr-header-v3--hide-on-scroll-up-mobile chr-header-v3--hide-on-scroll-up-tablet chr-header-v3--hide-on-scroll-up-desktop cta--hidden" id="js-header" data-comp="Header" role="banner" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;navbar&#34;, &#34;section_header&#34;: true}}" >
  <div class="chr-header-v3__wrapper shadow-elevation-1">
<div class="chr-header-v3__hamburger">
        <div class="chr-header-v3__hamburger-wrapper" id="js-hamburger-button">
          <button type="button"
                  class="chr-header-v3__hamburger-button"
                  aria-controls="js-header__drawer"
                  aria-expanded="false"
                  tabindex="0"
                  role= "button"
                  aria-label="Open the navigation drawer">
            
      <svg class="chr-header-v3__hamburger-icon chr-header-v3__hamburger-icon--standard" >
        <title>Menu</title>
        <use xlink:href="/chrome/static/images/site-icons.svg#hamburger"></use>
        </svg>
      <svg class="chr-header-v3__hamburger-icon chr-header-v3__hamburger-icon--reversed" >
        <title>Menu</title>
        <use xlink:href="/chrome/static/images/site-icons.svg#hamburger"></use>
        </svg></button>
        </div>
      </div>
<div class="chr-header-v3__lockup">
      <div class="chr-header-v3__logo">

<a href="/chrome/"  title="Google Chrome"  tabindex="0"  class=" chr-header-v3__logo-link"  ga-on="click"  ga-event-category="main_navigation"  ga-event-action="clicked" ga-event-label="google-chrome" data-g-event="main_navigation" data-g-action="clicked" data-g-label="google-chrome" >
      <svg class="chr-header-v3__logo-icon" >
        <defs>
          <linearGradient id="linear-gradient" x1="23.9" y1="1076.02" x2="5.15" y2="1043.54" gradientTransform="translate(0 -1034)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#1e8e3e"/>
              <stop offset="1" stop-color="#34a853"/>
              </linearGradient>
              <linearGradient id="linear-gradient-2" x1="18.32" y1="1077.42" x2="37.07" y2="1044.93" gradientTransform="translate(0 -1034)" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#fcc934"/>
                  <stop offset="1" stop-color="#fbbc04"/>
                  </linearGradient>
                  <linearGradient id="linear-gradient-3" x1="2.87" y1="1047.56" x2="40.19" y2="1047.56" gradientTransform="translate(0 -1034)" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#d93025"/>
                      <stop offset="1" stop-color="#ea4335"/>
                      </linearGradient>
                    </defs>
        <title>icon chrome logo</title>
        <use xlink:href="#color-google-logo-2023"></use>
        <image src="/chrome/static/images/fallback/chrome-logo-2023.png" xlink:href="" alt="Google Chrome" width="134" height="36" class="svg-fallback"/>
        </svg></a></div>
      <a href="#jump-content" class="chr-header-v3__jump-to-content">
        <span class="chr-header-v3__jump-to-content-text">Jump to content</span>
      </a>
    </div>
<nav class="chr-header-v3__nav" aria-label="navigation menu" aria-hidden="false">
      <ul class="chr-header-v3__nav-list" role="menu" aria-label= "Navigation drawer">
<li class="chr-header-v3__nav-li" role="menuitem" >

<a href="/chrome/ai-innovations/"  aria-label="ai innovations"  tabindex="0"  class=" chr-header-v3__nav-li-link chr-link--nav chr-link-first-level"  ga-on="click"  ga-event-category="navigation"  ga-event-action="clicked" ga-event-label="chrome-ai" data-g-event="main_navigation" data-g-action="clicked" data-g-label="chrome-ai" >AI Innovations</a></li>
<li class="chr-header-v3__nav-li" role="menuitem" >

<a href="/chrome/safety/"  aria-label="safety"  class=" chr-header-v3__nav-li-link chr-link--nav chr-link-first-level"  ga-on="click"  ga-event-category="navigation"  ga-event-action="clicked" ga-event-label="safety" data-g-event="main_navigation" data-g-action="clicked" data-g-label="safety" >Safety</a></li>
<li class="chr-header-v3__nav-li" role="menuitem" >

<a href="/chrome/browser-tools/"  aria-label="By Google"  tabindex="0"  class=" chr-header-v3__nav-li-link chr-link--nav chr-link-first-level"  ga-on="click"  ga-event-category="navigation"  ga-event-action="clicked" ga-event-label="the-browser-by-google" data-g-event="main_navigation" data-g-action="clicked" data-g-label="the-browser-by-google" >By Google</a></li>
<li class="environment chr-header-v3__nav-li" role="menuitem"  data-environment="mac|win|linux|chromeOS,ALL,chrome" >

<a href="https://chromewebstore.google.com/category/extensions"  aria-label="extensions"  tabindex="0"  target="_blank" rel="noopener"  class="chr-link chr-link--external chr-header-v3__nav-link-support chr-header-v3__nav-li-link chr-link--nav chr-link-first-level"  ga-on="click"  ga-event-category="navigation"  ga-event-action="clicked" ga-event-label="extensions" data-g-event="main_navigation" data-g-action="clicked" data-g-label="extensions" >Extensions
      <svg class="chr-icon chr-icon--link"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></a></li>
</ul>
    </nav>
</div>
<div id="js-header__drawer" class="chr-header-v3__drawer">
    <div class="chr-header-v3__drawer-close">
      <button type="button" class="chr-btn-close-drawer"
        aria-controls="js-header__drawer"
        aria-label="close the navigation drawer"
        tabindex="0"
        role="button">
        
      <svg class="chr-header-v3__drawer-close-icon" >
        <title>close drawer</title>
        <use xlink:href="/chrome/static/images/site-icons.svg#close-white"></use>
        </svg></button>
    </div>
    <div class="chr-header-v3__drawer-content">
      <div class="chr-header-v3__logo">

<a href="/chrome/"  title="Google Chrome"  tabindex="0"  class=" chr-header-v3__logo-link"  ga-on="click"  ga-event-category="main_navigation"  ga-event-action="clicked" ga-event-label="google-chrome" data-g-event="main_navigation" data-g-action="clicked" data-g-label="google-chrome" >
      <svg class="chr-header-v3__logo-icon" >
        <defs>
          <linearGradient id="linear-gradient" x1="23.9" y1="1076.02" x2="5.15" y2="1043.54" gradientTransform="translate(0 -1034)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#1e8e3e"/>
              <stop offset="1" stop-color="#34a853"/>
              </linearGradient>
              <linearGradient id="linear-gradient-2" x1="18.32" y1="1077.42" x2="37.07" y2="1044.93" gradientTransform="translate(0 -1034)" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#fcc934"/>
                  <stop offset="1" stop-color="#fbbc04"/>
                  </linearGradient>
                  <linearGradient id="linear-gradient-3" x1="2.87" y1="1047.56" x2="40.19" y2="1047.56" gradientTransform="translate(0 -1034)" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#d93025"/>
                      <stop offset="1" stop-color="#ea4335"/>
                      </linearGradient>
                    </defs>
        <title>icon chrome logo</title>
        <use xlink:href="#color-google-logo-2023"></use>
        <image src="/chrome/static/images/fallback/chrome-logo-2023.png" xlink:href="" alt="Google Chrome" width="134" height="36" class="svg-fallback"/>
        </svg></a></div>
      <nav class="chr-header-v3__drawer-nav">
        <ul class="chr-header-v3__drawer-nav-list" role="menubar" aria-label="Navigation drawer">
<li class="chr-header-v3__drawer-nav-li " role="menuitem" >

<a href="/chrome/ai-innovations/"  aria-label="ai innovations"  tabindex="0"  class=" chr-header-v3__drawer-nav-li-link chr-link--nav chr-link-first-level"  ga-on="click"  ga-event-category="navigation"  ga-event-action="clicked" ga-event-label="chrome-ai" data-g-event="main_navigation" data-g-action="clicked" data-g-label="chrome-ai" >AI Innovations</a></li>
<li class="chr-header-v3__drawer-nav-li " role="menuitem" >

<a href="/chrome/safety/"  aria-label="safety"  class=" chr-header-v3__drawer-nav-li-link chr-link--nav chr-link-first-level"  ga-on="click"  ga-event-category="navigation"  ga-event-action="clicked" ga-event-label="safety" data-g-event="main_navigation" data-g-action="clicked" data-g-label="safety" >Safety</a></li>
<li class="chr-header-v3__drawer-nav-li " role="menuitem" >

<a href="/chrome/browser-tools/"  aria-label="By Google"  tabindex="0"  class=" chr-header-v3__drawer-nav-li-link chr-link--nav chr-link-first-level"  ga-on="click"  ga-event-category="navigation"  ga-event-action="clicked" ga-event-label="the-browser-by-google" data-g-event="main_navigation" data-g-action="clicked" data-g-label="the-browser-by-google" >By Google</a></li>
<li class="chr-header-v3__drawer-nav-li environment" role="menuitem"  data-environment="mac|win|linux|chromeOS,ALL,chrome" >

<a href="https://chromewebstore.google.com/category/extensions"  aria-label="extensions"  tabindex="0"  target="_blank" rel="noopener"  class="chr-link chr-link--external chr-header-v3__drawer-nav-li-link chr-link--nav chr-link-first-level js-drawer-focus-trap"  ga-on="click"  ga-event-category="navigation"  ga-event-action="clicked" ga-event-label="extensions" data-g-event="main_navigation" data-g-action="clicked" data-g-label="extensions" >Extensions
      <svg class="chr-icon chr-icon--link"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></a></li>
</ul>
      </nav>
      <div class="chr-header-v3__drawer-cta-container shadow-elevation-2">
        <button type="button"
 id="js-download-mobile-drawer"        class="chr-download-button chr-download-button--mobile-drawer js-download"
data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;download_chrome_cta_click&#34;, &#34;module_name&#34;: &#34;mobile nav&#34;, &#34;section_header&#34;: false}}"  ga-on="click"  ga-event-category="cta"  ga-event-action="clicked" ga-event-label="download | mobile-drawer" data-g-event="cta" data-g-action="clicked" data-g-label="download | mobile-drawer" >
<span class="environment" data-environment="android|ios,ALL,ALL">Get Chrome</span>
<span class="environment" data-environment="not android|ios,ALL,ALL">Download Chrome</span>
</button>

</div>
    </div>
  </div>
  <div id="js-drawer-backdrop" class="chr-header-v3__drawer-backdrop">
  </div>
</div>
    <div role="main" class="chr-main " id="jump-content"> <!-- PROMO BANNER -->
      <section  id="jumplinks"  class="chr-section js-section " data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;jumplinks&#34;, &#34;section_header&#34;: true}}" ><div class="chr-jumplinks-v2" data-comp="JumplinksV2" aria-hidden="true">
    <ul class="chr-jumplinks-v2__list shadow-elevation-2">
<li class="chr-jumplinks-v2__list-item">
<a
      href="#fast"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;fast&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
Fast</a>
</li>
<li class="chr-jumplinks-v2__list-item">
<a
      href="#ai"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;ai&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
AI</a>
</li>
<li class="chr-jumplinks-v2__list-item">
<a
      href="#safe"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;safe&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
Safe</a>
</li>
<li class="chr-jumplinks-v2__list-item">
<a
      href="#yours"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;yours&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
Yours</a>
</li>
<li class="chr-jumplinks-v2__list-item">
<a
      href="#by-google"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;by-google&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
By Google</a>
</li>
<li class="chr-jumplinks-v2__list-item">
          <button type="button"
 id="js-download-header-jumplink"        class="chr-download-button chr-download-button--jumplink  js-download chr-download-button--jumplink chr-jumplinks-v2__download-button cta-container"
data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;download_chrome_cta_click&#34;, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"  tabindex="-1" >
Download
      <svg class="chr-button__icon chr-download-button__icon" >
        <use xlink:href="/chrome/static/images/site-icons.svg#download"></use>
        </svg></button>

</li>
</ul>
  </div>
</section>
      <section  id="jumplinks-mobile"  class="chr-section js-section " data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;jumplinks-mobile&#34;, &#34;section_header&#34;: true}}" ><div class="chr-jumplinks-mobile chr-jumplinks-mobile--hide-on-download chr-jumplinks-mobile--download-button" data-comp="JumplinksV2" data-mobile="True">
    <div class="chr-jumplinks-mobile__menu-root">
      <button type="button"
        class="chr-button chr-button--inverted chr-cta  chr-jumplinks-mobile__button"
 aria-expanded="false"           aria-controls="chr-jumplinks-mobile__button"
>Explore
      <svg class="chr-button__icon" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-down"></use>
        </svg></button>
<ul class="chr-jumplinks-mobile__menu-list" aria-hidden="true">
<li class="chr-jumplinks-mobile__menu-item">
            <a
      href="#fast"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;fast&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
Fast</a>
</li>
<li class="chr-jumplinks-mobile__menu-item">
            <a
      href="#ai"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;ai&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
AI</a>
</li>
<li class="chr-jumplinks-mobile__menu-item">
            <a
      href="#safe"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;safe&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
Safe</a>
</li>
<li class="chr-jumplinks-mobile__menu-item">
            <a
      href="#yours"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;yours&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
Yours</a>
</li>
<li class="chr-jumplinks-mobile__menu-item">
            <a
      href="#by-google"
      class="chr-link   chr-link--jumplink"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;by-google&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 tabindex="-1" >
By Google</a>
</li>
</ul>
    </div>
<span class="chr-jumplinks-mobile__list-item">
          <button type="button"
 id="js-download-header"        class="chr-download-button chr-download-button--primary chr-download-button--reversed chr-download-button--jumplink chr-jumplinks-mobile__download-button js-download home-jumplink-download"
data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;download_chrome_cta_click&#34;, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >
Get Chrome</button>

</span>
</div>
</section>
      <section  id="hero"  class="chr-section js-section " data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;hero&#34;, &#34;section_header&#34;: true}}" ><div class="chr-non-chrome-hero">
    <div class="chr-grid-default-parent chr-non-chrome-hero__parent">
      <div class="chr-grid-default chr-non-chrome-hero__wrapper">
<div class="chr-non-chrome-hero__logo">
            
<img class=""  src="/chrome/static/images/chrome-logo-m100.svg"  role="img"  alt="Google Chrome logo."  width="63"  height="63"  /></div>
<h1
    class="chr-headline-0     chr-non-chrome-hero__heading chr-non-chrome-hero__heading--pause-button-variant"
      aria-label="The browser built to be fast, safe, yours"
    style=""
 >

<span aria-hidden="true">
        The browser <br> built to be <span
      data-lottie="progressive"
      class="chr-heading-pill chr-heading-pill__pill-container chr-heading-pill__pill-container--green-blue-red chr-heading-pill__pill-container--large"
      data-type="green-blue-red"
      aria-hidden="true"
      data-comp="Pill"
      data-lottie-animation-delay=""
      data-remote-play=""
      data-text-animation-delay=""
      data-pause-state="Pause animation"
      data-play-state="Play animation">
      <span class="chr-heading-pill__mock"></span>
<div
    style=""
    class="chr-lottie-animation chr-heading-pill__icon chr-heading-pill__icon"
    data-lottie-file="/chrome/static/lottie-animations/pills/Sequence.json"
    data-lottie-name="lottie-sequence"
    data-lottie-autoplay="false"
    data-container-selector=""
    data-lottie-loop="False"
    id="lottie-sequence"
>
  </div><span class="chr-heading-pill__pill-text chr-heading-pill__pill-text--vertical">
<span class="chr-heading-pill__label">
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    f</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    a</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    s</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    t</span>
</span>
<span class="chr-heading-pill__label">
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    s</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    a</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    f</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    e</span>
</span>
<span class="chr-heading-pill__label">
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    y</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    o</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    u</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    r</span>
<span
                    class="chr-heading-pill__pill-char"
                    style="--animation-delay: 100ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                    s</span>
</span>
</span>
    </span>

<div class="chr-heading-pill__pause-button-wraper chr-heading-pill__pause-button-wraper--beside">
      <button class="chr-heading-pill__pause-button chr-heading-pill__pause-button--circle"
        aria-label="Play/Pause animation" tabindex="0">
        <div class="chr-heading-pill__icons-button">
          <svg class="chr-heading-pill__icon-pause"
      aria-hidden="true"
>

<use xlink:href="/chrome/static/images/site-icons.svg#pause-icon"></use>
</svg>
<svg class="chr-heading-pill__icon-play"
      aria-hidden="true"
>

<use xlink:href="/chrome/static/images/site-icons.svg#play-icon"></use>
</svg>
</div>
</button>
<div class="chr-heading-pill__tooltip-button" aria-hidden="true">
          <span class="chr-heading-pill__label-pause">Pause animation</span>
          <span class="chr-heading-pill__label-play">Play animation</span>
        </div>
</div>
</span>

  </h1>
<div class="chr-non-chrome-hero__download">
            <div class="chr-simplified-download-wrapper ">
<button type="button"
 id="js-download-hero"        class="chr-download-button chr-download-button--hero js-download home-download-hero"
          data-download-simplified="simplified"
data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;download_chrome_cta_click&#34;, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >
<span class="environment" data-environment="android|ios,ALL,ALL">Get Chrome</span>
<span class="environment" data-environment="not android|ios,ALL,ALL">Download Chrome</span>

      <svg class="chr-button__icon chr-download-button__icon" >
        <use xlink:href="/chrome/static/images/site-icons.svg#download"></use>
        </svg></button>

<div id="js-simplified-download" class="chr-simplified-download">
<div class="chr-platform-list hero-platforms">
<div class="os os-win" aria-hidden="true">
<div
            id="hero-u-d-channel-win-stable"
            class="platform channel-win-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For Windows 10 32-bit</p>

</div>

<div class="hero-chrome-update-cta environment" data-environment="win,ALL,chrome">

                    <a href="/chrome/update/" class="chr-link chr-link--primary  chr-link--internal" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;update_chrome_cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >I want to update Chrome</a></div>
<div
            id="hero-u-d-channel-win64-stable"
            class="platform channel-win64-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For Windows 11/10 64-bit</p>

</div>

<div class="hero-chrome-update-cta environment" data-environment="win64,ALL,chrome">

                    <a href="/chrome/update/" class="chr-link chr-link--primary  chr-link--internal" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;update_chrome_cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >I want to update Chrome</a></div>
<div
            id="hero-u-d-channel-win_arm64-stable"
            class="platform channel-win_arm64-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For Windows 11 ARM</p>

</div>

<div class="hero-chrome-update-cta environment" data-environment="win_arm64,ALL,chrome">

                    <a href="/chrome/update/" class="chr-link chr-link--primary  chr-link--internal" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;update_chrome_cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >I want to update Chrome</a></div>
<div
            id="hero-u-d-channel-win49-stable"
            class="platform channel-win49-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For Windows XP/Vista</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

<div
            id="hero-u-d-channel-win110-stable"
            class="platform channel-win110-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For Windows 8.1/8/7 32-bit</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

<div
            id="hero-u-d-channel-win110_64-stable"
            class="platform channel-win110_64-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For Windows 8.1/8/7 64-bit</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

</div>
<div class="os os-mac" aria-hidden="true">
<div
            id="hero-u-d-channel-mac-stable"
            class="platform channel-mac-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For macOS 11 or later.</p>

</div>

<div class="hero-chrome-update-cta environment" data-environment="mac,ALL,chrome">

                    <a href="/chrome/update/" class="chr-link chr-link--primary  chr-link--internal" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;update_chrome_cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >I want to update Chrome</a></div>
<div
            id="hero-u-d-channel-mac49-stable"
            class="platform channel-mac49-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
This computer will no longer receive Google Chrome updates because macOS 10.6 - 10.12 are no longer supported.</p>

</div>

<div
            id="hero-u-d-channel-mac65-stable"
            class="platform channel-mac65-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
This computer will no longer receive Google Chrome updates because macOS 10.6 - 10.12 are no longer supported.</p>

</div>

<div
            id="hero-u-d-channel-mac88-stable"
            class="platform channel-mac88-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
This computer will no longer receive Google Chrome updates because macOS 10.6 - 10.12 are no longer supported.</p>

</div>

<div
            id="hero-u-d-channel-mac104-stable"
            class="platform channel-mac104-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
This computer will no longer receive Google Chrome updates because macOS 10.6 - 10.12 are no longer supported.</p>

</div>

<div
            id="hero-u-d-channel-mac116-stable"
            class="platform channel-mac116-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For macOS 10.13/10.14<br/>
</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

<div
            id="hero-u-d-channel-mac129-stable"
            class="platform channel-mac129-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
For macOS 10.15<br/>
</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

</div>
<div class="os os-linux" aria-hidden="true">
<div
            id="hero-u-d-channel-linux-stable"
            class="platform channel-linux-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
Debian/Ubuntu/Fedora/openSUSE.</p>

</div>

<div class="hero-chrome-update-cta environment" data-environment="linux,ALL,chrome">

                    <a href="https://support.google.com/chrome/answer/95414?co=GENIE.Platform%3DDesktop&amp;hl=hl=en" class="chr-link chr-link--primary  chr-link--external" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;update_chrome_cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"  rel="noopener"  target="_blank" >I want to update 
                    <span class="chr-link-icon">Chrome
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a></div>
</div>
<div class="os os-ios" aria-hidden="true">
<div
            id="hero-u-d-channel-ios-stable"
            class="platform channel-ios-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
</p>

</div>

<div class="hero-chrome-update-cta environment" data-environment="ios,ALL,chrome">

                    <a href="/chrome/update/" class="chr-link chr-link--primary  chr-link--internal" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;update_chrome_cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >I want to update Chrome</a></div>
</div>
<div class="os os-chromeOS" aria-hidden="true">
<div
            id="hero-u-d-channel-chromeOS-stable"
            class="platform channel-chromeOS-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
</p>

</div>

<div class="hero-chrome-update-cta environment" data-environment="chromeOS,ALL,chrome">

                    <a href="https://support.google.com/chromebook/answer/177889?hl=en" class="chr-link chr-link--primary  chr-link--external" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;update_chrome_cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"  rel="noopener"  target="_blank" >Learn how to 
                    <span class="chr-link-icon">update
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a></div>
</div>
<div class="os os-android" aria-hidden="true">
<div
            id="hero-u-d-channel-android-stable"
            class="platform channel-android-stable"
            aria-hidden="true">
            <p
              class="chr-caption">
</p>

</div>

<div class="hero-chrome-update-cta environment" data-environment="android,ALL,chrome">

                    <a href="/chrome/update/" class="chr-link chr-link--primary  chr-link--internal" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;update_chrome_cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >I want to update Chrome</a></div>
</div>
</div><div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win-stable">
                  
<div class="chr-checkbox environment default-browser-opt default-fallback" data-environment="win,xp|vista|win7,ALL">
<input id="win-stable-js-default-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-default-cb-sdf"  value="set">
<label for="win-stable-js-default-cb" class="chr-checkbox__label chr-caption">Set Google Chrome as my default browser</label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win-stable">
                  
<div class="chr-checkbox " >
<input id="win-stable-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win-stable-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win-dev">
                  
<div class="chr-checkbox " >
<input id="win-dev-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win-dev-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win-beta">
                  
<div class="chr-checkbox " >
<input id="win-beta-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win-beta-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win-canary">
                  
<div class="chr-checkbox " >
<input id="win-canary-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win-canary-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win64-stable">
                  
<div class="chr-checkbox environment default-browser-opt default-fallback" data-environment="win,xp|vista|win7,ALL">
<input id="win64-stable-js-default-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-default-cb-sdf"  value="set">
<label for="win64-stable-js-default-cb" class="chr-checkbox__label chr-caption">Set Google Chrome as my default browser</label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win64-stable">
                  
<div class="chr-checkbox " >
<input id="win64-stable-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win64-stable-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win64-dev">
                  
<div class="chr-checkbox " >
<input id="win64-dev-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win64-dev-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win64-beta">
                  
<div class="chr-checkbox " >
<input id="win64-beta-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win64-beta-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win64-canary">
                  
<div class="chr-checkbox " >
<input id="win64-canary-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win64-canary-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win_arm64-stable">
                  
<div class="chr-checkbox environment default-browser-opt default-fallback" data-environment="win,xp|vista|win7,ALL">
<input id="win_arm64-stable-js-default-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-default-cb-sdf"  value="set">
<label for="win_arm64-stable-js-default-cb" class="chr-checkbox__label chr-caption">Set Google Chrome as my default browser</label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win_arm64-stable">
                  
<div class="chr-checkbox " >
<input id="win_arm64-stable-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win_arm64-stable-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win_arm64-canary">
                  
<div class="chr-checkbox " >
<input id="win_arm64-canary-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win_arm64-canary-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win_arm64-dev">
                  
<div class="chr-checkbox " >
<input id="win_arm64-dev-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win_arm64-dev-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win_arm64-beta">
                  
<div class="chr-checkbox " >
<input id="win_arm64-beta-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win_arm64-beta-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win49-stable">
                  
<div class="chr-checkbox environment default-browser-opt default-fallback" data-environment="win,xp|vista|win7,ALL">
<input id="win49-stable-js-default-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-default-cb-sdf"  value="set">
<label for="win49-stable-js-default-cb" class="chr-checkbox__label chr-caption">Set Google Chrome as my default browser</label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win49-stable">
                  
<div class="chr-checkbox " >
<input id="win49-stable-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win49-stable-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win110-stable">
                  
<div class="chr-checkbox environment default-browser-opt default-fallback" data-environment="win,xp|vista|win7,ALL">
<input id="win110-stable-js-default-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-default-cb-sdf"  value="set">
<label for="win110-stable-js-default-cb" class="chr-checkbox__label chr-caption">Set Google Chrome as my default browser</label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win110-stable">
                  
<div class="chr-checkbox " >
<input id="win110-stable-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win110-stable-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win110_64-stable">
                  
<div class="chr-checkbox environment default-browser-opt default-fallback" data-environment="win,xp|vista|win7,ALL">
<input id="win110_64-stable-js-default-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-default-cb-sdf"  value="set">
<label for="win110_64-stable-js-default-cb" class="chr-checkbox__label chr-caption">Set Google Chrome as my default browser</label>
</div></div>
<div class="chr-simplified-download__simplified-opt chr-homepage-hero__simplified-opt platform channel-win110_64-stable">
                  
<div class="chr-checkbox " >
<input id="win110_64-stable-js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input js-stats-cb-sdf"  value="">
<label for="win110_64-stable-js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win-dev chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win-beta chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win-canary chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win64-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win64-dev chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win64-beta chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win64-canary chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win_arm64-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win_arm64-canary chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win_arm64-dev chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win_arm64-beta chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win49-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win110-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-win110_64-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac-dev chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac-beta chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac-canary chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac49-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac65-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac88-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac104-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac116-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<p id="js-simplified-legal-links" class="chr-simplified-download__simplified-links channel-mac129-stable chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
</div>
        </div>
</div>
<div class="chr-non-chrome-hero__link">
            
                    <a href="#fast" class="chr-link chr-link--primary  chr-link--anchor" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;anchor_link_click&#34;, &#34;link_url&#34;: &#34;#fast&#34;, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >Scroll for 
                    <span class="chr-link-icon">more
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#mi-expand"></use>
        </svg></span></a></div>
<div class="chr-non-chrome-hero__media-wrapper">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMDgwIDYyNCc+PC9zdmc+" data-src="/chrome/static/images/dev-components/home-poster.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMDgwIDYyNCc+PC9zdmc+ 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAyMTYwIDEyNDgnPjwvc3ZnPg== 2x" data-srcset="/chrome/static/images/dev-components/home-poster.webp 1x, /chrome/static/images/dev-components/home-poster-2x.webp 2x"  alt="A browser is able to save up to 148 MB of memory thanks to Memory Saver mode."  /></div>
</div>
    </div>
  </div>
</section>
      <section  id="fast"  class="chr-section js-section "  data-offset="{&#34;default&#34;: &#34;-40&#34;, &#34;phone&#34;: &#34;28&#34;, &#34;tablet&#34;: &#34;-40&#34;}" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;fast&#34;, &#34;section_header&#34;: true}}" ><div class="chr-hero-fast-scrolable-container">
<div
    data-comp="FastScrollableHero"
    data-page-type="non-chrome-user"
    class="chr-hero-fast-scrolable"
 >
    <div class="chr-non-chrome-fast">
    <div class="chr-grid-default-parent chr-non-chrome-fast__parent">
      <div class="chr-grid-default chr-non-chrome-fast__wrapper">
<h2
    class="chr-headline-1     chr-non-chrome-fast__heading"
      aria-label="The fast way to do things online"
    style=""
 >

<span aria-hidden="true">
        The <span
      data-lottie="lottie-faster-hero"
      class="chr-heading-pill chr-heading-pill__pill-container chr-heading-pill__pill-container--green chr-heading-pill__pill-container--medium"
      data-type=""
      aria-hidden="true"
      data-comp="Pill"
      data-lottie-animation-delay=""
      data-remote-play=""
      data-text-animation-delay=""
      data-pause-state=""
      data-play-state="">
      <span class="chr-heading-pill__mock"></span>
<div
    style=""
    class="chr-lottie-animation chr-heading-pill__icon chr-heading-pill__icon"
    data-lottie-file="/chrome/static/lottie-animations/pills/Faster.json"
    data-lottie-name="lottie-faster-hero"
    data-lottie-autoplay="false"
    data-container-selector=""
    data-lottie-loop="False"
    id="lottie-faster-hero"
>
  </div><span class="chr-heading-pill__pill-text ">
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                f</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                a</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                s</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                t</span>
</span>
    </span>

 way to do things&nbsp;online</span>

  </h2>
</div>
    </div>
  </div>
<div class="chr-hero-fast-scrolable__headline-trigger"></div>
    <div class="chr-hero-fast-scrolable__cards-desktop">
      <div
    class="chr-static-carousel chr-carousel chr-static-carousel--scrollable chr-static-carousel"
    data-comp="CarouselCards"

      id=fast-scrollable-carousel
    style=""
 >
<div class="chr-static-carousel__container chr-carousel__container">
    <ul class="chr-static-carousel__wrapper chr-carousel__wrapper">
<li
    data-index="0"
      data-target="prioritize-performance"

    class="chr-static-carousel__card chr-carousel__card chr-static-carousel__card--is-feature chr-carousel__card--is-feature"
 >
<div class="chr-carousel-card chr-carousel-card--landscape  chr-carousel-card--border   chr-static-carousel__gallery-card">
    <div class="chr-carousel-card__text-wrapper">
      <div class="chr-carousel-card__headings-wrapper">
<h3
    class="chr-headline-4     chr-carousel-card__heading"
    style=""
 >

Prioritize performance
  </h3>
</div>
      <div class="chr-carousel-card__body-wrapper">
<p class="chr-copy chr-carousel-card__body">
            Chrome is built for performance. Optimize your experience with features like Energy Saver and Memory Saver.</p>
</div>
</div>
<div class="chr-carousel-card__image-wrapper chr-carousel-card__image-wrapper--stick-right chr-carousel-card__image-wrapper--only-on-mobile">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_mobile.webp 1x, /chrome/static/images/homepage/fast/energy-saver_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_mobile.png 1x, /chrome/static/images/homepage/fast/energy-saver_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_desktop.webp 1x, /chrome/static/images/homepage/fast/energy-saver_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_desktop.png 1x, /chrome/static/images/homepage/fast/energy-saver_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_tablet.webp 1x, /chrome/static/images/homepage/fast/energy-saver_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_tablet.png 1x, /chrome/static/images/homepage/fast/energy-saver_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/homepage/fast/energy-saver_desktop.png" srcset="/chrome/static/images/homepage/fast/energy-saver_desktop.png" alt="A cursor has clicked on the Energy Saver icon, which explains background activity and some visual effects have been limited to save memory."  width="300"  height="83" >
      </picture></div>
<div class="chr-carousel-card__video-wrapper">
        <video
muted playsinline    data-comp="LazyLoader"
    preload="none"
class="chr-carousel__video shadow-ui" aria-hidden="true" poster="/chrome/static/images/dev-components/home-poster-2x.webp"      data-props-videoplayer='{
          "in_overlay": ""
        }'
>
<source
        type="video/webm"
            data-src="/chrome/static/videos/dev-components/non-chrome.webm"
>
<source
        type="video/mp4"
            data-src="/chrome/static/videos/dev-components/non-chrome.mp4"
>
</video>
  <img
class="chr-carousel__video shadow-ui"    data-img-fallback="/chrome/static/images/dev-components/home-poster-2x.webp"
    style="display: none;"/></div>
</div>

</li>
<li
    data-index="1"
      data-target="stay-on-top-of-tabs"

    class="chr-static-carousel__card chr-carousel__card"
 >
<div class="chr-carousel-card chr-carousel-card--landscape chr-carousel-card--yellow chr-carousel-card--border   chr-static-carousel__gallery-card">
    <div class="chr-carousel-card__text-wrapper">
      <div class="chr-carousel-card__headings-wrapper">
<h3
    class="chr-headline-4     chr-carousel-card__heading"
    style=""
 >

Stay on top of&nbsp;tabs
  </h3>
</div>
      <div class="chr-carousel-card__body-wrapper">
<p class="chr-copy chr-carousel-card__body">
            Chrome has tools to help you manage the tabs you’re not quite ready to close. Group, label, and color code your tabs to stay organized and work faster.</p>
</div>
</div>
<div class="chr-carousel-card__image-wrapper chr-carousel-card__image-wrapper--stick-left">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_mobile.webp 1x, /chrome/static/images/homepage/fast/tabs-groups_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_mobile.png 1x, /chrome/static/images/homepage/fast/tabs-groups_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_desktop.webp 1x, /chrome/static/images/homepage/fast/tabs-groups_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_desktop.png 1x, /chrome/static/images/homepage/fast/tabs-groups_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_tablet.webp 1x, /chrome/static/images/homepage/fast/tabs-groups_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_tablet.png 1x, /chrome/static/images/homepage/fast/tabs-groups_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/homepage/fast/tabs-groups_desktop.png" srcset="/chrome/static/images/homepage/fast/tabs-groups_desktop.png" alt="A browser UI features three groups of tabs: Personal, Trip to Arches, and Work." >
      </picture></div>
</div>

</li>
<li
    data-index="2"
      data-target="optimized-for-your-device"

    class="chr-static-carousel__card chr-carousel__card"
 >
<div class="chr-carousel-card chr-carousel-card--vertical-qr chr-carousel-card--green chr-carousel-card--border   chr-static-carousel__gallery-card">
    <div class="chr-carousel-card__text-wrapper">
      <div class="chr-carousel-card__headings-wrapper">
<h3
    class="chr-headline-4     chr-carousel-card__heading"
    style=""
 >

Optimized for your&nbsp;device
  </h3>
</div>
      <div class="chr-carousel-card__body-wrapper">
<p class="chr-copy chr-carousel-card__body">
            Chrome is built to work with your device across platforms. That means a smooth experience on whatever you’re working with.</p>
</div>
<div class="chr-carousel-card__qr-wrapper">
<div class="chr-qr-code chr-qr-code--container chr-carousel-card__qr">
      <div class="chr-qr-code__qr-container">

<img class="js-lazy-load  chr-qr-code__image"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAzMTggMzE4Jz48L3N2Zz4=" data-src="/chrome/static/images/v2/go-mobile-qrs/conversion-hp-optimize-your-device-card.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAzMTggMzE4Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MzYgNjM2Jz48L3N2Zz4= 2x" data-srcset="/chrome/static/images/v2/go-mobile-qrs/conversion-hp-optimize-your-device-card.webp 1x, /chrome/static/images/v2/go-mobile-qrs/conversion-hp-optimize-your-device-card-2x.webp 2x"  alt="QR code to download chrome browser in mobile devices"  /></div>
<div class="chr-qr-code__content">
<p
    class="chr-caption"
    style=""
 >

Get Chrome for your phone
  </p>
</div>
</div>
</div>
</div>
<div class="chr-carousel-card__image-wrapper chr-carousel-card__image-wrapper--stick-left">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_mobile.webp 1x, /chrome/static/images/homepage/fast/devices_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_mobile.png 1x, /chrome/static/images/homepage/fast/devices_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_desktop.webp 1x, /chrome/static/images/homepage/fast/devices_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_desktop.png 1x, /chrome/static/images/homepage/fast/devices_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_tablet.webp 1x, /chrome/static/images/homepage/fast/devices_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_tablet.png 1x, /chrome/static/images/homepage/fast/devices_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/homepage/fast/devices_desktop.png" srcset="/chrome/static/images/homepage/fast/devices_desktop.png" alt="A mobile device and a desktop computer both show the Google homepage on Chrome." >
      </picture></div>
</div>

</li>
<li
    data-index="3"

    class="chr-static-carousel__card chr-carousel__card"
 >
<div class="chr-carousel-card chr-carousel-card--landscape chr-carousel-card--light-blue chr-carousel-card--border   chr-static-carousel__gallery-card">
    <div class="chr-carousel-card__text-wrapper">
      <div class="chr-carousel-card__headings-wrapper">
<h3
    class="chr-headline-4     chr-carousel-card__heading"
    style=""
 >

Automatic updates
  </h3>
</div>
      <div class="chr-carousel-card__body-wrapper">
<p class="chr-copy chr-carousel-card__body">
            There’s a new Chrome update every four weeks, making it easy to have the newest features and a faster, safer browser.</p>
</div>
</div>
<div class="chr-carousel-card__image-wrapper chr-carousel-card__image-wrapper--stick-right">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_mobile.webp 1x, /chrome/static/images/homepage/fast/update_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_mobile.png 1x, /chrome/static/images/homepage/fast/update_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_desktop.webp 1x, /chrome/static/images/homepage/fast/update_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_desktop.png 1x, /chrome/static/images/homepage/fast/update_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_tablet.webp 1x, /chrome/static/images/homepage/fast/update_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_tablet.png 1x, /chrome/static/images/homepage/fast/update_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/homepage/fast/update_desktop.png" srcset="/chrome/static/images/homepage/fast/update_desktop.png" alt="A browser UI features a green Update pill which tells the user it’s ready to be restarted for an automatic update." >
      </picture></div>
</div>

</li>
<span
    class="chr-static-carousel__card-offset chr-carousel__card-offset"
    aria-hidden="true"
 ></span>
</ul>
  </div>
<div class="chr-grid-default-parent">
      <div class="chr-grid-default">
<div class="chr-static-carousel__controls chr-carousel__controls">

                    <button class="chr-action-icon chr-action-icon--secondary chr-action-icon--regular chr-static-carousel__control-btn chr-static-carousel__control-btn--prev chr-carousel__control-btn--prev"  title="Previous Card"  aria-controls="fast-scrollable-carousel"  aria-hidden="true" tabindex="-1" >
      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-forward"></use>
        </svg></button>
                    <button class="chr-action-icon chr-action-icon--secondary chr-action-icon--regular chr-static-carousel__control-btn chr-static-carousel__control-btn--next chr-carousel__control-btn--next"  title="Next Card"  aria-controls="fast-scrollable-carousel"  aria-hidden="true" tabindex="-1" >
      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-forward"></use>
        </svg></button></div>
</div>
    </div>
  </div>
</div>
    <div class="chr-hero-fast-scrolable__cards-mobile">
<div
    class="chr-static-carousel chr-carousel chr-static-carousel--scrollable chr-static-carousel"
    data-comp="CarouselCards"

      id=fast-scrollable-carousel
    style=""
 >
<div class="chr-static-carousel__container chr-carousel__container">
    <ul class="chr-static-carousel__wrapper chr-carousel__wrapper">
<li
    data-index="0"
      data-target="prioritize-performance"

    class="chr-static-carousel__card chr-carousel__card chr-static-carousel__card--is-feature chr-carousel__card--is-feature"
 >
<div class="chr-carousel-card chr-carousel-card--landscape  chr-carousel-card--border   chr-static-carousel__gallery-card">
    <div class="chr-carousel-card__text-wrapper">
      <div class="chr-carousel-card__headings-wrapper">
<h3
    class="chr-headline-4     chr-carousel-card__heading"
    style=""
 >

Prioritize performance
  </h3>
</div>
      <div class="chr-carousel-card__body-wrapper">
<p class="chr-copy chr-carousel-card__body">
            Chrome is built for performance. Optimize your experience with features like Energy Saver and Memory Saver.</p>
</div>
</div>
<div class="chr-carousel-card__image-wrapper chr-carousel-card__image-wrapper--stick-right chr-carousel-card__image-wrapper--only-on-mobile">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_mobile.webp 1x, /chrome/static/images/homepage/fast/energy-saver_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_mobile.png 1x, /chrome/static/images/homepage/fast/energy-saver_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_desktop.webp 1x, /chrome/static/images/homepage/fast/energy-saver_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_desktop.png 1x, /chrome/static/images/homepage/fast/energy-saver_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_tablet.webp 1x, /chrome/static/images/homepage/fast/energy-saver_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/energy-saver_tablet.png 1x, /chrome/static/images/homepage/fast/energy-saver_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/homepage/fast/energy-saver_desktop.png" srcset="/chrome/static/images/homepage/fast/energy-saver_desktop.png" alt="A cursor has clicked on the Energy Saver icon, which explains background activity and some visual effects have been limited to save memory."  width="300"  height="83" >
      </picture></div>
<div class="chr-carousel-card__video-wrapper">
        <video
muted playsinline    data-comp="LazyLoader"
    preload="none"
class="chr-carousel__video shadow-ui" aria-hidden="true" poster="/chrome/static/images/dev-components/home-poster-2x.webp"      data-props-videoplayer='{
          "in_overlay": ""
        }'
>
<source
        type="video/webm"
            data-src="/chrome/static/videos/dev-components/non-chrome.webm"
>
<source
        type="video/mp4"
            data-src="/chrome/static/videos/dev-components/non-chrome.mp4"
>
</video>
  <img
class="chr-carousel__video shadow-ui"    data-img-fallback="/chrome/static/images/dev-components/home-poster-2x.webp"
    style="display: none;"/></div>
</div>

</li>
<li
    data-index="1"
      data-target="stay-on-top-of-tabs"

    class="chr-static-carousel__card chr-carousel__card"
 >
<div class="chr-carousel-card chr-carousel-card--landscape chr-carousel-card--yellow chr-carousel-card--border   chr-static-carousel__gallery-card">
    <div class="chr-carousel-card__text-wrapper">
      <div class="chr-carousel-card__headings-wrapper">
<h3
    class="chr-headline-4     chr-carousel-card__heading"
    style=""
 >

Stay on top of&nbsp;tabs
  </h3>
</div>
      <div class="chr-carousel-card__body-wrapper">
<p class="chr-copy chr-carousel-card__body">
            Chrome has tools to help you manage the tabs you’re not quite ready to close. Group, label, and color code your tabs to stay organized and work faster.</p>
</div>
</div>
<div class="chr-carousel-card__image-wrapper chr-carousel-card__image-wrapper--stick-left">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_mobile.webp 1x, /chrome/static/images/homepage/fast/tabs-groups_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_mobile.png 1x, /chrome/static/images/homepage/fast/tabs-groups_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_desktop.webp 1x, /chrome/static/images/homepage/fast/tabs-groups_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_desktop.png 1x, /chrome/static/images/homepage/fast/tabs-groups_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_tablet.webp 1x, /chrome/static/images/homepage/fast/tabs-groups_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/tabs-groups_tablet.png 1x, /chrome/static/images/homepage/fast/tabs-groups_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/homepage/fast/tabs-groups_desktop.png" srcset="/chrome/static/images/homepage/fast/tabs-groups_desktop.png" alt="A browser UI features three groups of tabs: Personal, Trip to Arches, and Work." >
      </picture></div>
</div>

</li>
<li
    data-index="2"
      data-target="optimized-for-your-device"

    class="chr-static-carousel__card chr-carousel__card"
 >
<div class="chr-carousel-card chr-carousel-card--vertical-qr chr-carousel-card--green chr-carousel-card--border   chr-static-carousel__gallery-card">
    <div class="chr-carousel-card__text-wrapper">
      <div class="chr-carousel-card__headings-wrapper">
<h3
    class="chr-headline-4     chr-carousel-card__heading"
    style=""
 >

Optimized for your&nbsp;device
  </h3>
</div>
      <div class="chr-carousel-card__body-wrapper">
<p class="chr-copy chr-carousel-card__body">
            Chrome is built to work with your device across platforms. That means a smooth experience on whatever you’re working with.</p>
</div>
<div class="chr-carousel-card__qr-wrapper">
<div class="chr-qr-code chr-qr-code--container chr-carousel-card__qr">
      <div class="chr-qr-code__qr-container">

<img class="js-lazy-load  chr-qr-code__image"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAzMTggMzE4Jz48L3N2Zz4=" data-src="/chrome/static/images/v2/go-mobile-qrs/conversion-hp-optimize-your-device-card.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAzMTggMzE4Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MzYgNjM2Jz48L3N2Zz4= 2x" data-srcset="/chrome/static/images/v2/go-mobile-qrs/conversion-hp-optimize-your-device-card.webp 1x, /chrome/static/images/v2/go-mobile-qrs/conversion-hp-optimize-your-device-card-2x.webp 2x"  alt="QR code to download chrome browser in mobile devices"  /></div>
<div class="chr-qr-code__content">
<p
    class="chr-caption"
    style=""
 >

Get Chrome for your phone
  </p>
</div>
</div>
</div>
</div>
<div class="chr-carousel-card__image-wrapper chr-carousel-card__image-wrapper--stick-left">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_mobile.webp 1x, /chrome/static/images/homepage/fast/devices_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_mobile.png 1x, /chrome/static/images/homepage/fast/devices_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_desktop.webp 1x, /chrome/static/images/homepage/fast/devices_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_desktop.png 1x, /chrome/static/images/homepage/fast/devices_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_tablet.webp 1x, /chrome/static/images/homepage/fast/devices_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/devices_tablet.png 1x, /chrome/static/images/homepage/fast/devices_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/homepage/fast/devices_desktop.png" srcset="/chrome/static/images/homepage/fast/devices_desktop.png" alt="A mobile device and a desktop computer both show the Google homepage on Chrome." >
      </picture></div>
</div>

</li>
<li
    data-index="3"

    class="chr-static-carousel__card chr-carousel__card"
 >
<div class="chr-carousel-card chr-carousel-card--landscape chr-carousel-card--light-blue chr-carousel-card--border   chr-static-carousel__gallery-card">
    <div class="chr-carousel-card__text-wrapper">
      <div class="chr-carousel-card__headings-wrapper">
<h3
    class="chr-headline-4     chr-carousel-card__heading"
    style=""
 >

Automatic updates
  </h3>
</div>
      <div class="chr-carousel-card__body-wrapper">
<p class="chr-copy chr-carousel-card__body">
            There’s a new Chrome update every four weeks, making it easy to have the newest features and a faster, safer browser.</p>
</div>
</div>
<div class="chr-carousel-card__image-wrapper chr-carousel-card__image-wrapper--stick-right">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_mobile.webp 1x, /chrome/static/images/homepage/fast/update_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_mobile.png 1x, /chrome/static/images/homepage/fast/update_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_desktop.webp 1x, /chrome/static/images/homepage/fast/update_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA5NjAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxOTIwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_desktop.png 1x, /chrome/static/images/homepage/fast/update_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_tablet.webp 1x, /chrome/static/images/homepage/fast/update_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2MDAgNDAwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjAwIDgwMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/homepage/fast/update_tablet.png 1x, /chrome/static/images/homepage/fast/update_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/homepage/fast/update_desktop.png" srcset="/chrome/static/images/homepage/fast/update_desktop.png" alt="A browser UI features a green Update pill which tells the user it’s ready to be restarted for an automatic update." >
      </picture></div>
</div>

</li>
<span
    class="chr-static-carousel__card-offset chr-carousel__card-offset"
    aria-hidden="true"
 ></span>
</ul>
  </div>
<div class="chr-grid-default-parent">
      <div class="chr-grid-default">
<div class="chr-static-carousel__controls chr-carousel__controls">

                    <button class="chr-action-icon chr-action-icon--secondary chr-action-icon--regular chr-static-carousel__control-btn chr-static-carousel__control-btn--prev chr-carousel__control-btn--prev"  title="Previous Card"  aria-controls="fast-scrollable-carousel"  aria-hidden="true" tabindex="-1" >
      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-forward"></use>
        </svg></button>
                    <button class="chr-action-icon chr-action-icon--secondary chr-action-icon--regular chr-static-carousel__control-btn chr-static-carousel__control-btn--next chr-carousel__control-btn--next"  title="Next Card"  aria-controls="fast-scrollable-carousel"  aria-hidden="true" tabindex="-1" >
      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-forward"></use>
        </svg></button></div>
</div>
    </div>
  </div>
</div>
  </div>
</div>
</section>
      <section  id="ai"  class="chr-section js-section  spacer-05"  data-offset="{&#34;default&#34;: &#34;-40&#34;, &#34;phone&#34;: &#34;12&#34;, &#34;tablet&#34;: &#34;-56&#34;}" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;ai&#34;, &#34;section_header&#34;: true}}" ><div class="chr-pill-section-title__media-handler chr-grid-default-parent">
        <button class="handler-media handler-media--button-text handler-media--sm handler-media     chr-background__light-blue-01"
      data-id-media="pill-ai-home"
      data-comp="HandlerMedia"
 aria-label="Play animation" >
      <span class="handler-media__container">
<span data-media-state="play" class="handler-media__label handler-media__play" aria-hidden="false">
    <svg class="handler-media__icon"
    role="presentation "
    aria-hidden="True"
>

    <use xlink:href="/chrome/static/images/site-icons.svg#play-icon"></use>
</svg>
<span class="handler-media__text">
      Play animation</span>
  </span>
<span data-media-state="pause" class="handler-media__label handler-media__pause" aria-hidden="true">
    <svg class="handler-media__icon"
    role="presentation "
    aria-hidden="True"
>

    <use xlink:href="/chrome/static/images/site-icons.svg#pause-icon"></use>
</svg>
<span class="handler-media__text">
      Pause animation</span>
  </span>
<span data-media-state="replay" class="handler-media__label handler-media__replay" aria-hidden="true">
    <svg class="handler-media__icon"
    role="presentation "
    aria-hidden="True"
>

    <use xlink:href="/chrome/static/images/site-icons.svg#replay-icon"></use>
</svg>
<span class="handler-media__text">
      Replay animation</span>
  </span>
</span>
    </button>
</div>

    <div class="chr-pill-section-title  chr-pill-section-title--media-handler-included">
      <h2
    class="chr-headline-1"
      aria-label="Supercharge your browser with AI built right in"
    style=""
 >

<span aria-hidden="true">
        Supercharge your browser with <span
      data-lottie="lottie-safe-ai"
      class="chr-heading-pill chr-heading-pill__pill-container chr-heading-pill__pill-container--ai chr-heading-pill__pill-container--medium"
      data-type=""
      aria-hidden="true"
      data-comp="Pill"
      data-lottie-animation-delay=""
      data-remote-play=""
      data-text-animation-delay=""
      data-pause-state=""
      data-play-state="">
      <span class="chr-heading-pill__mock"></span>
<svg class="chr-heading-pill__background" viewBox="0 0 100 100" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg">

    <defs>
      <linearGradient id="ai-pill-gradient" x1="0" y1="10" x2="80" y2="80" gradientUnits="userSpaceOnUse">
        <stop offset="0.00132785" stop-color="#4B96F8"/>
        <stop offset="0.65" stop-color="#DDE3FD"/>
      </linearGradient>
    </defs>
<path d="M 0 0 L 100 0 L 0 -50 L 0 100 Z" fill="url(#ai-pill-gradient)">
      <animate class="chr-heading-pill__morph"
        attributeName="d" dur="1s" fill="freeze"
        calcMode="spline" keyTimes="0;1" keySplines="0.42 0 0.58 1"
        from="M 0 0 L 100 0 L 0 -50 L 0 100 Z"
      />
    </path>
  </svg>
<div
    style=""
    class="chr-lottie-animation chr-heading-pill__icon chr-heading-pill__icon"
    data-lottie-file="/chrome/static/lottie-animations/pills/ai-hero.json"
    data-lottie-name="lottie-safe-ai"
    data-lottie-autoplay="false"
    data-container-selector=""
    data-lottie-loop="True"
    id="pill-ai-home"
>
  </div><span class="chr-heading-pill__pill-text ">
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 0ms; --animation-duration: .7s; --animation-name: charSlideInUp">
                A</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 0ms; --animation-duration: .7s; --animation-name: charSlideInUp">
                I</span>
</span>
    </span>

 built right in</span>

  </h2>

    </div>

<div
    class="chr-grid-layout chr-grid-default-parent  chr-grid-layout--cn-2"


    style=""
 >
      <ul class="chr-grid-layout__cards chr-grid-default">
    <li
      class="chr-card-flip     chr-background__ai-blue-muted-light chr-grid-layout__card chr-grid-layout__card-1"
      data-comp="CardFlip"
data-id="google-lens"
      style=""
     >

  <div
    class="chr-card-adaptive  chr-card-flip__cover     card-height__unset chr-card-adaptive chr-card-adaptive--scale chr-card-adaptive--none chr-card-adaptive--standard chr-card-adaptive chr-card-adaptive chr-card-adaptive--media-bottom    chr-card-adaptive--padding-md chr-card-adaptive--media-cover chr-card-adaptive--media-shadow-none"
    style="
      --text-wrap-width:360px;
    "
>
    <div class="chr-card-adaptive__background chr-card-flip__cover-background"></div>
<div class="chr-card-adaptive__decoration chr-card-flip__cover-decoration">
        
<img class=""  src="/chrome/static/images/gen-ai/ai-bk-googlelens.webp"  srcset="/chrome/static/images/gen-ai/ai-bk-googlelens.webp, /chrome/static/images/gen-ai/ai-bk-googlelens-2x.webp 2x"  aria-hidden='true'  /></div>
<div class="chr-card-adaptive__content-wrapper chr-card-flip__cover-content-wrapper">

<div class="chr-card-adaptive__upper-wrapper">
<h3
    class="chr-eyebrow chr-card-adaptive__eyebrow     chr-text-content"
    style=""
 >

Google Lens
  </h3>
</div>
<h4
    class="chr-headline-3     chr-card-adaptive__heading chr-card-flip__heading     chr-text-heading"
    style=""
 >

See anything, search anything.
  </h4>
<div class="chr-card-adaptive__body-wrapper">
</div>

</div>

<div class="chr-card-adaptive__media chr-card-flip__cover-media        aspect-ratio-4-3" style="--scale_start: 1; --scale_end: 1.1;">


<img class="js-lazy-load   chr-card-adaptive__image-1"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4=" data-src="/chrome/static/images/gen-ai/ai-googlelens-1.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/gen-ai/ai-googlelens-1.webp 1x, /chrome/static/images/gen-ai/ai-googlelens-1-2x.webp 2x"  aria-hidden='true'  />
<img class="js-lazy-load   chr-card-adaptive__image-2"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4=" data-src="/chrome/static/images/gen-ai/ai-googlelens-2.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/gen-ai/ai-googlelens-2.webp 1x, /chrome/static/images/gen-ai/ai-googlelens-2-2x.webp 2x"  alt="Rounded white corners of a square surround a red spiky plant which is identified as red ginger"  /></div>

  </div>


                    <button class="chr-action-icon chr-action-icon--card chr-action-icon--regular chr-action-icon--dark chr-card-flip__action-button"  aria-label="Flip card"  tabindex="0" >
      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#plus"></use>
        </svg></button>
  <div
    class="chr-card-simple chr-card-simple--simple chr-card-simple  chr-card-flip__back      card-height__unset"
>
<div class="chr-card-simple__media chr-card-flip__back-media       aspect-ratio-16-9">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4=" data-src="/chrome/static/images/gen-ai/ai-googlelens.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDcyMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/gen-ai/ai-googlelens.webp 1x, /chrome/static/images/gen-ai/ai-googlelens-2x.webp 2x"  alt="Rounded white corners of a square surround a red spiky plant which is identified as red ginger"  /></div>
<div class="chr-card-simple__content-wrapper chr-card-flip__back-content-wrapper">
<p
    class="chr-card-simple__body chr-copy     chr-text-content"
    style=""
 >

Search, translate, identify, or shop with Google Lens in Chrome. You can ask questions about what you see, whether it’s something you come across on a website or a photo you take.
  </p>

                    <a href="/chrome/ai-innovations/" class="chr-link chr-link--primary  chr-link--internal     chr-text-wrap--balance chr-card-simple__link chr-card-flip__back-link" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;Google Lens&#34;, &#34;section_header&#34;: true}}" >Learn more about AI in Chrome</a></div>
  </div>

    </li>
    <li
      class="chr-card-flip     chr-background__ai-bright-blue-10 chr-grid-layout__card chr-grid-layout__card-2"
      data-comp="CardFlip"
data-id="ai-theme"
      style=""
     >

  <div
    class="chr-card-adaptive  chr-card-flip__cover     card-height__unset chr-card-adaptive chr-card-adaptive--scale chr-card-adaptive--none chr-card-adaptive--standard chr-card-adaptive chr-card-adaptive chr-card-adaptive--media-bottom    chr-card-adaptive--padding-md chr-card-adaptive--media-cover chr-card-adaptive--media-shadow-none"
    style="
      --text-wrap-width:360px;
    "
>
    <div class="chr-card-adaptive__background chr-card-flip__cover-background"></div>
<div class="chr-card-adaptive__decoration chr-card-flip__cover-decoration">
        
<img class=""  src="/chrome/static/images/gen-ai/ai-themes-bk.webp"  srcset="/chrome/static/images/gen-ai/ai-themes-bk.webp, /chrome/static/images/gen-ai/ai-themes-bk-2x.webp 2x"  aria-hidden='true'  /></div>
<div class="chr-card-adaptive__content-wrapper chr-card-flip__cover-content-wrapper">

<div class="chr-card-adaptive__upper-wrapper">
<h3
    class="chr-eyebrow chr-card-adaptive__eyebrow     chr-text-content"
    style=""
 >

Generative themes
  </h3>
</div>
<h4
    class="chr-headline-3     chr-card-adaptive__heading chr-card-flip__heading     chr-text-heading"
    style=""
 >

Create a theme that’s uniquely yours.
  </h4>
<div class="chr-card-adaptive__body-wrapper">
</div>

</div>

<div class="chr-card-adaptive__media chr-card-flip__cover-media        aspect-ratio-4-3" style="--scale_start: 1; --scale_end: 1.1;">


<img class="js-lazy-load   chr-card-adaptive__image-1"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4=" data-src="/chrome/static/images/gen-ai/ai-themes-1.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/gen-ai/ai-themes-1.webp 1x, /chrome/static/images/gen-ai/ai-themes-1-2x.webp 2x"  aria-hidden='true'  />
<img class="js-lazy-load   chr-card-adaptive__image-2"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4=" data-src="/chrome/static/images/gen-ai/ai-themes-2.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/gen-ai/ai-themes-2.webp 1x, /chrome/static/images/gen-ai/ai-themes-2-2x.webp 2x"  alt="A browser UI shows the option to create a theme with AI. A purple aurora borealis is the example given."  /></div>

  </div>


                    <button class="chr-action-icon chr-action-icon--card chr-action-icon--regular chr-action-icon--light chr-card-flip__action-button"  aria-label="Flip card"  tabindex="0" >
      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#plus"></use>
        </svg></button>
  <div
    class="chr-card-simple chr-card-simple--simple chr-card-simple  chr-card-flip__back      card-height__unset"
>
<div class="chr-card-simple__media chr-card-flip__back-media       aspect-ratio-16-9">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4=" data-src="/chrome/static/images/gen-ai/ai-recent-themes.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDcyMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/gen-ai/ai-recent-themes.webp 1x, /chrome/static/images/gen-ai/ai-recent-themes-2x.webp 2x"  alt="6 Icons display different AI themes in varying artistic and featuring different subjects."  /></div>
<div class="chr-card-simple__content-wrapper chr-card-flip__back-content-wrapper">
<p
    class="chr-card-simple__body chr-copy     chr-text-content"
    style=""
 >

Bring your imagination to life with a Chrome theme that’s unmistakably you. The power of AI lets you play with subject, color, art style, and mood for a one-of-a-kind browsing experience.
  </p>

                    <a href="/chrome/ai-innovations/" class="chr-link chr-link--primary  chr-link--internal     chr-text-wrap--balance chr-card-simple__link chr-card-flip__back-link" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;cta_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;Generative themes&#34;, &#34;section_header&#34;: true}}" >Learn more about AI in Chrome</a></div>
  </div>

    </li>
      </ul>
</div>

</section>
      <section  id="safe"  class="chr-section js-section  spacer-05"  data-offset="{&#34;default&#34;: &#34;-40&#34;, &#34;phone&#34;: &#34;12&#34;, &#34;tablet&#34;: &#34;-56&#34;}" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;safe&#34;, &#34;section_header&#34;: true}}" >
    <div class="chr-pill-section-title chr-show-only-in-mobile">
      <h2
    class="chr-headline-1"
      aria-label="Stay safe while you browse"
    style=""
 >

<span aria-hidden="true">
        Stay <span
      data-lottie="lottie-safe-mobile"
      class="chr-heading-pill chr-heading-pill__pill-container chr-heading-pill__pill-container--blue chr-heading-pill__pill-container--medium"
      data-type=""
      aria-hidden="true"
      data-comp="Pill"
      data-lottie-animation-delay=""
      data-remote-play=""
      data-text-animation-delay=""
      data-pause-state=""
      data-play-state="">
      <span class="chr-heading-pill__mock"></span>
<div
    style=""
    class="chr-lottie-animation chr-heading-pill__icon chr-heading-pill__icon"
    data-lottie-file="/chrome/static/lottie-animations/pills/Safer.json"
    data-lottie-name="lottie-safe-mobile"
    data-lottie-autoplay="false"
    data-container-selector=""
    data-lottie-loop="False"
    id="lottie-safe-mobile"
>
  </div><span class="chr-heading-pill__pill-text ">
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                s</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                a</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                f</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                e</span>
</span>
    </span>

 while you browse</span>

  </h2>

    </div>


    <div class="chr-pill-section-title chr-show-only-in-desktop">
      <h2
    class="chr-headline-1"
      aria-label="Stay safe while you browse"
    style=""
 >

<span aria-hidden="true">
        Stay <span
      data-lottie="lottie-safe"
      class="chr-heading-pill chr-heading-pill__pill-container chr-heading-pill__pill-container--blue chr-heading-pill__pill-container--medium"
      data-type=""
      aria-hidden="true"
      data-comp="Pill"
      data-lottie-animation-delay=""
      data-remote-play=""
      data-text-animation-delay=""
      data-pause-state=""
      data-play-state="">
      <span class="chr-heading-pill__mock"></span>
<div
    style=""
    class="chr-lottie-animation chr-heading-pill__icon chr-heading-pill__icon"
    data-lottie-file="/chrome/static/lottie-animations/pills/Safer.json"
    data-lottie-name="lottie-safe"
    data-lottie-autoplay="false"
    data-container-selector=""
    data-lottie-loop="False"
    id="lottie-safe"
>
  </div><span class="chr-heading-pill__pill-text ">
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                s</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                a</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                f</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                e</span>
</span>
    </span>

 <br>while you browse</span>

  </h2>

    </div>

<div class="chr-gallery  chr-gallery--4-up chr-grid-default-parent">
    <ul class="chr-gallery__container chr-grid-default">
<li class="chr-gallery-card chr-gallery-card--interactive chr-gallery-card--blue    chr-gallery-card--back-card-larger"
      data-comp="GalleryCard"
>
<div class="chr-gallery-card-cover chr-gallery-card-cover--interactive chr-gallery-card-cover--blue  chr-gallery-card-cover--no-image">
<div class="chr-gallery-card-cover__text-wrapper">
<h3
    class="chr-eyebrow chr-gallery-card-cover__eyebrow"
    style=""
 >

PASSWORD MANAGER
  </h3>
<h4
    class="chr-headline-3     chr-gallery-card-cover__heading"
    style=""
 >

Use strong passwords on every site.
  </h4>
</div>
<div class="chr-gallery-card-cover__dynamic-images-wrapper">
<div class="chr-gallery-card-cover__image chr-gallery-card-cover__image--cover" style="--type: cover;--layer: 1;--scale_start: 1;--scale_end: 1;">
        
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/passwords-fill-1.webp"  srcset="/chrome/static/images/v2/gallery/passwords-fill-1.webp, /chrome/static/images/v2/gallery/passwords-fill-1-2x.webp 2x"  aria-hidden='true'  width="614"  height="461"  /></div>
<div class="chr-gallery-card-cover__image chr-gallery-card-cover__image--cover" style="--type: cover;--layer: 2;--scale_start: 1;--scale_end: 1.1;">
        
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/passwords-fill-2.webp"  srcset="/chrome/static/images/v2/gallery/passwords-fill-2.webp, /chrome/static/images/v2/gallery/passwords-fill-2-2x.webp 2x"  aria-hidden='true'  width="614"  height="461"  /></div>
</div>
<div class="chr-gallery-card-cover__static-images-wrapper">
      
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/passwords-fill.webp"  srcset="/chrome/static/images/v2/gallery/passwords-fill.webp, /chrome/static/images/v2/gallery/passwords-fill-2x.webp 2x"  aria-hidden='true'  width="200"  height="150"  /></div>
</div>
<button
    class="chr-action-icon chr-action-icon--card chr-action-icon--regular chr-action-icon--dark chr-gallery-card__action-icon"
 aria-label="Flip card"  tabindex="0" >

      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#plus"></use>
        </svg></button>
<div
    class="chr-gallery-card-back chr-gallery-card-back--interactive chr-gallery-card-back--blue">
<div class="chr-gallery-card-back__image-wrapper">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4=" data-src="/chrome/static/images/v2/gallery/save-password.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDcyMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/save-password.webp 1x, /chrome/static/images/v2/gallery/save-password-2x.webp 2x"  alt="A prompt asks the user if they want to save their password to Google Password Manager."  /></div>
<div class="chr-gallery-card-back__text-wrapper chr-gallery-card-back__text-wrapper--padding">
<p class="chr-copy-xl chr-gallery-card-back__body">
          Chrome has Google Password Manager built in, which makes it simple to save, manage, and protect your passwords online. It also helps you create stronger passwords for every account you use.</p>
</div>
  </div>
  </li>
<li class="chr-gallery-card chr-gallery-card--interactive chr-gallery-card--white chr-gallery-card--small   chr-gallery-card--back-card-larger"
      data-comp="GalleryCard"
>
<div class="chr-gallery-card-cover chr-gallery-card-cover--interactive chr-gallery-card-cover--white chr-gallery-card-cover--small chr-gallery-card-cover--no-image">
<div class="chr-gallery-card-cover__text-wrapper">
<h3
    class="chr-eyebrow chr-gallery-card-cover__eyebrow"
    style=""
 >

ENHANCED SAFE BROWSING
  </h3>
<h4
    class="chr-headline-2     chr-gallery-card-cover__heading"
    style=""
 >

Browse with the confidence that you're staying safer online.
  </h4>
</div>
</div>
<button
    class="chr-action-icon chr-action-icon--card chr-action-icon--regular chr-action-icon--light chr-gallery-card__action-icon"
 aria-label="Flip card"  tabindex="0" >

      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#plus"></use>
        </svg></button>
<div
    class="chr-gallery-card-back chr-gallery-card-back--interactive chr-gallery-card-back--white">
<div class="chr-gallery-card-back__image-wrapper chr-gallery-card-back__image-wrapper--small">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMjY0Jz48L3N2Zz4=" data-src="/chrome/static/images/v2/gallery/malware-alert.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/malware-alert.webp 1x, /chrome/static/images/v2/gallery/malware-alert-2x.webp 2x"  alt="A red alert warns the user that a site they are trying to visit contains malware."  /></div>
<div class="chr-gallery-card-back__text-wrapper chr-gallery-card-back__text-wrapper--padding">
<p class="chr-copy-xl chr-gallery-card-back__body">
          Chrome's Safe Browsing warns you about malware or phishing attacks. Turn on Enhanced Safe Browsing for even more safety protections.</p>
</div>
  </div>
  </li>
<li class="chr-gallery-card chr-gallery-card--interactive chr-gallery-card--light-blue-01 chr-gallery-card--small   chr-gallery-card--back-card-larger"
      data-comp="GalleryCard"
>
<div class="chr-gallery-card-cover chr-gallery-card-cover--interactive chr-gallery-card-cover--light-blue-01 chr-gallery-card-cover--small chr-gallery-card-cover--no-image">
<div class="chr-gallery-card-cover__text-wrapper">
<h3
    class="chr-eyebrow chr-gallery-card-cover__eyebrow"
    style=""
 >

SAFETY CHECK
  </h3>
<h4
    class="chr-headline-2     chr-gallery-card-cover__heading"
    style=""
 >

Check your safety level in real time with just one click.
  </h4>
</div>
</div>
<button
    class="chr-action-icon chr-action-icon--card chr-action-icon--regular chr-action-icon--light chr-gallery-card__action-icon"
 aria-label="Flip card"  tabindex="0" >

      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#plus"></use>
        </svg></button>
<div
    class="chr-gallery-card-back chr-gallery-card-back--interactive chr-gallery-card-back--light-blue-01">
<div class="chr-gallery-card-back__image-wrapper chr-gallery-card-back__image-wrapper--small">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMjY0Jz48L3N2Zz4=" data-src="/chrome/static/images/v2/gallery/safety-check.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMjY0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDUyOCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/safety-check.webp 1x, /chrome/static/images/v2/gallery/safety-check-2x.webp 2x"  alt="An alert shows that Chrome’s safety check has been completed and the browser is up to date."  /></div>
<div class="chr-gallery-card-back__text-wrapper chr-gallery-card-back__text-wrapper--padding">
<p class="chr-copy-xl chr-gallery-card-back__body">
          Chrome's Safety Check confirms the overall security and privacy of your browsing experience, including your saved passwords, extensions, and settings. If something needs attention, Chrome will help you fix it.</p>
</div>
  </div>
  </li>
<li class="chr-gallery-card chr-gallery-card--interactive chr-gallery-card--dark-blue"
      data-comp="GalleryCard"
>
<div class="chr-gallery-card-cover chr-gallery-card-cover--interactive chr-gallery-card-cover--dark-blue">
<div class="chr-gallery-card-cover__text-wrapper">
<h3
    class="chr-eyebrow chr-gallery-card-cover__eyebrow"
    style=""
 >

PRIVACY GUIDE
  </h3>
<h4
    class="chr-headline-3     chr-gallery-card-cover__heading"
    style=""
 >

Keep your privacy under your control with easy-to-use settings.
  </h4>
</div>
<div class="chr-gallery-card-cover__image-wrapper">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4=" data-src="/chrome/static/images/v2/gallery/google-safety.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgNDgwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDk2MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/google-safety.webp 1x, /chrome/static/images/v2/gallery/google-safety-2x.webp 2x"  aria-hidden='true'  /></div>
</div>
<button
    class="chr-action-icon chr-action-icon--card chr-action-icon--regular chr-action-icon--dark chr-gallery-card__action-icon"
 aria-label="Flip card"  tabindex="0" >

      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#plus"></use>
        </svg></button>
<div
    class="chr-gallery-card-back chr-gallery-card-back--interactive chr-gallery-card-back--dark-blue">
<div class="chr-gallery-card-back__image-wrapper">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4=" data-src="/chrome/static/images/v2/gallery/privacy-guide.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDcyMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/privacy-guide.webp 1x, /chrome/static/images/v2/gallery/privacy-guide-2x.webp 2x"  alt="An isolated module asks users if they would like to explore the Privacy Guide."  /></div>
<div class="chr-gallery-card-back__text-wrapper chr-gallery-card-back__text-wrapper--padding">
<p class="chr-copy-xl chr-gallery-card-back__body">
          Chrome makes it easy to understand exactly what you’re sharing online and who you’re sharing it with. Simply use the Privacy Guide, a step-by-step tour of your privacy settings.</p>
</div>
  </div>
  </li>
</ul>
  </div>
</section>
      <section  id="yours"  class="chr-section js-section  spacer-05"  data-offset="{&#34;default&#34;: &#34;0&#34;, &#34;phone&#34;: &#34;12&#34;, &#34;tablet&#34;: &#34;-56&#34;}" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;yours&#34;, &#34;section_header&#34;: true}}" >
    <div class="chr-pill-section-title chr-show-only-in-mobile">
      <h2
    class="chr-headline-1"
      aria-label="Make it yours and take it with you"
    style=""
 >

<span aria-hidden="true">
        Make it <span
      data-lottie="lottie-yours"
      class="chr-heading-pill chr-heading-pill__pill-container chr-heading-pill__pill-container--red chr-heading-pill__pill-container--medium"
      data-type=""
      aria-hidden="true"
      data-comp="Pill"
      data-lottie-animation-delay=""
      data-remote-play=""
      data-text-animation-delay=""
      data-pause-state=""
      data-play-state="">
      <span class="chr-heading-pill__mock"></span>
<div
    style=""
    class="chr-lottie-animation chr-heading-pill__icon chr-heading-pill__icon"
    data-lottie-file="/chrome/static/lottie-animations/pills/Yours.json"
    data-lottie-name="lottie-yours"
    data-lottie-autoplay="false"
    data-container-selector=""
    data-lottie-loop="False"
    id="lottie-yours"
>
  </div><span class="chr-heading-pill__pill-text ">
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                y</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                o</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                u</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                r</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 100ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                s</span>
</span>
    </span>

 and take it with you</span>

  </h2>

    </div>

<div
    class="chr-take-over-animation spacer-07--bottom"
    style="--heading-grid: 3 / span 8; --heading-max-width: 0;"
    data-comp="TakeOverAnimation"
 >
    <div class="chr-take-over-animation__wrapper chr-grid-default-parent">
      <div class="chr-grid-default chr-take-over-animation__container">
        <div class="chr-take-over-animation__title-container">
<h2
    class="chr-headline-1"
      aria-label="Make it yours and take it with you"
    style=""
 >

<span aria-hidden="true">
        Make it <span
      data-lottie="lottie-yours-take-over"
      class="chr-heading-pill chr-heading-pill__pill-container chr-heading-pill__pill-container--red chr-heading-pill__pill-container--medium"
      data-type=""
      aria-hidden="true"
      data-comp="Pill"
      data-lottie-animation-delay=""
      data-remote-play=""
      data-text-animation-delay=""
      data-pause-state=""
      data-play-state="">
      <span class="chr-heading-pill__mock"></span>
<div
    style=""
    class="chr-lottie-animation chr-heading-pill__icon chr-heading-pill__icon"
    data-lottie-file="/chrome/static/lottie-animations/pills/Yours.json"
    data-lottie-name="lottie-yours-take-over"
    data-lottie-autoplay="false"
    data-container-selector=""
    data-lottie-loop="False"
    id="lottie-yours-take-over"
>
  </div><span class="chr-heading-pill__pill-text ">
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                y</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                o</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                u</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                r</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 100ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                s</span>
</span>
    </span>

 and take it with you</span>

  </h2>
</div>
        <div class="chr-take-over-animation__mask">
    <div class="chr-take-over-animation__take-over">

<img class="chr-take-over-animation__image"  class=""  src="/chrome/static/images/v2/yours-take-over/theme-arches.webp"  srcset="/chrome/static/images/v2/yours-take-over/theme-arches.webp, /chrome/static/images/v2/yours-take-over/theme-arches-2x.webp 2x"  aria-hidden='true'  width="933"  height="525"  /></div>
    <div class="chr-take-over-animation__take-over-v2">

<img class="chr-take-over-animation__image"  class=""  src="/chrome/static/images/v2/yours-take-over/theme-ui-1.webp"  srcset="/chrome/static/images/v2/yours-take-over/theme-ui-1.webp, /chrome/static/images/v2/yours-take-over/theme-ui-1-2x.webp 2x"  aria-hidden='true'  width="933"  height="525"  /></div>
    <div class="chr-take-over-animation__take-over-v3">

<img class="chr-take-over-animation__image chr-take-over-animation__image--border"  class=""  src="/chrome/static/images/v2/yours-take-over/theme-ui-2.webp"  srcset="/chrome/static/images/v2/yours-take-over/theme-ui-2.webp, /chrome/static/images/v2/yours-take-over/theme-ui-2-2x.webp 2x"  aria-hidden='true'  width="933"  height="525"  /></div>
  </div></div>
    </div>
  </div><div
    class="chr-accordion-timed chr-grid-default-parent"
    data-comp="AccordionTimed"
data-props-accordiontimed="{&#34;animation_duration&#34;: 5000, &#34;panel_fade_in_duration&#34;: 1000, &#34;panel_fade_out_duration&#34;: 500, &#34;item_content_display_duration&#34;: 500}">
    <div
      class="chr-accordion-timed__container chr-grid-default"
      style=""
   >
      <div class="chr-accordion-timed__panel-wrapper">
<div class="chr-accordion-timed__panel chr-accordion-timed__panel--active"
    id="chr-accordion-timed__panel-customize-chrome" role="region" aria-hidden="false"
      data-has-video="true"
>
<video
muted playsinline autoplay    data-comp="LazyLoader"
    preload="none"
 aria-hidden="true" poster="/chrome/static/images/v2/accordion-timed/themes-poster.webp">
<source
        type="video/webm"
            data-src="/chrome/static/videos/v2/accordion-timed/themes.webm"
>
<source
        type="video/mp4"
            data-src="/chrome/static/videos/v2/accordion-timed/themes.mp4"
>
</video>
  <img
    data-img-fallback="/chrome/static/images/v2/accordion-timed/themes-poster.webp"
    style="display: none;"/></div>
<div class="chr-accordion-timed__panel"
    id="chr-accordion-timed__panel-browse-devices" role="region" aria-hidden="true"
>

<img class=""  loading="eager"  src="/chrome/static/images/v2/accordion-timed/tab-sync.webp"  srcset="/chrome/static/images/v2/accordion-timed/tab-sync.webp, /chrome/static/images/v2/accordion-timed/tab-sync-2x.webp 2x"  alt="A mobile browser loads tabs from a desktop browser, including Google Maps and NYC parking info."  width="624" height="624"  /></div>
<div class="chr-accordion-timed__panel"
    id="chr-accordion-timed__panel-save-time" role="region" aria-hidden="true"
      data-has-video="true"
>
<video
muted playsinline    data-comp="LazyLoader"
    preload="none"
 aria-hidden="true" poster="/chrome/static/images/v2/yours/autofill-poster.png">
<source
        type="video/mp4"
            data-src="/chrome/static/videos/yours/autofill.mp4"
>
</video>
  <img
    data-img-fallback="/chrome/static/images/v2/yours/autofill-poster.png"
    style="display: none;"/></div>
</div>
<div class="chr-accordion-timed__side-content">
        <div class="chr-accordion-timed__list">
<div class="chr-accordion-timed__item chr-accordion-timed__item--active" 
    data-target="customize-chrome">
    <div class="chr-accordion-timed__item__progress-wrapper">
      <div class="chr-accordion-timed__item__progress"></div>
      <div class="chr-accordion-timed__item__progress chr-accordion-timed__item__progress--active"></div>
    </div>

    <div class="chr-accordion-timed__item__content">
      <div class="chr-accordion-timed__item__title chr-accordion-timed__item__title-container" role="button" tabindex="0" aria-expanded="true" aria-controls="customize-chrome-body">
        <h3
    class="chr-accordion-timed__item__title chr-headline-4"
    style=""
 >

Customize your Chrome
  </h3>
</div>
      <div class="chr-accordion-timed__item__body" id="customize-chrome-body">
        <div class="chr-accordion-timed__item__inner-body">
          <p class="chr-copy-xl">Personalize your web browser with themes, dark mode and other options built just for you.</p>
</div>
      </div>
    </div>
  </div>
<div class="chr-accordion-timed__item" 
    data-target="browse-devices">
    <div class="chr-accordion-timed__item__progress-wrapper">
      <div class="chr-accordion-timed__item__progress"></div>
      <div class="chr-accordion-timed__item__progress chr-accordion-timed__item__progress--active"></div>
    </div>

    <div class="chr-accordion-timed__item__content">
      <div class="chr-accordion-timed__item__title chr-accordion-timed__item__title-container" role="button" tabindex="0" aria-expanded="false" aria-controls="browse-devices-body">
        <h3
    class="chr-accordion-timed__item__title chr-headline-4"
    style=""
 >

Browse across devices
  </h3>
</div>
      <div class="chr-accordion-timed__item__body" id="browse-devices-body">
        <div class="chr-accordion-timed__item__inner-body">
          <p class="chr-copy-xl">Sign in to Chrome on any device to access your bookmarks, saved passwords, and more.</p>
</div>
      </div>
    </div>
  </div>
<div class="chr-accordion-timed__item" 
    data-target="save-time">
    <div class="chr-accordion-timed__item__progress-wrapper">
      <div class="chr-accordion-timed__item__progress"></div>
      <div class="chr-accordion-timed__item__progress chr-accordion-timed__item__progress--active"></div>
    </div>

    <div class="chr-accordion-timed__item__content">
      <div class="chr-accordion-timed__item__title chr-accordion-timed__item__title-container" role="button" tabindex="0" aria-expanded="false" aria-controls="save-time-body">
        <h3
    class="chr-accordion-timed__item__title chr-headline-4"
    style=""
 >

Save time with autofill
  </h3>
</div>
      <div class="chr-accordion-timed__item__body" id="save-time-body">
        <div class="chr-accordion-timed__item__inner-body">
          <p class="chr-copy-xl">Use Chrome to save addresses, passwords, and more to quickly autofill your details.</p>
</div>
      </div>
    </div>
  </div>
</div>
</div>
    </div>

    <div class="chr-accordion-timed__container-mobile chr-grid-default">
      <div class="chr-accordion-timed__panel-mobile-wrapper">
<div
            class="chr-accordion-timed__panel-mobile-content"
            style=""
         >
            <div class="chr-accordion-timed__mobile-content__panel">

<img class=""  src="/chrome/static/images/v2/accordion-timed/themes-mobile.webp"  srcset="/chrome/static/images/v2/accordion-timed/themes-mobile.webp, /chrome/static/images/v2/accordion-timed/themes-mobile-2x.webp 2x"  alt="Icons display nine different themes. If the user clicks the theme the background image will change."  width="624" height="624"  /></div>
<div class="chr-accordion-timed__mobile-content__item chr-accordion-timed__mobile-content__item--active" >
    <div class="chr-accordion-timed__mobile-content__item__content">
      <h3
    class="chr-accordion-timed__mobile-content__item__title chr-headline-4"
    style=""
 >

Customize your Chrome
  </h3>
<div class="chr-accordion-timed__mobile-content__item__body">
        <div class="chr-accordion-timed__mobile-content__item__inner-body">
          <p class="chr-copy">Personalize your web browser with themes, dark mode and other options built just for you.</p>
</div>
      </div>
    </div>
  </div>
</div>
<div
            class="chr-accordion-timed__panel-mobile-content"
            style=""
         >
            <div class="chr-accordion-timed__mobile-content__panel">

<img class=""  src="/chrome/static/images/v2/accordion-timed/tab-sync-mobile.webp"  srcset="/chrome/static/images/v2/accordion-timed/tab-sync-mobile.webp, /chrome/static/images/v2/accordion-timed/tab-sync-mobile-2x.webp 2x"  alt="A mobile browser loads tabs from a desktop browser, including Google Maps and NYC parking info."  width="624" height="624"  /></div>
<div class="chr-accordion-timed__mobile-content__item" >
    <div class="chr-accordion-timed__mobile-content__item__content">
      <h3
    class="chr-accordion-timed__mobile-content__item__title chr-headline-4"
    style=""
 >

Browse across devices
  </h3>
<div class="chr-accordion-timed__mobile-content__item__body">
        <div class="chr-accordion-timed__mobile-content__item__inner-body">
          <p class="chr-copy">Sign in to Chrome on any device to access your bookmarks, saved passwords, and more.</p>
</div>
      </div>
    </div>
  </div>
</div>
<div
            class="chr-accordion-timed__panel-mobile-content"
            style=""
         >
            <div class="chr-accordion-timed__mobile-content__panel">

<img class=""  src="/chrome/static/images/v2/accordion-timed/autofill-mobile.webp"  srcset="/chrome/static/images/v2/accordion-timed/autofill-mobile.webp, /chrome/static/images/v2/accordion-timed/autofill-mobile-2x.webp 2x"  alt="A user is able to instantly enter their name and address to a form using autofill."  width="624" height="624"  /></div>
<div class="chr-accordion-timed__mobile-content__item" >
    <div class="chr-accordion-timed__mobile-content__item__content">
      <h3
    class="chr-accordion-timed__mobile-content__item__title chr-headline-4"
    style=""
 >

Save time with autofill
  </h3>
<div class="chr-accordion-timed__mobile-content__item__body">
        <div class="chr-accordion-timed__mobile-content__item__inner-body">
          <p class="chr-copy">Use Chrome to save addresses, passwords, and more to quickly autofill your details.</p>
</div>
      </div>
    </div>
  </div>
</div>
</div>
    </div>
  </div>
<div
    class="chr-media-content   chr-grid-default-parent chr-show-only-in-desktop environment"

      data-environment="not ios|android,ALL,ALL"

    style=""
 >
    <div class="chr-media-content__container chr-grid-default">
<div
    class="chr-media-content__content-wrapper"
>
<h3
    class="chr-headline-4   chr-responsive-headline-2--lg chr-responsive-headline-2--xl chr-media-content__heading"
    style=""
 >

Extend your experience
  </h3>
<p
    class="chr-media-content__body chr-copy     chr-text-content"
    style=""
 >

From shopping and entertainment to productivity, find extensions to improve your experience in the Chrome Web Store.
  </p>
</div><div
    class="chr-media-content__media-wrapper       chr-media-content__media-wrapper--no-aspect-ratio"
>
<img
    class="js-lazy-load"
      data-comp="LazyLoader"
      
      src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA4ODAgNTQ5Jz48L3N2Zz4="
      data-src="/chrome/static/images/dev-components/extensions-ui.webp"
        srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA4ODAgNTQ5Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxNzYwIDEwOTcnPjwvc3ZnPg== 2x"
data-srcset="/chrome/static/images/dev-components/extensions-ui.webp 1x,
            /chrome/static/images/dev-components/extensions-ui-2x.webp 2x"
      alt="An abstract Chrome UI is surrounded by icons that represent categories for browser extensions. The icons represent Shopping, Entertainment, Tools, Art & Design, and Accessibility."
  />

<div class="chr-keyframe-animation" data-comp="KeyframeAnimation">
<div
        class="chr-keyframe-animation__layer"
        style="
          --opacity: ;
          --scale: ;
          --width: 84;"
        data-props-keyframes='{&#34;enter&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 15, &#34;left&#34;: -15, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 25, &#34;left&#34;: 3, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 25, &#34;left&#34;: 3, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 15, &#34;left&#34;: -15, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}], &#34;enter@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 15, &#34;left&#34;: -15, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 25, &#34;left&#34;: 3, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 25, &#34;left&#34;: 3, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 15, &#34;left&#34;: -15, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}]}'>
<img
    class=""
      src="/chrome/static/images/dev-components/extensions-shop.png"
          srcset="/chrome/static/images/dev-components/extensions-shop.png,
                  /chrome/static/images/dev-components/extensions-shop-2x.png 2x"
      aria-hidden='true'
 width="84"  height="84"   />

</div>
<div
        class="chr-keyframe-animation__layer"
        style="
          --opacity: ;
          --scale: ;
          --width: 151;"
        data-props-keyframes='{&#34;enter&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: -20, &#34;left&#34;: 70, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 0, &#34;left&#34;: 62, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 0, &#34;left&#34;: 62, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: -20, &#34;left&#34;: 70, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}], &#34;enter@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: -20, &#34;left&#34;: 70, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 0, &#34;left&#34;: 62, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 0, &#34;left&#34;: 62, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: -20, &#34;left&#34;: 70, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}]}'>
<img
    class=""
      src="/chrome/static/images/dev-components/extensions-icon.png"
          srcset="/chrome/static/images/dev-components/extensions-icon.png,
                  /chrome/static/images/dev-components/extensions-icon-2x.png 2x"
      aria-hidden='true'
 width="151"  height="151"   />

</div>
<div
        class="chr-keyframe-animation__layer"
        style="
          --opacity: ;
          --scale: ;
          --width: 84;"
        data-props-keyframes='{&#34;enter&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 33, &#34;left&#34;: 110, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 36, &#34;left&#34;: 87, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 36, &#34;left&#34;: 87, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 33, &#34;left&#34;: 110, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}], &#34;enter@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 33, &#34;left&#34;: 110, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 36, &#34;left&#34;: 87, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 36, &#34;left&#34;: 87, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 33, &#34;left&#34;: 110, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}]}'>
<img
    class=""
      src="/chrome/static/images/dev-components/extensions-video.png"
          srcset="/chrome/static/images/dev-components/extensions-video.png,
                  /chrome/static/images/dev-components/extensions-video-2x.png 2x"
      aria-hidden='true'
 width="84"  height="84"   />

</div>
<div
        class="chr-keyframe-animation__layer"
        style="
          --opacity: ;
          --scale: ;
          --width: 84;"
        data-props-keyframes='{&#34;enter&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 95, &#34;left&#34;: 95, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 73, &#34;left&#34;: 67, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 73, &#34;left&#34;: 67, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 95, &#34;left&#34;: 95, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}], &#34;enter@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 95, &#34;left&#34;: 95, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 73, &#34;left&#34;: 67, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 73, &#34;left&#34;: 67, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 95, &#34;left&#34;: 95, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}]}'>
<img
    class=""
      src="/chrome/static/images/dev-components/extensions-paint.png"
          srcset="/chrome/static/images/dev-components/extensions-paint.png,
                  /chrome/static/images/dev-components/extensions-paint-2x.png 2x"
      aria-hidden='true'
 width="84"  height="84"   />

</div>
<div
        class="chr-keyframe-animation__layer"
        style="
          --opacity: ;
          --scale: ;
          --width: 101;"
        data-props-keyframes='{&#34;enter&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 96, &#34;left&#34;: 5, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 77, &#34;left&#34;: 18, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 77, &#34;left&#34;: 18, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 95, &#34;left&#34;: 5, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}], &#34;enter@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 96, &#34;left&#34;: 5, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 77, &#34;left&#34;: 18, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}], &#34;exit@&#34;: [{&#34;keyframe&#34;: null, &#34;ratio&#34;: 0, &#34;top&#34;: 77, &#34;left&#34;: 18, &#34;opacity&#34;: 1, &#34;scale&#34;: 1}, {&#34;keyframe&#34;: null, &#34;ratio&#34;: 1, &#34;top&#34;: 95, &#34;left&#34;: 5, &#34;opacity&#34;: 0, &#34;scale&#34;: 0.9}]}'>
<img
    class=""
      src="/chrome/static/images/dev-components/extensions-person.png"
          srcset="/chrome/static/images/dev-components/extensions-person.png,
                  /chrome/static/images/dev-components/extensions-person-2x.png 2x"
      aria-hidden='true'
 width="101"  height="101"   />

</div>
</div>
</div></div>
  </div>
</section>
      <section  id="by-google"  class="chr-section js-section  spacer-05"  data-offset="{&#34;default&#34;: &#34;-40&#34;, &#34;phone&#34;: &#34;16&#34;, &#34;tablet&#34;: &#34;-56&#34;}" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;by-google&#34;, &#34;section_header&#34;: true}}" >
    <div class="chr-pill-section-title">
      <h2
    class="chr-headline-1"
      aria-label="The browser built by Google"
    style=""
 >

<span aria-hidden="true">
        The browser <span
      data-lottie="lottie-built"
      class="chr-heading-pill chr-heading-pill__pill-container chr-heading-pill__pill-container--yellow chr-heading-pill__pill-container--medium"
      data-type=""
      aria-hidden="true"
      data-comp="Pill"
      data-lottie-animation-delay=""
      data-remote-play=""
      data-text-animation-delay=""
      data-pause-state=""
      data-play-state="">
      <span class="chr-heading-pill__mock"></span>
<div
    style=""
    class="chr-lottie-animation chr-heading-pill__icon chr-heading-pill__icon"
    data-lottie-file="/chrome/static/lottie-animations/pills/Built.json"
    data-lottie-name="lottie-built"
    data-lottie-autoplay="false"
    data-container-selector=""
    data-lottie-loop="False"
    id="lottie-built"
>
  </div><span class="chr-heading-pill__pill-text ">
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 20ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                b</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 40ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                u</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 60ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                i</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 80ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                l</span>
<span
                class="chr-heading-pill__pill-char"
                style="--animation-delay: 100ms; --animation-duration: .7s; --animation-name: charBounceSlideInUp">
                t</span>
</span>
    </span>

 <span class="nowrap">by Google</span></span>

  </h2>

    </div>

<div class="chr-gallery  chr-gallery--3-up chr-grid-default-parent">
    <ul class="chr-gallery__container chr-grid-default">
<li class="chr-gallery-card chr-gallery-card--static chr-gallery-card--yellow chr-gallery-card--large chr-gallery-card--media-contained"
>
<div class="chr-gallery-card-cover chr-gallery-card-cover--static chr-gallery-card-cover--yellow chr-gallery-card-cover--large chr-gallery-card-cover--no-image chr-gallery-card-cover--media-contained">
<div class="chr-gallery-card-cover__text-wrapper">
<h3
    class="chr-eyebrow chr-gallery-card-cover__eyebrow"
    style=""
 >

GOOGLE SEARCH
  </h3>
<h4
    class="chr-headline-3     chr-gallery-card-cover__heading"
    style=""
 >

The search bar you love, built right in.
  </h4>
<div class="chr-gallery-card-cover__body-wrapper">
            <p class="chr-copy-xl chr-gallery-card-cover__body">
              Access a world of knowledge at your fingertips. Check the weather, solve math equations, and get instant search results, all contained inside your browser's <span class="nowrap">address bar</span>.</p>

</div>
</div>
<div class="chr-gallery-card-cover__image-wrapper chr-gallery-card-cover__image-wrapper--only-on-mobile">

<picture  data-comp="LazyLoader"  class="js-lazy-load" >
<source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1NDAgMzIwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMDgwIDY0MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/search_mobile.webp 1x, /chrome/static/images/v2/gallery/search_mobile-2x.webp 2x" type="image/webp">
  <source media="(max-width: 599px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1NDAgMzIwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMDgwIDY0MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/search_mobile.png 1x, /chrome/static/images/v2/gallery/search_mobile-2x.png 2x" type="image/png">
    <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMzAwIDM2OCc+PC9zdmc+ 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAyNjAwIDczNic+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/search_desktop.webp 1x, /chrome/static/images/v2/gallery/search_desktop-2x.webp 2x" type="image/webp">
      <source media="(min-width: 1024px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMzAwIDM2OCc+PC9zdmc+ 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAyNjAwIDczNic+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/search_desktop.png 1x, /chrome/static/images/v2/gallery/search_desktop-2x.png 2x" type="image/png">
    <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1NDAgMzIwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMDgwIDY0MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/search_tablet.webp 1x, /chrome/static/images/v2/gallery/search_tablet-2x.webp 2x" type="image/webp">
      <source media="(min-width: 600px)" srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1NDAgMzIwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMDgwIDY0MCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/search_tablet.png 1x, /chrome/static/images/v2/gallery/search_tablet-2x.png 2x" type="image/png">
        <img data-src="/chrome/static/images/v2/gallery/search_desktop.png" srcset="/chrome/static/images/v2/gallery/search_desktop.png" alt="A user typed "weather in Paris" into Chrome's address bar and it has instantly generated results." >
      </picture></div>
<div class="chr-gallery-card-cover__video-wrapper">
          <video
 id="video-google-search" muted loop playsinline    data-comp="Video  LazyLoader VideoController"
    preload="none"
    class="chr-video"
 data-props-videocontroller="{&#34;threshold&#34;: 0.5}"  aria-hidden="true"  poster="/chrome/static/images/v2/gallery/search_desktop-2x.webp"       width="1248"
      height="1248"
poster="/chrome/static/images/v2/gallery/search_desktop-2x.webp">
<source
        type="video/webm"
            data-src="/chrome/static/images/v2/gallery/search_predictive.webm"
>
<source
        type="video/mp4"
            data-src="/chrome/static/images/v2/gallery/search_predictive.mp4"
>
</video>

<button class="handler-media handler-media--button-text handler-media--sm handler-media     chr-background__light-blue-01 chr-video__handler-media-button chr-video__handler-media-button-bottom-right"
      data-id-media="video-google-search"
      data-comp="HandlerMedia"
 aria-label="Play animation" >
      <span class="handler-media__container">
<span data-media-state="play" class="handler-media__label handler-media__play" aria-hidden="false">
    <svg class="handler-media__icon"
    role="presentation "
    aria-hidden="True"
>

    <use xlink:href="/chrome/static/images/site-icons.svg#play-icon"></use>
</svg>
<span class="handler-media__text">
      Play animation</span>
  </span>
<span data-media-state="pause" class="handler-media__label handler-media__pause" aria-hidden="true">
    <svg class="handler-media__icon"
    role="presentation "
    aria-hidden="True"
>

    <use xlink:href="/chrome/static/images/site-icons.svg#pause-icon"></use>
</svg>
<span class="handler-media__text">
      Pause animation</span>
  </span>
<span data-media-state="replay" class="handler-media__label handler-media__replay" aria-hidden="true">
    <svg class="handler-media__icon"
    role="presentation "
    aria-hidden="True"
>

    <use xlink:href="/chrome/static/images/site-icons.svg#replay-icon"></use>
</svg>
<span class="handler-media__text">
      Replay animation</span>
  </span>
</span>
    </button>
<img
    class="chr-video"
 data-img-fallback=""     style="display: none;"/></div>
</div>
  </li>
<li class="chr-gallery-card chr-gallery-card--interactive chr-gallery-card--dark-yellow    chr-gallery-card--back-card-larger"
      data-comp="GalleryCard"
>
<div class="chr-gallery-card-cover chr-gallery-card-cover--interactive chr-gallery-card-cover--dark-yellow  chr-gallery-card-cover--no-image">
<div class="chr-gallery-card-cover__text-wrapper">
<h3
    class="chr-eyebrow chr-gallery-card-cover__eyebrow"
    style=""
 >

GOOGLE PAY
  </h3>
<h4
    class="chr-headline-3     chr-gallery-card-cover__heading"
    style=""
 >

Pay for things as quick as you&nbsp;click.
  </h4>
</div>
<div class="chr-gallery-card-cover__dynamic-images-wrapper">
<div class="chr-gallery-card-cover__image chr-gallery-card-cover__image--cover" style="--layer: 1;--type: cover;--scale_start: 1;--scale_end: 1;">
        
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/gpay-1.webp"  srcset="/chrome/static/images/v2/gallery/gpay-1.webp, /chrome/static/images/v2/gallery/gpay-1-2x.webp 2x"  aria-hidden='true'  width="614"  height="461"  /></div>
<div class="chr-gallery-card-cover__image chr-gallery-card-cover__image--cover" style="--layer: 2;--type: cover;--scale_start: 1;--scale_end: 1.1;">
        
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/gpay-2.webp"  srcset="/chrome/static/images/v2/gallery/gpay-2.webp, /chrome/static/images/v2/gallery/gpay-2-2x.webp 2x"  aria-hidden='true'  width="614"  height="461"  /></div>
</div>
<div class="chr-gallery-card-cover__static-images-wrapper">
      
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/gpay.webp"  srcset="/chrome/static/images/v2/gallery/gpay.webp, /chrome/static/images/v2/gallery/gpay-2x.webp 2x"  aria-hidden='true'  width="1280"  height="960"  /></div>
</div>
<button
    class="chr-action-icon chr-action-icon--card chr-action-icon--regular chr-action-icon--dark chr-gallery-card__action-icon"
 aria-label="Flip card"  tabindex="0" >

      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#plus"></use>
        </svg></button>
<div
    class="chr-gallery-card-back chr-gallery-card-back--interactive chr-gallery-card-back--dark-yellow">
<div class="chr-gallery-card-back__image-wrapper">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4=" data-src="/chrome/static/images/v2/gallery/gpay-back.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDcyMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/gpay-back.webp 1x, /chrome/static/images/v2/gallery/gpay-back-2x.webp 2x"  alt="Google Pay displays only the last four digits of three credit cards for security purposes."  /></div>
<div class="chr-gallery-card-back__text-wrapper">
<p class="chr-copy-xl chr-gallery-card-back__body">
          Google Pay makes it easy to pay online. When you securely store your payment info in your Google Account, you can stop typing your credit card and check out faster.</p>
</div>
  </div>
  </li>
<li class="chr-gallery-card chr-gallery-card--interactive chr-gallery-card--white"
      data-comp="GalleryCard"
>
<div class="chr-gallery-card-cover chr-gallery-card-cover--interactive chr-gallery-card-cover--white  chr-gallery-card-cover--no-image">
<div class="chr-gallery-card-cover__text-wrapper">
<h3
    class="chr-eyebrow chr-gallery-card-cover__eyebrow"
    style=""
 >

GOOGLE WORKSPACE
  </h3>
<h4
    class="chr-headline-3     chr-gallery-card-cover__heading"
    style=""
 >

Get things done, with or without <span class="nowrap">Wi-Fi.</span>
  </h4>
</div>
<div class="chr-gallery-card-cover__dynamic-images-wrapper">
<div class="chr-gallery-card-cover__image chr-gallery-card-cover__image--cover" style="--layer: 1;--type: cover;--scale_start: 1;--scale_end: 1;">
        
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/workspace-1.webp"  srcset="/chrome/static/images/v2/gallery/workspace-1.webp, /chrome/static/images/v2/gallery/workspace-1-2x.webp 2x"  aria-hidden='true'  width="612"  height="459"  /></div>
<div class="chr-gallery-card-cover__image chr-gallery-card-cover__image--cover" style="--layer: 2;--type: cover;--scale_start: 1;--scale_end: 1.3;">
        
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/workspace-2.webp"  srcset="/chrome/static/images/v2/gallery/workspace-2.webp, /chrome/static/images/v2/gallery/workspace-2-2x.webp 2x"  aria-hidden='true'  width="612"  height="459"  /></div>
</div>
<div class="chr-gallery-card-cover__static-images-wrapper">
      
<img class=""  loading="eager"  src="/chrome/static/images/v2/gallery/workspace.webp"  srcset="/chrome/static/images/v2/gallery/workspace.webp, /chrome/static/images/v2/gallery/workspace-2x.webp 2x"  aria-hidden='true'  width="267"  height="150"  /></div>
</div>
<button
    class="chr-action-icon chr-action-icon--card chr-action-icon--regular chr-action-icon--light chr-gallery-card__action-icon"
 aria-label="Flip card"  tabindex="0" >

      <svg class="chr-action-icon__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#plus"></use>
        </svg></button>
<div
    class="chr-gallery-card-back chr-gallery-card-back--interactive chr-gallery-card-back--white">
<div class="chr-gallery-card-back__image-wrapper">

<img class="js-lazy-load"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4=" data-src="/chrome/static/images/v2/gallery/offline.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA2NDAgMzYwJz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMjgwIDcyMCc+PC9zdmc+ 2x" data-srcset="/chrome/static/images/v2/gallery/offline.webp 1x, /chrome/static/images/v2/gallery/offline-2x.webp 2x"  alt="A toggle allows users to access their files while working offline."  /></div>
<div class="chr-gallery-card-back__text-wrapper">
<p class="chr-copy-xl chr-gallery-card-back__body">
          Get things done in Gmail, Google Docs, Google Slides, Google Sheets, Google Translate and Google Drive, even without an internet connection.</p>
</div>
  </div>
  </li>
</ul>
  </div>
</section>
      <section  id="faq"  class="chr-section js-section  chr-background-light-grey" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;faq&#34;, &#34;section_header&#34;: true}}" ><div class="chr-grid-default-parent">
    <div class="chr-grid-default">
      <div class="chr-accordion-group__wrapper">
<h2 class="chr-headline-2 chr-accordion-group__heading">Frequently asked questions</h2>
<div data-comp="CollapsibleGroup" class="chr-accordion-group"
            data-props-collapsiblegroup='{
              "group_id": "group_1",
              "scroll_to_collapsible": "false"
            }'>
<div data-comp="Collapsible" class="chr-accordion chr-accordion--faq" data-props-collapsible="{&#34;version&#34;: &#34;2&#34;, &#34;group_id&#34;: &#34;group_1&#34;}">
    <div id="faq-section-1" class="chr-accordion__hint-container js-collapsible__hint" role="button" tabindex="0" aria-controls="faq-section-1-collapsible" aria-expanded="false" tabindex="-1">
      <span class="chr-accordion__hint js-collapsible__hint-title">
<div class="chr-accordion__header">
            <h3 class="chr-accordion__heading chr-headline-4">How do I install Chrome?</h3>
          </div>

      <svg class="chr-icon chr-icon--32 chr-accordion__icon js-collapsible__icon" >
        <title></title>
        <use xlink:href="/chrome/static/images/site-icons.svg#plus-icon"></use>
        </svg></span>
    </div><div id="faq-section-1-collapsible" class="chr-accordion__panel js-collapsible__panel" role="region" aria-hidden="true">
      <div class="chr-accordion__content js-collapsible__panel_content" style="height: 0; padding:0;">
        <p class="chr-copy chr-accordion__description">To install Chrome, simply download the installation file, then look for it in your downloads folder. Open the file and follow the instructions. Once Chrome is installed, you can delete the install file. <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" href="https://support.google.com/chrome/answer/95346?co=GENIE.Platform%3DDesktop" target="_blank" rel="noopener noreferrer" class="chr-accordion__link">Learn more about downloading Chrome here</a>.</p>
</div>
    </div>
  </div><div data-comp="Collapsible" class="chr-accordion chr-accordion--faq" data-props-collapsible="{&#34;version&#34;: &#34;2&#34;, &#34;group_id&#34;: &#34;group_1&#34;}">
    <div id="faq-section-2" class="chr-accordion__hint-container js-collapsible__hint" role="button" tabindex="0" aria-controls="faq-section-2-collapsible" aria-expanded="false" tabindex="-1">
      <span class="chr-accordion__hint js-collapsible__hint-title">
<div class="chr-accordion__header">
            <h3 class="chr-accordion__heading chr-headline-4">Does Chrome work on my operating system?</h3>
          </div>

      <svg class="chr-icon chr-icon--32 chr-accordion__icon js-collapsible__icon" >
        <title></title>
        <use xlink:href="/chrome/static/images/site-icons.svg#plus-icon"></use>
        </svg></span>
    </div><div id="faq-section-2-collapsible" class="chr-accordion__panel js-collapsible__panel" role="region" aria-hidden="true">
      <div class="chr-accordion__content js-collapsible__panel_content" style="height: 0; padding:0;">
        <p class="chr-copy chr-accordion__description">Chrome is compatible with devices that run Windows and Mac operating systems, provided they meet the minimum system requirements. In order to install Chrome and receive adequate support, you must meet the system requirements. <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" href="https://support.google.com/chrome/a/answer/7100626?text=To%20use%20Chrome%20browser%20on,Windows%20Server%202016%20or%20later" target="_blank" rel="noopener noreferrer" class="chr-accordion__link">Learn more about using Chrome on your device</a>.</p>
</div>
    </div>
  </div><div data-comp="Collapsible" class="chr-accordion chr-accordion--faq" data-props-collapsible="{&#34;version&#34;: &#34;2&#34;, &#34;group_id&#34;: &#34;group_1&#34;}">
    <div id="faq-section-3" class="chr-accordion__hint-container js-collapsible__hint" role="button" tabindex="0" aria-controls="faq-section-3-collapsible" aria-expanded="false" tabindex="-1">
      <span class="chr-accordion__hint js-collapsible__hint-title">
<div class="chr-accordion__header">
            <h3 class="chr-accordion__heading chr-headline-4">How do I make Chrome my default browser?</h3>
          </div>

      <svg class="chr-icon chr-icon--32 chr-accordion__icon js-collapsible__icon" >
        <title></title>
        <use xlink:href="/chrome/static/images/site-icons.svg#plus-icon"></use>
        </svg></span>
    </div><div id="faq-section-3-collapsible" class="chr-accordion__panel js-collapsible__panel" role="region" aria-hidden="true">
      <div class="chr-accordion__content js-collapsible__panel_content" style="height: 0; padding:0;">
        <p class="chr-copy chr-accordion__description">You can set Chrome as your default browser on Windows or Mac operating systems as well as your iPhone, iPad or Android device. When you set Chrome as your default browser, any link you click will automatically open in Chrome. <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" href="https://support.google.com/chrome/answer/95417?co=GENIE.Platform%3DDesktop" target="_blank" rel="noopener noreferrer" class="chr-accordion__link">Find specific instructions for your device here</a>.</p>
</div>
    </div>
  </div><div data-comp="Collapsible" class="chr-accordion chr-accordion--faq" data-props-collapsible="{&#34;version&#34;: &#34;2&#34;, &#34;group_id&#34;: &#34;group_1&#34;}">
    <div id="faq-section-4" class="chr-accordion__hint-container js-collapsible__hint" role="button" tabindex="0" aria-controls="faq-section-4-collapsible" aria-expanded="false" tabindex="-1">
      <span class="chr-accordion__hint js-collapsible__hint-title">
<div class="chr-accordion__header">
            <h3 class="chr-accordion__heading chr-headline-4">What are Chrome's safety settings?</h3>
          </div>

      <svg class="chr-icon chr-icon--32 chr-accordion__icon js-collapsible__icon" >
        <title></title>
        <use xlink:href="/chrome/static/images/site-icons.svg#plus-icon"></use>
        </svg></span>
    </div><div id="faq-section-4-collapsible" class="chr-accordion__panel js-collapsible__panel" role="region" aria-hidden="true">
      <div class="chr-accordion__content js-collapsible__panel_content" style="height: 0; padding:0;">
        <p class="chr-copy chr-accordion__description">Chrome uses cutting-edge safety and security features to help you manage your safety. Use Safety Check to instantly audit for compromised passwords, safe browsing status and any available Chrome updates. <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" href="https://support.google.com/chrome/answer/10468685?co=GENIE.Platform%3DAndroid" target="_blank" rel="noopener noreferrer" class="chr-accordion__link">Learn more about safety and security on Chrome</a>.</p>
</div>
    </div>
  </div></div>
</div>
    </div>
  </div>
</section>
      <section  id="closing-banner"  class="chr-section js-section  chr-background-light-grey" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;closing-banner&#34;, &#34;section_header&#34;: true}}" ><div class="chr-grid-default-parent">
    <div class="chr-grid-default">
      <div class="chr-banner     chr-background__blue">
        <div class="chr-banner__content chr-text-align--center">

<h2
    class="chr-headline-1     chr-banner__title     chr-text-white"
    style=""
 >

Take your browser with you
  </h2>
<p
    class="chr-banner__body chr-copy-xl     chr-text-white"
    style=""
 >

Download Chrome on your mobile device or tablet and sign into your account for the same browser experience, everywhere.
  </p>
</div><span class="chr-banner__button">
<button type="button"
 id="js-download-now"        class="chr-download-button chr-download-button--hero chr-download-button--inverted js-download home-download-now"
data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;download_chrome_cta_click&#34;, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}" >
<span class="environment" data-environment="android|ios,ALL,ALL">Get Chrome</span>
<span class="environment" data-environment="not android|ios,ALL,ALL">Download Chrome</span>

      <svg class="chr-button__icon chr-download-button__icon" >
        <use xlink:href="/chrome/static/images/site-icons.svg#download"></use>
        </svg></button>

</span><div class="chr-banner__qr chr-text-align--center">
            <div class="chr-qr-code chr-qr-code--container">
      <div class="chr-qr-code__qr-container">

<img class="js-lazy-load  chr-qr-code__image"  data-comp="LazyLoader"  src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAyOTQgMjk0Jz48L3N2Zz4=" data-src="/chrome/static/images/v2/go-mobile-qrs/conversion-hp-take-it-with-you.webp"  srcset="data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAyOTQgMjk0Jz48L3N2Zz4= 1x, data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1ODggNTg4Jz48L3N2Zz4= 2x" data-srcset="/chrome/static/images/v2/go-mobile-qrs/conversion-hp-take-it-with-you.webp 1x, /chrome/static/images/v2/go-mobile-qrs/conversion-hp-take-it-with-you-2x.webp 2x"  alt="QR code to download chrome browser in mobile devices"  /></div>
<div class="chr-qr-code__content">
<p
    class="chr-caption chr-text-heading     chr-text-heading"
    style=""
 >

Get Chrome for your phone
  </p>
</div>
</div>
</div></div>
    </div>
  </div></section>
    </div><footer id="js-footer" class="chr-footer" data-comp="Footer">
<div class="chr-footer__social">
      <div class="chr-footer__social__container">
        <h3 class="chr-footer__social__title chr-headline-6"> Follow us</h3>
        <ul class="chr-footer__social__list">
<li>
<a href="https://www.youtube.com/user/googlechrome"  title="Youtube"  target="_blank" rel="noopener nofollow"  class=" chr-footer__social-link"  ga-on="click"  ga-event-category="chrome-footer-social"  ga-event-action="clicked" ga-event-label="follow-us:youtube" data-g-event="chrome-footer-social" data-g-action="clicked" data-g-label="follow-us:youtube"  data-g-cookieless="true" data-g-cookieless-name="social clicks" data-g-cookieless-category="footer" >
      <svg class="chr-icon chr-icon--24" >
        <title>Youtube</title>
        <use xlink:href="/chrome/static/images/site-icons.svg#social-youtube"></use>
        <image src="/chrome/static/images/fallback/icon-youtube.jpg" xlink:href="" alt="Youtube" width="24" height="24" class="svg-fallback"/>
        </svg></a></li>
<li>
<a href="https://twitter.com/googlechrome"  title="X"  target="_blank" rel="noopener nofollow"  class=" chr-footer__social-link"  ga-on="click"  ga-event-category="chrome-footer-social"  ga-event-action="clicked" ga-event-label="follow-us:x" data-g-event="chrome-footer-social" data-g-action="clicked" data-g-label="follow-us:x"  data-g-cookieless="true" data-g-cookieless-name="social clicks" data-g-cookieless-category="footer" >
      <svg class="chr-icon chr-icon--24" >
        <title>X</title>
        <use xlink:href="/chrome/static/images/site-icons.svg#social-twitter"></use>
        <image src="/chrome/static/images/fallback/icon-twitter.jpg" xlink:href="" alt="X" width="24" height="24" class="svg-fallback"/>
        </svg></a></li>
<li>
<a href="https://www.facebook.com/googlechrome/"  title="Facebook"  target="_blank" rel="noopener nofollow"  class=" chr-footer__social-link"  ga-on="click"  ga-event-category="chrome-footer-social"  ga-event-action="clicked" ga-event-label="follow-us:facebook" data-g-event="chrome-footer-social" data-g-action="clicked" data-g-label="follow-us:facebook"  data-g-cookieless="true" data-g-cookieless-name="social clicks" data-g-cookieless-category="footer" >
      <svg class="chr-icon chr-icon--24" >
        <title>Facebook</title>
        <use xlink:href="/chrome/static/images/site-icons.svg#social-facebook"></use>
        <image src="/chrome/static/images/fallback/icon-fb.jpg" xlink:href="" alt="Facebook" width="24" height="24" class="svg-fallback"/>
        </svg></a></li>
</ul>
      </div>
</div>
<div class="chr-footer__links">
      <nav class="chr-footer__links__grid">
<div class="chr-footer__links__group">
            <h4 class="chr-headline-6 chr-footer__links__heading js-footer-link" tabindex="0">
              Chrome Family<svg role="img" class="chr-icon chr-icon--24">
                <use xlink:href="/chrome/static/images/site-icons.svg#mi-expand"></use>
              </svg>
            </h4>
            <ul class="chr-footer__links__list">
<li class="chr-link chr-footer__links__list-item footer-other-platform">
                  <a
      href="#"
      class="chr-link chr-link--primary  chr-link--internal     chr-footer__link js-download"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 id="js-other-platform">
Other Platforms</a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://www.google.com/intl/en_US/chromebook/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chromebooks
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://www.google.com/chromecast/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chromecast
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://chromewebstore.google.com/?hl=en"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chrome Web 
                    <span class="chr-link-icon">Store
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
</ul>
          </div>
<div class="chr-footer__links__group">
            <h4 class="chr-headline-6 chr-footer__links__heading js-footer-link" tabindex="0">
              Enterprise<svg role="img" class="chr-icon chr-icon--24">
                <use xlink:href="/chrome/static/images/site-icons.svg#mi-expand"></use>
              </svg>
            </h4>
            <ul class="chr-footer__links__list">
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://chromeenterprise.google/browser/download/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Download Chrome 
                    <span class="chr-link-icon">Browser
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://chromeenterprise.google/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chrome Browser for 
                    <span class="chr-link-icon">Enterprise
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://chromeenterprise.google/devices/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chrome 
                    <span class="chr-link-icon">Devices
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://chromeenterprise.google/os/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
ChromeOS
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://cloud.google.com/?hl=en"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Google 
                    <span class="chr-link-icon">Cloud
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://gsuite.google.com/?hl=en"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Google 
                    <span class="chr-link-icon">Workspace
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
</ul>
          </div>
<div class="chr-footer__links__group">
            <h4 class="chr-headline-6 chr-footer__links__heading js-footer-link" tabindex="0">
              Education<svg role="img" class="chr-icon chr-icon--24">
                <use xlink:href="/chrome/static/images/site-icons.svg#mi-expand"></use>
              </svg>
            </h4>
            <ul class="chr-footer__links__list">
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://edu.google.com/intl/en_US/products/more-products/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Google for 
                    <span class="chr-link-icon">Education
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://edu.google.com/intl/en_US/chromebooks/find-a-chromebook/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Devices for 
                    <span class="chr-link-icon">schools
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
</ul>
          </div>
<div class="chr-footer__links__group">
            <h4 class="chr-headline-6 chr-footer__links__heading js-footer-link" tabindex="0">
              Dev and Partners<svg role="img" class="chr-icon chr-icon--24">
                <use xlink:href="/chrome/static/images/site-icons.svg#mi-expand"></use>
              </svg>
            </h4>
            <ul class="chr-footer__links__list">
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://www.chromium.org/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chromium
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://www.chromium.org/chromium-os"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
ChromeOS
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://developer.chrome.com/webstore/?hl=en"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chrome Web 
                    <span class="chr-link-icon">Store
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://www.chromeexperiments.com/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chrome 
                    <span class="chr-link-icon">Experiments
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="/chrome/beta/"
      class="chr-link chr-link--primary  chr-link--internal     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
>
Chrome Beta</a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="/chrome/dev/"
      class="chr-link chr-link--primary  chr-link--internal     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
>
Chrome Dev</a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="/chrome/canary/"
      class="chr-link chr-link--primary  chr-link--internal     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
>
Chrome Canary</a>
</li>
</ul>
          </div>
<div class="chr-footer__links__group">
            <h4 class="chr-headline-6 chr-footer__links__heading js-footer-link" tabindex="0">
              Support<svg role="img" class="chr-icon chr-icon--24">
                <use xlink:href="/chrome/static/images/site-icons.svg#mi-expand"></use>
              </svg>
            </h4>
            <ul class="chr-footer__links__list">
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://support.google.com/chrome/?hl=en&amp;rd=3#topic=7438008"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Chrome 
                    <span class="chr-link-icon">Help
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="/chrome/update/"
      class="chr-link chr-link--primary  chr-link--internal     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
>
Update Chrome</a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="/chrome/tips/"
      class="chr-link chr-link--primary  chr-link--internal     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
>
Chrome Tips</a>
</li>
<li class="chr-link chr-footer__links__list-item">
                  <a
      href="https://blog.google/products/chrome/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Google Chrome 
                    <span class="chr-link-icon">Blog
      <svg class="chr-link__icon"  aria-hidden="true" >
        <use xlink:href="/chrome/static/images/site-icons.svg#arrow-external"></use>
        </svg></span></a>
</li>
</ul>
          </div>
</nav>
    </div>
<div class="chr-footer__bottom">
    <div class="chr-footer__glinks-wrapper">
      <div class="chr-footer__logo">
        
<a href="https://www.google.com"  title="Google"  target="_blank" rel="noopener"  ga-event-category="footer"  ga-event-action="logo-click" ga-event-label="https://www.google.com" data-g-event="" data-g-action="logo-click" data-g-label="https://www.google.com" >
      <svg class="chr-footer__logo-img" >
        <title>Google</title>
        <use xlink:href="/chrome/static/images/site-icons.svg#google-logo"></use>
        <image src="/chrome/static/images/fallback/google-footer-logo.jpg" xlink:href="" alt="Google Logo" width="86" height="28" class="svg-fallback"/>
        </svg></a></div>
      <nav class="chr-footer__glinks">
        <ul class="chr-footer__glinks-list">
<li class="chr-footer__glinks-list-item">
              <a
      href="https://policies.google.com/privacy?hl=en&amp;hl=en"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Privacy and Terms</a>
</li>
<li class="chr-footer__glinks-list-item">
              <a
      href="https://about.google/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
About Google</a>
</li>
<li class="chr-footer__glinks-list-item">
              <a
      href="https://about.google/products/"
      class="chr-link chr-link--primary  chr-link--external     chr-footer__link"
      data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;nav_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: true, &#34;section_header&#34;: true}}"
 rel="noopener"  target="_blank" >
Google Products</a>
</li>
<li class="chr-footer-glinks-list-item">
            <button aria-hidden="true" class="glue-cookie-notification-bar-control chr-footer__link chr-footer__link--management">
              Cookies management controls
            </button>
          </li>
</ul>
      </nav>
    </div>

      <ul class="chr-footer__help-language">
        <li>

<a href="https://support.google.com/chrome/?hl=en&amp;rd=3#topic=7438008"  target="_blank" rel="noopener"  class=" chr-footer__link chr-link--nav"  ga-event-category="nav-subfooter"  ga-event-action="" ga-event-label="help" data-g-event="nav-subfooter" data-g-action="" data-g-label="help" >
      <svg class="chr-icon chr-icon--24" >
        <title>Help</title>
        <use xlink:href="/chrome/static/images/site-icons.svg#mi-help"></use>
        <image src="/chrome/static/images/fallback/icon-help.jpg" xlink:href="" alt="Help" width="20" height="21" class="svg-fallback"/>
        </svg>Help</a></li>
<li class="chr-footer__language-dropdown">
          <label for="language-selector">Change language or region</label>
          <select class="chr-headline-6 chr-footer__language-dropdown__select js-footer-language-select" id="language-selector"
               ga-on="click"  ga-event-category="chrome-footer-dropdown"  ga-event-action="clicked" ga-event-label="footer:select-language" data-g-event="chrome-footer-dropdown" data-g-action="clicked" data-g-label="footer:select-language" >
<option value="/intl/ca/chrome/" >
Català - Espanya</option>
<option value="/intl/da/chrome/" >
Dansk - Danmark</option>
<option value="/intl/de/chrome/" >
Deutsch - Deutschland</option>
<option value="/intl/et/chrome/" >
Eesti - Eesti</option>
<option value="/intl/en_au/chrome/" >
English - Australia</option>
<option value="/intl/en_ca/chrome/" >
English - Canada</option>
<option value="/intl/en_uk/chrome/" >
English - United Kingdom</option>
<option value="/intl/en_hk/chrome/" >
English - Hong Kong SAR China</option>
<option value="/intl/en_ie/chrome/" >
English - Ireland</option>
<option value="/intl/en_in/chrome/" >
English - India</option>
<option value="/intl/en_ph/chrome/" >
English - Philippines</option>
<option value="/intl/en_pk/chrome/" >
English - Pakistan</option>
<option value="/intl/en_sg/chrome/" >
English - Singapore</option>
<option value="/intl/en_us/chrome/"  selected>
English - United States</option>
<option value="/intl/es-419/chrome/" >
Español - Latinoamérica</option>
<option value="/intl/es_es/chrome/" >
Español - España</option>
<option value="/intl/es_us/chrome/" >
Español - Estados Unidos</option>
<option value="/intl/fil/chrome/" >
Filipino - Pilipinas</option>
<option value="/intl/fr_ca/chrome/" >
Français - Canada</option>
<option value="/intl/fr/chrome/" >
Français - France</option>
<option value="/intl/hr/chrome/" >
Hrvatski - Hrvatska</option>
<option value="/intl/id/chrome/" >
Indonesia - Indonesia</option>
<option value="/intl/it/chrome/" >
Italiano - Italia</option>
<option value="/intl/lv/chrome/" >
Latviešu - Latvija</option>
<option value="/intl/lt/chrome/" >
Lietuvių - Lietuva</option>
<option value="/intl/hu/chrome/" >
Magyar - Magyarország</option>
<option value="/intl/ms/chrome/" >
Melayu - Malaysia</option>
<option value="/intl/nl/chrome/" >
Nederlands - Nederland</option>
<option value="/intl/no/chrome/" >
Norsk Bokmål - Norge</option>
<option value="/intl/pl/chrome/" >
Polski - Polska</option>
<option value="/intl/pt-PT/chrome/" >
Português - Portugal</option>
<option value="/intl/pt-BR/chrome/" >
Português - Brasil</option>
<option value="/intl/ro/chrome/" >
Română - România</option>
<option value="/intl/sk/chrome/" >
Slovenčina - Slovensko</option>
<option value="/intl/sl/chrome/" >
Slovenščina - Slovenija</option>
<option value="/intl/fi/chrome/" >
Suomi - Suomi</option>
<option value="/intl/sv/chrome/" >
Svenska - Sverige</option>
<option value="/intl/vi/chrome/" >
Tiếng Việt - Việt Nam</option>
<option value="/intl/tr/chrome/" >
Türkçe - Türkiye</option>
<option value="/intl/cs/chrome/" >
Čeština - Česko</option>
<option value="/intl/el/chrome/" >
Ελληνικά - Ελλάδα</option>
<option value="/intl/bg/chrome/" >
Български - България</option>
<option value="/intl/ru/chrome/" >
Русский - Россия</option>
<option value="/intl/sr/chrome/" >
Српски - Србија</option>
<option value="/intl/uk/chrome/" >
Українська - Україна</option>
<option value="/intl/iw/chrome/" >
עברית</option>
<option value="/intl/ar/chrome/" >
العربية - المملكة العربية السعودية</option>
<option value="/intl/fa/chrome/" >
فارسی</option>
<option value="/intl/hi/chrome/" >
हिन्दी - भारत</option>
<option value="/intl/th/chrome/" >
ไทย - ไทย</option>
<option value="/intl/zh-CN/chrome/" >
简体中文</option>
<option value="/intl/zh-HK/chrome/" >
繁體中文 (香港)</option>
<option value="/intl/zh-TW/chrome/" >
繁體中文 (台灣)</option>
<option value="/intl/ja/chrome/" >
日本語 - 日本</option>
<option value="/intl/ko/chrome/" >
한국어 - 대한민국</option>
</select>
        </li>
</ul>
    </div>
  </div>
  <div class="chr-footer__jumplinks-mobile-container">
  </div>
  <div class="chr-footer__glue-cookie-banner">
  </div>
</footer>
<div class="chr-modal" id="js-eula-modal">
  <div class="chr-modal-dialog__overlay js-eula-overlay" data-ga-config="{&#34;elementIsVisible&#34;: {&#34;event&#34;: &#34;module_impression&#34;, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}" ></div>
  <div class="chr-modal-dialog eula js-eula-dialog shadow-elevation-2" aria-hidden="true" role="dialog" tabindex="0" aria-modal="true" aria-labelledby="js-eula-content" aria-describedby="js-eula-content">
    <div class="chr-modal-dialog__header">
      
<img class=""  src="/chrome/static/images/chrome-logo-landscape.svg"  loading="lazy"  role="img"  loading="lazy"  alt="Google Chrome logo."  />
  <button type="button" class="chr-cta__button chr-cta__button-- chr-modal-dialog__close no-text js-eula-close"  ga-on="click"  ga-event-category="eula"  ga-event-action="clicked" ga-event-label="eula-close" data-g-event="eula" data-g-action="clicked" data-g-label="eula-close"  tabindex="-1" >
      <svg class="chr-icon" >
        <title></title>
        <use xlink:href="/chrome/static/images/site-icons.svg#close-btn"></use>
        </svg>
</button></div>
    <div id="js-eula-content" class="eula-content stablechannel shadow-elevation-2" aria-hidden="true">
<div class="chr-platform-list chr-modal-dialog__headline">
<div class="os os-win" aria-hidden="true">
          <h2 id="eula-u-h-os-win" class="chr-headline-3">Get Chrome for Windows</h2>
<div
            id="eula-u-d-channel-win-stable"
            class="platform channel-win-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For Windows 10 32-bit</p>

</div>

<div
            id="eula-u-d-channel-win64-stable"
            class="platform channel-win64-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For Windows 11/10 64-bit</p>

</div>

<div
            id="eula-u-d-channel-win_arm64-stable"
            class="platform channel-win_arm64-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For Windows 11 ARM</p>

</div>

<div
            id="eula-u-d-channel-win49-stable"
            class="platform channel-win49-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For Windows XP/Vista</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

<div
            id="eula-u-d-channel-win110-stable"
            class="platform channel-win110-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For Windows 8.1/8/7 32-bit</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

<div
            id="eula-u-d-channel-win110_64-stable"
            class="platform channel-win110_64-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For Windows 8.1/8/7 64-bit</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

</div>
<div class="os os-mac" aria-hidden="true">
          <h2 id="eula-u-h-os-mac" class="chr-headline-3">Get Chrome for Mac</h2>
<div
            id="eula-u-d-channel-mac-stable"
            class="platform channel-mac-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For macOS 11 or later.</p>

</div>

<div
            id="eula-u-d-channel-mac49-stable"
            class="platform channel-mac49-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
This computer will no longer receive Google Chrome updates because macOS 10.6 - 10.12 are no longer supported.</p>

</div>

<div
            id="eula-u-d-channel-mac65-stable"
            class="platform channel-mac65-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
This computer will no longer receive Google Chrome updates because macOS 10.6 - 10.12 are no longer supported.</p>

</div>

<div
            id="eula-u-d-channel-mac88-stable"
            class="platform channel-mac88-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
This computer will no longer receive Google Chrome updates because macOS 10.6 - 10.12 are no longer supported.</p>

</div>

<div
            id="eula-u-d-channel-mac104-stable"
            class="platform channel-mac104-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
This computer will no longer receive Google Chrome updates because macOS 10.6 - 10.12 are no longer supported.</p>

</div>

<div
            id="eula-u-d-channel-mac116-stable"
            class="platform channel-mac116-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For macOS 10.13/10.14<br/>
</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

<div
            id="eula-u-d-channel-mac129-stable"
            class="platform channel-mac129-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
For macOS 10.15<br/>
</p>

<div class="chr-warning-by-env chr-warning-by-env--no-enviroment">
    <div class="chr-warning-by-env__logo">
      
<img class=""  src="/chrome/static/images/icons/frozen_deprecation_icon.svg"  role="img"  aria-hidden='true'  data-img-fallback="/chrome/static/images/fallback/frozen_deprecation_icon.png"  /></div>
    <div class="chr-warning-by-env__text-wrapper">
      <p class="chr-warning-by-env__text chr-caption">This device won’t receive updates because Google Chrome no longer supports your operating system.</p>
    </div>
  </div>
</div>

</div>
<div class="os os-linux" aria-hidden="true">
          <h2 id="eula-u-h-os-linux" class="chr-headline-3">Get Chrome for Linux</h2>
<div
            id="eula-u-d-channel-linux-stable"
            class="platform channel-linux-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
Debian/Ubuntu/Fedora/openSUSE.</p>

</div>

<div class="packages-wrapper">
            <p id="linux-distro-options-label"
                class="chr-caption packages-wrapper__title">Please select your download package:</p>
            <fieldset aria-labelledby="linux-distro-options-label">
              
<div class="chr-radio">
<input id="js-linux-ubuntu"  name="linux-distro-options"  checked="checked"  aria-checked="true"  tabindex="0"  data-form-field type="radio" class="chr-radio__input js-linux-distro-options"  value="linux_ubuntu_x86_64">
<label for="js-linux-ubuntu" class="chr-radio__label chr-caption">64 bit .deb (For Debian/Ubuntu)</label></div>
<div class="chr-radio">
<input id="js-linux-fedora"  name="linux-distro-options"  aria-checked="false"  tabindex="-1"  data-form-field type="radio" class="chr-radio__input js-linux-distro-options"  value="linux_fedora_x86_64">
<label for="js-linux-fedora" class="chr-radio__label chr-caption">64 bit .rpm (For Fedora/openSUSE)</label></div></fieldset>
<p class="chr-caption packages-wrapper__others">Not Debian/Ubuntu or Fedora/openSUSE? There may be a community-supported version for your distribution. <a id="js-linux-community" rel="noopener" target="_blank" href="https://chromium.googlesource.com/chromium/src/+/refs/heads/main/docs/linux/chromium_packages.md">See Linux Chromium packages</a></p>
</div>
</div>
<div class="os os-ios" aria-hidden="true">
          <h2 id="eula-u-h-os-ios" class="chr-headline-3">Get Chrome for iOS</h2>
<div
            id="eula-u-d-channel-ios-stable"
            class="platform channel-ios-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
</p>

</div>

</div>
<div class="os os-chromeOS" aria-hidden="true">
          <h2 id="eula-u-h-os-chromeOS" class="chr-headline-3">Get Chrome for chromeOS</h2>
<div
            id="eula-u-d-channel-chromeOS-stable"
            class="platform channel-chromeOS-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
</p>

</div>

</div>
<div class="os os-android" aria-hidden="true">
          <h2 id="eula-u-h-os-android" class="chr-headline-3">Get Chrome for android</h2>
<div
            id="eula-u-d-channel-android-stable"
            class="platform channel-android-stable"
            aria-hidden="true">
            <p
              class="chr-copy-xl">
</p>

</div>

</div>
</div><div class="chr-modal-dialog__content">
<div class="chr-modal-dialog__opt-in os os-win" aria-hidden="true">

<div class="chr-checkbox default-browser-opt default-fallback" >
<input id="js-default-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input"  value="set">
<label for="js-default-cb" class="chr-checkbox__label chr-caption">Set Google Chrome as my default browser</label>
</div>
<div class="chr-checkbox " >
<input id="js-stats-cb"  checked="checked"  data-form-field type="checkbox" class="chr-checkbox__input"  value="">
<label for="js-stats-cb" class="chr-checkbox__label chr-caption">Help make Google Chrome better by automatically sending usage statistics and crash reports to Google.
                    <a href="https://www.google.com/support/chrome/bin/answer.py?answer=96817&amp;hl=en" class="chr-link   chr-link--inline" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  target="_blank" >What are crash reports?</a></label>
</div></div>
<div class="os os-linux disclaimer" aria-hidden="true">
              <p class="chr-caption"><strong>Note:</strong> Installing Google Chrome will <strong>add the Google repository</strong> so your system will automatically keep Google Chrome up to date. If you don’t want Google's repository, do “sudo touch /etc/default/google-chrome” before installing the package.</p>
            </div>
<p class="js-simplified-legal-links chr-caption">By downloading Chrome, you agree to the <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}" class="chr-link chr-link--inline chr-link--external" href="https://policies.google.com/terms" target="_blank" rel="noopener" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="terms:google">Google Terms of Service</a>&nbsp;and <a data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}" class="chr-link chr-link--inline chr-link--external" href="/chrome/terms/"  rel="noopener" target="_blank" ga-on="click" ga-event-category="chrome-body-link" ga-event-action="clicked" ga-event-label="eula:simplified-download-privacy">Chrome and ChromeOS Additional Terms of Service</a></p>
<div class="chr-modal-dialog__buttons">
<div class="os os-win" aria-hidden="true">
<button type="button"
 id="js-accept-install--win"        class="chr-download-button chr-download-button--header blue js-accept-install"
 ga-on="click"  ga-event-category="eula"  ga-event-action="clicked" ga-event-label="eula-acceptinstall" data-g-event="eula" data-g-action="clicked" data-g-label="eula-acceptinstall" >
Accept and Install</button>

<span id="js-throbber" class="throbber">
                  
<img class="download-spinner"  class=""  src="/chrome/static/images/chrome_throbber_fast.gif"  srcset="/chrome/static/images/chrome_throbber_fast.gif, /chrome/static/images/chrome_throbber_fast-2x.gif 2x"  alt="loading"  title="loading"  /></span>
</div>
<div class="os os-linux" aria-hidden="true">
<button type="button"
 id="js-accept-install--linux"        class="chr-download-button chr-download-button--header blue js-accept-install"
 ga-on="click"  ga-event-category="eula"  ga-event-action="clicked" ga-event-label="eula-acceptinstall" data-g-event="eula" data-g-action="clicked" data-g-label="eula-acceptinstall" >
Accept and Install</button>

<span id="js-throbber" class="throbber">
                  
<img class="download-spinner"  class=""  src="/chrome/static/images/chrome_throbber_fast.gif"  srcset="/chrome/static/images/chrome_throbber_fast.gif, /chrome/static/images/chrome_throbber_fast-2x.gif 2x"  alt="loading"  title="loading"  /></span>
</div>
<div class="os os-mac" aria-hidden="true">
<button type="button"
 id="js-accept-install--mac"        class="chr-download-button chr-download-button--header blue js-accept-install"
 ga-on="click"  ga-event-category="eula"  ga-event-action="clicked" ga-event-label="eula-acceptinstall" data-g-event="eula" data-g-action="clicked" data-g-label="eula-acceptinstall" >
Accept and Install</button>

<span id="js-throbber" class="throbber">
                  
<img class="download-spinner"  class=""  src="/chrome/static/images/chrome_throbber_fast.gif"  srcset="/chrome/static/images/chrome_throbber_fast.gif, /chrome/static/images/chrome_throbber_fast-2x.gif 2x"  alt="loading"  title="loading"  /></span>
</div>
</div>
      </div>
    </div>
    <div id="js-other-platform-modal" class="other-platform stablechannel" aria-hidden="true">
<h2 class="chr-headline-3">Get Chrome</h2>
      <div class="chr-modal-dialog__content">
<div class="other-platform__wrapper">
<p class="chr-copy ">Download for phone or tablet</p>
            <ul >
<li class="other-android">
<a href="//play.google.com/store/apps/details?id=com.android.chrome&amp;pcampaignid=websitedialog"  id="js-other-android" target="_blank" rel="noopener"  class=" chr-link" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:android" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:android" >Android</a></li>
<li class="other-ios">
<a href="//itunes.apple.com/us/app/chrome/id535886823"  id="js-other-ios" target="_blank" rel="noopener"  class=" chr-link" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:ios" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:ios" >iOS</a></li>
</ul>
          </div>
<div class="other-platform__wrapper">
<p class="chr-copy ">Download for another desktop OS</p>
            <ul >
<li class="other-win64">
<a href="#"  id="js-other-win64" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:windows-10-to-11-64-bit" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:windows-10-to-11-64-bit" >Windows 11/10 64-bit</a></li>
<li class="other-win">
<a href="#"  id="js-other-win" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:windows-10-32-bit" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:windows-10-32-bit" >Windows 10 32-bit</a></li>
<li class="other-win_arm64">
<a href="#"  id="js-other-win_arm64" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:windows-11-arm-64-bit" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:windows-11-arm-64-bit" >Windows 11 ARM</a></li>
<li class="other-mac">
<a href="#"  id="js-other-mac" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:mac-10-10-later" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:mac-10-10-later" >macOS 11 or later</a></li>
<li class="other-linux">
<a href="#"  id="js-other-linux" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:linux" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:linux" >Linux</a></li>
</ul>
          </div>
<div class="other-platform__wrapper">
<p class="chr-copy frozen-version">Frozen versions</p>
            <ul  class="frozen-version" >
<li class="other-winXP">
<a href="#"  id="js-other-xp" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:windows-xp" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:windows-xp" >Windows XP</a></li>
<li class="other-winVista">
<a href="#"  id="js-other-vista" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:windows-vista" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:windows-vista" >Windows Vista</a></li>
<li class="other-win110">
<a href="#"  id="js-other-win110" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:windows-7-8" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:windows-7-8" >Windows 8.1/8/7 32-bit</a></li>
<li class="other-win110_64">
<a href="#"  id="js-other-win110_64" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:windows-7-8_64" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:windows-7-8_64" >Windows 8.1/8/7 64-bit</a></li>
<li class="other-mac49">
<a href="#"  id="js-other-mac49" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:mac-10-6-to-10-8" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:mac-10-6-to-10-8" >Mac 10.6 - 10.8</a></li>
<li class="other-mac65">
<a href="#"  id="js-other-mac65" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:mac-10-9" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:mac-10-9" >Mac 10.9</a></li>
<li class="other-mac88">
<a href="#"  id="js-other-mac88" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:mac-10-10" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:mac-10-10" >Mac 10.10</a></li>
<li class="other-mac104">
<a href="#"  id="js-other-mac104" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:mac-10-11-to-10-12" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:mac-10-11-to-10-12" >Mac 10.11 - 10.12</a></li>
<li class="other-mac116">
<a href="#"  id="js-other-mac116" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:mac-10-13-to-10-14" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:mac-10-13-to-10-14" >Mac 10.13 - 10.14</a></li>
<li class="other-mac129">
<a href="#"  id="js-other-mac129" class=" chr-link js-other-option" data-ga-config="{&#34;click&#34;: {&#34;event&#34;: &#34;general_link_click&#34;, &#34;link_url&#34;: true, &#34;link_text&#34;: true, &#34;link_type&#34;: true, &#34;module_name&#34;: &#34;eula modal&#34;, &#34;section_header&#34;: &#34;Get Chrome&#34;}}"  ga-on="click"  ga-event-category="chrome-cta-button"  ga-event-action="clicked" ga-event-label="other-platforms:mac-10-15" data-g-event="chrome-cta-button" data-g-action="clicked" data-g-label="other-platforms:mac-10-15" >Mac 10.15</a></li>
</ul>
          </div>
</div>
    </div>
<div id="js-chrome-os-modal" class="chrome-os stablechannel" aria-hidden="true">
<h2 class="chr-headline-3">Looks like you’re already using Chrome browser. Nice!</h2>
          <div class="chr-modal-dialog__content">
<p class="chr-copy">The device you have runs on ChromeOS, which already has Chrome browser built-in. No need to manually install or update it — with automatic updates, you’ll always get the latest version. <a id="js-chrome-os-update" href="/chrome/browser-tools/">Learn more about automatic updates.</a>
</p>
<h3 class="chr-headline-5">Looking for Chrome for a different operating system?</h3>
<p class="chr-copy">See the <a id="js-chrome-os-other" href="#" class="js-download">full list of supported operating systems</a>.
</p>
</div>
</div>
</div>
</div><svg class="svg-assets" xmlns="http://www.w3.org/2000/svg">
  <!-- material design icons -->

  <symbol id="color-google-logo-2023" viewBox="0 0 160 44" fill="none">
    <g>
      <path
      d="M21.5285 32.2928C17.5957 32.2928 14.1563 30.1839 12.2781 27.0347L8.5 20.5L2.88396 10.7643L2.88224 10.7617L2.88052 10.7643C1.04887 13.9307 0 17.6069 0 21.5286C0 33.4183 9.63875 43.0571 21.5285 43.0571L30.7789 27.0347C28.9008 30.1839 25.4614 32.2928 21.5285 32.2928Z"
      fill="url(#linear-gradient)"/>
      <path
      d="M40.1773 10.7646H38H21.5293C27.4746 10.7646 32.2936 15.5836 32.2936 21.5289C32.2936 23.4338 31.7984 25.2223 30.9304 26.775C30.8813 26.8628 30.8313 26.9489 30.7797 27.035L21.5293 43.0574C33.4191 43.0574 43.0578 33.4187 43.0578 21.5289C43.0578 17.6073 42.009 13.9311 40.1773 10.7646Z"
      fill="url(#linear-gradient-2)"/>
      <path
      d="M40.1736 10.7643C36.4501 4.33068 29.4964 0 21.5291 0C13.5618 0 6.60639 4.3281 2.88281 10.7617L7 18L12.128 26.7746C11.26 25.222 10.7648 23.4334 10.7648 21.5285C10.7648 15.5832 15.5838 10.7643 21.5291 10.7643H40.1736Z"
      fill="url(#linear-gradient-3)"/>
      <path
      d="M32.2922 21.5289C32.2922 15.5836 27.4733 10.7646 21.5279 10.7646C15.5826 10.7646 10.7637 15.5836 10.7637 21.5289C10.7637 23.4338 11.2588 25.2223 12.1269 26.775L12.2776 27.035C14.1557 30.1842 17.5951 32.2932 21.5279 32.2932C25.4608 32.2932 28.9002 30.1842 30.7783 27.035L30.929 26.775C31.797 25.2223 32.2922 23.4338 32.2922 21.5289Z"
      fill="white"/>
      <path
      d="M30.3546 21.5288C30.3546 16.6539 26.4028 12.7021 21.5279 12.7021C16.6529 12.7021 12.7012 16.6539 12.7012 21.5288C12.7012 23.091 13.1076 24.5575 13.8189 25.8302L13.9421 26.0438C15.4827 28.6255 18.3029 30.3555 21.5279 30.3555C24.7528 30.3555 27.5731 28.6255 29.1137 26.0438L29.2368 25.8302C29.9481 24.5575 30.3546 23.091 30.3546 21.5288Z"
      fill="#1A73E8"/>
      <path
      d="M59.91 30.4147C59.2374 30.4147 58.5812 30.2631 57.9758 29.9772C57.3705 29.6913 56.8322 29.2875 56.3948 28.7828C55.4191 27.6392 54.8809 26.1589 54.8809 24.6451C54.8809 23.1312 55.4191 21.6681 56.3948 20.5073C56.8322 20.0026 57.3705 19.5988 57.9758 19.3129C58.5812 19.027 59.2374 18.8754 59.91 18.8754C60.8856 18.8246 61.8441 19.0941 62.6518 19.6323C63.4596 20.1706 64.065 20.9611 64.3844 21.886L67.1427 20.7424C66.6045 19.4308 65.696 18.2864 64.5524 17.4459C63.2408 16.5374 61.6762 16.0664 60.0951 16.1344C58.9851 16.1008 57.8914 16.3023 56.8658 16.7234C55.8402 17.1437 54.9145 17.7826 54.1575 18.5731C53.3842 19.38 52.7952 20.3393 52.3913 21.3822C51.9875 22.425 51.8023 23.5523 51.8359 24.6623C51.8023 25.7895 51.9875 26.8995 52.3913 27.9424C52.778 28.9852 53.3834 29.9437 54.1575 30.7514C54.9308 31.5583 55.8393 32.1809 56.8658 32.6011C57.8914 33.0214 58.9851 33.2237 60.0951 33.1902C61.6925 33.241 63.2736 32.7699 64.6023 31.8278C65.7795 31.0037 66.688 29.8601 67.2426 28.5477L64.5687 27.4041C63.711 29.4054 62.1644 30.4147 59.91 30.4147Z"
      fill="#5F6368"/>
      <path
      d="M77.8252 16.1176C76.7659 16.1176 75.724 16.4035 74.8163 16.9253C73.9758 17.3628 73.2534 18.0353 72.7659 18.8603H72.6316L72.7659 16.6394V9.30078H69.752V32.6377H72.7659V23.7912C72.7487 22.5296 73.169 21.3189 73.9423 20.3432C74.2953 19.8721 74.7483 19.4855 75.2865 19.2332C75.8239 18.9645 76.412 18.8465 77.0002 18.8629C79.6379 18.8629 80.9476 20.3432 80.9476 23.3029V32.6377H83.9616V22.8465C83.9616 20.7944 83.4234 19.1626 82.3814 17.9509C81.3214 16.723 79.8092 16.1176 77.8252 16.1176Z"
      fill="#5F6368"/>
      <path
      d="M95.2561 16.2631C94.2795 16.2966 93.3366 16.5989 92.5116 17.1371C91.6358 17.6082 90.9796 18.3642 90.6257 19.2891H90.506V16.7057H87.5781V32.6368H90.5921L90.6257 23.8781C90.5921 23.2564 90.6929 22.651 90.9116 22.0628C91.1303 21.4747 91.467 20.9537 91.8881 20.4999C92.2584 20.096 92.6968 19.7774 93.2014 19.5586C93.6896 19.3399 94.2451 19.2228 94.7842 19.2228C95.3232 19.2056 95.8614 19.29 96.3669 19.4579L97.5122 16.6506C96.8052 16.381 96.0311 16.2467 95.2561 16.2631Z"
      fill="#5F6368"/>
      <path
      d="M113.276 21.3826C112.889 20.3397 112.284 19.3813 111.51 18.5735C109.946 17.0097 107.81 16.1348 105.59 16.1348C103.37 16.1348 101.25 17.0097 99.6693 18.5735C98.896 19.3976 98.2897 20.3397 97.9031 21.3826C97.4992 22.4254 97.314 23.5526 97.3476 24.6626C97.314 25.7899 97.4992 26.8999 97.9031 27.9427C98.2897 28.9856 98.8951 29.944 99.6693 30.7518C101.233 32.3156 103.37 33.1905 105.59 33.1905C107.81 33.1905 109.929 32.3156 111.51 30.7518C112.267 29.9449 112.872 28.9856 113.276 27.9427C113.68 26.8999 113.865 25.7727 113.832 24.6626C113.865 23.5354 113.68 22.4254 113.276 21.3826ZM110.484 26.9154C110.216 27.6387 109.812 28.3113 109.274 28.8667C108.803 29.3714 108.231 29.7752 107.592 30.0439C106.969 30.3298 106.28 30.4642 105.59 30.4642C104.901 30.4642 104.228 30.3126 103.589 30.0439C102.95 29.758 102.378 29.3714 101.907 28.8667C101.369 28.2949 100.965 27.6387 100.697 26.9154C100.428 26.192 100.31 25.4187 100.344 24.6446C100.31 23.8713 100.428 23.0971 100.697 22.3737C100.965 21.6504 101.386 20.9778 101.907 20.4224C102.378 19.9178 102.95 19.5139 103.589 19.2452C104.212 18.9593 104.901 18.825 105.59 18.825C106.28 18.825 106.953 18.9765 107.592 19.2452C108.231 19.5311 108.803 19.9178 109.274 20.4224C109.812 20.9942 110.216 21.6504 110.484 22.3737C110.754 23.0971 110.871 23.8704 110.837 24.6446C110.872 25.4187 110.754 26.1929 110.484 26.9154Z"
      fill="#5F6368"/>
      <path
      d="M135.168 16.0965C134.059 16.063 132.95 16.3325 131.959 16.8716C130.984 17.4106 130.161 18.2193 129.59 19.1794C128.682 17.1411 127.002 16.1138 124.515 16.1138C123.524 16.131 122.565 16.4169 121.709 16.9224C120.834 17.3607 120.112 18.0341 119.591 18.8591H119.439V16.6201H116.598V32.6373H119.612V23.7908C119.594 22.5465 119.999 21.3349 120.774 20.3601C121.111 19.889 121.549 19.5187 122.055 19.25C122.56 18.9977 123.133 18.8634 123.706 18.8634C124.194 18.8126 124.683 18.897 125.154 19.0649C125.626 19.25 126.03 19.5187 126.384 19.8718C126.974 20.5443 127.277 21.6879 127.277 23.3026V32.6373H130.205V23.7908C130.187 22.5465 130.608 21.3349 131.367 20.3601C131.704 19.889 132.142 19.5187 132.648 19.25C133.153 18.9977 133.726 18.8634 134.298 18.8634C134.804 18.8126 135.292 18.897 135.764 19.0649C136.219 19.25 136.64 19.5187 136.994 19.8718C137.584 20.5443 137.87 21.6879 137.87 23.3026V32.6373H140.884V22.8341C140.901 20.7622 140.413 19.1286 139.455 17.9161C138.496 16.7028 137.067 16.0965 135.168 16.0965Z"
      fill="#5F6368"/>
      <path
      d="M154.567 16.6871C153.575 16.2832 152.515 16.098 151.456 16.1489C150.397 16.1316 149.32 16.334 148.345 16.7715C147.369 17.2089 146.495 17.8651 145.805 18.672C144.342 20.3374 143.552 22.4731 143.602 24.6767C143.517 26.9303 144.341 29.134 145.872 30.7822C146.629 31.5891 147.538 32.2117 148.563 32.632C149.573 33.0522 150.683 33.2373 151.776 33.2037C153.357 33.2546 154.887 32.8171 156.182 31.9422C157.326 31.1689 158.217 30.0752 158.755 28.8137L156.081 27.7036C155.308 29.537 153.861 30.4455 151.691 30.4455C151.052 30.4627 150.413 30.3275 149.808 30.0924C149.219 29.8574 148.664 29.5034 148.21 29.0496C147.185 28.0403 146.613 26.6617 146.596 25.2313H159.277L159.311 24.6759C159.311 22.0856 158.604 20.0171 157.175 18.4696C156.434 17.6963 155.56 17.0909 154.567 16.6871ZM146.814 22.734C147.066 21.6403 147.655 20.6655 148.495 19.925C149.303 19.2188 150.329 18.8485 151.388 18.8658C152.918 18.8658 154.062 19.286 154.836 20.1101C155.559 20.7999 155.997 21.742 156.098 22.734H146.814Z"
      fill="#5F6368"/>
    </g>
  </symbol>
    <symbol id="write-generate" width="334" height="90" viewBox="0 0 334 90" fill="none"
  xmlns="http://www.w3.org/2000/svg">
  <rect width="333" height="89" rx="8.29" fill="#F8F9FA" />
  <g mask="url(#write_generate_mask)">
    <rect x="13.8711" y="17.6484" width="283" height="52" fill="url(#write_generate_gradient)" />
  </g>
</symbol><symbol id="write-icon" width="87" height="87" viewBox="0 0 87 87" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#write_icon_filter)">
    <rect x="8" y="6" width="71" height="71" rx="11.8677" fill="#D7F6FF"
      shape-rendering="crispEdges" />
    <g clip-path="url(#write_icon_clip)">
      <path
        d="M34.2268 52.6181H36.1182L52.0653 36.671L50.1739 34.7796L34.2268 50.7267V52.6181ZM31.6307 55.2142V49.6512L52.0653 29.2908C52.3126 29.0435 52.5846 28.8581 52.8812 28.7345C53.2027 28.5861 53.5364 28.512 53.8826 28.512C54.2287 28.512 54.5625 28.5861 54.8839 28.7345C55.2053 28.8581 55.4897 29.0435 55.7369 29.2908L57.5912 31.1451C57.8385 31.3923 58.0239 31.6767 58.1475 31.9981C58.2711 32.3195 58.3329 32.6533 58.3329 32.9994C58.3329 33.3208 58.2711 33.6423 58.1475 33.9637C58.0239 34.2851 57.8385 34.5694 57.5912 34.8167L37.1937 55.2142H31.6307ZM55.6998 32.9623L53.8826 31.1451L55.6998 32.9623ZM51.1382 35.7438L50.1739 34.7796L52.0653 36.671L51.1382 35.7438ZM35.3394 41.6776C35.3394 39.4525 34.5606 37.5734 33.0029 36.0405C31.47 34.4829 29.591 33.7041 27.3658 33.7041C29.591 33.7041 31.47 32.9376 33.0029 31.4047C34.5606 29.8471 35.3394 27.9557 35.3394 25.7305C35.3394 27.9557 36.1058 29.8471 37.6387 31.4047C39.1964 32.9376 41.0878 33.7041 43.3129 33.7041C41.0878 33.7041 39.1964 34.4829 37.6387 36.0405C36.1058 37.5734 35.3394 39.4525 35.3394 41.6776Z"
        fill="#202124" />
    </g>
  </g>
</symbol><symbol viewBox="0 0 64 64" fill="none" id="chrome-logo" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#chrome_logo_clip)">
  <path d="M32 47.986c8.837 0 16-7.164 16-16 0-8.837-7.163-16-16-16s-16 7.163-16 16c0 8.836 7.163 16 16 16Z" fill="#fff"/>
  <path d="M45.856 40 32.015 63.975A31.95 31.95 0 0 0 59.685 16H32l-.023.014A15.978 15.978 0 0 1 45.857 40Z" fill="url(#chrome_logo_paint_0)"/>
  <path d="M18.143 40 4.303 16.027a31.95 31.95 0 0 0 27.713 47.949L45.857 40v-.026A15.98 15.98 0 0 1 18.144 40Z" fill="url(#chrome_logo_paint_1)"/>
  <path d="M32 44.667c6.995 0 12.667-5.67 12.667-12.666S38.995 19.334 32 19.334c-6.996 0-12.667 5.671-12.667 12.667 0 6.995 5.671 12.666 12.667 12.666Z" fill="#1A73E8"/>
  <path d="M32 16h27.683a31.95 31.95 0 0 0-55.382.025L18.143 40l.024.013A15.98 15.98 0 0 1 32 16Z" fill="url(#chrome_logo_paint_2)"/>
  </g>
</symbol><symbol id="play-icon" width="16" height="16" viewBox="0 0 16 16" fill="none"
  xmlns="http://www.w3.org/2000/svg">
  <path d="M4.33398 3.33325V12.6666L11.6673 7.99992L4.33398 3.33325Z" fill="currentColor" />
</symbol><symbol id="pause-icon" width="16" height="16" viewBox="0 0 16 16" fill="none"
  xmlns="http://www.w3.org/2000/svg">
  <path d="M4 12.6666H6.66667V3.33325H4V12.6666ZM9.33333 3.33325V12.6666H12V3.33325H9.33333Z"
    fill="currentColor" />
</symbol><symbol viewBox="0 0 32 32" id="plus" xmlns="http://www.w3.org/2000/svg">
  <path d="M25.333 17.333h-8v8h-2.666v-8h-8v-2.666h8v-8h2.666v8h8v2.666Z"/>
</symbol><symbol viewBox="0 0 32 32" id="arrow-forward" xmlns="http://www.w3.org/2000/svg">
  <path d="m8.153 26.973 2.36 2.36L23.847 16 10.513 2.667l-2.36 2.36L19.127 16 8.153 26.973Z"/>
</symbol><svg id="pause" width="16" height="16" viewBox="0 0 16 16"  xmlns="http://www.w3.org/2000/svg">
<path d="M4 12.6666H6.66667V3.33325H4V12.6666ZM9.33333 3.33325V12.6666H12V3.33325H9.33333Z" />
</svg><svg id="play" width="16" height="16" viewBox="0 0 16 16"  xmlns="http://www.w3.org/2000/svg">
<path d="M4.33398 3.33325V12.6666L11.6673 7.99992L4.33398 3.33325Z" />
</svg></svg><svg class="svg-assets" xmlns="http://www.w3.org/2000/svg">
  <symbol id="benefits-icon-fast" viewBox="0 0 72 72">
    <g fill="none" transform="">
      <circle cx="36" cy="36" fill="#f1f3f4" r="36" />
      <path d="m54 21-21.46 16.11c-2.0720202 1.4252202-3.0353697 3.991385-2.4131169 6.4280471.6222527 2.436662 2.698054 4.2267159 5.1997238 4.4839484 2.5016699.2572325 4.898307-1.0729459 6.0033931-3.3319955z"
        fill="#a8dab5" />
      <path d="m36 15c-14.9116882 0-27 12.0883118-27 27h12c0-8.2842712 6.7157288-15 15-15 2.5822819.0158195 5.1157411.7051962 7.35 2l10.1-7.58c-4.8673061-4.1484477-11.0546686-6.4248298-17.45-6.42z"
        fill="#5ab974" />
      <path d="m63.0000007 42c.0018026-7.826907-3.4041798-15.2667699-9.3300007-20.38l-5.95 11.12c2.1114977 2.6255641 3.2680635 5.8907468 3.28 9.26z"
        fill="#07732d" />
    </g>
  </symbol>

  <symbol id="benefits-icon-search" viewBox="0 0 72 72">
    <g fill="none" transform="">
      <circle cx="36" cy="36" fill="#f1f3f4" r="36"/>
      <circle cx="36" cy="36" fill="#4285f4" r="27"/>
      <path d="m19.25 17h-1.18l-.42-.41c3.4163828-3.9807309 3.0751127-9.94998778-.7726938-13.5154156-3.8478065-3.56542783-9.82572749-3.45161613-13.53503271.25768909s-3.82311692 9.68722621-.25768909 13.53503271c3.56542782 3.8478065 9.5346847 4.1890766 13.5154156.7726938l.41.42v1.18l7.5 7.48 2.23-2.23zm-9 0c-3.72792206 0-6.75-3.0220779-6.75-6.75 0-3.72792206 3.02207794-6.75 6.75-6.75 3.7279221 0 6.75 3.02207794 6.75 6.75.0026593 1.7910252-.7076454 3.5094597-1.9740929 4.7759071-1.2664474 1.2664475-2.9848819 1.9767522-4.7759071 1.9740929z" fill="#aecbfa" transform="translate(22 22)"/>
      <path d="m38.59 39.65.41.41v1.19l7.5 7.48 2.23-2.23-7.48-7.5h-1.19l-.41-.41" fill="#d2e3fc"/>
    </g>
  </symbol>

  <symbol id="benefits-icon-shield" viewBox="0 0 72 72">
    <g fill="none" fill-rule="evenodd" transform="">
      <g fill-rule="nonzero">
        <circle cx="36" cy="36" fill="#f1f3f4" r="36"/>
        <path d="m15 36h21v-24l-21 9z" fill="#fbbc07"/>
        <path d="m57 36h-21v-24l21 9z" fill="#fde293"/>
        <path d="m51 48-15 12v-24h21z" fill="#fbbc07"/>
        <path d="m21 48 15 12v-24h-21z" fill="#f29902"/>
      </g>
      <path d="m36 60s19.5-7.5 21-27v-12l-21-9-21 9v12c1.5 19.5 21 27 21 27z" stroke="#fbbc07" stroke-width="3"/>
    </g>
  </symbol>

  <symbol id="benefits-icon-world" viewBox="0 0 72 72">
    <g fill="none">
      <circle cx="36" cy="36" fill="#f1f3f4" r="36"/>
      <rect fill="#e94235" height="36" rx="3" width="48" x="12" y="18"/>
      <path d="m30 57h12v3h-12z" fill="#f28b82"/>
      <path d="m15 0c-8.28427125 0-15 6.71572875-15 15 0 8.2842712 6.71572875 15 15 15 8.2842712 0 15-6.7157288 15-15 0-3.9782473-1.5803526-7.79355604-4.3933983-10.60660172-2.8130457-2.81304567-6.6283544-4.39339828-10.6066017-4.39339828zm10.4 9h-4.4c-.4694198-1.85782861-1.1645942-3.65117719-2.07-5.34 2.7214473.94778304 5.010938 2.84326843 6.45 5.34zm-10.4-5.94c1.2582487 1.8181661 2.2233259 3.8225572 2.86 5.94h-5.72c.6366741-2.1174428 1.6017513-4.1218339 2.86-5.94zm-11.61 14.94c-.52015873-1.9661794-.52015873-4.0338206 0-6h5.07c-.13069673.9948237-.20082595 1.9966697-.21 3 .00917405 1.0033303.07930327 2.0051763.21 3zm1.23 3h4.38c.46941978 1.8578286 1.1645942 3.6511772 2.07 5.34-2.72144725-.947783-5.01093804-2.8432684-6.45-5.34zm4.38-12h-4.38c1.44888811-2.50414408 3.75368341-4.40053959 6.49-5.34-.9192347 1.68693471-1.62786952 3.48035185-2.11 5.34zm6 17.94c-1.2582487-1.8181661-2.2233259-3.8225572-2.86-5.94h5.73c-.6397717 2.1180499-1.608239 4.1224736-2.87 5.94zm3.51-8.94h-7c-.1481689-.9933741-.2283553-1.995704-.24-3 .0110489-1.0043232.0912385-2.0066938.24-3h7c.1487615.9933062.2289511 1.9956768.24 3-.0116447 1.004296-.0918311 2.0066259-.24 3zm.38 8.34c.9192347-1.6869347 1.6278695-3.4803518 2.11-5.34h4.42c-1.4586952 2.5116011-3.7788167 4.4089133-6.53 5.34zm2.65-8.34c.1306967-.9948237.2008259-1.9966697.21-3-.0091741-1.0033303-.0793033-2.0051763-.21-3h5.07c.5201587 1.9661794.5201587 4.0338206 0 6z" fill="#f6aea9" transform="translate(21 21)"/>
      <rect fill="#c5221f" height="24" rx="3" width="12" x="45" y="36"/>
    </g>
  </symbol>
</svg><script type="application/ld+json" nonce="ybHWTf_2bjronyNWExNH1Q">
{
  "@context": "http://schema.org",
  "@id": "https://www.google.com/chrome/",
  "@type": "Organization",
  "name": "Google Chrome",
  "url": "https://www.google.com/chrome/",
  "logo": "https://www.google.com/chrome/static/images/chrome-logo_112px.svg",
  "brand": "Google Chrome",
  "parentOrganization": "Google LLC",
  "sameAs": [
      "https://twitter.com/googlechrome",
      "https://blog.google/products/chrome/",
      "https://www.youtube.com/googlechrome",
      "https://www.facebook.com/googlechrome",
      "https://en.wikipedia.org/wiki/Google_Chrome",
      "https://www.wikidata.org/wiki/Q777"
  ]
}
</script>
    <script defer
        src="//www.gstatic.com/external_hosted/intersectionobserver_polyfill/intersection-observer.min.js" nonce="ybHWTf_2bjronyNWExNH1Q"></script>
    <script defer src="//www.gstatic.com/external_hosted/lottie/lottie_light.js" nonce="ybHWTf_2bjronyNWExNH1Q"></script>
    <script defer src="//www.gstatic.com/external_hosted/greensock3/dist/gsap.min.js" nonce="ybHWTf_2bjronyNWExNH1Q"></script>
    <script defer src="//www.gstatic.com/external_hosted/greensock3/dist/ScrollTrigger.min.js" nonce="ybHWTf_2bjronyNWExNH1Q"></script>
    <script defer src="/chrome/static/js/pages/home/<USER>" nonce="ybHWTf_2bjronyNWExNH1Q"></script>
    <script defer src="/chrome/static/js/installer.min.js" nonce="ybHWTf_2bjronyNWExNH1Q"></script>
  </body>
</html>