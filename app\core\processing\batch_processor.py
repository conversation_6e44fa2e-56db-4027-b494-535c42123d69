"""Batch processing system for ML operations."""

from typing import Dict, List, Optional, Any, Callable
import asyncio
from datetime import datetime
import logging
from dataclasses import dataclass
import json
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
from queue import Queue, PriorityQueue
import uuid

logger = logging.getLogger(__name__)

@dataclass
class BatchTask:
    """Batch processing task."""
    task_id: str
    priority: int
    input_data: Dict[str, Any]
    task_type: str
    created_at: datetime
    timeout: Optional[float] = None
    retries: int = 0
    max_retries: int = 3

class BatchProcessor:
    """Batch processing system for ML operations."""
    
    def __init__(self, num_workers: Optional[int] = None):
        self.task_queue = PriorityQueue()
        self.result_store = {}
        self.task_status = {}
        self.worker_pool = ThreadPoolExecutor(
            max_workers=num_workers or multiprocessing.cpu_count()
        )
        self.task_handlers = {}
        self.running = True
        
        # Start worker tasks
        self.workers = [
            asyncio.create_task(self._worker_loop(i))
            for i in range(self.worker_pool._max_workers)
        ]
        
        # Start monitoring task
        self.monitor_task = asyncio.create_task(self._monitor_loop())
    
    async def submit_task(
        self,
        task_type: str,
        input_data: Dict[str, Any],
        priority: int = 1,
        timeout: Optional[float] = None
    ) -> str:
        """Submit a task for batch processing."""
        task = BatchTask(
            task_id=str(uuid.uuid4()),
            priority=priority,
            input_data=input_data,
            task_type=task_type,
            created_at=datetime.utcnow(),
            timeout=timeout
        )
        
        # Add to queue with priority
        await self._enqueue_task(task)
        
        return task.task_id
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of a task."""
        return self.task_status.get(task_id, {
            "status": "unknown",
            "error": "Task not found"
        })
    
    async def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the result of a completed task."""
        return self.result_store.get(task_id)
    
    def register_task_handler(
        self,
        task_type: str,
        handler: Callable[[Dict[str, Any]], Any]
    ):
        """Register a handler for a task type."""
        self.task_handlers[task_type] = handler
    
    async def _enqueue_task(self, task: BatchTask):
        """Add task to queue with priority."""
        # Update task status
        self.task_status[task.task_id] = {
            "status": "queued",
            "queued_at": datetime.utcnow().isoformat(),
            "priority": task.priority,
            "task_type": task.task_type
        }
        
        # Add to priority queue (lower number = higher priority)
        self.task_queue.put((-task.priority, task))
    
    async def _worker_loop(self, worker_id: int):
        """Worker loop for processing tasks."""
        logger.info(f"Starting worker {worker_id}")
        
        while self.running:
            try:
                # Get task from queue
                _, task = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self.task_queue.get,
                    True,  # blocking
                    1.0    # timeout
                )
                
                # Process task
                await self._process_task(task, worker_id)
                
                # Mark task as done
                self.task_queue.task_done()
                
            except Exception as e:
                if self.running:  # Only log if not shutting down
                    logger.error(f"Error in worker {worker_id}: {str(e)}")
                await asyncio.sleep(1)
    
    async def _process_task(self, task: BatchTask, worker_id: int):
        """Process a single task."""
        try:
            # Update status
            self.task_status[task.task_id].update({
                "status": "processing",
                "started_at": datetime.utcnow().isoformat(),
                "worker_id": worker_id
            })
            
            # Get handler
            handler = self.task_handlers.get(task.task_type)
            if not handler:
                raise ValueError(f"No handler for task type: {task.task_type}")
            
            # Execute task with timeout if specified
            if task.timeout:
                result = await asyncio.wait_for(
                    self._execute_task(handler, task.input_data),
                    timeout=task.timeout
                )
            else:
                result = await self._execute_task(handler, task.input_data)
            
            # Store result
            self.result_store[task.task_id] = {
                "result": result,
                "completed_at": datetime.utcnow().isoformat()
            }
            
            # Update status
            self.task_status[task.task_id].update({
                "status": "completed",
                "completed_at": datetime.utcnow().isoformat()
            })
            
        except asyncio.TimeoutError:
            await self._handle_task_timeout(task)
            
        except Exception as e:
            await self._handle_task_error(task, str(e))
    
    async def _execute_task(
        self,
        handler: Callable[[Dict[str, Any]], Any],
        input_data: Dict[str, Any]
    ) -> Any:
        """Execute task in thread pool."""
        return await asyncio.get_event_loop().run_in_executor(
            self.worker_pool,
            handler,
            input_data
        )
    
    async def _handle_task_timeout(self, task: BatchTask):
        """Handle task timeout."""
        if task.retries < task.max_retries:
            # Retry task
            task.retries += 1
            await self._enqueue_task(task)
            
            self.task_status[task.task_id].update({
                "status": "retrying",
                "retries": task.retries,
                "last_error": "Task timed out"
            })
        else:
            # Mark as failed
            self.task_status[task.task_id].update({
                "status": "failed",
                "error": "Task timed out",
                "failed_at": datetime.utcnow().isoformat()
            })
    
    async def _handle_task_error(self, task: BatchTask, error: str):
        """Handle task error."""
        if task.retries < task.max_retries:
            # Retry task
            task.retries += 1
            await self._enqueue_task(task)
            
            self.task_status[task.task_id].update({
                "status": "retrying",
                "retries": task.retries,
                "last_error": error
            })
        else:
            # Mark as failed
            self.task_status[task.task_id].update({
                "status": "failed",
                "error": error,
                "failed_at": datetime.utcnow().isoformat()
            })
    
    async def _monitor_loop(self):
        """Monitor loop for cleanup and maintenance."""
        while self.running:
            try:
                await self._cleanup_old_results()
                await self._check_stuck_tasks()
                await asyncio.sleep(60)  # Run every minute
                
            except Exception as e:
                logger.error(f"Error in monitor loop: {str(e)}")
                await asyncio.sleep(5)
    
    async def _cleanup_old_results(self):
        """Clean up old results and status entries."""
        current_time = datetime.utcnow()
        
        # Keep results for 24 hours
        cutoff = current_time.timestamp() - (24 * 60 * 60)
        
        # Clean up completed results
        self.result_store = {
            task_id: result
            for task_id, result in self.result_store.items()
            if datetime.fromisoformat(result["completed_at"]).timestamp() > cutoff
        }
        
        # Clean up old status entries
        self.task_status = {
            task_id: status
            for task_id, status in self.task_status.items()
            if (
                status["status"] not in ["completed", "failed"] or
                datetime.fromisoformat(
                    status.get("completed_at", status.get("failed_at"))
                ).timestamp() > cutoff
            )
        }
    
    async def _check_stuck_tasks(self):
        """Check for and handle stuck tasks."""
        current_time = datetime.utcnow()
        
        for task_id, status in self.task_status.items():
            if status["status"] == "processing":
                started_at = datetime.fromisoformat(status["started_at"])
                
                # If task has been processing for more than 1 hour
                if (current_time - started_at).total_seconds() > 3600:
                    logger.warning(f"Found stuck task: {task_id}")
                    
                    # Mark as failed
                    status.update({
                        "status": "failed",
                        "error": "Task appears to be stuck",
                        "failed_at": current_time.isoformat()
                    })
    
    async def shutdown(self):
        """Shutdown the batch processor."""
        self.running = False
        
        # Cancel all workers
        for worker in self.workers:
            worker.cancel()
        
        # Cancel monitor task
        self.monitor_task.cancel()
        
        # Shutdown thread pool
        self.worker_pool.shutdown(wait=True)
""" 