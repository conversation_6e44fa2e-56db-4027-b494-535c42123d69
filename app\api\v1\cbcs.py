from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy import select
from app.core.database import get_db
from app.models.cbcs import CBCSScan, Document
from app.models.document_type import DocumentType
from app.schemas.cbcs import CBCSScanResponse
from app.core.security import get_current_active_user
from app.models.user import User
from app.services.ocr_service import process_document
from typing import List, Dict
import os
from app.core.config import settings
import logging
from app.services.cbcs_service import CBCSService
from app.schemas.cbcs import ScanResponse, ScanCreate, ScanUpdate, ScanAnalysis

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/cbcs", tags=["cbcs"])

def upload_document(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    try:
        # Validate file type
        if not file.content_type in ['application/pdf', 'image/jpeg', 'image/png']:
            raise HTTPException(status_code=400, detail="Unsupported file type")

        # Save the file temporarily
        file_path = os.path.join(settings.UPLOAD_DIR, file.filename)
        with open(file_path, "wb") as f:
            content = file.file.read()
            f.write(content)

        # Create document record
        document = Document(
            user_id=current_user.id,
            filename=file.filename,
            file_path=file_path,
            mime_type=file.content_type,
            status="pending"
        )
        db.add(document)
        db.commit()
        db.refresh(document)

        # Create CBCS scan record
        scan = CBCSScan(
            user_id=current_user.id,
            document_id=document.id,
            status="pending"
        )
        db.add(scan)
        db.commit()
        db.refresh(scan)

        # Offload OCR processing to Celery
        process_document.delay(file_path, {"use_tesseract": True, "use_easyocr": True})

        return scan
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

def get_scans(db: Session = Depends(get_db), current_user: User = Depends(get_current_active_user)):
    query = select(CBCSScan).where(CBCSScan.user_id == current_user.id)
    result = db.execute(query)
    scans = result.scalars().all()
    return scans

def get_compliance_reviews(db: Session = Depends(get_db), current_user: User = Depends(get_current_active_user)):
    query = select(ComplianceReview).where(ComplianceReview.reviewer_id == current_user.id)
    result = db.execute(query)
    reviews = result.scalars().all()
    return reviews

@router.post("/scan/{document_id}", response_model=ScanResponse)
async def create_document_scan(
    document_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict:
    """Create a new CBCS scan for a document."""
    service = CBCSService(db)
    scan = await service.create_scan(document_id)
    return scan

@router.post("/scan/{scan_id}/start", response_model=ScanResponse)
async def start_scan(
    scan_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict:
    """Start processing a CBCS scan."""
    service = CBCSService(db)
    scan = await service.start_scan(scan_id)
    return scan

@router.post("/scan/{scan_id}/complete", response_model=ScanResponse)
async def complete_scan(
    scan_id: int,
    results: Dict,
    error_message: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict:
    """Complete a CBCS scan with results."""
    service = CBCSService(db)
    scan = await service.complete_scan(scan_id, results, error_message)
    return scan

@router.get("/scan/{scan_id}", response_model=ScanResponse)
async def get_scan(
    scan_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict:
    """Get a specific CBCS scan."""
    service = CBCSService(db)
    scan = await service.get_scan(scan_id)
    return scan

@router.get("/document/{document_id}/scans", response_model=List[ScanResponse])
async def get_document_scans(
    document_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> List[Dict]:
    """Get all CBCS scans for a document."""
    service = CBCSService(db)
    scans = await service.get_document_scans(document_id)
    return scans

@router.get("/document/{document_id}/latest-scan", response_model=ScanResponse)
async def get_latest_scan(
    document_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict:
    """Get the most recent CBCS scan for a document."""
    service = CBCSService(db)
    scan = await service.get_latest_scan(document_id)
    if not scan:
        raise HTTPException(status_code=404, detail="No scans found for document")
    return scan

@router.get("/scan/{scan_id}/analysis", response_model=ScanAnalysis)
async def analyze_scan(
    scan_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict:
    """Analyze the results of a completed CBCS scan."""
    service = CBCSService(db)
    analysis = await service.analyze_scan_results(scan_id)
    return analysis