from typing import Optional, BinaryIO
from fastapi import UploadFile
import aiofiles
import os
from datetime import datetime
from app.core.config import settings

class DocumentStorage:
    def __init__(self):
        self.base_path = settings.DOCUMENT_STORAGE_PATH
        os.makedirs(self.base_path, exist_ok=True)
    
    async def save_document(self, file: UploadFile, dr_number: str) -> str:
        """Save an uploaded document to storage"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{dr_number}_{timestamp}_{file.filename}"
        filepath = os.path.join(self.base_path, filename)
        
        async with aiofiles.open(filepath, 'wb') as out_file:
            content = await file.read()
            await out_file.write(content)
        
        return filepath
    
    async def get_document(self, filepath: str) -> Optional[BinaryIO]:
        """Retrieve a document from storage"""
        if not os.path.exists(filepath):
            return None
        return open(filepath, 'rb')
    
    async def delete_document(self, filepath: str) -> bool:
        """Delete a document from storage"""
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                return True
            return False
        except Exception:
            return False
    
    async def list_documents(self, dr_number: str) -> list[str]:
        """List all documents for a specific disaster"""
        try:
            files = os.listdir(self.base_path)
            return [f for f in files if f.startswith(dr_number)]
        except Exception:
            return []
