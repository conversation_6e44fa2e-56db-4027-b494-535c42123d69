from fastapi import HTT<PERSON>Ex<PERSON>, UploadFile
from typing import Set, Dict, Any, Optional
from datetime import datetime
import re

class FileValidator:
    # 10MB max file size
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes
    ALLOWED_CONTENT_TYPES = {
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/png',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }

    @classmethod
    async def validate_file(cls, file: UploadFile, allowed_types: Set[str] = None) -> None:
        """Validate file upload."""
        if not file:
            raise HTTPException(status_code=400, detail="No file provided")

        # Check file size
        file_size = 0
        chunk_size = 8192  # 8KB chunks
        
        while chunk := await file.read(chunk_size):
            file_size += len(chunk)
            if file_size > cls.MAX_FILE_SIZE:
                await file.seek(0)  # Reset file pointer
                raise HTTPException(
                    status_code=400,
                    detail=f"File size exceeds maximum allowed size of {cls.MAX_FILE_SIZE // (1024*1024)}MB"
                )
        
        await file.seek(0)  # Reset file pointer for subsequent reads

        # Check content type
        content_type = file.content_type
        allowed = allowed_types if allowed_types is not None else cls.ALLOWED_CONTENT_TYPES
        
        if content_type not in allowed:
            raise HTTPException(
                status_code=400,
                detail=f"File type '{content_type}' not allowed. Allowed types: {', '.join(allowed)}"
            )

class FEMAValidator:
    FEMA_ID_PATTERN = r'^[A-Z]{2}-\d{4}-[A-Z]{2}-\d{4}$'
    STATE_CODE_PATTERN = r'^[A-Z]{2}$'
    COUNTY_CODE_PATTERN = r'^\d{3}$'
    
    @staticmethod
    def validate_fema_id(fema_id: str) -> bool:
        """Validate FEMA disaster declaration number format."""
        if not fema_id:
            return True  # Optional field
        if not re.match(FEMAValidator.FEMA_ID_PATTERN, fema_id):
            raise HTTPException(
                status_code=400,
                detail="Invalid FEMA ID format. Expected format: XX-####-XX-####"
            )
        return True

    @staticmethod
    def validate_state_code(state_code: str) -> bool:
        """Validate state code format."""
        if not state_code:
            return True  # Optional field
        if not re.match(FEMAValidator.STATE_CODE_PATTERN, state_code):
            raise HTTPException(
                status_code=400,
                detail="Invalid state code format. Expected format: XX (two uppercase letters)"
            )
        return True

    @staticmethod
    def validate_county_code(county_code: str) -> bool:
        """Validate county code format."""
        if not county_code:
            return True  # Optional field
        if not re.match(FEMAValidator.COUNTY_CODE_PATTERN, county_code):
            raise HTTPException(
                status_code=400,
                detail="Invalid county code format. Expected format: ### (three digits)"
            )
        return True

    @staticmethod
    def validate_incident_period(start: datetime, end: Optional[datetime] = None) -> bool:
        """Validate incident period dates."""
        if not end:
            return True
        if end < start:
            raise HTTPException(
                status_code=400,
                detail="Incident period end date must be after start date"
            )
        return True

    @staticmethod
    def validate_mitigation_plan(data: Dict[str, Any]) -> bool:
        """Validate mitigation plan data."""
        if 'mitigation_plan_expires' in data and data['mitigation_plan_expires']:
            expires = data['mitigation_plan_expires']
            if expires < datetime.utcnow():
                raise HTTPException(
                    status_code=400,
                    detail="Mitigation plan expiration date must be in the future"
                )
        
        if 'mitigation_funding_amount' in data and data['mitigation_funding_amount']:
            amount = data['mitigation_funding_amount']
            if amount < 0:
                raise HTTPException(
                    status_code=400,
                    detail="Mitigation funding amount cannot be negative"
                )
        
        return True

    @staticmethod
    def validate_compliance_update(data: Dict[str, Any]) -> bool:
        """Validate compliance update data."""
        if 'compliance_percentage' in data and data['compliance_percentage'] is not None:
            percentage = data['compliance_percentage']
            if not 0 <= percentage <= 100:
                raise HTTPException(
                    status_code=400,
                    detail="Compliance percentage must be between 0 and 100"
                )
        
        if 'review_frequency_days' in data and data['review_frequency_days'] is not None:
            days = data['review_frequency_days']
            if days < 1:
                raise HTTPException(
                    status_code=400,
                    detail="Review frequency must be at least 1 day"
                )
        
        return True
