#!/usr/bin/env python3
import os
import sys
import logging
from pathlib import Path
import shutil
import alembic.config
from sqlalchemy_utils import database_exists, create_database, drop_database
from app.core.database import engine, Base
from app.core.config import settings

# Import all models to ensure they're registered with SQLAlchemy
from app.models import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def reset_migrations():
    """Reset the migrations directory"""
    migrations_dir = Path("migrations")
    versions_dir = migrations_dir / "versions"
    
    # Remove old versions
    if versions_dir.exists():
        logger.info("Removing old migration versions...")
        shutil.rmtree(versions_dir)
        os.makedirs(versions_dir)

def reset_database():
    """Reset the database"""
    db_url = str(engine.url)
    
    logger.info("Checking database...")
    if database_exists(db_url):
        logger.info("Dropping existing database...")
        drop_database(db_url)
    
    logger.info("Creating new database...")
    create_database(db_url)

def init_alembic():
    """Initialize Alembic migrations"""
    logger.info("Initializing Alembic...")
    alembicArgs = [
        '--raiseerr',
        'revision',
        '--autogenerate',
        '-m', 'Initial migration'
    ]
    alembic.config.main(argv=alembicArgs)

def upgrade_head():
    """Upgrade database to latest revision"""
    logger.info("Upgrading database to head...")
    alembicArgs = [
        '--raiseerr',
        'upgrade', 'head'
    ]
    alembic.config.main(argv=alembicArgs)

def init_db():
    """Initialize the database and migrations"""
    try:
        # Reset everything
        reset_migrations()
        reset_database()
        
        # Create all tables
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        
        # Setup and run migrations
        init_alembic()
        upgrade_head()
        
        logger.info("Database initialization completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False

if __name__ == "__main__":
    success = init_db()
    sys.exit(0 if success else 1)
