from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models import (
    Base, User, Document, PolicyDocument, ApplicantDocument,
    PolicyComparisonAssociation, DocumentComparison, FEMAAppeal
)
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_db():
    """Initialize the database"""
    # Create database URL
    SQLALCHEMY_DATABASE_URL = settings.SQLITE_DB

    # Create engine with SQLite-specific settings
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={"check_same_thread": False}  # Needed for SQLite
    )

    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Successfully created database tables")

        # Create session
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        # Initialize any required data here
        # For example, create admin user if it doesn't exist
        # db.add(admin_user)
        # db.commit()

        db.close()

    except Exception as e:
        logger.error(f"Error creating database: {e}")
        raise

if __name__ == "__main__":
    init_db()
