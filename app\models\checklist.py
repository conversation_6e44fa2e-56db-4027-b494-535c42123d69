from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, JSON, Float, Enum as SQLEnum
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base_class import Base
from app.schemas.fema_checklist import RequirementStatus, ComplianceStatus

class ChecklistItem(Base):
    __tablename__ = "checklist_items"

    id = Column(Integer, primary_key=True, index=True)
    category = Column(String, index=True)
    requirement = Column(String, nullable=False)
    description = Column(String, nullable=True)
    status = Column(SQLEnum(RequirementStatus), default=RequirementStatus.INCOMPLETE)
    notes = Column(String, nullable=True)
    
    # Enhanced fields for advanced compliance checking
    required_keywords = Column(JSON, nullable=True)  # List of required keywords/phrases
    requires_dates = Column(Boolean, default=False)
    date_format = Column(String, nullable=True)  # Expected date format if requires_dates is True
    requires_amounts = Column(Boolean, default=False)
    requires_supporting_docs = Column(Boolean, default=False)
    supporting_doc_types = Column(JSON, nullable=True)  # List of required supporting document types
    
    # Compliance scoring and validation
    confidence_threshold = Column(Float, default=0.7)  # Minimum confidence score for compliance
    weight = Column(Float, default=1.0)  # Weight of this requirement in overall compliance
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=True)
    document = relationship("Document", back_populates="checklist_items")

    def __repr__(self):
        return f"<ChecklistItem(id={self.id}, category={self.category}, requirement={self.requirement})>" 