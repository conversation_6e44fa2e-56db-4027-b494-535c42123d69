import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../config';

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  is_active: boolean;
}

interface AuthContextType {
  user: User | null;
  setAuthData: (data: any) => void;
  isAuthenticated: boolean;
  login: (formData: FormData) => Promise<any>;
  logout: () => void;
  loading: boolean;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  
  // Setup axios interceptor for authentication
  useEffect(() => {
    const interceptor = axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Unauthorized - clear authentication state
          logout();
        }
        return Promise.reject(error);
      }
    );
    
    return () => {
      axios.interceptors.response.eject(interceptor);
    };
  }, []);

  // Load user on initial render if token exists
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('access_token');
        if (token) {
          // Set default authorization header
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          // Fetch current user info
          const response = await axios.get(`${API_BASE_URL}/auth/me`);
          setUser(response.data);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error("Authentication validation error:", error);
        localStorage.removeItem('access_token');
        delete axios.defaults.headers.common['Authorization'];
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, []);

  const setAuthData = (data: any) => {
    setUser(data.user || data);
    setIsAuthenticated(true);
    
    if (data.access_token) {
      localStorage.setItem('access_token', data.access_token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${data.access_token}`;
    }
  };

  // Login method that properly connects to the backend
  const login = async (formData: FormData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, formData);
      
      if (response.data.access_token) {
        // Store token and setup authentication
        localStorage.setItem('access_token', response.data.access_token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.access_token}`;
        
        // Fetch user details
        const userResponse = await axios.get(`${API_BASE_URL}/auth/me`);
        const userData = userResponse.data;
        
        setUser(userData);
        setIsAuthenticated(true);
        return { ...response.data, user: userData };
      } else if (response.data.requires_second_factor) {
        // Handle 2FA if implemented
        return response.data;
      }
      
      return response.data;
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    delete axios.defaults.headers.common['Authorization'];
    setUser(null);
    setIsAuthenticated(false);
  };

  return (
    <AuthContext.Provider value={{ user, setAuthData, isAuthenticated, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};
